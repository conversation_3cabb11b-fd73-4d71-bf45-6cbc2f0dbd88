query GetAllChecklists($userType: String) {
  getAllChecklists(user_type: $userType) {
    result
    message
    data {
      id
      name
      is_active
      created_at
      updated_at
    }
  }
}
query GetUserChecklistData($userId: Int!, $userType: String) {
  getUserChecklistData(user_id: $userId, user_type: $userType) {
    result
    message
    data {
      id
      name
      user_checklist_data {
        id
        hiring_completed
        hiring_completed_at
        technician_completed
        technician_completed_at
        created_at
        updated_at
        created_by
        edited_by
        creator {
          id
          name
        }
        editor {
          id
          name
        }
        user {
          id
          name
        }
        meta
      }
    }
  }
}

query GetUserChecklistHistory($userId: Int!) {
  getUserChecklistHistory(user_id: $userId) {
    result
    message
    data {
      created_at
      updated_at
      creator {
        type
        name
        id
      }
      last_editor {
        type
        name
        id
      }
    }
  }
}

mutation CreateChecklist($data: CreateChecklistInput!) {
  createChecklist(data: $data) {
    result
    message
    data {
      id
      name
      is_active
      created_at
      updated_at
    }
  }
}

mutation UpdateChecklist($data: UpdateChecklistInput!) {
  updateChecklist(data: $data) {
    result
    message
  }
}

mutation UpdateUserChecklistData($data: [UpdateUserChecklistDataInput]!) {
  updateUserChecklistData(data: $data) {
    result
    message
  }
}
mutation UpdateTechnicianFeedbackData($data: UpdateUserChecklistDataInput!) {
  updateTechnicianFeedbackData(data: $data) {
    result
    message
  }
}
