import React, { useEffect, useState } from "react";
import { AiOutlineZoomIn, AiOutlineZoomOut } from "react-icons/ai";
import { LuArrowLeft, LuArrowRight } from "react-icons/lu";
import { Document, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import PolicyAcknowledge from "./policy/PolicyAcknowledge";
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

interface PdfViewerProps {
  pdfUrl: string;
  size?: number;
  showAcknowledge?: boolean;
  policyId?: string;
  onClose: () => void;
}

const PdfViewerWithNav: React.FC<PdfViewerProps> = ({
  pdfUrl,
  size,
  showAcknowledge,
  policyId,
  onClose
}) => {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1.0);

  // Add meta viewport tag to allow pinch zoom
  useEffect(() => {
    const metaViewport = document.querySelector('meta[name="viewport"]');
    if (metaViewport) {
      metaViewport.setAttribute(
        "content",
        "width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"
      );
    }

    return () => {
      // Restore default viewport settings when component unmounts
      if (metaViewport) {
        metaViewport.setAttribute("content", "width=device-width, initial-scale=1.0");
      }
    };
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Calculate appropriate width based on device size
  const getPageWidth = () => {
    if (size) return size;
    if (windowWidth < 480) return windowWidth - 40; // Mobile
    if (windowWidth < 768) return windowWidth - 60; // Tablet
    return 400; // Desktop default
  };

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  // Zoom in function - increase scale by 0.2
  const zoomIn = () => {
    setScale((prevScale) => Math.min(prevScale + 0.2, 3.0));
  };

  // Zoom out function - decrease scale by 0.2
  const zoomOut = () => {
    setScale((prevScale) => Math.max(prevScale - 0.2, 0.5));
  };

  return (
    <div
      className="h-full w-full overflow-x-hidden overflow-y-auto flex flex-col items-center "
      style={{ WebkitOverflowScrolling: "touch" }}
    >
      <Document
        className="w-full flex flex-col items-center "
        file={pdfUrl}
        onLoadSuccess={onDocumentLoadSuccess}
        loading={<div className="text-center py-4">Loading PDF...</div>}
      >
        <div
          className="pdf-container "
          style={{
            touchAction: "manipulation",
            overflow: "auto",
            maxWidth: "100%",
            position: "relative",
            WebkitOverflowScrolling: "touch"
          }}
        >
          <Page
            renderTextLayer={false}
            width={getPageWidth()}
            scale={scale}
            className="mx-auto shadow-md rounded "
            pageNumber={pageNumber}
          />
        </div>
        <div
          className={`flex items-center ${numPages && numPages > 1 ? "justify-between" : "justify-center"} } w-full bg-white rounded-lg shadow-sm p-1 border border-solid mt-3 `}
        >
          {/* Zoom controls */}
          <div className="flex items-center bg-gray-50 p-1 rounded-lg border border-gray-200">
            <button
              className="px-2 py-1 bg-blue-50 text-blue-600 rounded-l-md hover:bg-blue-100 disabled:opacity-50 transition-colors"
              onClick={zoomOut}
              disabled={scale <= 0.5}
              aria-label="Zoom out"
            >
              <AiOutlineZoomOut size={16} />
            </button>

            <span className="text-xs px-2 font-medium text-gray-700">
              {Math.round(scale * 100)}%
            </span>

            <button
              className="px-2 py-1 bg-blue-50 text-blue-600 rounded-r-md hover:bg-blue-100 disabled:opacity-50 transition-colors"
              onClick={zoomIn}
              disabled={scale >= 3.0}
              aria-label="Zoom in"
            >
              <AiOutlineZoomIn size={16} />
            </button>
          </div>

          {numPages && numPages > 1 && (
            <div className="flex gap-x-2 items-center">
              <button
                className="px-2 py-1 bg-blue-50 text-blue-600 rounded-l-md hover:bg-blue-100 disabled:opacity-50 transition-colors"
                disabled={pageNumber <= 1}
                onClick={() => setPageNumber(pageNumber - 1)}
                aria-label="Previous page"
              >
                <LuArrowLeft size={16} />
              </button>

              <div className="text-xs font-medium text-gray-700 whitespace-nowrap px-1">
                Page {pageNumber}
              </div>

              <button
                className="px-2 py-1 bg-blue-50 text-blue-600 rounded-r-md hover:bg-blue-100 disabled:opacity-50 transition-colors"
                disabled={pageNumber >= numPages}
                onClick={() => setPageNumber(pageNumber + 1)}
                aria-label="Next page"
              >
                <LuArrowRight size={16} />
              </button>
            </div>
          )}
        </div>
      </Document>
      {showAcknowledge && (
        <PolicyAcknowledge
          policyId={policyId || ""}
          onClose={() => {
            onClose();
          }}
        />
      )}
    </div>
  );
};

export default PdfViewerWithNav;
