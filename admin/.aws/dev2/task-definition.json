{"taskDefinitionArn": "arn:aws:ecs:ap-south-1:194153695972:task-definition/dev2-admin:1", "containerDefinitions": [{"name": "Onboarding-admin-Container2", "image": "194153695972.dkr.ecr.ap-south-1.amazonaws.com/onboardingadmindev2:latest", "cpu": 0, "portMappings": [{"name": "onboarding-admin-container2-80-tcp", "containerPort": 80, "hostPort": 80, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/dev2-admin", "awslogs-region": "ap-south-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "dev2-admin", "taskRoleArn": "arn:aws:iam::194153695972:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::194153695972:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2024-05-15T06:15:53.439Z", "registeredBy": "arn:aws:iam::194153695972:user/<EMAIL>", "tags": []}