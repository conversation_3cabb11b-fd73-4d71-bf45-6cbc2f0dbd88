-- DropIndex
DROP INDEX "app_installation_device_id_key";

-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';

-- CreateTable
CREATE TABLE "history" (
    "id" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "entity_module" TEXT,
    "entity_id" TEXT,
    "org_id" INTEGER,
    "user_id" INTEGER,
    "update_type" TEXT,
    "new_values" JSONB,
    "event_title" TEXT,
    "creator_id" INTEGER,
    "creator_type" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "meta" JSONB,

    CONSTRAINT "history_pkey" PRIMARY KEY ("id")
);
