name: Deploy main to PROD
on: [workflow_dispatch]

jobs:
  ToProd:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: AWS CodePipeline Trigger
      uses: zulhfreelancer/aws-codepipeline-action@v1.0.7
      with:
       aws-region: "ap-south-1"
       aws-access-key: ${{ secrets.AWS_PIPELINE_ACCESS_KEY }}
       aws-secret-key: ${{ secrets.AWS_PIPELINE_SECRET_KEY }}
       pipeline-name: "Prod-Onboarding-Admin"