import { <PERSON><PERSON>, <PERSON>, Space } from "antd";
import J<PERSON><PERSON><PERSON><PERSON> from "react-json-pretty";
import { useNavigate } from "react-router-dom";

interface ErrorComponentProps {
  error: any;
}

const ErrorComponent: React.FC<ErrorComponentProps> = ({ error }) => {
  const navigate = useNavigate();

  return (
    <div className="w-full h-full flex flex-col justify-center items-center bg-gray-50">
      <Card className="m-6 max-w-[900px] max-h-[60vh] overflow-auto">
        <div className="flex flex-col justify-center items-center">
          <img className="md:max-w-[400px] w-[300px]" src="/images/error.svg" alt="error image" />
          <div className="md:text-4xl text-2xl text-center mt-5">There was some error</div>
          <Space className="mt-5" direction="horizontal">
            <Button type="primary" onClick={() => window.location.reload()}>
              Refresh
            </Button>
            <Button
              onClick={() => {
                navigate("/");
              }}
            >
              Go to home
            </Button>
          </Space>
          <Card
            title={"This is what you need to tell us"}
            className="md:w-[700px] w-[300px] h-[300px] mt-5 overflow-auto"
          >
            <JSONPretty className="md:w-full" data={JSON.stringify(error)} />
          </Card>
        </div>
      </Card>
    </div>
  );
};

export default ErrorComponent;
