-- CreateTable
CREATE TABLE "policy" (
    "id" TEXT NOT NULL,
    "policy_name" TEXT NOT NULL,
    "policy_name_hindi" TEXT,
    "url" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "policy_pkey" PRIMARY KEY ("id")
);
-- CreateTable
CREATE TABLE "user_policy_tracking" (
    "id" TEXT NOT NULL,
    "user_id" INTEGER NOT NULL,
    "policy_id" TEXT NOT NULL,
    "accepted" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "user_policy_tracking_pkey" PRIMARY KEY ("id")
);
-- CreateIndex
CREATE UNIQUE INDEX "user_policy_tracking_user_id_policy_id_key" ON "user_policy_tracking"("user_id", "policy_id");
-- AddForeignKey
ALTER TABLE "user_policy_tracking"
ADD CONSTRAINT "user_policy_tracking_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- AddForeignKey
ALTER TABLE "user_policy_tracking"
ADD CONSTRAINT "user_policy_tracking_policy_id_fkey" FOREIGN KEY ("policy_id") REFERENCES "policy"("id") ON DELETE CASCADE ON UPDATE CASCADE;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
INSERT INTO policy (
        policy_name,
        policy_name_hindi,
        is_active,
        created_at,
        updated_at,
        url,
        id
    )
VALUES (
        'Disciplinary Policy',
        'अनुशासनात्मक नीति',
        true,
        now(),
        now(),
        'https://prod-rojgaar.s3.ap-south-1.amazonaws.com/9169044.pdf',
        uuid_generate_v4()
    ),
    (
        'GPA Information',
        'GPA जानकारी',
        true,
        now(),
        now(),
        'https://prod-rojgaar.s3.ap-south-1.amazonaws.com/6786917.pdf',
        uuid_generate_v4()
    ),
    (
        'Workersmen Compensation Information',
        'श्रमिक मुआवजा जानकारी',
        true,
        now(),
        now(),
        'https://prod-rojgaar.s3.ap-south-1.amazonaws.com/5214291.pdf',
        uuid_generate_v4()
    ),
    (
        'Medical Expense Claim Process',
        'चिकित्सा व्यय दावा प्रक्रिया',
        true,
        now(),
        now(),
        'https://prod-rojgaar.s3.ap-south-1.amazonaws.com/7375027.pdf',
        uuid_generate_v4()
    ),
    (
        'Technician Handbook',
        'तकनीशियन पुस्तिका',
        true,
        now(),
        now(),
        'https://prod-rojgaar.s3.ap-south-1.amazonaws.com/5786803.pdf',
        uuid_generate_v4()
    ),
    (
        'Uniform Policy – Technician',
        'यूनिफार्म नीति – तकनीशियन',
        true,
        now(),
        now(),
        'https://prod-rojgaar.s3.ap-south-1.amazonaws.com/7198426.pdf',
        uuid_generate_v4()
    ),
    (
        'Working Hours Policy',
        'कार्य घंटों की नीति',
        true,
        now(),
        now(),
        'https://prod-rojgaar.s3.ap-south-1.amazonaws.com/2233933.pdf',
        uuid_generate_v4()
    ),
    (
        'Grievance Procedure Policy',
        'शिकायत प्रक्रिया नीति',
        true,
        now(),
        now(),
        'https://prod-rojgaar.s3.ap-south-1.amazonaws.com/8117558.pdf',
        uuid_generate_v4()
    ),
    (
        'Code of Conduct',
        'आचार संहिता',
        true,
        now(),
        now(),
        'https://prod-rojgaar.s3.ap-south-1.amazonaws.com/3936614.pdf',
        uuid_generate_v4()
    );