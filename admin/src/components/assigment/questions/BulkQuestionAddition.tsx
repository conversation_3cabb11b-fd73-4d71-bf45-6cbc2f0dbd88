import React, { useState } from "react";
import ShowBulkUploadQuestionTemplate from "./ShowBulkUploadQuestionTemplate";
import ShowFilledQuestionsFromTemplate from "./ShowFilledQuestionsFromTemplate";

export interface BulkQuestionAdditionProps {
  question: string;
  option_1: string;
  option_2: string;
  option_3: string;
  option_4: string;
  correct_option: string;
  difficulty: string;
  bucket: string;
  bucket_name?: string;
}

const BulkQuestionAddition: React.FC = () => {
  //Hooks
  const [isTemplateUploaded, setIsTemplateUploaded] = useState(false);

  const [filledQuestions, setFilledQuestions] = useState<Array<BulkQuestionAdditionProps>>([]);

  return (
    <div>
      {isTemplateUploaded ? (
        <ShowFilledQuestionsFromTemplate filledQuestions={filledQuestions} />
      ) : (
        // Render the ShowBulkUploadQuestionTemplate component and pass the function to set the state
        <ShowBulkUploadQuestionTemplate
          setIsTemplateUploaded={setIsTemplateUploaded}
          setFilledQuestions={setFilledQuestions}
        />
      )}
    </div>
  );
};

export default BulkQuestionAddition;
