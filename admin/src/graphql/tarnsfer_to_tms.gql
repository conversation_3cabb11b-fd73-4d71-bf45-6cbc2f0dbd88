query GetUserForTTT(
  $userId: Int
  $search: String
  $filter: GetUserTTTInputFilter
  $pagination: Pagination
) {
  getUserForTTT(user_id: $userId, search: $search, filter: $filter, pagination: $pagination) {
    data {
      bank_details {
        account_number
        bank_name
        ifsc
      }
      doc_verification_state
      email
      employee_id
      gender
      meta
      id
      isActive
      location {
        city
        id
        landmark
        pincode
        state
        work_address
      }
      location_id
      name
      onboarding_stage
      organization {
        created_at
        description
        id
        location_id
        logo
        logo_url
        name
        trades
        updated_at
      }
      organization_id
      phone
      phoneVerified
      phone_verified
      photoUrl
      transfer_to_tms_status
      user_onboarding_data {
        aadhar_address
        aadhar_number
        assessed_by
        assessment_score
        city
        current_address
        date_of_joining
        designation
        employee_id
        esic_number
        family_details
        form_no_11
        hiring_criteria
        hiring_manager_id
        martial_status
        meta
        mothers_name
        pancard_number
        remark
        salary_offered
        source
        user_created_in_tms
        user_id
      }
      designation {
        id
        level
        name
      }
      location {
        city
        landmark
        pincode
        state
        work_address
      }
    }
    total_count
  }
}

mutation CreateOrUpdateUsersOnboardingData($userOnboardingData: UserOnboardingDataInput) {
  createOrUpdateUsersOnboardingData(userOnboardingData: $userOnboardingData) {
    result
    message
  }
}

mutation CreateOrUpdateUserBankData($userBankData: UserBankDetailsInput) {
  createOrUpdateUserBankData(userBankData: $userBankData) {
    result
    message
  }
}
