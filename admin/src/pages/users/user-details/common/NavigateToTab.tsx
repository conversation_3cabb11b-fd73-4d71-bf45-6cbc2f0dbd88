import { GoArrowRight } from "react-icons/go";

const NavigateToTab = ({
  handleClick,
  title
}: {
  handleClick: any;
  key: string;
  title: string;
}) => {
  return (
    <div
      className="flex gap-x-2 items-center justify-end text-colorPrimary cursor-pointer"
      onClick={handleClick}
    >
      <span>{title}</span>
      <GoArrowRight />
    </div>
  );
};

export default NavigateToTab;
