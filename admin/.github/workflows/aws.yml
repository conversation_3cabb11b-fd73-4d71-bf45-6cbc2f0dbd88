name: Deploy to DEV
on: [workflow_dispatch]
#   workflow_dispatch:
#   push:
#     branches: ["main"]

#on:
#  workflow_run:
#    workflows: ["Check code quality and build"]
#    types:
#      - completed

env:
  AWS_REGION: ap-south-1 # set this to your preferred AWS region, e.g. us-west-1
  ECR_REPOSITORY: onboarding<PERSON><PERSON><PERSON> # set this to your Amazon ECR repository name
  ECS_SERVICE: dev-onboarding-admin # set this to your Amazon ECS service name
  ECS_CLUSTER: dev-onboarding-frontend # set this to your Amazon ECS cluster name
  VITE_BACKEND_URL: ${{ secrets.BACKEND_URL }}
  ECS_TASK_DEFINITION:
    .aws/task-definition.json 
  CONTAINER_NAME:
    Onboarding-admin-Container
permissions:
  contents: read

jobs:
  deploy:
  #notify:
    name: Deploy
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
        VITE_BACKEND_URL: ${{ secrets.BACKEND_URL }}
   
      run: |
        # Build a docker container and
        # push it to ECR so that it can
        # be deployed to ECS
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT
    - name: Fill in the new image ID in the Amazon ECS task definition
      id: task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: ${{ env.ECS_TASK_DEFINITION }}
        container-name: ${{ env.CONTAINER_NAME }}
        image: ${{ steps.build-image.outputs.image }}

    - name: Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true



