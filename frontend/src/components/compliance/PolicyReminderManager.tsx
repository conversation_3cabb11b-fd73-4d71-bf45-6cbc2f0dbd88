import { Image } from "@chakra-ui/react";
import { useEffect, useState } from "react";
import { useGetPoliciesWithUserStatusQuery, useUserDetailsQuery } from "../../__generated__";
import { showPolicyReminder } from "../../utils/common";
import Storage from "../../utils/Storage";
import { PolicyReminder } from "./PolicyReminder";

export function PolicyReminderManager() {
  const [showReminder, setShowReminder] = useState(false);

  const { data: userDetails } = useUserDetailsQuery();
  const user_id = userDetails?.userDetail?.data?.id;

  const {
    data: policiesData,
    loading,
    error
  } = useGetPoliciesWithUserStatusQuery({
    variables: { userId: user_id ? +user_id : -1 },
    fetchPolicy: "network-only"
  });
  const policies = policiesData?.getPoliciesWithUserStatus?.data || [];

  const unreadPolicies = policies.filter(
    (policy: any) =>
      !policy.user_policy_tracking ||
      policy.user_policy_tracking.length === 0 ||
      !policy.user_policy_tracking[0].accepted
  );

  const unreadCount = unreadPolicies.length;

  const checkReminder = async () => {
    if (policiesData?.getPoliciesWithUserStatus?.data) {
      if (unreadPolicies.length > 0) {
        const shouldShow = await showPolicyReminder();
        setShowReminder(shouldShow);
      }
    }
  };
  useEffect(() => {
    checkReminder();
  }, [policiesData]);
  const handleClose = async () => {
    setShowReminder(false);
    const latestReminderDate = Date.now().toString();

    await Storage.set("policyReminderDismissed", latestReminderDate);
  };

  if (!showReminder) return null;
  if (loading) {
    return (
      <div className="w-full h-screen flex justify-center items-center">
        <Image src="/images/loading.gif" alt="Loading" className="w-[50px] h-[50px]" />
      </div>
    );
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }
  return <PolicyReminder unreadCount={unreadCount} onClose={handleClose} />;
}
