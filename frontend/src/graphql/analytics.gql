mutation TrackAnalytics($input: TrackAnalyticsInput!) {
  trackAnalytics(input: $input) {
    success
    message
    data
  }
}

query GetDailyAnalytics($date: DateTime) {
  getDailyAnalytics(date: $date) {
    id
    date
    total_visitors
    unique_visitors
    total_signups
    total_logins
    average_session_time
    bounce_rate
    platform_breakdown
    language_breakdown
    funnel_metrics
    created_at
    updated_at
  }
}

query GetUserActivity($userId: Int!, $startDate: DateTime, $endDate: DateTime) {
  getUserActivity(userId: $userId, startDate: $startDate, endDate: $endDate) {
    id
    user_id
    session_id
    activity_type
    platform_type
    language
    funnel_stage
    start_time
    end_time
    time_spent
    url_path
    user_agent
    took_action
    meta
    created_at
    updated_at
  }
}
