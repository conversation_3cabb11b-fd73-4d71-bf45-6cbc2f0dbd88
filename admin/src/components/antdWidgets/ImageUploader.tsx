import { DeleteOutlined, LoadingOutlined, UploadOutlined } from "@ant-design/icons";
import { Button, Image, message, notification, Tooltip, Upload, UploadProps } from "antd";
import { RcFile, UploadChangeParam, UploadFile } from "antd/es/upload";
import axios from "axios";
import React from "react";

const { Dragger } = Upload;

interface ImageUploaderProps {
  value?: string;
  onChange?: (url: string) => void;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ value, onChange }) => {
  const [imageLoading, setLoading] = React.useState(false);
  const image = value || "";

  const uploadFileToS3 = async (file: File): Promise<string> => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`,
        { file_type: file.type },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("adminToken")}`
          }
        }
      );

      const { status, data: signedUrl, Key } = response.data;

      if (status) {
        await axios.put(signedUrl, file, {
          headers: {
            "Content-Type": file.type
          }
        });

        return import.meta.env.VITE_S3_URL + "/" + Key;
      } else {
        notification.error({
          message: "There was some issue during uploading the file. Please try again later."
        });
      }
    } catch (err) {
      notification.error({
        message: "There was some issue during uploading the file. Please try again later."
      });
    }
    return "";
  };

  const handleChange: UploadProps["onChange"] = async (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === "uploading") {
      setLoading(true);
      return;
    }
    if (info.file.status === "done") {
      try {
        const url = await uploadFileToS3(info.file.originFileObj as File);
        message.success(`${info.file.name} file uploaded successfully`);
        onChange?.(url);
        setLoading(false);
      } catch (error) {
        console.error("errr", error);
      }
    }
  };

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) message.error("You can only upload JPG/PNG file!");

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) message.error("Image must be smaller than 10MB!");

    return isJpgOrPng && isLt10M;
  };

  const uploadButton = (
    <button
      style={{ border: 0, background: "none", cursor: "pointer" }}
      type="button"
      hidden={!!image}
    >
      {imageLoading ? <LoadingOutlined /> : <UploadOutlined />}
      <div style={{ marginTop: 8 }}>Upload Image</div>
    </button>
  );

  return (
    <Dragger
      onChange={handleChange}
      beforeUpload={beforeUpload}
      listType="picture-card"
      className="avatar-uploader"
      disabled={!!image}
      showUploadList={false}
      multiple={false}
      action={`${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`}
    >
      {image ? (
        <div className="relative">
          <Image src={image} alt="uploaded" width={"100px"} height={"100px"} />
          <Button
            className="absolute top-0 right-0"
            icon={
              <Tooltip title="Remove Image">
                <DeleteOutlined className="text-red-500" />
              </Tooltip>
            }
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onChange?.(""); // 👈 clears the form field
            }}
          />
        </div>
      ) : (
        uploadButton
      )}
    </Dragger>
  );
};

export default ImageUploader;
