/*
  Warnings:

  - Added the required column `updated_at` to the `feedback_template` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "feedback_template" ADD COLUMN     "interviewee_feedback_enabled" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "interviewee_feedback_meta" JSONB NOT NULL DEFAULT '{}',
ADD COLUMN     "interviewer_feedback_enabled" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "interviewer_feedback_meta" JSONB NOT NULL DEFAULT '{}',
ADD COLUMN     "meta" JSONB NOT NULL DEFAULT '{}';

-- AlterTable
ALTER TABLE "interview_feedback" ADD COLUMN     "interviewee_feedback_enabled" BOOLEAN DEFAULT false,
ADD COLUMN     "interviewee_feedback_result" JSONB DEFAULT '{}',
ADD COLUMN     "interviewer_feedback_enabled" BOOLEAN DEFAULT false,
ADD COLUMN     "interviewer_feedback_result" JSONB DEFAULT '{}',
ADD COLUMN     "is_form_meta_enabled" BOOLEAN DEFAULT false,
ADD COLUMN     "meta" JSONB DEFAULT '{}';

-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';
