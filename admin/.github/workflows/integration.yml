name: Check code quality and build

on:
  pull_request:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
          # cache: "npm"
      - run: npm i -g yarn
        env:
          NODE_OPTIONS: --max_old_space_size=4096
      - run: yarn add -D eslint-config-prettier
      - run: yarn run lint
      - run: |
          export NODE_OPTIONS=--max_old_space_size=8192
          yarn run build
