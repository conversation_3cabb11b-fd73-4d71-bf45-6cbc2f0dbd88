import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";
import { Menu } from "antd";
import { ClipboardList, FileCheck } from "lucide-react";
import React, { useContext, useState } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { IsMenuCollapsed } from "../../container/DashboardContainer";

const HRMenu: React.FC = () => {
  const mainMenu = useContext(IsMenuCollapsed);
  const [subMenuOpen, setSubMenuOpen] = useState(true);
  const location = useLocation();
  const base_path = localStorage.getItem("base_path") || "";

  const navigate = useNavigate();

  return (
    <div className="flex h-full -ml-4 -mt-4">
      <div className={`border-t-2 h-[98%] ${subMenuOpen ? "w-[220px]" : "w-[80px]"} flex-shrink-0`}>
        <Menu
          className="rounded-lg bg-white "
          style={{ height: "100%", width: "100%" }}
          inlineCollapsed={!subMenuOpen}
          defaultChecked={true}
          defaultSelectedKeys={[location.pathname]}
          items={[
            {
              label: "Policy Tracking",
              key: `/dashboard${base_path && "/" + base_path}/hr`,
              icon: <ClipboardList size={16} />,
              onClick: () => {
                navigate(`/dashboard${base_path && "/" + base_path}/hr`);
              }
            },
            {
              label: "Policy List",
              key: `/dashboard${base_path && "/" + base_path}/hr/policy-list`,
              icon: <FileCheck size={16} />,
              onClick: () => {
                navigate(`/dashboard${base_path && "/" + base_path}/hr/policy-list`);
              }
            }
          ]}
        />
        <div
          className="m-1 p-2 text-enter hover:bg-purple-300 rounded-md flex justify-center bg-purple-50 border-dashed border border-purple-500 cursor-pointer"
          onClick={() => {
            if (subMenuOpen === false) {
              mainMenu?.setCollapsed && mainMenu.setCollapsed(true);
            }
            setSubMenuOpen(!subMenuOpen);
          }}
        >
          {subMenuOpen ? (
            <MenuFoldOutlined className="text-xl text-purple-800" />
          ) : (
            <MenuUnfoldOutlined className="text-xl text-purple-800" />
          )}
        </div>
      </div>
      <div className={"flex-grow overflow-auto"}>
        <Outlet />
      </div>
    </div>
  );
};

export default HRMenu;
