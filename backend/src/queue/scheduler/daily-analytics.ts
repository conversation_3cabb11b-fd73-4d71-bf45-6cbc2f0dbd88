import { AppPlatformType } from '@prisma/client'
import dayjs from 'dayjs'
import DataProvider from '../../data'

const db = DataProvider.getDbInstance()

export async function generateDailyAnalytics() {
    const startDate = dayjs().utc().startOf('day') //start of the day
    const endDate = dayjs().utc().endOf('day') //end of the day

    const dateRange = {
        gte: startDate.toISOString(),
        lte: endDate.toISOString(),
    }
    //count of the total number of android app installations
    const getAndroidAppInstallation = await db.app_installation.count({
        where: {
            platform_type: AppPlatformType.ANDROID,
            installation_date: dateRange,
        },
    })
    const getWebAppInstallation = await db.app_installation.count({
        where: {
            platform_type: AppPlatformType.WEB,
            installation_date: dateRange,
        },
    })

    const getTotalUserSignUpByWeb = await db.user.count({
        where: {
            created_at: dateRange,
            app_installation_user_map: {
                some: {
                    app_installation: {
                        platform_type: AppPlatformType.WEB,
                    },
                },
            },
        },
    })
    const getTotalUserSignUpByAndroid = await db.user.count({
        where: {
            created_at: dateRange,
            app_installation_user_map: {
                some: {
                    app_installation: {
                        platform_type: AppPlatformType.ANDROID,
                    },
                },
            },
        },
    })
    const getTotalUserSignUpWebByServiceProvider = await db.user.count({
        where: {
            user_type: 'SERVICE_PROVIDER',
            created_at: dateRange,
            app_installation_user_map: {
                some: {
                    app_installation: {
                        platform_type: AppPlatformType.WEB,
                    },
                },
            },
        },
    })
    const getTotalUserSignUpAndroidByServiceProvider = await db.user.count({
        where: {
            user_type: 'SERVICE_PROVIDER',
            created_at: dateRange,
            app_installation_user_map: {
                some: {
                    app_installation: {
                        platform_type: AppPlatformType.ANDROID,
                    },
                },
            },
        },
    })

    const getTotalUserSignUpWebByTechnician = await db.user.count({
        where: {
            user_type: 'TECHNICIAN',
            created_at: dateRange,
            app_installation_user_map: {
                some: {
                    app_installation: {
                        platform_type: AppPlatformType.WEB,
                    },
                },
            },
        },
    })
    const getTotalUserSignUpAndroidByTechnician = await db.user.count({
        where: {
            user_type: 'TECHNICIAN',
            created_at: dateRange,
            app_installation_user_map: {
                some: {
                    app_installation: {
                        platform_type: AppPlatformType.ANDROID,
                    },
                },
            },
        },
    })

    //count of the total number of android app installations by service provider
    const getAndroidAppInstallationByServiceProvider =
        await db.app_installation.count({
            where: {
                platform_type: AppPlatformType.ANDROID,
                user: {
                    user_type: 'SERVICE_PROVIDER',
                },
                installation_date: dateRange,
            },
        })
    //count of the total number of android app installations by technician
    const getAndroidAppInstallationByTechnician =
        await db.app_installation.count({
            where: {
                platform_type: AppPlatformType.ANDROID,
                user: {
                    user_type: 'TECHNICIAN',
                },
                installation_date: dateRange,
            },
        })

    const getUniqueVisitors = await db.app_installation.count({
        where: {
            installation_date: dateRange,
            platform_type: AppPlatformType.WEB,
        },
    })
    const getUniqueVisitorsByServiceProvider = await db.app_installation.count({
        where: {
            user: {
                user_type: 'SERVICE_PROVIDER',
            },
            installation_date: dateRange,
            platform_type: AppPlatformType.WEB,
        },
    })
    const getUniqueVisitorsByTechnician = await db.app_installation.count({
        where: {
            user: {
                user_type: 'TECHNICIAN',
            },
            installation_date: dateRange,
            platform_type: AppPlatformType.WEB,
        },
    })

    const getDropOffUser = await db.app_installation.count({
        where: {
            user_id: null,
            installation_date: dateRange,
            is_dropped: true,
            platform_type: AppPlatformType.WEB,
        },
    })
    const getDropOffUserByAndroid = await db.app_installation.count({
        where: {
            user_id: null,
            installation_date: dateRange,
            is_dropped: true,
            platform_type: AppPlatformType.ANDROID,
        },
    })
    const getDropOffUserByWeb = await db.app_installation.count({
        where: {
            user_id: null,
            installation_date: dateRange,
            is_dropped: true,
            platform_type: AppPlatformType.WEB,
        },
    })
    const inputData = {
        app_installations: getAndroidAppInstallation,
        app_installation_breakdown: {
            android: getAndroidAppInstallation,
            web: getWebAppInstallation,
            service_provider: getAndroidAppInstallationByServiceProvider,
            technician: getAndroidAppInstallationByTechnician,
        },
        unique_visitors: getUniqueVisitors,
        unique_visitors_breakdown: {
            android: getUniqueVisitors,
            web: getUniqueVisitors,
            service_provider: getUniqueVisitorsByServiceProvider,
            technician: getUniqueVisitorsByTechnician,
        },
        signup: getTotalUserSignUpByWeb,
        signup_breakdown: {
            android: getTotalUserSignUpByAndroid,
            web: getTotalUserSignUpByWeb,
            android_service_provider:
                getTotalUserSignUpAndroidByServiceProvider,
            web_service_provider: getTotalUserSignUpWebByServiceProvider,
            android_technician: getTotalUserSignUpAndroidByTechnician,
            web_technician: getTotalUserSignUpWebByTechnician,
        },
        drop_off: getDropOffUser,
        drop_off_breakdown: {
            android: getDropOffUserByAndroid,
            web: getDropOffUserByWeb,
        },
    }
    const getDailyAnalytics = await db.daily_analytics.findFirst({
        where: {
            date: {
                gte: startDate.toISOString(),
                lte: endDate.toISOString(),
            },
        },
    })
    if (getDailyAnalytics) {
        await db.daily_analytics.update({
            where: { id: getDailyAnalytics.id },
            data: inputData,
        })
    } else {
        await db.daily_analytics.create({
            data: {
                ...inputData,
                date: startDate.toISOString(),
            },
        })
    }
}
