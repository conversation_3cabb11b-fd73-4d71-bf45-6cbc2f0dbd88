# App Analytics Implementation

This implementation provides comprehensive user analytics tracking capabilities across web and mobile platforms, focusing on user journey, engagement metrics, and conversion tracking.

## Core Features

1. **User Journey Tracking**

    - Login to Home Screen navigation flow
    - Page-by-page navigation tracking
    - Time spent on each screen
    - Language preference tracking
    - Drop-off points identification

2. **Installation Analytics**

    - Platform-specific installation tracking (Web/Android)
    - Installation/Uninstallation metrics
    - User type segmentation (Technician/Service Provider)

3. **Engagement Metrics**

    - Session duration tracking
    - Bounce rate analysis
    - User actions per session
    - Language preference distribution
    - Platform usage breakdown

4. **Conversion Funnel**
    - Visitor to signup conversion
    - Login completion rates
    - Onboarding stage progression
    - Drop-off analysis

## Database Models

### User Activity Tracking

```prisma
model user_activity {
  id            String             @id @default(uuid())
  user_id       Int?
  session_id    String             @unique
  ip_address    String?
  activity_type String
  platform_type AppPlatformType
  language      preferred_language
  funnel_stage  UserFunnelStage?
  start_time    DateTime
  end_time      DateTime?
  time_spent    Int?
  url_path      String?
  user_agent    String?
  took_action   <PERSON>ole<PERSON>            @default(false)
  meta          Json?              @default("{}")
}
```

### Daily Analytics Aggregation

```prisma
model daily_analytics {
  id                   String   @id @default(uuid())
  date                 DateTime
  total_visitors       Int
  unique_visitors      Int
  total_signups        Int
  total_logins         Int
  average_session_time Float
  bounce_rate          Float
  platform_breakdown   Json
  language_breakdown   Json
  funnel_metrics       Json
}
```

## API Endpoints

### Analytics Queries

```graphql
type Query {
    getUserJourney(userId: Int!): [UserActivity]
    getScreenTimeMetrics(dateRange: DateRange!): ScreenTimeMetrics
    getConversionMetrics(dateRange: DateRange!): ConversionMetrics
    getLanguageDistribution(dateRange: DateRange!): LanguageMetrics
    getDropOffAnalytics(dateRange: DateRange!): DropOffMetrics
}
```

### Activity Tracking Mutations

```graphql
type Mutation {
    trackUserActivity(input: UserActivityInput!): UserActivity
    trackScreenView(input: ScreenViewInput!): ScreenView
    updateUserLanguage(input: LanguageInput!): User
}
```

## Implementation Examples

### Track Screen Navigation

```typescript
function trackScreenNavigation(
    userId: number,
    screenPath: string,
    action: 'ENTER' | 'EXIT'
) {
    return prisma.user_activity.create({
        data: {
            user_id: userId,
            url_path: screenPath,
            activity_type: action,
            start_time: new Date(),
            platform_type: detectPlatform(),
            session_id: getCurrentSessionId(),
        },
    })
}
```

### Calculate Time to Home Screen

```sql
WITH user_journey AS (
  SELECT
    user_id,
    MIN(CASE WHEN url_path = '/login' THEN start_time END) as login_time,
    MIN(CASE WHEN url_path = '/home' THEN start_time END) as home_time
  FROM user_activity
  WHERE created_at >= NOW() - INTERVAL '24 hours'
  GROUP BY user_id
)
SELECT
  AVG(EXTRACT(EPOCH FROM (home_time - login_time))) as avg_time_to_home
FROM user_journey;
```

### Aggregate Daily Analytics

```typescript
async function aggregateDailyAnalytics(date: Date) {
    const dayStart = startOfDay(date)
    const dayEnd = endOfDay(date)

    const metrics = await prisma.user_activity.groupBy({
        by: ['platform_type', 'language'],
        where: {
            created_at: {
                gte: dayStart,
                lte: dayEnd,
            },
        },
        _count: true,
    })

    return prisma.daily_analytics.create({
        data: {
            date: dayStart,
            total_visitors: calculateTotalVisitors(metrics),
            unique_visitors: calculateUniqueVisitors(metrics),
            bounce_rate: calculateBounceRate(metrics),
            platform_breakdown: aggregatePlatformMetrics(metrics),
            language_breakdown: aggregateLanguageMetrics(metrics),
            funnel_metrics: await calculateFunnelMetrics(dayStart, dayEnd),
        },
    })
}
```

## Client Integration

### Web/Mobile Event Tracking

```typescript
// Track page views and user actions
useEffect(() => {
    trackScreenView({
        path: location.pathname,
        timestamp: new Date(),
        sessionId: getSessionId(),
        platform: 'WEB',
    })
}, [location.pathname])
```

### Language Preference Tracking

```typescript
function onLanguageChange(language: 'ENGLISH' | 'HINDI') {
    trackUserActivity({
        type: 'LANGUAGE_CHANGE',
        language,
        timestamp: new Date(),
    })
}
```

## Analytics Dashboard Queries

### User Journey Analysis

```typescript
const userJourneyMetrics = await prisma.user_activity.findMany({
    where: {
        created_at: {
            gte: startDate,
            lte: endDate,
        },
    },
    orderBy: {
        start_time: 'asc',
    },
    select: {
        url_path: true,
        time_spent: true,
        funnel_stage: true,
    },
})
```

### Drop-off Analysis

```typescript
const dropOffMetrics = await prisma.daily_analytics.findMany({
    where: {
        date: {
            gte: startDate,
            lte: endDate,
        },
    },
    select: {
        funnel_metrics: true,
        bounce_rate: true,
    },
})
```

## Monitoring and Alerts

1. Set up alerts for:

    - Unusual bounce rates
    - Significant drop in conversions
    - Extended time to reach home screen
    - Language preference anomalies

2. Daily aggregation jobs:
    - User journey metrics
    - Platform distribution
    - Language preferences
    - Conversion rates

## Future Enhancements

1. Real-time analytics dashboard
2. A/B testing integration
3. User behavior pattern analysis
4. Predictive analytics for user drop-off
5. Custom event tracking
6. Enhanced segmentation capabilities
7. Integration with external analytics tools
