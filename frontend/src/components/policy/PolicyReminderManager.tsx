import { useEffect, useState } from "react";
import { useGetPoliciesWithUserStatusQuery, useUserDetailsQuery } from "../../__generated__";
import { PolicyReminder } from "./PolicyReminder";

export function PolicyReminderManager() {
  const [showReminder, setShowReminder] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  const { data: userDetails } = useUserDetailsQuery();
  const user_id = userDetails?.userDetail?.data?.id;

  const {
    data: policiesData,
    loading,
    error
  } = useGetPoliciesWithUserStatusQuery({
    variables: { userId: user_id ? +user_id : -1 },
    fetchPolicy: "network-only"
  });
  const gracePeriodInMs = 15 * 60 * 1000;
  useEffect(() => {
    if (policiesData?.getPoliciesWithUserStatus?.data) {
      const policies = policiesData.getPoliciesWithUserStatus.data;

      // Count unread policies
      const unreadPolicies = policies.filter(
        (policy: any) =>
          !policy.user_policy_tracking ||
          policy.user_policy_tracking.length === 0 ||
          !policy.user_policy_tracking[0].accepted
      );

      setUnreadCount(unreadPolicies.length);

      // Only proceed if there are unread policies
      if (unreadPolicies.length > 0) {
        const currentDate = new Date();
        const lastShownDateString = localStorage.getItem("policyReminderLastShownDate") || "";
        const lastShownDate = lastShownDateString ? JSON.parse(lastShownDateString) : null;

        // Set the last shown date initially if not set
        if (!lastShownDate) {
          localStorage.setItem("policyReminderLastShownDate", JSON.stringify(currentDate));
        }

        // Check if it's a new day compared to when the reminder was last shown
        const isNewDate = currentDate.getDate() !== new Date(lastShownDate)?.getDate();
        const isWithinGracePeriod =
          currentDate.getTime() <= new Date(lastShownDate)?.getTime() + gracePeriodInMs;

        // Show popup if it's a new day
        if (isNewDate || (isNewDate && isWithinGracePeriod)) {
          setShowReminder(true);
          localStorage.setItem("policyReminderLastShownDate", JSON.stringify(currentDate));
        }
      }
    }
  }, [policiesData]);
  const handleClose = () => {
    setShowReminder(false);
    // Keep the old localStorage key for backward compatibility
    localStorage.setItem("policyReminderDismissed", Date.now().toString());
    // No need to update the lastShownDate as we want it to show again tomorrow
  };

  if (!showReminder) return null;
  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }
  return <PolicyReminder unreadCount={unreadCount} onClose={handleClose} />;
}
