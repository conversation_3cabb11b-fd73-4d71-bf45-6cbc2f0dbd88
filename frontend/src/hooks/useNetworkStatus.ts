import { Capacitor, PluginListenerHandle } from "@capacitor/core";
import { Network } from "@capacitor/network";
import { useEffect, useState } from "react";

export function useNetworkStatus() {
  const [isOffline, setIsOffline] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    let networkListener: PluginListenerHandle | null = null;

    const isNative = Capacitor.isNativePlatform();

    if (isNative) {
      const setupNativeNetworkListener = async () => {
        const status = await Network.getStatus();
        setIsOffline(!status.connected);
        setIsInitialized(true);

        networkListener = await Network.addListener("networkStatusChange", (status) => {
          setIsOffline(!status.connected);
        });
      };

      setupNativeNetworkListener();

      return () => {
        if (networkListener) {
          networkListener.remove();
        }
      };
    } else {
      // Web fallback using navigator
      const updateOnlineStatus = () => {
        setIsOffline(!navigator.onLine);
        if (!isInitialized) {
          setIsInitialized(true);
        }
      };

      // Initial status check
      updateOnlineStatus();

      window.addEventListener("online", updateOnlineStatus);
      window.addEventListener("offline", updateOnlineStatus);

      return () => {
        window.removeEventListener("online", updateOnlineStatus);
        window.removeEventListener("offline", updateOnlineStatus);
      };
    }
  }, []);

  return { isOffline, isInitialized };
}
