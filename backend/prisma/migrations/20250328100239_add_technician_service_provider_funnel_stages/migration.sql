-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "UserFunnelStage" ADD VALUE 'TECHNICIAN_HOMEPAGE';
ALTER TYPE "UserFunnelStage" ADD VALUE 'SERVICE_PROVIDER_HOMEPAGE';

-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';
