import { Card, FormInstance, Modal, Tabs } from "antd";
import { useForm } from "antd/es/form/Form";
import React, { createContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import AddSingleQuestion from "./AddSingleQuestion";
import AIGeneratedQuestions from "./AiGenerateQuestions";
import BulkQuestionAddition from "./BulkQuestionAddition";

export const AddQuestionFormContext = createContext<FormInstance<any> | undefined>(undefined);

const AddQuestionsModal: React.FC = () => {
  const nav = useNavigate();
  const [form] = useForm();
  const [okButtonLoading, setOkButtonLoading] = useState(false);

  return (
    <Modal
      onOk={() => form.submit()}
      onCancel={() => nav(-1)}
      okText={"Create"}
      width={"90%"}
      open
      centered
      okButtonProps={{ loading: okButtonLoading }}
    >
      <AddQuestionFormContext.Provider value={form}>
        <Card title={"Add question"} bodyStyle={{ padding: "5px 10px" }}>
          <Tabs
            items={[
              {
                label: "Single question",
                key: "single_question",
                children: <AddSingleQuestion okConfirmLoading={setOkButtonLoading} />
              },
              {
                label: "Bulk addition",
                key: "bulk",
                children: <BulkQuestionAddition />
              },
              {
                label: "AI Generated questions",
                key: "ai_gen_questions",
                children: <AIGeneratedQuestions />
              }
            ]}
          />
        </Card>
      </AddQuestionFormContext.Provider>
    </Modal>
  );
};

export default AddQuestionsModal;
