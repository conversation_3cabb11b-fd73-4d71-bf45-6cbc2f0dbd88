import { Modal } from "antd";
import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-icons/lu";
import UpcomingFeatureBadge from "../../../../../components/UpcomingFeatureBadge";

const certifications = [
  {
    id: 1,
    name: "Professional Carpenter Level 1",
    issueDate: "2024-12-30",
    status: "ACTIVE",
    credentialId: "WIFY-CARP-L1-2024-1234",
    issuedBy: "<PERSON><PERSON>"
  },
  {
    id: 2,
    name: "Safety Standards & Protocols",
    issueDate: "2024-12-15",
    status: "ACTIVE",
    credentialId: "WIFY-SSP-2024-5678",
    issuedBy: "<PERSON><PERSON>"
  },
  {
    id: 3,
    name: "Advanced Kitchen Installation",
    issueDate: "2024-11-20",
    status: "PENDING_RENEWAL",
    credentialId: "WIFY-AKI-2024-9012",
    issuedBy: "<PERSON>it <PERSON>"
  }
];
const CertificationsSection = () => {
  const [selectedCertificate, setSelectedCertificate] = useState<null | {
    name: string;
    issueDate: string;
    credentialId: string;
    issuedBy: string;
  }>(null);
  return (
    <div className="bg-white rounded-lg shadow ">
      <div
        style={{
          borderBottom: "1px solid #e5e7eb",
          borderBottomWidth: "1px",
          borderBottomColor: "#e5e7eb",
          borderBottomStyle: "solid"
        }}
        className="px-6 py-5 border-b border-gray-200"
      >
        <div
          className="flex items-center
        gap-x-2"
        >
          <div className="flex items-center">
            <LuAward className="h-6 w-6 text-indigo-600 mr-2" />
            <div className="text-xl font-semibold text-gray-900">Certifications</div>
          </div>
          <UpcomingFeatureBadge />
        </div>
      </div>
      <div className="p-6 relative">
        <div className="absolute h-full inset-0 bg-gray-50 bg-opacity-40 backdrop-blur-sm z-10 pointer-events-none "></div>
        <div className="space-y-6">
          {certifications.map((cert) => (
            <div key={cert.id} className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="space-y-1">
                  <h3 className="text-lg font-medium text-gray-900">{cert.name}</h3>
                  <p className="text-sm text-gray-600">Issued by Wify</p>
                  <p className="text-sm text-gray-500">Certified by: {cert.issuedBy}</p>
                  <p className="text-sm text-gray-500">Credential ID: {cert.credentialId}</p>
                </div>
                <span
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    cert.status === "ACTIVE"
                      ? "bg-green-100 text-green-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {cert.status === "ACTIVE" ? "Active" : "Pending Renewal"}
                </span>
              </div>
              <div className="mt-4 flex items-center">
                <LuCalendar className="h-4 w-4 text-gray-400 mr-2" />
                <div>
                  <div className="text-xs text-gray-500">Issue Date</div>
                  <div className="text-sm font-medium text-gray-900">{cert.issueDate}</div>
                </div>
              </div>
              <div className="mt-4">
                <div
                  onClick={() =>
                    setSelectedCertificate({
                      name: cert.name,
                      issueDate: cert.issueDate,
                      credentialId: cert.credentialId,
                      issuedBy: cert.issuedBy
                    })
                  }
                  className="text-sm text-indigo-600 hover:text-indigo-800 font-medium border-0 bg-gray-50"
                >
                  View Certificate
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <Modal
        footer={null}
        width={800}
        open={selectedCertificate !== null}
        onCancel={() => setSelectedCertificate(null)}
      >
        <div className="flex min-h-full items-center justify-center ">
          <div className="relative transform overflow-hidden rounded-lg   transition-all sm:w-full sm:max-w-3xl">
            {/* Certificate Content */}
            <div className="p-8">
              <div
                style={{ border: "4px solid", borderColor: "#7c3aed" }}
                className="border-4 border-indigo-600 p-8 rounded-lg z-10"
              >
                <div className="text-center">
                  <div className="flex justify-center mb-6">
                    <LuAward className="h-16 w-16 text-[#7c3aed]" />
                  </div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    Certificate of Achievement
                  </h1>
                  <p className="text-lg text-gray-600 mb-8">This is to certify that</p>
                  <p className="text-2xl font-semibold text-[#7c3aed] mb-8">Manikant sharma</p>
                  <p className="text-lg text-gray-600 mb-8">
                    has successfully completed the certification for
                  </p>
                  <p className="text-2xl font-bold text-gray-900 mb-8">
                    {selectedCertificate?.name}
                  </p>

                  <div
                    style={{ borderTop: "2px solid", borderColor: "#E5E7EB" }}
                    className="border-t-2 border-gray-200 pt-6 mt-8"
                  >
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Issue Date</p>
                        <p className="font-medium">{selectedCertificate?.issueDate}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Credential ID</p>
                        <p className="font-medium">{selectedCertificate?.credentialId}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Issued By</p>
                        <p className="font-medium">{selectedCertificate?.issuedBy}</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-8">
                    <img
                      src="https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=https://wify.com/verify/cert/{certificate.credentialId}"
                      alt="Certificate QR Code"
                      className="mx-auto h-24 w-24"
                    />
                    <p className="text-sm text-gray-500 mt-2">Scan to verify certificate</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CertificationsSection;
