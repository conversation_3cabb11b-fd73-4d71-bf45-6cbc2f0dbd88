<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Offline - Wify</title>
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: Avenir, Inter, Helvetica, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        /* background: linear-gradient(135deg, #e6b5a8 0%, #ffffff 100%); */

        color: #fff;

        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 2rem;
        text-align: center;
        animation: fadeIn 0.8s ease-in;
      }

      .container {
        max-width: 600px;
        width: 100%;
        padding: 2rem;
      }

      .svg-wrapper {
        margin: 0 auto 2rem;
      }

      .title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #ff4757;
        margin-bottom: 0.5rem;
        letter-spacing: -0.5px;
      }

      .subtitle {
        font-size: 1rem;
        color: #666;
        margin-bottom: 2.5rem;
        line-height: 1.5;
      }
      .retry-btn {
        background: #fef9c3;

        /* linear-gradient(300deg, #a0cff4, #0391ff); */
        color: #222;
        font-weight: 600;
        border: none;
        padding: 0.75rem 1.75rem;
        font-size: 1rem;
        border-radius: 10px;
        cursor: pointer;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition:
          transform 0.2s ease,
          box-shadow 0.2s ease;
        position: relative;
        overflow: hidden;
      }

      .retry-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
      }

      .retry-btn:active {
        transform: scale(0.98);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }

      .retry-btn::after {
        content: "";
        position: absolute;
        top: 0;
        left: -75%;
        width: 50%;
        height: 100%;
        background: rgba(255, 255, 255, 0.2);
        transform: skewX(-20deg);
        transition: left 0.75s ease;
      }

      .retry-btn:hover::after {
        left: 125%;
      }

      .retry-btn:active {
        transform: translateY(0);
      }

      .cloud-group {
        animation: moveClouds 70s linear infinite;
      }
      .cloud1 {
        animation-delay: 0s;
      }
      .cloud2 {
        animation-delay: 0s;
      }
      .cloud3 {
        animation-delay: 0s;
      }
      .cloud4 {
        animation-delay: 0s;
      }

      @keyframes moveClouds {
        0% {
          animation-delay: 2s;
          transform: translateX(-1vw);
        }
        100% {
          transform: translateX(30vw);
        }
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      @keyframes slideRight {
        from {
          opacity: 0;
          transform: translateX(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
          opacity: 1;
        }
        50% {
          transform: scale(1.1);
          opacity: 0.8;
        }
      }

      .pulse-animation {
        animation: pulse 2s ease-in-out infinite;
      }

      @media (max-width: 480px) {
        .container {
          padding: 1.5rem;
        }

        .title {
          font-size: 1.5rem;
        }

        .subtitle {
          font-size: 0.95rem;
        }
      }
    </style>
  </head>
  <script>
    function retryConnection() {
      if (navigator.onLine) {
        // Use Android interface to return to original URL
        if (typeof AndroidInterface !== "undefined" && AndroidInterface.retryConnection) {
          AndroidInterface.retryConnection();
        } else {
          // Fallback: try to go back in history or reload
          if (window.history.length > 1) {
            window.history.back();
          } else {
            window.location.reload();
          }
        }
      } else {
        alert("Still offline");
      }
    }

    // Auto-refresh when online
    window.addEventListener("online", () => {
      // Use Android interface to return to original URL
      if (typeof AndroidInterface !== "undefined" && AndroidInterface.retryConnection) {
        AndroidInterface.retryConnection();
      } else {
        // Fallback: try to go back in history or reload
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.reload();
        }
      }
    });

    window.retryConnection = retryConnection;
  </script>
  <body>
    <div class="container">
      <div class="svg-wrapper">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          viewBox="0 0 110 130"
          width="110"
          height="130"
          preserveAspectRatio="xMidYMid meet"
          style="
            width: 100%;
            height: 100%;
            transform: translate3d(0px, 0px, 0px);
            content-visibility: visible;
          "
        >
          <defs>
            <clipPath id="__lottie_element_1001">
              <rect width="110" height="130" x="0" y="0"></rect>
            </clipPath>
            <clipPath id="__lottie_element_1006">
              <path d="M0,0 L512,0 L512,512 L0,512z"></path>
            </clipPath>
          </defs>
          <g clip-path="url(#__lottie_element_1001)">
            <g transform="matrix(1,0,0,1,58.5,63.5)" opacity="1" style="display: block">
              <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                <path
                  fill="rgb(237,244,255)"
                  fill-opacity="1"
                  d=" M-23.222999572753906,47.220001220703125 C-32.25699996948242,46.933998107910156 -41.284000396728516,48.07500076293945 -49.946998596191406,50.65399932861328 C-56.303001403808594,52.547000885009766 -59.86199951171875,54.43899917602539 -48.67399978637695,54.43899917602539 C-26.29800033569336,54.43899917602539 -69.58999633789062,63.840999603271484 -27.104000091552734,64.93499755859375 C15.381999969482422,66.03099822998047 53.97999954223633,63.01900100708008 29.343000411987305,55.9010009765625 C5.718999862670898,49.07699966430664 58.108001708984375,49.803001403808594 -23.222999572753906,47.21900177001953 C-23.222999572753906,47.21900177001953 -23.222999572753906,47.220001220703125 -23.222999572753906,47.220001220703125z"
                ></path>
              </g>
              <g transform="translate(0, 10)">
                <g opacity="1" class="cloud-group cloud1" transform="matrix(1,0,0,1,0,10)">
                  <path
                    fill="rgb(237,244,255)"
                    fill-opacity="1"
                    d=" M-35.18199920654297,-19.402999877929688 C-35.18199920654297,-19.402999877929688 -57.375999450683594,-19.402999877929688 -57.375999450683594,-19.402999877929688 C-57.375999450683594,-19.402999877929688 -52.78499984741211,-20 -52.4900016784668,-20.18400001525879 C-52.19499969482422,-20.368000030517578 -50.07600021362305,-23.972999572753906 -47.07400131225586,-22.288999557495117 C-44.07099914550781,-20.604999542236328 -44.01300048828125,-22.312000274658203 -42.89400100708008,-21.89900016784668 C-41.7760009765625,-21.48699951171875 -41.83399963378906,-20.611000061035156 -39.89099884033203,-20.695999145507812 C-37.948001861572266,-20.7810001373291 -36.35900115966797,-28.177000045776367 -31.64900016784668,-25.42099952697754 C-26.940000534057617,-22.663999557495117 -28.05900001525879,-22.422000885009766 -25.173999786376953,-22.719999313354492 C-22.288999557495117,-23.01799964904785 -22.347999572753906,-19.402999877929688 -22.347999572753906,-19.402999877929688 C-22.347999572753906,-19.402999877929688 -35.18199920654297,-19.402999877929688 -35.18199920654297,-19.402999877929688z"
                  ></path>
                </g>
              </g>
              <g transform="translate(0, 20)">
                <g opacity="1" class="cloud-group cloud2" transform="matrix(1,0,0,1,0,20)">
                  <path
                    fill="rgb(237,244,255)"
                    fill-opacity="1"
                    d=" M25.219999313354492,7.5 C25.219999313354492,7.5 55,7.5 55,7.5 C55,7.5 48.8390007019043,6.690999984741211 48.444000244140625,6.440999984741211 C48.04899978637695,6.190999984741211 45.20500183105469,1.2999999523162842 41.176998138427734,3.5859999656677246 C37.14799880981445,5.872000217437744 37.069000244140625,3.555000066757202 35.56800079345703,4.113999843597412 C34.06700134277344,4.673999786376953 34.14699935913086,5.860000133514404 31.540000915527344,5.744999885559082 C28.933000564575195,5.630000114440918 26.799999237060547,-4.40500020980835 20.481000900268555,-0.6650000214576721 C14.16100025177002,3.075000047683716 15.661999702453613,3.4040000438690186 11.791999816894531,2.999000072479248 C7.921000003814697,2.5940001010894775 8,7.499000072479248 8,7.499000072479248 C8,7.499000072479248 25.219999313354492,7.499000072479248 25.219999313354492,7.499000072479248 C25.219999313354492,7.499000072479248 25.219999313354492,7.5 25.219999313354492,7.5z"
                  ></path>
                </g>
              </g>

              <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                <path
                  fill="rgb(0,145,255)"
                  fill-opacity="1"
                  d=" M-5.808000087738037,-30.413999557495117 C-5.831999778747559,-30.538000106811523 -5.863999843597412,-30.658000946044922 -5.9039998054504395,-30.773000717163086 C-4.879000186920166,-31.511999130249023 -4.2230000495910645,-32.731998443603516 -4.265999794006348,-34.10200119018555 C-4.331999778747559,-36.180999755859375 -6.0329999923706055,-37.86600112915039 -8.112000465393066,-37.915000915527344 C-10.333000183105469,-37.96699905395508 -12.151000022888184,-36.18199920654297 -12.151000022888184,-33.97200012207031 C-12.151000022888184,-32.6619987487793 -11.51200008392334,-31.503000259399414 -10.529999732971191,-30.785999298095703 C-10.571999549865723,-30.666000366210938 -10.607000350952148,-30.54199981689453 -10.631999969482422,-30.413000106811523 C-10.631999969482422,-30.413000106811523 -25.118000030517578,45.57500076293945 -25.118000030517578,45.57500076293945 C-25.118000030517578,45.57500076293945 -25.1200008392334,45.58300018310547 -25.121000289916992,45.5880012512207 C-25.121000289916992,45.5880012512207 -26.854999542236328,54.68600082397461 -26.854999542236328,54.68600082397461 C-26.985000610351562,55.37300109863281 -26.53499984741211,56.0359992980957 -25.849000930786133,56.16699981689453 C-25.16200065612793,56.29800033569336 -24.499000549316406,55.84700012207031 -24.368999481201172,55.1609992980957 C-24.368999481201172,55.1609992980957 -22.83099937438965,47.0989990234375 -22.83099937438965,47.0989990234375 C-22.83099937438965,47.0989990234375 6.413000106811523,47.0989990234375 6.413000106811523,47.0989990234375 C6.413000106811523,47.0989990234375 7.952000141143799,55.1609992980957 7.952000141143799,55.1609992980957 C8.083000183105469,55.847999572753906 8.746999740600586,56.29800033569336 9.434000015258789,56.16699981689453 C10.121000289916992,56.03499984741211 10.571000099182129,55.37200164794922 10.4399995803833,54.685001373291016 C10.4399995803833,54.685001373291016 -5.807000160217285,-30.413000106811523 -5.807000160217285,-30.413000106811523 C-5.807000160217285,-30.413000106811523 -5.808000087738037,-30.413999557495117 -5.808000087738037,-30.413999557495117z M0.7570000290870667,17.47599983215332 C0.7570000290870667,17.47599983215332 -14.428999900817871,9.82800006866455 -14.428999900817871,9.82800006866455 C-14.428999900817871,9.82800006866455 -2.132999897003174,2.3389999866485596 -2.132999897003174,2.3389999866485596 C-2.132999897003174,2.3389999866485596 0.7570000290870667,17.47599983215332 0.7570000290870667,17.47599983215332z M-3.0339999198913574,-2.378999948501587 C-3.0339999198913574,-2.378999948501587 -11.572999954223633,-7.948999881744385 -11.572999954223633,-7.948999881744385 C-11.572999954223633,-7.948999881744385 -5.178999900817871,-13.612000465393066 -5.178999900817871,-13.612000465393066 C-5.178999900817871,-13.612000465393066 -3.0339999198913574,-2.378999948501587 -3.0339999198913574,-2.378999948501587z M-8.220000267028809,-29.54599952697754 C-8.220000267028809,-29.54599952697754 -5.730999946594238,-16.506000518798828 -5.730999946594238,-16.506000518798828 C-5.730999946594238,-16.506000518798828 -11.717000007629395,-11.204000473022461 -11.717000007629395,-11.204000473022461 C-11.717000007629395,-11.204000473022461 -8.220000267028809,-29.54599952697754 -8.220000267028809,-29.54599952697754z M-12.765999794006348,-5.703999996185303 C-12.765999794006348,-5.703999996185303 -3.6059999465942383,0.27000001072883606 -3.6059999465942383,0.27000001072883606 C-3.6059999465942383,0.27000001072883606 -15.256999969482422,7.364999771118164 -15.256999969482422,7.364999771118164 C-15.256999969482422,7.364999771118164 -12.765000343322754,-5.704999923706055 -12.765000343322754,-5.704999923706055 C-12.765000343322754,-5.704999923706055 -12.765999794006348,-5.703999996185303 -12.765999794006348,-5.703999996185303z M-16.106000900268555,11.817000389099121 C-16.106000900268555,11.817000389099121 -0.5590000152587891,19.648000717163086 -0.5590000152587891,19.648000717163086 C-0.5590000152587891,19.648000717163086 -19.05500030517578,27.28700065612793 -19.05500030517578,27.28700065612793 C-19.05500030517578,27.28700065612793 -16.106000900268555,11.817000389099121 -16.106000900268555,11.817000389099121z M-22.350000381469727,44.564998626708984 C-22.350000381469727,44.564998626708984 -19.829999923706055,31.349000930786133 -19.829999923706055,31.349000930786133 C-19.829999923706055,31.349000930786133 2.7839999198913574,44.564998626708984 2.7839999198913574,44.564998626708984 C2.7839999198913574,44.564998626708984 -22.350000381469727,44.564998626708984 -22.350000381469727,44.564998626708984z M-17.895000457763672,29.54599952697754 C-17.895000457763672,29.54599952697754 1.5299999713897705,21.524999618530273 1.5299999713897705,21.524999618530273 C1.5299999713897705,21.524999618530273 5.692999839782715,43.332000732421875 5.692999839782715,43.332000732421875 C5.692999839782715,43.332000732421875 -17.895000457763672,29.54599952697754 -17.895000457763672,29.54599952697754z"
                ></path>
              </g>
            </g>
            <g
              clip-path="url(#__lottie_element_1006)"
              transform="matrix(0.14000000059604645,0,0,0.14000000059604645,14.45199966430664,-19.34000015258789)"
              opacity="1"
              style="display: block"
            >
              <g
                transform="matrix(0.8399999737739563,0,0,0.8399999737739563,255.98292541503906,341.49993896484375)"
                opacity="1"
                style="display: block"
              >
                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="miter"
                    fill-opacity="0"
                    stroke-miterlimit="4"
                    stroke="rgb(0,145,255)"
                    stroke-opacity="1"
                    stroke-width="32"
                    d=" M-66.625,-53.125 C-66.625,-53.125 -44.250999450683594,-84.75 0,-84.75 C42.75,-84.75 65.25,-54.375 65.25,-54.375"
                  ></path>
                </g>
              </g>
              <g
                transform="matrix(0.8399999737739563,0,0,0.8399999737739563,256.1399841308594,341.5003967285156)"
                opacity="1"
                style="display: block"
              >
                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="miter"
                    fill-opacity="0"
                    stroke-miterlimit="4"
                    stroke="rgb(0,145,255)"
                    stroke-opacity="1"
                    stroke-width="32"
                    d=" M-111.5,-99.875 C-111.5,-99.875 -76.5,-149 0.125,-149.68800354003906 C75,-150.36000061035156 109.75,-101.75 109.75,-101.75"
                  ></path>
                </g>
              </g>
              <g
                transform="matrix(0.8399999737739563,0,0,0.8399999737739563,255.8770751953125,341.5)"
                opacity="1"
                style="display: block"
              >
                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="miter"
                    fill-opacity="0"
                    stroke-miterlimit="4"
                    stroke="rgb(0,145,255)"
                    stroke-opacity="1"
                    stroke-width="32"
                    d=" M-156.25,-147.25 C-156.25,-147.25 -113,-214.5 0,-214.5 C102,-214.5 155.125,-148.875 155.125,-148.875"
                  ></path>
                </g>
              </g>
              <g
                transform="matrix(2.006119966506958,0,0,2.006119966506958,16.820037841796875,343.8101501464844)"
                opacity="1"
                style="display: block"
              >
                <g
                  opacity="1"
                  transform="matrix(0.720990002155304,0,0,0.720990002155304,171.56500244140625,-63.130001068115234)"
                >
                  <path
                    class="pulse-animation"
                    fill="rgb(234,81,96)"
                    fill-opacity="1"
                    d=" M0,-42 C23.179800033569336,-42 42,-23.179800033569336 42,0 C42,23.179800033569336 23.179800033569336,42 0,42 C-23.179800033569336,42 -42,23.179800033569336 -42,0 C-42,-23.179800033569336 -23.179800033569336,-42 0,-42z"
                  ></path>
                </g>
              </g>
              <g
                transform="matrix(1.2211251258850098,0,0,1.2211251258850098,234.0030059814453,266.61907958984375)"
                opacity="1"
                style="display: block"
              >
                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                  <path
                    stroke-linecap="butt"
                    stroke-linejoin="miter"
                    fill-opacity="0"
                    stroke-miterlimit="4"
                    stroke="rgb(255,255,255)"
                    stroke-opacity="1"
                    stroke-width="12"
                    d=" M104,-69 C104,-69 104,-29 104,-29"
                  ></path>
                </g>
              </g>
              <g
                transform="matrix(1.2211251258850098,0,0,1.2211251258850098,234.0030059814453,304.7774658203125)"
                opacity="1"
                style="display: block"
              >
                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                  <path
                    stroke-linecap="butt"
                    stroke-linejoin="bevel"
                    fill-opacity="0"
                    stroke="rgb(255,255,255)"
                    stroke-opacity="1"
                    stroke-width="12"
                    d=" M104,-56.75 C104,-56.75 104,-46.25 104,-46.25"
                  ></path>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </div>

      <div class="title">No Internet Connection</div>
      <div class="subtitle">Please check your connection and try again</div>

      <button class="retry-btn" onclick="retryConnection()">Try Again</button>
    </div>
  </body>
</html>
