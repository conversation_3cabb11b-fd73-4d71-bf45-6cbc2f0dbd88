-- AlterTable
-- ALTER TABLE "feedback_result"
-- ADD COLUMN "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP \ -- CreateTable
CREATE TABLE "unregistered_referrals" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "phone_number" TEXT NOT NULL,
    "referrer_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "meta" JSONB NOT NULL DEFAULT '{}',
    CONSTRAINT "unregistered_referrals_pkey" PRIMARY KEY ("id")
);
-- CreateIndex
CREATE INDEX "unregistered_referrals_phone_number_idx" ON "unregistered_referrals"("phone_number");
-- CreateIndex
CREATE INDEX "unregistered_referrals_referrer_id_idx" ON "unregistered_referrals"("referrer_id");
-- AddForeignKey
ALTER TABLE "unregistered_referrals"
ADD CONSTRAINT "unregistered_referrals_referrer_id_fkey" FOREIGN KEY ("referrer_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;