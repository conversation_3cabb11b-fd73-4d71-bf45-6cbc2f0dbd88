import dayjs from "dayjs";
import { CheckSquare, Square } from "lucide-react";
import React from "react";
import { useParams } from "react-router-dom";
import { useGetPoliciesWithUserStatusQuery } from "../../__generated__";
import ErrorComponent from "../../components/error/Error";
import Loading from "../../components/loading/Loading";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import ChecklistSection from "./ChecklistSection";

type ComplianceItem = {
  id: string;
  policy_name: string;
  policy_name_hindi?: string | null;
  url?: string | null;
  user_policy_tracking?:
    | ({
        created_at?: Date | null;
        accepted?: boolean | null;
      } | null)[]
    | null;
} | null;

const PolicySection: React.FC<{
  items: ComplianceItem[] | null | undefined;
}> = ({ items }) => (
  <div className="bg-white rounded-lg shadow-sm p-4 mb-6  ">
    <div className="flex justify-between items-center mb-4">
      <div className="text-lg font-semibold">Policy</div>
    </div>
    <table className="w-full">
      <thead className="bg-gray-100">
        <tr>
          <th className="text-left py-2 px-3 text-gray-600 font-medium rounded-l-lg text-sm">
            Policy Name
          </th>
          <th className="text-center py-2 text-gray-600 font-medium w-20 text-sm">Yes/No</th>

          <th className="text-right py-2 px-3 text-gray-600 font-medium w-32 rounded-r-lg text-sm">
            Accepted On
          </th>
        </tr>
      </thead>
      <tbody>
        {items?.length &&
          items.map((item) => (
            <tr key={item?.id} className="hover:bg-gray-50">
              <td className="py-2 px-3">
                <div className="text-gray-700 text-sm">{item?.policy_name}</div>
                <div className="text-gray-500 text-xs">{item?.policy_name_hindi}</div>
              </td>
              <td className="text-center">
                {item?.user_policy_tracking?.length && item?.user_policy_tracking[0]?.accepted ? (
                  <CheckSquare className="w-5 h-5  text-green-500 inline" />
                ) : (
                  <Square className="w-5 h-5 cursor-not-allowed text-gray-300 inline" />
                )}
              </td>

              <td className="text-right justify-center py-2 px-3 text-xs text-gray-500">
                {item?.user_policy_tracking?.length
                  ? dayjs(item?.user_policy_tracking[0]?.created_at).format("DD MMM YYYY")
                  : ""}
              </td>
            </tr>
          ))}
      </tbody>
    </table>
  </div>
);

const Compliance: React.FC = () => {
  const { user_id } = useParams();
  const userId = user_id ? +user_id : -1;

  // State for policy data
  const {
    data: policyData,
    loading: policyLoading,
    error: policyError
  } = useGetPoliciesWithUserStatusQuery({
    variables: { userId },
    fetchPolicy: "network-only"
  });

  if (policyError) {
    return <ErrorComponent error={policyError} />;
  }

  if (policyLoading) {
    return <Loading tip="Loading ..." />;
  }

  return (
    <ProtectedRoute>
      {({ permissions }) => (
        <div className="w-full h-full overflow-y-scroll  bg-gray-50 mb-4">
          <>{console.log(permissions?.read, "permissions")}</>
          <PolicySection
            items={
              policyData?.getPoliciesWithUserStatus?.data?.length
                ? policyData?.getPoliciesWithUserStatus?.data
                : null
            }
          />
          <ChecklistSection />
        </div>
      )}
    </ProtectedRoute>
  );
};

export default Compliance;
