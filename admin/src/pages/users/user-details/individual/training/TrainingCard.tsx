import { <PERSON><PERSON><PERSON><PERSON>r, <PERSON><PERSON>ap<PERSON><PERSON>, <PERSON>User } from "react-icons/lu";
import { Training } from "./TrainingSection";
import { TrainingStatus } from "./TrainingStatus";

interface TrainingCardProps {
  training: Training;
}

const TrainingCard = ({ training }: TrainingCardProps) => {
  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-sm font-medium text-gray-900">{training.name}</h3>
          <div className="mt-1 space-y-1">
            <div className="flex items-center text-sm text-gray-500">
              <LuCalendar className="h-4 w-4 mr-1.5" />
              {training.durationDays} days ({training.startDate} - {training.endDate})
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <LuMapPin className="h-4 w-4 mr-1.5" />
              {training.location}
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <LuUser className="h-4 w-4 mr-1.5" />
              Assessor: {training.assessor}
            </div>
          </div>
        </div>
        <TrainingStatus status={training.status} />
      </div>
      {training.feedback && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <p className="text-sm text-gray-600">
            <span className="font-medium">Feedback:</span> {training.feedback}
          </p>
        </div>
      )}
    </div>
  );
};
export default TrainingCard;
