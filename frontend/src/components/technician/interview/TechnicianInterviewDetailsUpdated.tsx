import {
  Box,
  Button,
  Center,
  Container,
  Divider,
  Flex,
  Heading,
  HStack,
  IconButton,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Tag,
  Text,
  useColorModeValue,
  VStack,
  Wrap,
  WrapItem
} from "@chakra-ui/react";
import { motion, PanInfo, useAnimation } from "framer-motion";
import {
  Check,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  MapPin,
  MessageSquare,
  Phone
} from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { InterviewType, useGetInterviewsFrUserQuery } from "../../../__generated__";

const TechnicianInterviewDetailsUpdated = () => {
  const { t } = useTranslation();
  const carouselRef = useRef<HTMLDivElement>(null);

  //GraphQl Query
  const { data, loading, error, refetch } = useGetInterviewsFrUserQuery({
    fetchPolicy: "network-only", // Changed from no-cache to network-only for better caching behavior
    notifyOnNetworkStatusChange: true // This will ensure loading state is updated during refetches
  });

  // Safely access interviews data with null checks
  const interviews = useMemo(() => data?.getInterviewsFrUser || [], [data?.getInterviewsFrUser]);

  // Separate interviews by status - memoized to prevent recalculation on each render
  const scheduledInterviews = useMemo(
    () =>
      interviews.filter((interview) => {
        if (!interview) return false;

        // Always show SCHEDULED interviews
        if (interview.status === "SCHEDULED") {
          return true;
        }

        // For COMPLETED interviews, check feedback conditions
        if (interview.status === "COMPLETED") {
          // If interviewee_feedback_enabled is explicitly false, don't show the interview
          if (interview.feedback?.template?.interviewee_feedback_enabled === false) {
            return false;
          }

          // If interviewee feedback is already COMPLETED, don't show the interview
          if (interview.feedback?.interviewee_feedback_state === "COMPLETED") {
            return false;
          }

          // In all other cases (including undefined/null values), show the interview
          return true;
        }

        // For all other interview statuses (like CANCELLED, etc.), show them
        return true;
      }),
    [interviews]
  );

  const pastInterviews = useMemo(
    () =>
      interviews.filter((interview) => {
        if (!interview) return false;

        // For COMPLETED interviews, check feedback conditions
        if (interview.status === "COMPLETED") {
          // If interviewee_feedback_enabled is explicitly false, don't show the interview
          if (interview.feedback?.template?.interviewee_feedback_enabled === true) {
            // If interviewee feedback is already COMPLETED, don't show the interview
            if (interview.feedback?.interviewee_feedback_state !== "COMPLETED") {
              return false;
            }
          }

          // In all other cases (including undefined/null values), show the interview
          return true;
        }

        // For all other interview statuses (like CANCELLED, etc.), show them
        return false;
      }),
    [interviews]
  );

  // State variables for carousel, feedback status and modals
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [_, setDragDirection] = useState<"left" | "right" | null>(null);
  const [showAllInterviews, setShowAllInterviews] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [showPastFeedbackModal, setShowPastFeedbackModal] = useState(false);
  const [selectedPastInterviewId, setSelectedPastInterviewId] = useState<number | null>(null);

  // Set the initial state of showAllInterviews based on interviews data
  // and reset currentIndex when scheduledInterviews changes
  useEffect(() => {
    // Reset current index to 0 when the scheduled interviews array changes
    // This prevents index out of bounds errors when refetching
    if (currentIndex >= scheduledInterviews.length) {
      setCurrentIndex(0);
    }

    // Show past interviews section if there are no scheduled interviews
    if (scheduledInterviews.length === 0 && pastInterviews.length > 0) {
      setShowAllInterviews(true);
    }
  }, [scheduledInterviews, pastInterviews.length, currentIndex]);

  // Animation controls for swipe
  const controls = useAnimation();

  // Function to generate feedback URL with the dynamic feedback_form_id - memoized for performance
  const getFeedbackUrl = useCallback((feedbackFormId?: string | null) => {
    if (!feedbackFormId) return "";
    return `${import.meta.env.VITE_ADMIN_LINK || ""}/feedbacks/technician/submit/${feedbackFormId}`;
  }, []);

  // Get the feedback URL for the current interview - memoized to prevent recalculation
  const currentFeedbackUrl = useMemo(() => {
    // Safety check to prevent index out of bounds errors
    if (scheduledInterviews.length === 0) return "";
    if (currentIndex >= scheduledInterviews.length) {
      return getFeedbackUrl(scheduledInterviews[0]?.feedback?.feedback_form_id);
    }
    return getFeedbackUrl(scheduledInterviews[currentIndex]?.feedback?.feedback_form_id);
  }, [getFeedbackUrl, scheduledInterviews, currentIndex]);

  // Memoize the current interview and derived data with safety checks
  const currentInterview = useMemo(() => {
    // Safety check to prevent index out of bounds errors
    if (scheduledInterviews.length === 0) return null;
    if (currentIndex >= scheduledInterviews.length) return scheduledInterviews[0];
    return scheduledInterviews[currentIndex];
  }, [scheduledInterviews, currentIndex]);

  const address = useMemo(
    () => currentInterview?.interviewer?.location?.work_address || "Address not available",
    [currentInterview]
  );

  // Format date and time from the interview data - memoized with useCallback
  const formatDateTime = useCallback((startTime?: string | null, endTime?: string | null) => {
    if (!startTime || !endTime) return "";

    const start = new Date(startTime);
    const end = new Date(endTime);

    const options: Intl.DateTimeFormatOptions = {
      weekday: "short",
      month: "short",
      day: "numeric"
    };
    const dateStr = start.toLocaleDateString("en-US", options);

    const startTimeStr = start.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true
    });
    const endTimeStr = end.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true
    });

    return `${dateStr}, ${startTimeStr} - ${endTimeStr}`;
  }, []);

  // Get the past interview feedback URL only when needed - memoized
  const selectedPastInterviewFeedbackUrl = useMemo(() => {
    if (!selectedPastInterviewId) return "";

    // Use the interviews array we created earlier instead of directly accessing data
    // This ensures consistency with the rest of the component
    const selectedInterview = interviews.find(
      (interview) => interview?.id === selectedPastInterviewId
    );

    if (!selectedInterview) {
      console.warn(`Interview with ID ${selectedPastInterviewId} not found in data`);
      return "";
    }

    return getFeedbackUrl(selectedInterview?.feedback?.feedback_form_id);
  }, [interviews, getFeedbackUrl, selectedPastInterviewId]);

  // Determine if feedback has been submitted based on the interview data
  const feedbackSubmitted = useMemo(
    () => currentInterview?.feedback?.interviewee_feedback_state === "COMPLETED",
    [currentInterview]
  );

  // Colors
  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.100", "gray.700");
  const headingColor = useColorModeValue("gray.900", "white");
  const textColor = useColorModeValue("gray.700", "gray.300");
  const labelBgColor = useColorModeValue("gray.50", "gray.700");
  const labelColor = useColorModeValue("gray.500", "gray.400");
  const buttonBorderColor = useColorModeValue("gray.200", "gray.600");
  const buttonHoverBgColor = useColorModeValue("gray.50", "gray.700");
  const blueBorderColor = useColorModeValue("blue.200", "blue.700");
  const blueTextColor = useColorModeValue("blue.700", "blue.300");
  const blueHoverBgColor = useColorModeValue("blue.50", "blue.900");
  const greenBorderColor = useColorModeValue("green.200", "green.700");
  const greenTextColor = useColorModeValue("green.600", "green.300");
  const cardBg = useColorModeValue("white", "gray.800");
  const titleColor = useColorModeValue("gray.900", "white");
  const itemBg = useColorModeValue("gray.50", "gray.700");

  // Calculate the width of the card container for proper positioning
  useEffect(() => {
    const updateCardWidth = () => {
      // Just ensure the ref is available
      if (carouselRef.current) {
        // We can access the width if needed in the future
      }
    };

    updateCardWidth();
    window.addEventListener("resize", updateCardWidth);

    return () => {
      window.removeEventListener("resize", updateCardWidth);
    };
  }, []);

  // Navigation functions with improved animations - memoized with useCallback
  const goToPrevious = useCallback(() => {
    if (currentIndex > 0) {
      controls
        .start({
          x: "100%",
          opacity: 0,
          transition: { duration: 0.3 }
        })
        .then(() => {
          setCurrentIndex(currentIndex - 1);
          controls.set({ x: "-100%" });
          controls.start({
            x: 0,
            opacity: 1,
            transition: { duration: 0.3 }
          });
        });
    }
  }, [currentIndex, controls]);

  const goToNext = useCallback(() => {
    if (currentIndex < scheduledInterviews.length - 1) {
      controls
        .start({
          x: "-100%",
          opacity: 0,
          transition: { duration: 0.3 }
        })
        .then(() => {
          setCurrentIndex(currentIndex + 1);
          controls.set({ x: "100%" });
          controls.start({
            x: 0,
            opacity: 1,
            transition: { duration: 0.3 }
          });
        });
    }
  }, [currentIndex, controls, scheduledInterviews.length]);

  // Handler for drag start - memoized
  const handleDragStart = useCallback(
    (_: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
      setIsDragging(true);
      setDragStartX(info.point.x);
    },
    []
  );

  // Handler for drag - memoized
  const handleDrag = useCallback(
    (_: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
      if (isDragging) {
        const currentDragX = info.point.x;
        setDragDirection(currentDragX < dragStartX ? "left" : "right");
      }
    },
    [isDragging, dragStartX]
  );

  // Handler for pagination dot clicks - memoized
  const handlePaginationClick = useCallback(
    (index: number) => {
      if (index > currentIndex) {
        controls
          .start({
            x: "-100%",
            opacity: 0,
            transition: { duration: 0.3 }
          })
          .then(() => {
            setCurrentIndex(index);
            controls.set({ x: "100%" });
            controls.start({
              x: 0,
              opacity: 1,
              transition: { duration: 0.3 }
            });
          });
      } else if (index < currentIndex) {
        controls
          .start({
            x: "100%",
            opacity: 0,
            transition: { duration: 0.3 }
          })
          .then(() => {
            setCurrentIndex(index);
            controls.set({ x: "-100%" });
            controls.start({
              x: 0,
              opacity: 1,
              transition: { duration: 0.3 }
            });
          });
      }
    },
    [controls, currentIndex]
  );

  // Handle drag (swipe) gestures with improved detection - memoized with useCallback
  const handleDragEnd = useCallback(
    (_: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
      setIsDragging(false);
      const threshold = 50; // minimum distance required for a swipe
      const dragDistance = info.offset.x;
      const dragVelocity = info.velocity.x;

      // Use both distance and velocity for better swipe detection
      if (dragDistance > threshold || (dragDistance > 20 && dragVelocity > 0.5)) {
        // Swiped right with enough force/distance
        goToPrevious();
      } else if (dragDistance < -threshold || (dragDistance < -20 && dragVelocity < -0.5)) {
        // Swiped left with enough force/distance
        goToNext();
      } else {
        // Return to center if not swiped far enough
        controls.start({
          x: 0,
          transition: { type: "spring", stiffness: 300, damping: 30 }
        });
      }

      setDragDirection(null);
    },
    [controls, goToPrevious, goToNext]
  );

  // Handlers for modals and UI interactions - memoized
  const handleToggleShowAllInterviews = useCallback(() => {
    setShowAllInterviews((prev) => !prev);
  }, []);

  const handleCloseFeedbackModal = useCallback(async () => {
    try {
      // First close the modal to improve perceived performance
      setShowFeedbackModal(false);
      // Then refetch data in the background
      await refetch();
    } catch (error) {
      console.error("Error refetching data after closing feedback modal:", error);
    }
  }, [refetch]);

  const handleClosePastFeedbackModal = useCallback(async () => {
    try {
      // First close the modal to improve perceived performance
      setShowPastFeedbackModal(false);
      // Then refetch data in the background
      await refetch();
    } catch (error) {
      console.error("Error refetching data after closing past feedback modal:", error);
    }
  }, [refetch]);

  const handleOpenFeedbackModal = useCallback(() => {
    setShowFeedbackModal(true);
  }, []);

  const handleOpenPastFeedbackModal = useCallback((interviewId: number) => {
    setSelectedPastInterviewId(interviewId);
    setShowPastFeedbackModal(true);
  }, []);

  // Handle initial loading and error states
  // Only show loading state on initial load, not during refetches
  // This prevents the entire component from being replaced during refetches
  const isInitialLoading = loading && !data;

  if (isInitialLoading) {
    return (
      <Box minH="100vh" bg={bgColor} display="flex" justifyContent="center" alignItems="center">
        <Text>Loading interview details...</Text>
      </Box>
    );
  }

  if (error) {
    console.error("Error in TechnicianInterviewDetailsUpdated:", error);
    return (
      <Box minH="100vh" bg={bgColor} display="flex" justifyContent="center" alignItems="center">
        <Text color="red.500">Error loading interview details. Please try again later.</Text>
      </Box>
    );
  }

  // If we're refetching (loading is true but we have data), we'll continue rendering with existing data
  // This prevents the UI from flickering during refetches

  return (
    <>
      <Box minH="100vh" bg={bgColor}>
        {/* Header */}
        <Box
          position="sticky"
          top={0}
          bg={bgColor}
          borderBottom="1px"
          borderColor={borderColor}
          zIndex={10}
        ></Box>

        {/* Main Content */}
        <Container maxW="md" px={3} py={4}>
          <VStack spacing={4} align="stretch">
            {/* Scheduled Interviews Carousel */}
            {scheduledInterviews.length > 0 && (
              <Box position="relative" overflow="hidden" ref={carouselRef}>
                {/* Navigation buttons - only show if more than one scheduled interview */}
                {scheduledInterviews.length > 1 && (
                  <>
                    <IconButton
                      aria-label="Previous interview"
                      icon={<ChevronLeft size={20} />}
                      position="absolute"
                      left={2}
                      top="50%"
                      transform="translateY(-50%)"
                      zIndex={2}
                      bg={cardBg}
                      color={titleColor}
                      borderRadius="full"
                      size="sm"
                      onClick={goToPrevious}
                      isDisabled={currentIndex === 0}
                      opacity={currentIndex === 0 ? 0.5 : 1}
                      _hover={{ bg: itemBg }}
                      boxShadow="md"
                    />
                    <IconButton
                      aria-label="Next interview"
                      icon={<ChevronRight size={20} />}
                      position="absolute"
                      right={2}
                      top="50%"
                      transform="translateY(-50%)"
                      zIndex={2}
                      bg={cardBg}
                      color={titleColor}
                      borderRadius="full"
                      size="sm"
                      onClick={goToNext}
                      isDisabled={currentIndex === scheduledInterviews.length - 1}
                      opacity={currentIndex === scheduledInterviews.length - 1 ? 0.5 : 1}
                      _hover={{ bg: itemBg }}
                      boxShadow="md"
                    />
                  </>
                )}

                {/* Carousel container */}
                <Box
                  position="relative"
                  width="100%"
                  px={scheduledInterviews.length > 1 ? 4 : 0}
                  overflow="visible"
                >
                  {/* Interview card with swipe functionality - only enable drag if more than one interview */}
                  <motion.div
                    drag={scheduledInterviews.length > 1 ? "x" : false}
                    dragConstraints={{ left: 0, right: 0 }}
                    onDragEnd={scheduledInterviews.length > 1 ? handleDragEnd : undefined}
                    onDragStart={scheduledInterviews.length > 1 ? handleDragStart : undefined}
                    onDrag={scheduledInterviews.length > 1 ? handleDrag : undefined}
                    animate={controls}
                    initial={{ opacity: 1, x: 0 }}
                    style={{
                      width: "100%",
                      position: "relative",
                      zIndex: 1
                    }}
                  >
                    {/* Current Interview Card */}
                    <Box border="1px" borderColor={blueBorderColor} borderRadius="2xl" p={5}>
                      <Flex align="flex-start" justify="space-between" mb={4}>
                        <Heading
                          as="h2"
                          fontSize="xl"
                          fontWeight="semibold"
                          lineHeight="tight"
                          color={headingColor}
                        >
                          {currentInterview?.feedback?.template?.title || "WIFY Office"}
                        </Heading>
                        <div>
                          <Tag
                            px={2.5}
                            py={1}
                            border="1px"
                            borderColor={blueBorderColor}
                            color={blueTextColor}
                            borderRadius="full"
                            fontSize="xs"
                            fontWeight="medium"
                            bg="transparent"
                            bgColor={currentInterview?.status === "CANCELLED" ? "red.200" : "none"}
                          >
                            {currentInterview?.status === "SCHEDULED"
                              ? "Upcoming"
                              : currentInterview?.status === "COMPLETED"
                                ? "Completed"
                                : currentInterview?.status === "CANCELLED"
                                  ? "Cancelled"
                                  : currentInterview?.status || "Unknown"}
                          </Tag>
                        </div>
                      </Flex>

                      <VStack spacing={4} align="stretch">
                        <Box>
                          <Tag
                            mb={1}
                            bg={labelBgColor}
                            color={labelColor}
                            fontSize="xs"
                            fontWeight="medium"
                            textTransform="uppercase"
                            letterSpacing="wider"
                            borderRadius="lg"
                            px={3}
                            py={1}
                          >
                            {t("address")}
                          </Tag>
                          <Text color={textColor} fontSize="sm" lineHeight="relaxed">
                            {address}
                          </Text>
                        </Box>

                        <Box>
                          <Tag
                            mb={1}
                            bg={labelBgColor}
                            color={labelColor}
                            fontSize="xs"
                            fontWeight="medium"
                            textTransform="uppercase"
                            letterSpacing="wider"
                            borderRadius="lg"
                            px={3}
                            py={1}
                          >
                            {t("scheduled_at")}
                          </Tag>
                          <Text color={textColor} fontSize="sm">
                            {formatDateTime(
                              currentInterview?.start_time,
                              currentInterview?.end_time
                            )}
                          </Text>
                        </Box>
                      </VStack>

                      {/* Action Buttons */}
                      <HStack spacing={3} mt={5}>
                        <Button
                          flex={1}
                          variant="outline"
                          borderColor={buttonBorderColor}
                          color={textColor}
                          py={2.5}
                          px={4}
                          borderRadius="xl"
                          fontSize="sm"
                          leftIcon={<MapPin size={16} />}
                          _hover={{ bg: buttonHoverBgColor }}
                          as="a"
                          href={`https://maps.google.com/?q=${encodeURIComponent(address)}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          View Map
                        </Button>
                        {currentInterview?.interview_type === InterviewType.InterviewOnline &&
                        currentInterview?.google_meet_link ? (
                          <Button
                            flex={1}
                            variant="outline"
                            borderColor={blueBorderColor}
                            color={blueTextColor}
                            py={2.5}
                            px={4}
                            borderRadius="xl"
                            fontSize="sm"
                            fontWeight="medium"
                            leftIcon={<MessageSquare size={16} />}
                            _hover={{ bg: blueHoverBgColor }}
                            as="a"
                            href={`https://meet.google.com/${currentInterview?.google_meet_link}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            Join Meeting
                          </Button>
                        ) : (
                          <Button
                            flex={1}
                            variant="outline"
                            borderColor={blueBorderColor}
                            color={blueTextColor}
                            py={2.5}
                            px={4}
                            borderRadius="xl"
                            fontSize="sm"
                            fontWeight="medium"
                            leftIcon={<Phone size={16} />}
                            _hover={{ bg: blueHoverBgColor }}
                            as="a"
                            href={`tel:+91 7021901010`}
                          >
                            Call HR
                          </Button>
                        )}
                      </HStack>

                      <Box
                        hidden={!currentInterview?.feedback?.template?.interviewee_feedback_enabled}
                      >
                        {/* Feedback Button */}
                        {feedbackSubmitted ? (
                          <Button
                            w="full"
                            mt={4}
                            py={2.5}
                            variant="outline"
                            borderColor={greenBorderColor}
                            borderRadius="xl"
                            fontSize="sm"
                            color={greenTextColor}
                            cursor="default"
                            leftIcon={<Check size={16} />}
                          >
                            {t("feedback_submitted")}
                          </Button>
                        ) : (
                          <Button
                            w="full"
                            mt={4}
                            py={2.5}
                            variant="outline"
                            borderColor={buttonBorderColor}
                            borderRadius="xl"
                            fontSize="sm"
                            color={textColor}
                            leftIcon={<MessageSquare size={16} />}
                            _hover={{ bg: buttonHoverBgColor }}
                            onClick={handleOpenFeedbackModal}
                            disabled={
                              currentInterview?.status === "CANCELLED" ||
                              currentInterview?.status !== "COMPLETED"
                            }
                          >
                            {t("share_your_feedback")}
                          </Button>
                        )}
                      </Box>
                    </Box>
                  </motion.div>

                  {/* Preview of adjacent cards - only show if more than one scheduled interview */}
                  {scheduledInterviews.length > 1 && (
                    <>
                      {/* Previous card preview (if not at the beginning) */}
                      {currentIndex > 0 && (
                        <Box
                          position="absolute"
                          left="-15%"
                          top="50%"
                          transform="translateY(-50%)"
                          width="30%"
                          height="80%"
                          bg={cardBg}
                          borderRadius="xl"
                          opacity={0.6}
                          boxShadow="sm"
                          zIndex={0}
                          display={["none", "block"]}
                        />
                      )}

                      {/* Next card preview (if not at the end) */}
                      {currentIndex < scheduledInterviews.length - 1 && (
                        <Box
                          position="absolute"
                          right="-15%"
                          top="50%"
                          transform="translateY(-50%)"
                          width="30%"
                          height="80%"
                          bg={cardBg}
                          borderRadius="xl"
                          opacity={0.6}
                          boxShadow="sm"
                          zIndex={0}
                          display={["none", "block"]}
                        />
                      )}
                    </>
                  )}
                </Box>

                {/* Pagination indicators - only show if more than one scheduled interview */}
                {scheduledInterviews.length > 1 && (
                  <Center mt={2} mb={2}>
                    <Wrap spacing={2} justify="center">
                      {scheduledInterviews.map((_, index) => (
                        <WrapItem key={index}>
                          <Box
                            w="8px"
                            h="8px"
                            borderRadius="full"
                            bg={index === currentIndex ? blueTextColor : "gray.300"}
                            cursor="pointer"
                            onClick={() => handlePaginationClick(index)}
                          />
                        </WrapItem>
                      ))}
                    </Wrap>
                  </Center>
                )}
              </Box>
            )}

            {/* Past Interviews Section */}
            <Box border="1px" borderColor={buttonBorderColor} borderRadius="2xl" overflow="hidden">
              <Button
                w="full"
                px={5}
                py={4}
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                textAlign="left"
                variant="ghost"
                _hover={{ bg: buttonHoverBgColor }}
                onClick={handleToggleShowAllInterviews}
              >
                <Text fontSize="base" fontWeight="medium" color={headingColor}>
                  {scheduledInterviews.length === 0 ? t("interviews") : t("past_interviews")}
                </Text>
                <Box
                  as={ChevronUp}
                  transform={showAllInterviews ? "rotate(0deg)" : "rotate(180deg)"}
                  transition="transform 0.2s"
                  color={labelColor}
                  boxSize={4}
                />
              </Button>

              {/* Past Interviews */}
              {showAllInterviews && (
                <Box px={5} pb={5} borderTop="1px" borderColor={borderColor}>
                  {pastInterviews.length > 0 ? (
                    // Map through past interviews (non-scheduled interviews)
                    pastInterviews.map((interview, index) => (
                      <Box pt={4} key={interview?.id || index}>
                        <VStack spacing={4} align="stretch">
                          <Flex justify="space-between" align="center">
                            <Text fontSize="base" fontWeight="medium" color={headingColor}>
                              {interview?.feedback?.template?.title || "WIFY Office"}
                            </Text>
                            <Tag
                              px={2.5}
                              py={1}
                              border="1px"
                              borderColor={
                                interview.status === "CANCELLED" ? "red.200" : blueBorderColor
                              }
                              color={blueTextColor}
                              borderRadius="full"
                              fontSize="xs"
                              fontWeight="medium"
                              bg="transparent"
                              textTransform="capitalize"
                              bgColor={interview.status === "CANCELLED" ? "red.200" : "none"}
                            >
                              {interview.status === "SCHEDULED"
                                ? "Upcoming"
                                : interview?.status === "CANCELLED"
                                  ? "Cancelled"
                                  : interview?.status === "COMPLETED"
                                    ? "Completed"
                                    : interview?.status}
                            </Tag>
                          </Flex>

                          <VStack spacing={4} align="stretch">
                            <Box>
                              <Tag
                                mb={1}
                                bg={labelBgColor}
                                color={labelColor}
                                fontSize="xs"
                                fontWeight="medium"
                                textTransform="uppercase"
                                letterSpacing="wider"
                                borderRadius="lg"
                                px={3}
                                py={1}
                              >
                                {t("address")}
                              </Tag>
                              <Text color={textColor} fontSize="sm" lineHeight="relaxed">
                                {interview?.interviewer?.location?.work_address ||
                                  "Address not available"}
                              </Text>
                            </Box>

                            <Box>
                              <Tag
                                mb={1}
                                bg={labelBgColor}
                                color={labelColor}
                                fontSize="xs"
                                fontWeight="medium"
                                textTransform="uppercase"
                                letterSpacing="wider"
                                borderRadius="lg"
                                px={3}
                                py={1}
                              >
                                {t("scheduled_at")}
                              </Tag>
                              <Text color={textColor} fontSize="sm">
                                {formatDateTime(interview?.start_time, interview?.end_time)}
                              </Text>
                            </Box>

                            {/* Past Interview Feedback Button */}
                            <Box
                              hidden={!interview?.feedback?.template?.interviewee_feedback_enabled}
                            >
                              {interview?.feedback?.interviewee_feedback_state === "COMPLETED" ? (
                                <Button
                                  w="full"
                                  py={2.5}
                                  variant="outline"
                                  borderColor={greenBorderColor}
                                  borderRadius="xl"
                                  fontSize="sm"
                                  color={greenTextColor}
                                  cursor="default"
                                  leftIcon={<Check size={16} />}
                                >
                                  {t("feedback_submitted")}
                                </Button>
                              ) : (
                                <Button
                                  w="full"
                                  py={2.5}
                                  variant="outline"
                                  borderColor={buttonBorderColor}
                                  borderRadius="xl"
                                  fontSize="sm"
                                  color={textColor}
                                  leftIcon={<MessageSquare size={16} />}
                                  _hover={{ bg: buttonHoverBgColor }}
                                  onClick={() => handleOpenPastFeedbackModal(interview?.id || 0)}
                                >
                                  {t("share_your_feedback")}
                                </Button>
                              )}
                            </Box>
                          </VStack>
                        </VStack>
                        <Divider className="my-4" />
                      </Box>
                    ))
                  ) : (
                    <Box pt={4}>
                      <Text color={textColor} fontSize="sm">
                        {t("no_past_interviews_found")}
                      </Text>
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          </VStack>
        </Container>
      </Box>

      {/* Feedback Modal for Web */}
      <Modal isOpen={showFeedbackModal} onClose={handleCloseFeedbackModal} isCentered size="xl">
        <ModalOverlay />
        <ModalContent maxH="90vh">
          <ModalCloseButton />
          <ModalBody pt={2} pb={6} pl={2} pr={2}>
            <iframe
              src={currentFeedbackUrl}
              style={{ width: "100%", height: "calc(90vh - 120px)", border: "none" }}
              title="Feedback Form"
              allow="camera; microphone"
            />
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Past Feedback Modal for Web */}
      <Modal
        isOpen={showPastFeedbackModal}
        onClose={handleClosePastFeedbackModal}
        isCentered
        size="xl"
      >
        <ModalOverlay />
        <ModalContent maxH="90vh">
          <ModalCloseButton />
          <ModalBody pt={2} pb={6} pl={2} pr={2}>
            <iframe
              src={selectedPastInterviewFeedbackUrl}
              style={{ width: "100%", height: "calc(90vh - 120px)", border: "none" }}
              title="Past Interview Feedback Form"
              allow="camera; microphone"
            />
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default TechnicianInterviewDetailsUpdated;
