import { getStatusColor, TrainingStatus as Status } from "./TrainingSection";

interface TrainingStatusProps {
  status: Status;
}
const getStatusText = (status: Status): string => {
  return status
    .split("_")
    .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
    .join(" ");
};
export function TrainingStatus({ status }: TrainingStatusProps) {
  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
        status
      )}`}
    >
      {getStatusText(status)}
    </span>
  );
}
