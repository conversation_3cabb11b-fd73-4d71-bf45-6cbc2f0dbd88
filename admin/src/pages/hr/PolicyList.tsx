import dayjs from "dayjs";
import { FileText } from "lucide-react";
import React from "react";
import { useGetAllPoliciesQuery } from "../../__generated__";
import ErrorComponent from "../../components/error/Error";
import Loading from "../../components/loading/Loading";

// interface PolicyData {
//   id: string;
//   policy_name: string;
//   policy_name_hindi?: string | null;
//   url?: string | null;
//   is_active: boolean;
//   updated_at?: Date;
// }

const PolicyList: React.FC = () => {
  const { data, loading, error } = useGetAllPoliciesQuery();

  if (loading) {
    return <Loading tip="Loading policies..." />;
  }

  if (error) {
    return <ErrorComponent error={error} />;
  }

  // Filter out any null values from the policies array
  const policies = (data?.getAllPolicies?.data || []).filter((policy) => policy !== null);

  const handlePolicyClick = (pdfUrl?: string | null) => {
    if (!pdfUrl) {
      return;
    }
    window.open(pdfUrl, "_blank");
  };
  return (
    <div className="flex-1 bg-gray-50 min-h-screen p-6">
      <h2 className="text-2xl font-semibold mb-6">Policy List</h2>
      <div className="bg-white rounded-lg shadow-sm p-6">
        <table className="w-full">
          <thead className="bg-gray-100">
            <tr>
              <th className="text-left py-3 px-4 text-[16px] leading-6 text-gray-500 font-medium rounded-l-lg">
                Policy Name
              </th>
              <th className="text-right py-3 px-4 text-gray-500  text-[16px] leading-6 font-medium rounded-r-lg">
                Last Updated
              </th>
            </tr>
          </thead>
          <tbody>
            {policies.map((item) => (
              <tr key={item?.id} className="hover:bg-gray-50">
                <td className="py-3 px-4">
                  <div
                    onClick={() => handlePolicyClick(item?.url)}
                    className="flex items-center group cursor-pointer text-left"
                  >
                    <FileText className="w-5 h-5 text-gray-400  group-hover:text-purple-600 mr-2" />
                    <div>
                      <div className="text-gray-700 group-hover:text-purple-600">
                        {item?.policy_name}
                      </div>
                      <div className="text-gray-500 text-sm">{item?.policy_name_hindi}</div>
                    </div>
                  </div>
                </td>
                <td className="text-right py-3 cursor-default px-4 text-sm text-gray-500">
                  {dayjs(item?.updated_at).format("DD MMM YYYY")}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PolicyList;
