import { DownloadOutlined, UserDeleteOutlined } from "@ant-design/icons";
import { gql, useQuery } from "@apollo/client";
import { <PERSON><PERSON>, Card, DatePicker, Modal, Space, Table, Tabs, Typography } from "antd";
import Search from "antd/es/input/Search";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { useCallback, useEffect, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import {
  ExportableDataType,
  OnboardingStage,
  useExportUnregisteredReferralsLazyQuery,
  useGetAllReferredUsersOfUserLazyQuery,
  useGetExportableReferredDataLazyQuery,
  useGetUnregisteredReferralsLazyQuery
} from "../../__generated__";
import ErrorComponent from "../../components/error/Error";
import Loading from "../../components/loading/Loading";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import { debounce } from "../../utils/utils";
import ExportJson from "./ExportJson";

const { Title } = Typography;
dayjs.extend(utc);

const ReferralHistory: React.FC = () => {
  //Hooks
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [referrerName, setReferrerName] = useState("");
  const [search, setSearchParams] = useSearchParams();
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(search.get("search") || "");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  //Graphql query
  const GET_REFERRED_COUNT = gql`
    query GetReferredUsersCount {
      getReferredUsersCount {
        onboard_count
        referred_count
      }
    }
  `;

  const [fetchSpecificReferredUsers, { data: specificReferredUsers }] =
    useGetAllReferredUsersOfUserLazyQuery();

  const { loading: countLoading, error: countError, data } = useQuery(GET_REFERRED_COUNT);

  const [
    fetchReferralData,
    { data: referralData, loading: referralDataLoading, error: referralDataError }
  ] = useGetExportableReferredDataLazyQuery();

  const [
    fetchUnregisteredReferrals,
    {
      data: unregisteredReferralsData,
      loading: unregisteredReferralsLoading,
      error: unregisteredReferralsError
    }
  ] = useGetUnregisteredReferralsLazyQuery();

  // Calculate the total number of data pages based on search results
  const totalDataPages = Math.ceil(
    referralData?.getExportableReferredData?.total_count || 0 / pageSize
  );

  // Calculate the total number of unregistered referrals pages
  const totalUnregisteredPages = Math.ceil(
    unregisteredReferralsData?.getUnregisteredReferrals?.total_count || 0 / pageSize
  );

  const [fetchExportableReferredData] = useGetExportableReferredDataLazyQuery();
  const [fetchExportUnregisteredReferrals] = useExportUnregisteredReferralsLazyQuery({
    fetchPolicy: "no-cache"
  });

  //Date Range in Date Field of FormBuilder
  const ranges = {
    "Today ": [dayjs().startOf("day"), dayjs().endOf("day")],
    "Yesterday ": [
      dayjs().subtract(1, "day").startOf("day"),
      dayjs().subtract(1, "day").endOf("day")
    ],
    "Last 7 days": [dayjs().subtract(6, "days").startOf("day"), dayjs()],
    "Last week": [
      dayjs().subtract(1, "week").startOf("week"),
      dayjs().subtract(1, "week").endOf("week")
    ],
    "Month to date": [dayjs().startOf("month"), dayjs()],
    "Previous Month": [
      dayjs().subtract(1, "month").startOf("month"),
      dayjs().subtract(1, "month").endOf("month")
    ],
    "Year to Date": [dayjs().startOf("year"), dayjs()]
  };

  const handleReferredCountClick = (id: number, name: string) => {
    setReferrerName(name);
    setIsModalOpen(true);
    fetchSpecificReferredUsers({
      variables: {
        userId: id
      }
    });
    return name;
  };

  //Handle Debounce Search
  const debounceSearch = useCallback(
    debounce((val: string) => {
      setCurrentPage(1);
      setSearchParams({
        search: val,
        tab: search.get("tab") || ""
      });
    }, 500),
    [search]
  );

  const handleTabChange = (key: string) => {
    setCurrentPage(1);
    setPageSize(10);
    setSearchTerm("");
    setSearchParams({
      search: "",
      tab: key
    });
  };

  const handleCurrentPage = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  //Formbuilder Meta
  const meta = {
    columns: 1,
    formItemLayout: null,
    fields: [
      {
        key: "user_creation_date",
        widgetProps: { style: { width: "290px" }, ranges: ranges },
        label: "Created On Date",
        widget: DatePicker.RangePicker
      }
    ]
  };

  const handleSearchOnChange = (value: string) => {
    setSearchTerm(value);
    debounceSearch(value);
  };

  useEffect(() => {
    const currentTab = search.get("tab") || "referred";

    if (currentTab === "unregistered") {
      fetchUnregisteredReferrals({
        variables: {
          search: search.get("search"),
          pagination: {
            take: pageSize,
            skip: currentPage - 1
          }
        }
      });
    } else {
      fetchReferralData({
        variables: {
          filter: {
            type:
              currentTab === "individual"
                ? ExportableDataType.Individual
                : ExportableDataType.Referred
          },
          search: search.get("search"),
          pagination: {
            take: pageSize,
            skip: currentPage - 1
          }
        }
      });
    }
  }, [search, currentPage, pageSize]);

  //Handle Loading
  if (countLoading) {
    return <Loading tip="Loading.. " />;
  }

  //Handle Error
  if (referralDataError || unregisteredReferralsError || countError) {
    return <ErrorComponent error={referralDataError || unregisteredReferralsError || countError} />;
  }
  return (
    <ProtectedRoute sectionIds={["unregistered_referrals"]}>
      {({ permissions, sectionPermissions }) => (
        <div className="p-3">
          <div className="text-xl mt-2 flex items-center p-2 mb-3">
            <UserDeleteOutlined className=" text-purple-500 text-3xl  mr-4" />{" "}
            <span className="font-semibold">Referral History</span>
          </div>
          <div className="flex mb-8 gap-4">
            <Card
              className="border-b-4 border-b-green-500"
              title={
                <Title
                  level={3}
                  style={{ fontWeight: "bold", textAlign: "center", fontSize: "40px" }}
                >
                  {data?.getReferredUsersCount?.referred_count}
                </Title>
              }
              bodyStyle={{ padding: "10px" }}
            >
              Number of technicians referred
            </Card>
            <Card
              className="border-b-4 border-b-green-500"
              title={
                <Title
                  level={3}
                  style={{ fontWeight: "bold", textAlign: "center", fontSize: "40px" }}
                >
                  {data?.getReferredUsersCount?.onboard_count}
                </Title>
              }
              bodyStyle={{ padding: "10px" }}
            >
              Number of technicians onboarded
            </Card>
          </div>
          <Modal
            title={`Referrer : ${referrerName} (${specificReferredUsers?.getAllReferredUsersOfUser?.length})`}
            open={isModalOpen}
            onCancel={() => setIsModalOpen(false)}
            footer={null}
          >
            <Table
              bordered
              dataSource={specificReferredUsers?.getAllReferredUsersOfUser as Array<any>}
              columns={[
                {
                  title: "Referred To",
                  dataIndex: "available_points",
                  key: "available_points",
                  render: (_, record) => {
                    return (
                      <Link to={`/dashboard/users/${record?.referred_to?.id}`}>
                        {record?.referred_to?.name}
                      </Link>
                    );
                  }
                },
                {
                  title: "Stage",
                  dataIndex: "available_points",
                  key: "available_points",
                  align: "right",
                  render: (_, record) => {
                    return record?.referred_to?.onboarding_stage == OnboardingStage.Dashboard
                      ? "SIGN UP COMPLETED"
                      : record?.referred_to?.onboarding_stage;
                  }
                }
              ]}
            />
          </Modal>
          <Space className="mb-4">
            <Search
              allowClear
              autoFocus
              value={searchTerm}
              onChange={(e) => handleSearchOnChange(e.target.value)}
              placeholder="Search name"
            />

            <Button
              disabled={!permissions?.adminData?.can_download}
              onClick={() => setIsExportModalOpen(true)}
            >
              <DownloadOutlined />
              Download
            </Button>
          </Space>
          <div>
            <Tabs
              type="card"
              className=""
              onChange={(key) => handleTabChange(key)}
              defaultActiveKey={search.get("tab") || "referred"}
            >
              <Tabs.TabPane tab="Referred" key="referred">
                <Table
                  loading={referralDataLoading}
                  pagination={{
                    current: currentPage,
                    pageSize,
                    onChange: (page, pageSize) => {
                      handleCurrentPage(page, pageSize);
                    },

                    total: totalDataPages || 1
                  }}
                  dataSource={referralData?.getExportableReferredData?.data?.map((data) => {
                    return {
                      id: data?.referred_to?.id,
                      name: data?.referred_to?.name,
                      onboarding_stage:
                        data?.referred_to?.onboarding_stage == OnboardingStage.Dashboard
                          ? "SIGN UP COMPLETED"
                          : data?.referred_to?.onboarding_stage,
                      referral_code: data?.referral_code || "-",
                      referrer_code: data?.referrer_code,
                      available_points: data?.available_points || "-",
                      referrer_name: data?.referred_by?.name,
                      referred_count: data?.referred_count,
                      created_at: data?.referred_to?.created_at,
                      updated_at: data?.referred_to?.updated_at,
                      designation: data?.referred_to?.designation?.name || "-",
                      referrer_designation: data?.referred_by?.designation?.name || "-"
                    };
                  })}
                  columns={[
                    {
                      title: "Name",
                      dataIndex: "name",
                      key: "name",
                      render(val, record) {
                        return <Link to={`/dashboard/users/${record?.id}`}>{val}</Link>;
                      }
                    },
                    {
                      title: "User Code",
                      dataIndex: "referral_code",
                      key: "referral_code",
                      align: "center"
                    },
                    {
                      title: "Stage",
                      dataIndex: "onboarding_stage",
                      key: "onboarding_stage",
                      align: "center"
                    },
                    {
                      title: "Designation",
                      key: "designation",
                      align: "center",
                      render: (val) => {
                        return <>{val?.designation}</>;
                      }
                    },
                    {
                      title: "No. of referred",
                      align: "center",
                      render: (obj) => {
                        return (
                          <Button
                            type="link"
                            style={{ border: "none" }}
                            onClick={() => handleReferredCountClick(obj.id, obj?.name)}
                          >
                            {" "}
                            {obj?.referred_count}{" "}
                          </Button>
                        );
                      }
                    },
                    {
                      title: "Available Points",
                      dataIndex: "available_points",
                      key: "available_points",
                      align: "center"
                    },
                    {
                      title: "Referrer",
                      dataIndex: "referrer_name",
                      key: "referrer",
                      align: "center"
                    },
                    {
                      title: "Referrer Code",
                      dataIndex: "referrer_code",
                      key: "referrer_code",
                      align: "center"
                    },
                    {
                      title: "Referrer Designation",
                      dataIndex: "referrer_designation",
                      key: "referrer_designation",
                      align: "center"
                    },
                    {
                      title: "Created On",
                      dataIndex: "created_at",
                      key: "created_at",
                      align: "center",
                      render: (val) => <> {val ? dayjs(val).format("MMM D, YYYY") : "-"}</>
                    },
                    {
                      title: "Updated On",
                      dataIndex: "updated_at",
                      key: "updated_at",
                      align: "center",
                      render: (val) => {
                        return <> {val ? dayjs(val).format("MMM D, YYYY") : "-"}</>;
                      }
                    }
                  ]}
                />
              </Tabs.TabPane>
              {sectionPermissions?.find((p: any) => p?.section_id === "unregistered_referrals")
                ?.read && (
                <Tabs.TabPane
                  tab="Unregistered Referrals"
                  key="unregistered"
                  id="unregistered_referrals"
                >
                  <Table
                    loading={unregisteredReferralsLoading}
                    pagination={{
                      current: currentPage,
                      pageSize,
                      onChange: (page, pageSize) => {
                        handleCurrentPage(page, pageSize);
                      },
                      total: totalUnregisteredPages || 1
                    }}
                    dataSource={
                      unregisteredReferralsData?.getUnregisteredReferrals?.data as Array<any>
                    }
                    columns={[
                      {
                        title: "Name",
                        dataIndex: "name",
                        key: "name",
                        render: (val) => <>{val || "-"}</>
                      },
                      {
                        title: "Phone Number",
                        dataIndex: "phone_number",
                        key: "phone_number",
                        align: "center"
                      },
                      {
                        title: "Referrer Name",
                        align: "center",
                        render: (val, record) => {
                          console.log(record);
                          return (
                            <Link to={`/dashboard/users/${record?.referrer?.id}`}>
                              {record?.referrer?.name || "-"}
                            </Link>
                          );
                        }
                        // render: (record) => <>{record?.referrer?.name || "-"}</>
                      },
                      {
                        title: "Referrer Phone Number",
                        align: "center",
                        render: (record) => {
                          return <>{record?.referrer?.phone || "-"}</>;
                        }
                      },
                      {
                        title: "Referrer Code",
                        align: "center",
                        render: (record) => {
                          return <>{record?.referrer?.user_referred_to?.referral_code || "-"}</>;
                        }
                      },
                      {
                        title: "Referred Date",
                        dataIndex: "created_at",
                        key: "created_at",
                        align: "center",
                        render: (val) => <>{val ? dayjs(val).format("MMM D, YYYY") : "-"}</>
                      }
                    ]}
                  />
                </Tabs.TabPane>
              )}
              <Tabs.TabPane tab="Individual" key="individual">
                <Table
                  loading={referralDataLoading}
                  pagination={{
                    current: currentPage,
                    pageSize,
                    onChange: (page, pageSize) => {
                      handleCurrentPage(page, pageSize);
                    },

                    total: totalDataPages || 1
                  }}
                  dataSource={referralData?.getExportableReferredData?.data as Array<any>}
                  columns={[
                    {
                      title: "Name",
                      dataIndex: "referred_to",
                      key: "referred_to",
                      render: (val) => {
                        return <Link to={`/dashboard/users/${val?.id}`}> {val?.name || "-"} </Link>;
                      }
                    },
                    {
                      title: "User Code",
                      dataIndex: "referral_code",
                      key: "referral_code",
                      align: "center",
                      render: (val) => <> {val} </>
                    },
                    {
                      title: "Stage",
                      dataIndex: "onboarding_stage",
                      key: "onboarding_stage",
                      align: "center",
                      render: (_, obj) => {
                        return (
                          <>
                            {" "}
                            {obj?.referred_to?.onboarding_stage == OnboardingStage.Dashboard
                              ? "SIGN UP COMPLETED"
                              : obj?.referred_to?.onboarding_stage}{" "}
                          </>
                        );
                      }
                    },
                    {
                      title: "Designation",
                      key: "designation",
                      align: "center",
                      render: (val) => {
                        return <>{val?.referred_to?.designation?.name || "-"}</>;
                      }
                    },
                    {
                      title: "No. of referred",
                      align: "center",
                      render: (_, obj) => {
                        return (
                          <Button
                            type="link"
                            style={{ border: "none" }}
                            onClick={() =>
                              handleReferredCountClick(obj?.referred_to?.id, obj?.referred_to?.name)
                            }
                          >
                            {" "}
                            {obj.referred_count}{" "}
                          </Button>
                        );
                      }
                    },
                    {
                      title: "Available Points",
                      dataIndex: "available_points",
                      key: "available_points",
                      align: "center",
                      render: (val) => <> {val || "-"} </>
                    },
                    {
                      title: "Created On",
                      dataIndex: "created_at",
                      key: "created_at",
                      align: "center",
                      render: (_, obj) => {
                        return <> {dayjs(obj?.referred_to?.created_at).format("MMM D, YYYY")}</>;
                      }
                    },
                    {
                      title: "Updated On",
                      dataIndex: "updated_at",
                      key: "updated_at",
                      align: "center",
                      render: (_, obj) => {
                        return (
                          <>
                            {" "}
                            {obj?.referred_to?.updated_at
                              ? dayjs(obj?.referred_to?.updated_at).format("MMM D, YYYY")
                              : "-"}
                          </>
                        );
                      }
                    }
                  ]}
                />
              </Tabs.TabPane>
            </Tabs>
          </div>
          {isExportModalOpen && (
            <ExportJson
              isExportModalOpen={isExportModalOpen}
              setIsExportModalOpen={setIsExportModalOpen}
              fetchExportableReferredData={fetchExportableReferredData}
              fetchExportUnregisteredReferrals={fetchExportUnregisteredReferrals}
              meta={meta}
              activeTab={search.get("tab") || ""}
            />
          )}
        </div>
      )}
    </ProtectedRoute>
  );
};

export default ReferralHistory;
