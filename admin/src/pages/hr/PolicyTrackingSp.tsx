import { CloseOutlined, FilterOutlined, SearchOutlined } from "@ant-design/icons";
import {
  Button,
  DatePicker,
  Form,
  Input,
  Modal,
  notification,
  Pagination,
  Radio,
  Table,
  Tag
} from "antd";
import dayjs from "dayjs";
import { debounce } from "lodash";
import { CheckCircle, Users } from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";
import { BiExport } from "react-icons/bi";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import {
  useGetExportSpPolicyTrackingLazyQuery,
  useGetSpPolicyTrackingQuery
} from "../../__generated__";
import ErrorComponent from "../../components/error/Error";
import Loading from "../../components/loading/Loading";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";

interface UserPolicyData {
  id: number;
  name: string;
  user_code: string;
  onboarded_date: Date | string;
  policies_accepted: number;
  total_policies: number;
  last_accepted_date: Date | string | null;
  all_accepted: boolean;
}

// Define filter types
type PolicyAcceptedFilter = "all" | "yes" | "no";

interface FilterValues {
  startDate: Date | null;
  endDate: Date | null;
  policyAccepted: PolicyAcceptedFilter;
}
const PolicyTrackingSp: React.FC = () => {
  const _navigate = useNavigate(); // Prefix with underscore to indicate it's not used yet
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [userData, setUserData] = useState<UserPolicyData[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [totalOnboardedUsers, setTotalOnboardedUsers] = useState(0);
  const [usersWithAllPoliciesAccepted, setUsersWithAllPoliciesAccepted] = useState(0);

  // Filter modal state
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [filterValues, setFilterValues] = useState<FilterValues>({
    startDate: null,
    endDate: null,
    policyAccepted: "all"
  });
  const [form] = Form.useForm();

  // Get parameters from URL
  const searchTerm = searchParams.get("search") || "";
  const startDateParam = searchParams.get("startDate");
  const endDateParam = searchParams.get("endDate");
  const policyAcceptedParam = (searchParams.get("policyAccepted") as PolicyAcceptedFilter) || "all";

  const [getExportSpPolicyTracking, { loading: exportLoading }] =
    useGetExportSpPolicyTrackingLazyQuery({
      fetchPolicy: "no-cache"
    });

  // Fetch policy tracking data from the backend
  const {
    data: policyTrackingData,
    loading: policyTrackingLoading,
    error: policyTrackingError
  } = useGetSpPolicyTrackingQuery({
    variables: {
      userType: "SERVICE_PROVIDER",
      search: searchTerm,
      pagination: {
        skip: currentPage - 1,
        take: pageSize
      },
      ...(startDateParam && endDateParam
        ? {
            filter: {
              startDate: startDateParam,
              endDate: endDateParam,
              ...(policyAcceptedParam !== "all"
                ? {
                    allAccepted: policyAcceptedParam === "yes"
                  }
                : {})
            }
          }
        : policyAcceptedParam !== "all"
        ? {
            filter: {
              allAccepted: policyAcceptedParam === "yes"
            }
          }
        : {})
    },
    fetchPolicy: "no-cache"
  });

  // Handle search with debounce
  const debounceSearch = useCallback(
    debounce((value: string) => {
      setCurrentPage(1);

      form.setFieldsValue({
        dateRange: undefined,
        policyAccepted: "all"
      });
      setSearchParams({ search: value, startDate: "", endDate: "", policyAccepted: "all" });
      setFilterValues({
        startDate: null,
        endDate: null,
        policyAccepted: "all"
      });
    }, 500),
    [setSearchParams]
  );

  // Handle pagination change
  const handlePaginationChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  // Handle filter modal open/close
  const showFilterModal = () => {
    setIsFilterModalOpen(true);
  };

  const closeFilterModal = () => {
    setIsFilterModalOpen(false);
  };

  // Handle filter apply
  const handleApplyFilters = (values: any) => {
    const { dateRange, policyAccepted } = values;

    const newFilterValues: FilterValues = {
      startDate: dateRange?.[0] || null,
      endDate: dateRange?.[1] || null,
      policyAccepted: policyAccepted || "all"
    };

    setFilterValues(newFilterValues);

    // Update URL params with filters
    const params: Record<string, string> = {};

    if (searchTerm) {
      params.search = "";
    }

    if (newFilterValues.startDate && newFilterValues.endDate) {
      // Convert Date objects to strings for URL parameters
      params.startDate = dayjs(newFilterValues.startDate).startOf("day").toISOString();
      params.endDate = dayjs(newFilterValues.endDate).endOf("day").toISOString();
    }

    if (newFilterValues.policyAccepted !== "all") {
      params.policyAccepted = newFilterValues.policyAccepted;
    }

    setSearchParams(params);
    closeFilterModal();
  };

  // Handle filter reset
  const handleResetFilters = () => {
    // First set the form values to undefined to avoid validation errors
    form.setFieldsValue({
      dateRange: undefined,
      policyAccepted: "all"
    });

    // Then reset the filter values state
    setFilterValues({
      startDate: null,
      endDate: null,
      policyAccepted: "all"
    });

    // Remove filter params from URL, keep search if exists
    if (searchTerm) {
      setSearchParams({ search: searchTerm });
    } else {
      setSearchParams({});
    }
  };

  // Handle export functionality
  const handleExport = () => {
    getExportSpPolicyTracking({
      variables: {
        userType: "SERVICE_PROVIDER",
        search: searchTerm,
        ...(startDateParam && endDateParam
          ? {
              filter: {
                startDate: startDateParam,
                endDate: endDateParam,
                ...(policyAcceptedParam !== "all"
                  ? {
                      allAccepted: policyAcceptedParam === "yes"
                    }
                  : {})
              }
            }
          : policyAcceptedParam !== "all"
          ? {
              filter: {
                allAccepted: policyAcceptedParam === "yes"
              }
            }
          : {})
      },
      fetchPolicy: "no-cache",
      onCompleted() {
        notification.success({
          message: "Export Successful",
          description: "You will shortly receive an email with the exported data."
        });
      },
      onError(error) {
        notification.error({
          message: "Export Failed",
          description: error.message
        });
      }
    });

    // Map userData to match the expected input type
  };

  // Process the data when it's loaded
  useEffect(() => {
    if (policyTrackingData?.getSpPolicyTracking?.data) {
      const {
        users,
        totalCount,
        totalOnboardedUsers: totalOnboardedUsers, // Prefix with underscore to indicate it's not used yet
        usersWithAllPoliciesAccepted: acceptedCount
      } = policyTrackingData.getSpPolicyTracking.data;

      if (users && users.length > 0) {
        const processedUsers = users
          .filter((user): user is NonNullable<typeof user> => user !== null)
          .map((user) => {
            const meta = user?.user_onboarding_data?.[0]?.meta ?? {};

            const transferDate = meta.transfer_to_tms_date;
            return {
              id: user.id,
              name: user.name,
              user_code: user?.user_onboarding_data?.length
                ? user?.user_onboarding_data[0]?.meta?.onboarding_data?.user_code
                : "-",
              onboarded_date:
                transferDate && dayjs(transferDate).isValid()
                  ? dayjs(transferDate).format("DD MMM YYYY")
                  : "-",

              policies_accepted: user.policies_accepted,
              total_policies: user.total_policies,
              last_accepted_date: user.last_accepted_date
                ? dayjs(user.last_accepted_date).format("DD MMM YYYY")
                : null,
              all_accepted: user.all_accepted
            };
          });
        const sortedUsers = processedUsers.sort((a, b) => {
          if (a.onboarded_date === "-" && b.onboarded_date === "-") {
            return 0;
          } else if (a.onboarded_date === "-") {
            return 1;
          } else if (b.onboarded_date === "-") {
            return -1;
          } else {
            return dayjs(b.onboarded_date).diff(dayjs(a.onboarded_date));
          }
        });

        setUserData(sortedUsers);
        setTotalUsers(totalCount || 0);
      } else {
        setUserData([]);
        setTotalUsers(0);
      }
      setTotalOnboardedUsers(totalOnboardedUsers || 0);
      setUsersWithAllPoliciesAccepted(acceptedCount || 0);
    }
  }, [policyTrackingData]);

  // Initialize filter values from URL parameters
  useEffect(() => {
    // Set filter values from URL parameters
    if (startDateParam || endDateParam || policyAcceptedParam !== "all") {
      const newFilterValues: FilterValues = {
        startDate: startDateParam ? dayjs(startDateParam).toDate() : null,
        endDate: endDateParam ? dayjs(endDateParam).toDate() : null,
        policyAccepted: policyAcceptedParam
      };

      setFilterValues(newFilterValues);

      // Set form values
      form.setFieldsValue({
        dateRange:
          startDateParam && endDateParam ? [dayjs(startDateParam), dayjs(endDateParam)] : undefined,
        policyAccepted: policyAcceptedParam
      });
    }
  }, [startDateParam, endDateParam, policyAcceptedParam, form]);

  // Refetch policy tracking data when search params, filters, or pagination changes
  useEffect(() => {
    // Build filter object based on URL parameters
    const filter: Record<string, any> = {};

    if (startDateParam && endDateParam) {
      filter.startDate = startDateParam;
      filter.endDate = endDateParam;
    }

    if (policyAcceptedParam !== "all") {
      filter.allAccepted = policyAcceptedParam === "yes";
    }
  }, [searchTerm, startDateParam, endDateParam, policyAcceptedParam, currentPage, pageSize]);

  if (policyTrackingLoading || exportLoading) {
    return <Loading tip="Loading policy data..." />;
  }

  if (policyTrackingError) {
    return <ErrorComponent error={policyTrackingError} />;
  }

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (name: string, record: UserPolicyData) => (
        <Link to={`/dashboard/sp/details/${record.id}/compliance?tab=compliance`}>
          <span className="text-purple-600 ">{name}</span>
        </Link>
      )
    },
    {
      title: "Code",
      dataIndex: "user_code",
      key: "user_code",
      render: (code: string) => code || "-"
    },
    {
      title: "Onboarded Date",
      dataIndex: "onboarded_date",
      key: "onboarded_date",
      render: (date: Date) => (dayjs(date).isValid() ? dayjs(date).format("DD MMM YYYY") : "-")
    },
    {
      title: "Number of Policies Accepted",
      dataIndex: "policies_accepted",
      key: "policies_accepted",
      render: (accepted: number, record: UserPolicyData) => `${accepted}/${record.total_policies}`
    },
    {
      title: "Accepted On",
      dataIndex: "last_accepted_date",
      key: "last_accepted_date",
      render: (date: Date | null) => (date ? dayjs(date).format("DD MMM YYYY") : "-")
    },
    {
      title: "All Accepted",
      dataIndex: "all_accepted",
      key: "all_accepted",
      render: (all_accepted: boolean) =>
        all_accepted ? (
          <Tag color="success" className="text-green-600">
            Yes
          </Tag>
        ) : (
          <Tag color="error" className="text-red-600">
            No
          </Tag>
        )
    }
  ];

  return (
    <ProtectedRoute>
      {({ permissions }) => (
        <div className="p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold">Policy Tracking Overview</h1>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-2 gap-6 mb-6">
            <div className="bg-white  rounded-lg shadow-sm items-start flex flex-col justify-center p-6">
              <div className="flex   items-center">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Users size={24} className="text-purple-600" />
                </div>
                <div className="ml-4">
                  <div className="text-gray-500 text-sm">Total Users Onboarded</div>
                  <div className="text-2xl font-semibold mt-1">{totalOnboardedUsers}</div>
                </div>
              </div>
            </div>

            <div className="bg-white  rounded-lg shadow-sm items-start flex flex-col justify-center p-6">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-lg">
                  <CheckCircle size={24} className="text-green-600" />
                </div>
                <div className="ml-4">
                  <div className="text-gray-500 text-sm">Users Accepted All Policies</div>
                  <div className="text-2xl font-semibold mt-1">{usersWithAllPoliciesAccepted}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Search and Actions */}
          <div className="flex justify-between mb-6">
            <div>
              <Input
                placeholder="Search by name..."
                prefix={<SearchOutlined />}
                style={{ width: 300 }}
                defaultValue={searchTerm}
                onChange={(e) => debounceSearch(e.target.value)}
                allowClear
                autoFocus
              />
            </div>
            <div className="flex gap-2">
              <Button
                type="primary"
                icon={<FilterOutlined />}
                className="bg-purple-600"
                onClick={showFilterModal}
              >
                Filter{" "}
                {(startDateParam || endDateParam || policyAcceptedParam !== "all") && (
                  <span className="ml-1 text-xs bg-white text-purple-600 rounded-full px-1.5">
                    {(startDateParam ? 1 : 0) + (policyAcceptedParam !== "all" ? 1 : 0)}
                  </span>
                )}
              </Button>

              <Button
                className="text-colorPrimary bg-white shadow-l flex items-center mr-2"
                disabled={!permissions?.adminData?.can_download}
                type="primary"
                icon={<BiExport />}
                onClick={handleExport}
                loading={exportLoading}
              >
                Export
              </Button>
            </div>

            {/* Filter Modal */}
            <Modal
              title={<div className="text-center text-purple-500 text-xl mb-4"> Add Filter </div>}
              open={isFilterModalOpen}
              onCancel={closeFilterModal}
              footer={null}
              closeIcon={<CloseOutlined />}
              centered
            >
              <Form
                form={form}
                layout="vertical"
                onFinish={handleApplyFilters}
                initialValues={{
                  dateRange:
                    filterValues.startDate && filterValues.endDate
                      ? [filterValues.startDate, filterValues.endDate]
                      : undefined,
                  policyAccepted: filterValues.policyAccepted
                }}
              >
                <Form.Item label="Date Range" name="dateRange">
                  <DatePicker.RangePicker style={{ width: "100%" }} format="DD/MM/YYYY" />
                </Form.Item>

                <Form.Item label="All Policies Accepted" name="policyAccepted">
                  <Radio.Group>
                    <Radio value="all">All</Radio>
                    <Radio value="yes">Yes</Radio>
                    <Radio value="no">No</Radio>
                  </Radio.Group>
                </Form.Item>

                <div className="flex justify-end mt-6">
                  <Button onClick={handleResetFilters} className="mr-2">
                    Clear Filter
                  </Button>
                  <Button type="primary" htmlType="submit" className="bg-purple-600">
                    Apply Filters
                  </Button>
                </div>
              </Form>
            </Modal>
          </div>

          {/* User Policy Table */}
          <div className="bg-white rounded-lg shadow p-6">
            <Table
              dataSource={userData}
              columns={columns}
              rowKey="id"
              pagination={false}
              loading={policyTrackingLoading}
            />

            {/* Custom Pagination */}
            <div className="flex justify-end mt-4">
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={totalUsers}
                onChange={handlePaginationChange}
              />
            </div>
          </div>
        </div>
      )}
    </ProtectedRoute>
  );
};

export default PolicyTrackingSp;
