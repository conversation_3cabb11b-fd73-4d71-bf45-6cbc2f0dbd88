mutation CreateContacts($contacts: String) {
  createContacts(contacts: $contacts) {
    result
    message
  }
}
query GetContactsOfTheUserFrAdmin($filter: UserContactsFilter, $userId: Int) {
  getContactsOfTheUserFrAdmin(filter: $filter, user_id: $userId) {
    data {
      contact {
        id
        name
        phone
        meta
        timeline
        is_active
        is_deleted
        moved_to_users
        created_at
        updated_at
        phone_contact_id
      }
      contact_id
      created_at
      is_active
      updated_at
      user_id
    }
    total_count
    total_moved_contacts_count
  }
}
