import {
  <PERSON>ge,
  Box,
  Button,
  Center,
  Flex,
  Heading,
  HStack,
  IconButton,
  Spinner,
  Text,
  useColorModeValue,
  VStack,
  Wrap,
  WrapItem
} from "@chakra-ui/react";
import { motion, PanInfo, useAnimation } from "framer-motion";
import { Calendar, ChevronLeft, ChevronRight, Clock, MapPin } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useGetInterviewsFrUserQuery } from "../../../__generated__";

interface TechnicianInterviewInfoProps {
  userName?: string;
  scheduledTime?: string;
  address?: string;
  duration?: string;
}

const TechnicianInterviewInfo: React.FC<TechnicianInterviewInfoProps> = (props) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const carouselRef = useRef<HTMLDivElement>(null);

  // GraphQL Query
  const { data, loading, error } = useGetInterviewsFrUserQuery({
    fetchPolicy: "no-cache"
  });

  // Get interviews to display based on the following rules:
  // 1. Show if interview status is SCHEDULED
  // 2. Show if interview status is COMPLETED with interviewee_feedback_enabled=true and interviewee feedback is not COMPLETED
  // 3. Do not show if interview status is COMPLETED with interviewee_feedback_enabled=false
  // 4. Do not show if interview status is COMPLETED and interviewee_feedback_state is COMPLETED

  // Filter interviews according to the requirements
  const interviewsToDisplay =
    data?.getInterviewsFrUser?.filter((interview) => {
      // Always show SCHEDULED interviews
      if (interview?.status === "SCHEDULED") {
        return true;
      }

      // For COMPLETED interviews, check feedback conditions
      if (interview?.status === "COMPLETED") {
        // If interviewee_feedback_enabled is explicitly false, don't show the interview
        if (interview?.feedback?.template?.interviewee_feedback_enabled === false) {
          return false;
        }

        // If interviewee feedback is already COMPLETED, don't show the interview
        if (interview?.feedback?.interviewee_feedback_state === "COMPLETED") {
          return false;
        }

        // In all other cases (including undefined/null values), show the interview
        return true;
      }

      // For all other interview statuses (like CANCELLED, etc.), show them
      return true;
    }) || [];

  // State for current interview index
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [_, setDragDirection] = useState<"left" | "right" | null>(null);

  // Get the current interview
  const userInterview = interviewsToDisplay[currentIndex];

  // Animation controls for swipe
  const controls = useAnimation();

  // Calculate the width of the card container for proper positioning
  useEffect(() => {
    const updateCardWidth = () => {
      // Just ensure the ref is available
      if (carouselRef.current) {
        // We can access the width if needed in the future
      }
    };

    updateCardWidth();
    window.addEventListener("resize", updateCardWidth);

    return () => {
      window.removeEventListener("resize", updateCardWidth);
    };
  }, []);

  // Navigation functions with improved animations
  const goToPrevious = () => {
    if (currentIndex > 0) {
      controls
        .start({
          x: "100%",
          opacity: 0,
          transition: { duration: 0.3 }
        })
        .then(() => {
          setCurrentIndex(currentIndex - 1);
          controls.set({ x: "-100%" });
          controls.start({
            x: 0,
            opacity: 1,
            transition: { duration: 0.3 }
          });
        });
    }
  };

  const goToNext = () => {
    if (currentIndex < interviewsToDisplay.length - 1) {
      controls
        .start({
          x: "-100%",
          opacity: 0,
          transition: { duration: 0.3 }
        })
        .then(() => {
          setCurrentIndex(currentIndex + 1);
          controls.set({ x: "100%" });
          controls.start({
            x: 0,
            opacity: 1,
            transition: { duration: 0.3 }
          });
        });
    }
  };

  // Handle drag (swipe) gestures with improved detection
  const handleDragEnd = (_: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(false);
    const threshold = 50; // minimum distance required for a swipe
    const dragDistance = info.offset.x;
    const dragVelocity = info.velocity.x;

    // Use both distance and velocity for better swipe detection
    if (dragDistance > threshold || (dragDistance > 20 && dragVelocity > 0.5)) {
      // Swiped right with enough force/distance
      goToPrevious();
    } else if (dragDistance < -threshold || (dragDistance < -20 && dragVelocity < -0.5)) {
      // Swiped left with enough force/distance
      goToNext();
    } else {
      // Return to center if not swiped far enough
      controls.start({
        x: 0,
        transition: { type: "spring", stiffness: 300, damping: 30 }
      });
    }

    setDragDirection(null);
  };

  // Format date and time from the interview data
  const formatDateTime = (startTime?: string | null, endTime?: string | null) => {
    if (!startTime || !endTime) return "";

    const start = new Date(startTime);
    const end = new Date(endTime);

    const options: Intl.DateTimeFormatOptions = {
      weekday: "short",
      month: "short",
      day: "numeric"
    };
    const dateStr = start.toLocaleDateString("en-US", options);

    const startTimeStr = start.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true
    });
    const endTimeStr = end.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true
    });

    return `${dateStr}, ${startTimeStr} - ${endTimeStr}`;
  };

  // Calculate duration from start and end times
  const calculateDuration = (startTime?: string | null, endTime?: string | null) => {
    if (!startTime || !endTime) return "";

    const start = new Date(startTime);
    const end = new Date(endTime);
    const durationMs = end.getTime() - start.getTime();
    const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
    const durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    if (durationHours > 0) {
      return (
        durationHours +
        (durationHours === 1 ? " hour" : " hours") +
        (durationMinutes > 0
          ? " " + durationMinutes + (durationMinutes === 1 ? " minute" : " minutes")
          : "")
      );
    } else {
      return durationMinutes + (durationMinutes === 1 ? " minute" : " minutes");
    }
  };

  // Get address, scheduled time, and duration from the interview data
  const address =
    userInterview?.interviewer?.location?.work_address || props.address || "Address not available";
  const scheduledTime = userInterview
    ? formatDateTime(userInterview.start_time, userInterview.end_time)
    : props.scheduledTime || "Schedule not available";
  const duration = userInterview
    ? calculateDuration(userInterview.start_time, userInterview.end_time)
    : props.duration || "Duration not available";

  // Define colors using Chakra's useColorModeValue for light/dark mode support
  const cardBg = useColorModeValue("white", "gray.800");
  const headingColor = useColorModeValue("gray.900", "white");
  const badgeBg = useColorModeValue("blue.50", "blue.900");
  const badgeColor = useColorModeValue("blue.700", "blue.200");
  const calendarItemBg = useColorModeValue("blue.50", "blue.900");
  const calendarIconColor = useColorModeValue("blue.600", "blue.200");
  const itemBg = useColorModeValue("gray.50", "gray.700");
  const itemIconColor = useColorModeValue("gray.600", "gray.400");
  const titleColor = useColorModeValue("gray.900", "white");
  const subtitleColor = useColorModeValue("gray.600", "gray.400");
  const buttonBg = useColorModeValue("blue.600", "blue.500");
  const buttonHoverBg = useColorModeValue("blue.700", "blue.600");

  // Handle loading state
  if (loading) {
    return (
      <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
        <Box
          bg={cardBg}
          borderRadius="xl"
          p={4}
          mb={6}
          boxShadow="sm"
          margin={2}
          textAlign="center"
        >
          <Spinner size="md" color={buttonBg} />
          <Text mt={2} fontSize="sm" color={subtitleColor}>
            {t("loading_interview_details")}
          </Text>
        </Box>
      </motion.div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
        <Box bg={cardBg} borderRadius="xl" p={4} mb={6} boxShadow="sm" margin={2}>
          <Text color="red.500" fontSize="sm">
            {t("error_loading_interview")}
          </Text>
        </Box>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className="mt-2"
    >
      {interviewsToDisplay.length > 0 ? (
        <Box position="relative" overflow="hidden" ref={carouselRef}>
          {/* Navigation buttons */}
          {interviewsToDisplay.length > 1 && (
            <>
              <IconButton
                aria-label="Previous interview"
                icon={<ChevronLeft size={20} />}
                position="absolute"
                left={2}
                top="50%"
                transform="translateY(-50%)"
                zIndex={2}
                bg={cardBg}
                color={titleColor}
                borderRadius="full"
                size="sm"
                onClick={goToPrevious}
                isDisabled={currentIndex === 0}
                opacity={currentIndex === 0 ? 0.5 : 1}
                _hover={{ bg: itemBg }}
                boxShadow="md"
              />
              <IconButton
                aria-label="Next interview"
                icon={<ChevronRight size={20} />}
                position="absolute"
                right={2}
                top="50%"
                transform="translateY(-50%)"
                zIndex={2}
                bg={cardBg}
                color={titleColor}
                borderRadius="full"
                size="sm"
                onClick={goToNext}
                isDisabled={currentIndex === interviewsToDisplay.length - 1}
                opacity={currentIndex === interviewsToDisplay.length - 1 ? 0.5 : 1}
                _hover={{ bg: itemBg }}
                boxShadow="md"
              />
            </>
          )}

          {/* Carousel container */}
          <Box
            position="relative"
            width="100%"
            px={interviewsToDisplay.length > 1 ? 4 : 0}
            overflow="visible"
          >
            {/* Interview card with swipe functionality */}
            <motion.div
              drag="x"
              dragConstraints={{ left: 0, right: 0 }}
              onDragEnd={handleDragEnd}
              onDragStart={(_, info) => {
                setIsDragging(true);
                setDragStartX(info.point.x);
              }}
              onDrag={(_, info) => {
                if (isDragging) {
                  const currentDragX = info.point.x;
                  // We track direction but don't need to use it currently
                  setDragDirection(currentDragX < dragStartX ? "left" : "right");
                }
              }}
              animate={controls}
              initial={{ opacity: 1, x: 0 }}
              style={{
                width: "100%",
                position: "relative",
                zIndex: 1
              }}
            >
              {/* Main card */}
              <Box
                bg={cardBg}
                borderRadius="xl"
                p={4}
                mb={6}
                boxShadow="md"
                margin={2}
                position="relative"
                zIndex={1}
              >
                <Flex align="center" justify="space-between" mb={3}>
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    <Heading as="h2" size="sm" color={headingColor}>
                      {userInterview?.feedback?.template?.title || t("interview")}
                    </Heading>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                  >
                    <Flex align="center">
                      {interviewsToDisplay.length > 1 && (
                        <Text fontSize="xs" color={subtitleColor} mr={2}>
                          {currentIndex + 1}/{interviewsToDisplay.length}
                        </Text>
                      )}
                      <Badge
                        px={2}
                        py={0.5}
                        bg={badgeBg}
                        color={badgeColor}
                        borderRadius="full"
                        fontSize="xs"
                        fontWeight="medium"
                        textTransform="capitalize"
                      >
                        {userInterview?.status === "SCHEDULED" ? "Upcoming" : userInterview?.status}
                      </Badge>
                    </Flex>
                  </motion.div>
                </Flex>

                <VStack spacing={2} align="stretch">
                  <motion.div
                    initial={{ scale: 0.7, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.5, type: "spring", stiffness: 260, delay: 0.2 }}
                    key={`calendar-${currentIndex}`}
                  >
                    <Box bg={calendarItemBg} borderRadius="lg" p={2.5}>
                      <HStack spacing={3}>
                        <Box color={calendarIconColor} flexShrink={0}>
                          <Calendar size={18} />
                        </Box>
                        <Box minW={0}>
                          <Text fontWeight="medium" fontSize="sm" color={titleColor}>
                            {t("interview_scheduled")}
                          </Text>
                          <Text fontSize="xs" color={subtitleColor} isTruncated>
                            {scheduledTime}
                          </Text>
                        </Box>
                      </HStack>
                    </Box>
                  </motion.div>

                  <motion.div
                    initial={{ scale: 0.7, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.5, type: "spring", stiffness: 260, delay: 0.3 }}
                    key={`location-${currentIndex}`}
                  >
                    <Box bg={itemBg} borderRadius="lg" p={2.5}>
                      <HStack spacing={3}>
                        <Box color={itemIconColor} flexShrink={0}>
                          <MapPin size={18} />
                        </Box>
                        <Box minW={0}>
                          <Text fontWeight="medium" fontSize="sm" color={titleColor}>
                            {t("location")}
                          </Text>
                          <Text fontSize="xs" color={subtitleColor} isTruncated>
                            {address}
                          </Text>
                        </Box>
                      </HStack>
                    </Box>
                  </motion.div>

                  <motion.div
                    initial={{ scale: 0.7, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.5, type: "spring", stiffness: 260, delay: 0.4 }}
                    key={`duration-${currentIndex}`}
                  >
                    <Box bg={itemBg} borderRadius="lg" p={2.5}>
                      <HStack spacing={3}>
                        <Box color={itemIconColor} flexShrink={0}>
                          <Clock size={18} />
                        </Box>
                        <Box minW={0}>
                          <Text fontWeight="medium" fontSize="sm" color={titleColor}>
                            {t("duration")}
                          </Text>
                          <Text fontSize="xs" color={subtitleColor}>
                            {duration}
                          </Text>
                        </Box>
                      </HStack>
                    </Box>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      onClick={() => navigate("/interview")}
                      w="full"
                      bg={buttonBg}
                      color="white"
                      py={2.5}
                      borderRadius="lg"
                      _hover={{ bg: buttonHoverBg }}
                      fontSize="sm"
                      fontWeight="medium"
                      mt={2}
                    >
                      {t("view_interview_details")}
                    </Button>
                  </motion.div>
                </VStack>
              </Box>
            </motion.div>

            {/* Preview of adjacent cards */}
            {interviewsToDisplay.length > 1 && (
              <>
                {/* Previous card preview (if not at the beginning) */}
                {currentIndex > 0 && (
                  <Box
                    position="absolute"
                    left="-15%"
                    top="50%"
                    transform="translateY(-50%)"
                    width="30%"
                    height="80%"
                    bg={cardBg}
                    borderRadius="xl"
                    opacity={0.6}
                    boxShadow="sm"
                    zIndex={0}
                    display={["none", "block"]}
                  />
                )}

                {/* Next card preview (if not at the end) */}
                {currentIndex < interviewsToDisplay.length - 1 && (
                  <Box
                    position="absolute"
                    right="-15%"
                    top="50%"
                    transform="translateY(-50%)"
                    width="30%"
                    height="80%"
                    bg={cardBg}
                    borderRadius="xl"
                    opacity={0.6}
                    boxShadow="sm"
                    zIndex={0}
                    display={["none", "block"]}
                  />
                )}
              </>
            )}
          </Box>

          {/* Pagination indicators */}
          {interviewsToDisplay.length > 1 && (
            <Center mt={2} mb={2}>
              <Wrap spacing={2} justify="center">
                {interviewsToDisplay.map((_, index) => (
                  <WrapItem key={index}>
                    <Box
                      w="8px"
                      h="8px"
                      borderRadius="full"
                      bg={index === currentIndex ? buttonBg : "gray.300"}
                      cursor="pointer"
                      onClick={() => {
                        if (index > currentIndex) {
                          controls
                            .start({
                              x: "-100%",
                              opacity: 0,
                              transition: { duration: 0.3 }
                            })
                            .then(() => {
                              setCurrentIndex(index);
                              controls.set({ x: "100%" });
                              controls.start({
                                x: 0,
                                opacity: 1,
                                transition: { duration: 0.3 }
                              });
                            });
                        } else if (index < currentIndex) {
                          controls
                            .start({
                              x: "100%",
                              opacity: 0,
                              transition: { duration: 0.3 }
                            })
                            .then(() => {
                              setCurrentIndex(index);
                              controls.set({ x: "-100%" });
                              controls.start({
                                x: 0,
                                opacity: 1,
                                transition: { duration: 0.3 }
                              });
                            });
                        }
                      }}
                    />
                  </WrapItem>
                ))}
              </Wrap>
            </Center>
          )}
        </Box>
      ) : null}
    </motion.div>
  );
};

export default TechnicianInterviewInfo;
