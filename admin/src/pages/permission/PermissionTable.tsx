import { Checkbox, Segmented, Table } from "antd";
import { useEffect, useState } from "react";
import { CgChevronDown, CgChevronUp } from "react-icons/cg";
import { RolePermissionData } from "./PublishModules";
interface Submodule {
  module_id?: number | null | undefined;
  sub_module_name?: string | null | undefined;
  [key: string]: any;
}

interface Module {
  module_id?: number | null | undefined;
  module_name?: string | null | undefined;
  submodules?: (Submodule | null)[] | null | undefined;
  [key: string]: any;
}

interface PermissionTableProps {
  modulesData: Module;
  modulePermissionInfo: RolePermissionData;
  setModulePermissionInfo: React.Dispatch<React.SetStateAction<RolePermissionData>>;
}

const PermissionTable = ({
  modulesData,
  modulePermissionInfo,
  setModulePermissionInfo
}: PermissionTableProps) => {
  const [closeRows, setCloseRows] = useState<Record<string, boolean>>({});
  const [segmentedValue, setSegmentedValue] = useState("Individual");
  const [selectAll, setSelectAll] = useState(true);

  const toggleDropdown = (element: string) => {
    setCloseRows((prev) => ({ ...prev, [element]: !prev[element] }));
  };
  const dataSource = [
    { key: modulesData.module_id || 0, module_name: modulesData.module_name, isModule: true },
    ...(closeRows[modulesData.module_id || 0]
      ? []
      : (modulesData.submodules?.filter((sub) => sub !== null) || []).map((sub) => ({
          key: sub?.feature_id ?? 0,
          sub_module_name: sub?.sub_module_name ?? "Unnamed Submodule",
          isModule: false
        })))
  ];
  useEffect(() => {
    // Check if all permissions for Individual are selected
    const allIndividualPermissionsChecked = [
      ...Object.values(modulePermissionInfo.permission_for_user || {}),
      ...(modulePermissionInfo.submodules?.flatMap((submodule) =>
        Object.values(submodule.permission_for_user || {})
      ) || [])
    ].every(Boolean);

    // Check if all permissions for SP are selected
    const allSpPermissionsChecked = [
      ...Object.values(modulePermissionInfo.permission_for_sp || {}),
      ...(modulePermissionInfo.submodules?.flatMap((submodule) =>
        Object.values(submodule.permission_for_sp || {})
      ) || [])
    ].every(Boolean);

    segmentedValue === "Individual"
      ? setSelectAll(allIndividualPermissionsChecked)
      : setSelectAll(allSpPermissionsChecked);
  }, [modulePermissionInfo, segmentedValue]);

  const columns = [
    {
      title: "",
      dataIndex: "chevron",
      key: "chevron",
      render: (_: any, record: any) =>
        record.isModule ? (
          !modulePermissionInfo.submodules?.length ? (
            <CgChevronDown className="mr-2 text-gray-300" size="20px" />
          ) : (
            <div
              className="flex items-center cursor-pointer"
              onClick={() => toggleDropdown(record.key)}
            >
              {closeRows[record.key] ? (
                <CgChevronDown className="mr-2 text-violet-400" size="20px" />
              ) : (
                <CgChevronUp className="mr-2 text-violet-400" size="20px" />
              )}
            </div>
          )
        ) : (
          {}
        )
    },
    {
      title: "",
      dataIndex: "module_name",
      key: "name",

      render: (text: string, record: any) => (
        <div className="min-w-[150px] capitalize ">
          {record.isModule ? text : record.sub_module_name}
        </div>
      )
    },
    ...(["create", "read", "update", "delete"] as const).map((action) => ({
      title: action.charAt(0).toUpperCase() + action.slice(1), // Capitalize for display
      dataIndex: action,
      key: action,
      className: "",
      render: (_: any, record: { key: number; isModule: boolean }) => {
        const isSubmodule = !record.isModule;

        return (
          <Checkbox
            checked={
              !isSubmodule
                ? segmentedValue === "Individual"
                  ? modulePermissionInfo.permission_for_user[action]
                  : modulePermissionInfo.permission_for_sp[action]
                : modulePermissionInfo.submodules?.find(
                    (submodule) => submodule.feature_id === record.key
                  )?.[
                    segmentedValue === "Individual" ? "permission_for_user" : "permission_for_sp"
                  ][action]
            }
            onChange={(e) => {
              const checked = e.target.checked;
              const isSpecialAction = ["create", "update", "delete"].includes(action);
              if (!isSubmodule) {
                setModulePermissionInfo((prev) => {
                  return {
                    ...prev,
                    ...(segmentedValue === "Individual"
                      ? {
                          permission_for_user: {
                            ...prev.permission_for_user,
                            [action]: e.target.checked,
                            ...(isSpecialAction && checked ? { read: true } : {})
                          }
                        }
                      : {
                          permission_for_sp: {
                            ...prev.permission_for_sp,
                            [action]: e.target.checked,
                            ...(isSpecialAction && checked ? { read: true } : {})
                          }
                        })
                  };
                });
              }
              setModulePermissionInfo((prev) => {
                return {
                  ...prev,
                  submodules: prev.submodules
                    ? prev.submodules.map((submodule) => {
                        if (submodule.feature_id === record.key) {
                          return {
                            ...submodule,
                            ...(segmentedValue === "Individual"
                              ? {
                                  permission_for_user: {
                                    ...submodule.permission_for_user,
                                    [action]: e.target.checked,
                                    ...(isSpecialAction && checked ? { read: true } : {})
                                  }
                                }
                              : {
                                  permission_for_sp: {
                                    ...submodule.permission_for_sp,
                                    [action]: e.target.checked,
                                    ...(isSpecialAction && checked ? { read: true } : {})
                                  }
                                })
                          };
                        }
                        return submodule;
                      })
                    : []
                };
              });
            }}
          />
        );
      }
    }))
  ];

  return (
    <div className="flex flex-col  h-[380px] w-full ">
      <div className="flex justify-between  items-center">
        <Segmented<string>
          options={[
            {
              label: (
                <div style={{ padding: 1, minWidth: "80px", alignItems: "center" }}>Individual</div>
              ),
              value: "Individual"
            },
            {
              label: <div style={{ padding: 1, minWidth: "80px" }}>Service Provider</div>,
              value: "SP"
            }
          ]}
          size="large"
          value={segmentedValue}
          className="w-fit mt-2  "
          onChange={(value) => {
            setSegmentedValue(value);
          }}
        />
        <div className="flex">
          <Checkbox
            checked={selectAll}
            onChange={(e) => {
              setSelectAll(e.target.checked);
              setModulePermissionInfo((prev) => ({
                ...prev,
                ...(segmentedValue === "Individual"
                  ? {
                      permission_for_user: {
                        create: e.target.checked,
                        read: e.target.checked,
                        update: e.target.checked,
                        delete: e.target.checked
                      }
                    }
                  : {
                      permission_for_sp: {
                        create: e.target.checked,
                        read: e.target.checked,
                        update: e.target.checked,
                        delete: e.target.checked
                      }
                    }),
                submodules: prev.submodules
                  ? prev.submodules?.map((submodule) => ({
                      ...submodule,
                      ...(segmentedValue === "Individual"
                        ? {
                            permission_for_user: {
                              create: e.target.checked,
                              read: e.target.checked,
                              update: e.target.checked,
                              delete: e.target.checked
                            }
                          }
                        : {
                            permission_for_sp: {
                              create: e.target.checked,
                              read: e.target.checked,
                              update: e.target.checked,
                              delete: e.target.checked
                            }
                          })
                    }))
                  : []
              }));
            }}
          >
            Select All
          </Checkbox>
        </div>
      </div>
      <Table
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        className="mb-4 w-full  overflow-y-scroll custom-permission-table"
        scroll={{ y: 250 }}
        rowClassName={(record) =>
          record.isModule ? "bg-violet-100 font-[400] w-full font-bold  " : "pl-4 font-[200] w-full"
        }
      />
    </div>
  );
};
export default PermissionTable;
