import { Form, Table, Tabs, Tag, notification } from "antd";
import TabPane from "antd/es/tabs/TabPane";
import isEmpty from "is-empty";
import { useContext } from "react";
import { useNavigate } from "react-router-dom";
import {
  GetAllQuestionsDocument,
  useCreateQuestionBulkMutation,
  useGetAllBucketsQuery
} from "../../../__generated__";
import { validateBulkFilledQuestions } from "../../../utils/excelHelper";
import { AddQuestionFormContext } from "./AddQuestionModal";
import { BulkQuestionAdditionProps } from "./BulkQuestionAddition";

interface BulkQuestionProps {
  filledQuestions: Array<BulkQuestionAdditionProps>;
}

export const bulkAdditionTableColumn = [
  {
    title: "Question",
    key: "question",
    dataIndex: "question"
  },
  {
    title: "Option 1",
    key: "option_1",
    dataIndex: "option_1"
  },
  {
    title: "Option 2",
    key: "option_2",
    dataIndex: "option_2"
  },
  {
    title: "Option 3",
    key: "option_3",
    dataIndex: "option_3"
  },
  {
    title: "Option 4",
    key: "option_4",
    dataIndex: "option_4"
  },
  {
    title: "Correct Answer",
    key: "correct_option",
    dataIndex: "correct_option",
    render: (data: string) => {
      return (
        <>
          {" "}
          <b>{data} </b>
        </>
      );
    }
  },
  {
    title: "Difficulty",
    key: "difficulty",
    dataIndex: "difficulty",
    render: (data: string) => {
      return (
        <>
          {" "}
          <Tag color={data == "EASY" ? "green" : data == "MEDIUM" ? "yellow" : "red"}>{data}</Tag>
        </>
      );
    }
  },
  {
    title: "Bucket",
    key: "bucket",
    dataIndex: "bucket",
    render: (data: string) => {
      return (
        <>
          {" "}
          <b>{data} </b>
        </>
      );
    }
  }
];

const ShowFilledQuestionsFromTemplate: React.FC<BulkQuestionProps> = ({ filledQuestions }) => {
  //navigation
  const nav = useNavigate();
  //form
  const form = useContext(AddQuestionFormContext);

  //Query
  const { data: buckets } = useGetAllBucketsQuery();

  //Mutation
  const [createQuestionBulk] = useCreateQuestionBulkMutation();

  function getBucketId(bucketNameArray: Array<BulkQuestionAdditionProps>) {
    for (let i = 0; i < bucketNameArray.length; i++) {
      const targetBucketName = bucketNameArray[i]?.bucket;

      const matchingBucket = buckets?.getAllBuckets?.data?.find(
        (bucket) => bucket?.name === targetBucketName
      );

      if (matchingBucket) {
        // Replace the bucket property with the id from the matchingBucket
        bucketNameArray[i].bucket = matchingBucket.id;
        bucketNameArray[i].bucket_name = matchingBucket.name || matchingBucket.id;
      }
    }
    return bucketNameArray;
  }

  const handleFormSubmit = async () => {
    try {
      if (isEmpty(filledQuestions)) {
        throw new Error("No Questions Found, Please Check the File Again");
      }
      await validateBulkFilledQuestions(filledQuestions); //add bucket id in validation [{}]

      const validatedBulkFilledQuestion = getBucketId(filledQuestions);

      const updatedQuestion = validatedBulkFilledQuestion?.map((question) => {
        return {
          bucket_name: question?.bucket,
          question_text: question?.question, //Question Name
          bucket_id: question?.bucket,
          options: [
            {
              option_text: question.option_1,
              is_right_option: question.option_1 == question?.correct_option ? true : false
            },
            {
              option_text: question?.option_2,
              is_right_option: question.option_2 == question?.correct_option ? true : false
            },
            {
              option_text: question?.option_3,
              is_right_option: question?.option_3 == question?.correct_option ? true : false
            },
            {
              option_text: question.option_4,
              is_right_option: question.option_4 == question.correct_option ? true : false
            }
          ],
          difficulty: question.difficulty
        };
      });
      await createQuestionBulk({
        variables: {
          data: updatedQuestion as any
        },
        onCompleted() {
          notification.success({ message: "All Questions Uploaded Successfully " });
          nav(-1);
        },
        onError: (error) => {
          notification.error({ message: error.message });
        },
        refetchQueries: [GetAllQuestionsDocument]
      });
    } catch (error: any) {
      notification.error({
        message: error.message
      });
    }
  };
  return (
    <>
      <Tabs defaultActiveKey="tab1">
        <TabPane key="tab1">
          <Form
            form={form}
            component={false}
            onFinishFailed={() => {
              !isEmpty(filledQuestions) && handleFormSubmit();
            }}
          >
            {/* <FormItem
              name={"bucket"}
              label={"Bucket"}
              rules={[{ required: true, message: "Please select a bucket" }]}
            >
              <Select
                options={buckets?.getAllBuckets?.map((b) => ({
                  value: b?.id,
                  label: b?.name
                }))}
              />
            </FormItem> */}
            <div style={{ overflowX: "auto", maxWidth: "100%" }}>
              <Table
                bordered
                scroll={{ x: "max-content" }}
                dataSource={filledQuestions.map((data, dataIndex) => {
                  return {
                    id: dataIndex,
                    key: dataIndex,
                    ...data
                  };
                })}
                columns={[
                  {
                    title: "Id",
                    key: "id",
                    dataIndex: "id",
                    align: "center",
                    render: (data: string) => {
                      return (
                        <>
                          {" "}
                          <b>{data} </b>
                        </>
                      );
                    }
                  },
                  ...bulkAdditionTableColumn
                ]}
              />
            </div>
          </Form>
        </TabPane>
      </Tabs>
    </>
  );
};
export default ShowFilledQuestionsFromTemplate;
