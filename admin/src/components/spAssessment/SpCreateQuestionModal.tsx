import { Card, FormInstance, Modal, Tabs } from "antd";
import { useForm } from "antd/es/form/Form";
import React, { createContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import SpCreateAIQuestions from "./SpCreateAIQuestions";
import SpCreateBulkQuestion from "./SpCreateBulkQuestion";
import SpCreateSingleQuestion from "./SpCreateSingleQuestion";

export const AddQuestionFormContext = createContext<FormInstance<any> | undefined>(undefined);

const SpCreateQuestionModal: React.FC = () => {
  const nav = useNavigate();
  const [form] = useForm();
  const [okButtonLoading, setOkButtonLoading] = useState(false);

  return (
    <Modal
      closeIcon={false}
      onOk={() => form.submit()}
      onCancel={() => nav(-1)}
      width={"90%"}
      open
      okText={"Create"}
      centered
      okButtonProps={{ loading: okButtonLoading }}
    >
      <AddQuestionFormContext.Provider value={form}>
        <Card title={"Add question"} bodyStyle={{ padding: "5px 10px" }}>
          <Tabs
            items={[
              {
                label: "Single question",
                key: "single_question",
                children: <SpCreateSingleQuestion okConfirmLoading={setOkButtonLoading} />
              },
              {
                label: "Bulk addition",
                key: "bulk",
                children: <SpCreateBulkQuestion okConfirmLoading={setOkButtonLoading} />
              },
              {
                label: "AI Generated questions",
                key: "ai_gen_questions",
                children: <SpCreateAIQuestions okConfirmLoading={setOkButtonLoading} />
              }
            ]}
          />
        </Card>
      </AddQuestionFormContext.Provider>
    </Modal>
  );
};

export default SpCreateQuestionModal;
