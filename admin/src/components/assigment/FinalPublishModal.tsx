import { Card, Collapse, Descriptions, Modal, Table, notification } from "antd";
import dayjs from "dayjs";
import React from "react";
import { BiNotepad } from "react-icons/bi";
import { useNavigate, useParams } from "react-router-dom";
import {
  GetAllAssignmentConfigDocument,
  useFinalPublishAssignmentConfigMutation,
  useGetSingleConfigDetailsQuery
} from "../../__generated__";
import ErrorComponent from "../error/Error";
import Loading from "../loading/Loading";

const FinalPublishModal: React.FC = () => {
  //router stuff
  const nav = useNavigate();
  const { assignment_config_id } = useParams();

  //network stuff
  const {
    data: configRes,
    error: configError,
    loading: configLoading
  } = useGetSingleConfigDetailsQuery({
    variables: {
      id: assignment_config_id || "invalid_id"
    }
  });
  const [finalSaveAndPublish, { loading }] = useFinalPublishAssignmentConfigMutation();

  const configData = configRes?.getSingleAssignmentConfig;

  const handlePublish = () => {
    if (
      configData?.questions_assignment_type === "MANUAL" &&
      configData?.no_of_questions !== configData?.questions?.length
    ) {
      notification.warning({
        message: "Please add all questions before publishing"
      });
    } else {
      finalSaveAndPublish({
        variables: {
          id: assignment_config_id || "invalid_id"
        },
        onCompleted: (data) => {
          if (data.finalSaveAndPublishAssignmentConfig.result) {
            notification.success({
              message: "Assignment config published successfully"
            });
            nav(-1);
          } else {
            notification.error({
              message: `Error while publishing assignment config \n ${data.finalSaveAndPublishAssignmentConfig.message}`
            });
          }
        },
        refetchQueries: [GetAllAssignmentConfigDocument],
        onError: (err) => {
          console.error(err);
          notification.error({
            message: `Error while publishing assignment config \n ${err.message}`
          });
        }
      });
    }
  };

  if (configError) {
    return <ErrorComponent error={configError} />;
  }

  if (configLoading) {
    return <Loading tip="Loading config data" />;
  }

  return (
    <Modal
      closeIcon={false}
      onCancel={() => {
        nav(-1);
      }}
      okText={"Publish"}
      onOk={handlePublish}
      open
      width={"80%"}
      okButtonProps={{ loading }}
    >
      <div className="text-2xl my-5 text-colorPrimary flex space-x-2 items-center">
        <BiNotepad size={32} />
        Please verify all information before publishing
      </div>
      <Descriptions
        labelStyle={{
          backgroundColor: "rgb(250,245,255)"
        }}
        bordered
        column={2}
      >
        <Descriptions.Item label={<div className="font-bold">Name</div>}>
          <div className="uppercase">
            <b>{configData?.name}</b>
          </div>
        </Descriptions.Item>
        <Descriptions.Item label={<div className="font-bold">Total questions</div>}>
          <div className="uppercase">
            <b>{configData?.no_of_questions}</b>
          </div>
        </Descriptions.Item>
        <Descriptions.Item label={<div className="font-bold">General instructions</div>}>
          <b>
            <div
              className="uppercase"
              dangerouslySetInnerHTML={{
                __html: configData?.general_instructions_md || "Not given"
              }}
            ></div>
          </b>
        </Descriptions.Item>
        <Descriptions.Item
          label={<div className="font-bold">Assignment config assignment info</div>}
        >
          <div>
            Type - <b className="uppercase">{configData?.config_assignment_type}</b>
          </div>{" "}
          <br />
          {configData?.config_assignment_type === "MANUAL" && (
            <>
              Rule name:{" "}
              <div className="uppercase">
                <b>{configData.config_assignment_rules?.name}</b>
              </div>
            </>
          )}
        </Descriptions.Item>
        <Descriptions.Item label={<div className="font-bold">Question assignment info</div>}>
          <div>
            Type - <b className="uppercase">{configData?.questions_assignment_type}</b>
          </div>{" "}
          <br />
          {configData?.questions_assignment_type === "AUTOMATIC" && (
            <>
              {" "}
              On the basis of - <b className="uppercase">
                {" "}
                {configData?.on_basis_of.join(", ")}
              </b>{" "}
              <br />
              {configData?.questions_assignment_type === "AUTOMATIC" && (
                <div>
                  Rule name: <b className="uppercase">{configData.config_assignment_rules?.name}</b>
                </div>
              )}
            </>
          )}
        </Descriptions.Item>
        <Descriptions.Item label={<div className="font-bold">Time bounded</div>}>
          <div className="uppercase">
            <b>{configData?.time_bounded ? "Yes" : "No"}</b>
          </div>
          <div className="uppercase">
            <b>
              {configData?.time_bounded && (
                <div>
                  Hard question time -{configData?.hard_time} seconds <br />
                  Medium question time -{configData?.medium_time} seconds <br />
                  Easy question time -{configData?.easy_time} seconds <br />
                </div>
              )}
            </b>
          </div>
        </Descriptions.Item>
        <Descriptions.Item label={<div className="font-bold">Last updated at</div>}>
          <div className="uppercase">
            <b>{dayjs(configData?.updated_at).format("DD MMM YYYY hh:mm A")}</b>
          </div>
        </Descriptions.Item>
        {configData?.questions_assignment_type === "MANUAL" && (
          <Descriptions.Item label={<div className="font-bold">Questions assigned so far</div>}>
            <div>
              <b>{configData?.questions?.length}</b>
            </div>
          </Descriptions.Item>
        )}
      </Descriptions>
      <div className="text-xl my-3">Questions</div>
      {configData?.questions_assignment_type === "MANUAL" ? (
        <Table
          bordered
          pagination={{
            pageSize: 3
          }}
          columns={[
            {
              title: "Text",
              dataIndex: "text",
              key: "q_text",
              align: "center"
            },
            {
              title: "Options",
              key: "q_options",
              dataIndex: "options",
              align: "center",
              render: (ele) => (
                <Collapse>
                  <Collapse.Panel key={"options"} header={"Options"}>
                    <ol>
                      {ele.map((op: any) => (
                        <li className={`${op.is_right_option && "text-green-500"}`}>
                          {op.option_text}
                        </li>
                      ))}
                    </ol>
                  </Collapse.Panel>
                </Collapse>
              )
            },
            {
              title: "Difficulty",
              key: "difficulty",
              dataIndex: "difficulty",
              align: "center",
              render: (ele) => (
                <span
                  className={`text-${ele === "EASY" ? "green" : ele === "HARD" ? "red" : "blue"}`}
                >
                  {ele}
                </span>
              )
            },
            {
              title: "Bucket",
              key: "bucket",
              dataIndex: "bucket",
              align: "center"
            }
          ]}
          dataSource={configData?.questions?.map((q) => ({
            key: q?.id,
            text: q?.question_text,
            options: q?.option,
            difficulty: q?.difficulty,
            bucket: q?.bucket?.name
          }))}
        />
      ) : (
        <Card>Questions will be assigned on the basis of rules</Card>
      )}
    </Modal>
  );
};

export default FinalPublishModal;
