import { GoogleM<PERSON>, Marker } from "@react-google-maps/api";
import { notification } from "antd";
import React, { useEffect, useState } from "react";

interface LatLng {
  lat: number;
  lng: number;
}

const defaultLocation = {
  lat: 19.13743297604252, //WIFY Mumbai Andheri
  lng: 72.83226800917944
};

interface SelectLocationFromGoogleMapProps {
  setLocation: (address: google.maps.GeocoderResult) => void;
  lat?: number;
  lng?: number;
}

export const SelectLocationFromGoogleMap: React.FC<SelectLocationFromGoogleMapProps> = ({
  setLocation,
  lat,
  lng
}) => {
  //Hooks
  const [markerPosition, setMarkerPosition] = useState<LatLng>(defaultLocation);

  const updateMarkerPosition = (newPosition: google.maps.LatLng) => {
    setMarkerPosition({
      lat: newPosition.lat(),
      lng: newPosition.lng()
    });

    getAddressFrmLatLng(newPosition);
  };

  const getAddressFrmLatLng = (latLng: google.maps.LatLng) => {
    const geocoder = new google.maps.Geocoder();
    geocoder.geocode({ location: latLng }, (results, status) => {
      if (status === "OK") {
        if (results && results[0]) {
          setLocation(results[0]);
          notification.info({
            message: "Location Selected",
            description: results[0].formatted_address
          });
        } else {
          console.log("No results found");
        }
      } else {
        console.log("Geocoder failed due to: " + status);
      }
    });
  };

  useEffect(() => {
    //Set the Default Marker Position
    if (lat && lng) {
      setMarkerPosition({
        lat: lat,
        lng: lng
      });
    }
  }, []);

  return (
    <>
      <GoogleMap
        mapContainerStyle={{
          width: "100%",
          height: "100%"
        }}
        onClick={(e) => {
          if (e.latLng) {
            updateMarkerPosition(e.latLng);
          }
        }}
        options={{
          zoomControl: true,
          streetViewControl: false,
          mapTypeControl: false,
          draggableCursor: "pointer"
        }}
        center={defaultLocation}
        zoom={5}
      >
        <Marker
          cursor="pointer"
          draggable={true}
          position={markerPosition}
          icon={{
            url: "https://maps.google.com/mapfiles/ms/icons/red-dot.png", // Custom icon
            scaledSize: new window.google.maps.Size(40, 40),
            anchor: new window.google.maps.Point(20, 40)
          }}
        />
      </GoogleMap>
    </>
  );
};

export default React.memo(SelectLocationFromGoogleMap);
