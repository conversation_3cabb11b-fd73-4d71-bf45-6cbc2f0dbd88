import { Card, Form } from "antd";
import FormBuilder, { FieldType } from "antd-form-builder";
import { useForm } from "antd/es/form/Form";
import React, { useEffect, useState } from "react";
import { useGetInterviewerFeedbackResultsQuery } from "../../__generated__";
import { createAntdFormMeta } from "../../utils/formbuilder.helper";
const InterviewFeedbackOfAUser: React.FC<{ feedback_id: number }> = ({ feedback_id }) => {
  //Graphql Query
  console.log(feedback_id, "feedback_id");
  const { data } = useGetInterviewerFeedbackResultsQuery({
    variables: {
      feedbackId: Number(feedback_id)
    }
  });
  const [meta, setMeta] = useState<FieldType[]>([]);
  const [form] = useForm();
  const feedback = data?.getInterviewerFeedbackResults;
  const formMeta = feedback?.template?.interviewer_feedback_meta;
  const formData = feedback?.interviewer_feedback_result;

  useEffect(() => {
    if (formData) {
      const meta = createAntdFormMeta(formMeta) as FieldType[];
      setMeta(meta);

      form.setFieldsValue(formData);
    }
  }, [form, formData]);

  return (
    // <Drawer size="large">
    <Card>
      <div className="max-h-[600px] overflow-y-auto p-6">
        <Form form={form} layout="vertical" disabled>
          <FormBuilder meta={meta} form={form} disabled />
        </Form>
      </div>
    </Card>
    // </Drawer>
  );
};

export default InterviewFeedbackOfAUser;
