import { Check, FileText, X } from "lucide-react";
import { useUpdatePolicyAcceptanceMutation } from "../../__generated__";

const PolicyAcknowledge = ({ policyId, onClose }: { policyId: string; onClose: () => void }) => {
  const [updatePolicyAcceptance] = useUpdatePolicyAcceptanceMutation();
  const handleAcknowledge = () => {
    updatePolicyAcceptance({
      variables: {
        data: {
          policy_id: policyId
        }
      },
      onCompleted: () => {
        // Clear loading state
        onClose();
      },
      onError: () => {
        // Clear loading state on error too

        console.log("");
      },
      refetchQueries: ["GetPoliciesWithUserStatus"]
    });
  };
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-xl max-w-sm w-full py-6 px-4  relative">
        <button
          onClick={onClose}
          className="absolute right-2 top-2 p-1.5 hover:bg-gray-100 rounded-full transition-colors"
        >
          <X className="h-4 w-4 text-gray-400" />
        </button>

        <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
          <FileText className="h-6 w-6 text-blue-600" />
        </div>

        <p className="text-gray-600 text-center text-sm mb-6">
          Please confirm that you have read and understood this policy. This acknowledgment will be
          recorded.
        </p>

        <button
          onClick={handleAcknowledge}
          className="w-full flex items-center justify-center gap-2 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
        >
          <Check className="h-4 w-4" />I Acknowledge
        </button>
      </div>
    </div>
  );
};

export default PolicyAcknowledge;
