type Admin {
    id: Int
    name: String
}

type UserAdminChecklistData {
    id: String
    hiring_completed: Boolean
    hiring_completed_at: Date
    technician_completed: Boolean
    technician_completed_at: Date
    created_at: Date
    updated_at: Date
    created_by: Int
    edited_by: Int
    creator: Admin
    editor: Admin
    user: Admin
    meta: JSON
}

type Checklist {
    id: String
    name: String
    is_active: Boolean
    created_at: Date
    updated_at: Date
}

type UserChecklistData {
    id: String!
    name: String
    user_checklist_data: [UserAdminChecklistData]
}

type ChecklistResponse {
    result: Boolean!
    message: String!
    data: [Checklist]
}

type UserChecklistDataResponse {
    result: Boolean!
    message: String!
    data: [UserChecklistData]
}

type SingleUserChecklistDataResponse {
    result: Boolean!
    message: String!
}

type ChecklistHistoryPerson {
    type: String
    name: String
    id: Int
}

type ChecklistHistoryData {
    created_at: Date
    updated_at: Date
    creator: ChecklistHistory<PERSON>erson
    last_editor: Checklist<PERSON><PERSON><PERSON><PERSON><PERSON>
}

type UserChecklistHistoryResponse {
    result: Boolean!
    message: String!
    data: ChecklistHistoryData
}

type FeedbackFormStatusResponse {
    result: Boolean!
    message: String!
    submitted: Boolean!
    onboarded: Boolean!
    user_id: Int
    language: JSON
}

input CreateChecklistInput {
    name: String!
    is_active: Boolean
}

input UpdateChecklistInput {
    id: String!
    name: String
    is_active: Boolean
}

input UpdateUserChecklistDataInput {
    user_id: Int!
    checklist_id: String
    hiring_completed: Boolean
    technician_completed: Boolean
    language: String
    meta: JSON
}

type Query {
    getAllChecklists(user_type: String): ChecklistResponse!
    getUserChecklistData(
        user_id: Int!
        user_type: String
    ): UserChecklistDataResponse!
    getSingleUserChecklistData(
        user_id: Int!
        checklist_id: String!
    ): SingleUserChecklistDataResponse!
    getUserChecklistHistory(user_id: Int!): UserChecklistHistoryResponse!
    getFeedbackFormStatus(user_id: Int!): FeedbackFormStatusResponse!
}

type Mutation {
    createChecklist(data: CreateChecklistInput!): ChecklistResponse!
    updateChecklist(data: UpdateChecklistInput!): ChecklistResponse!
    updateUserChecklistData(
        data: [UpdateUserChecklistDataInput]!
    ): SingleUserChecklistDataResponse!
    autoFillHiring(user_id: Int!): UserChecklistDataResponse!
    autoFillTechnician(user_id: Int!): UserChecklistDataResponse!
    updateTechnicianFeedbackData(
        data: UpdateUserChecklistDataInput!
    ): SingleUserChecklistDataResponse!
}
