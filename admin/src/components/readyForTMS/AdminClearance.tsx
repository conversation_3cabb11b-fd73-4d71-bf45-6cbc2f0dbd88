import { Button, Form, Input, Result, Select } from "antd";
import { Link, useSearchParams } from "react-router-dom";
import { HiringCriteria, useGetUserForTttQuery } from "../../__generated__";
import ErrorComponent from "../error/Error";
import Loading from "../loading/Loading";
import isEmpty from "is-empty";
import { toTitleCase } from "../../utils/utils";
import { FormInstance } from "antd/lib/form/Form";
import TextArea from "antd/es/input/TextArea";

const AdminClearance: React.FC<{ form: FormInstance<any>; handleSubmit: any }> = ({
  form,
  handleSubmit
}) => {
  //Hooks
  const [searchParams] = useSearchParams();

  //Admin hiring Criteria Options
  const hiringCriteriaOptions = [
    {
      label: toTitleCase(
        HiringCriteria.OnsitePracticalTestAndTheoreticalTest.toLowerCase().replace(/_/g, " ")
      ),
      value: HiringCriteria.OnsitePracticalTestAndTheoreticalTest
    },
    {
      label: toTitleCase(
        HiringCriteria.TechnicalRoundAndPastExperienceOfModular.toLowerCase().replace(/_/g, " ")
      ),
      value: HiringCriteria.TechnicalRoundAndPastExperienceOfModular
    },
    {
      label: toTitleCase(HiringCriteria.TrainingAssessmentTest.toLowerCase().replace(/_/g, " ")),
      value: HiringCriteria.TrainingAssessmentTest
    }
  ];
  //GraphQL Query
  const { data, loading, error } = useGetUserForTttQuery({
    variables: {
      userId: Number(searchParams.get("user_id")) || -1
    },
    //Setting Form on Getting the data
    onCompleted(data) {
      data.getUserForTTT?.data?.[0]?.user_onboarding_data?.map((data) => {
        //Setting General Details
        form.setFieldsValue({
          hiring_criteria: data?.hiring_criteria,
          assessed_by: data?.assessed_by,
          user_created_in_tms: data?.user_created_in_tms,
          mothers_name: data?.mothers_name,
          remark: data?.remark
        });
      });
      //Setting Bank Account Details
      form.setFieldsValue({
        bank_name: data.getUserForTTT?.data?.[0]?.bank_details?.bank_name,
        account_number: data.getUserForTTT?.data?.[0]?.bank_details?.account_number,
        ifsc: data.getUserForTTT?.data?.[0]?.bank_details?.ifsc
      });
    }
  });

  if (loading) {
    return <Loading tip="Loading..." />;
  }

  //Error Component
  if (error) {
    return <ErrorComponent error={error} />;
  }
  if (isEmpty(data?.getUserForTTT)) {
    return (
      <Result
        status="404"
        title="404"
        subTitle="User Not Found "
        extra={
          <Link to="/dashboard/transfer_to_tms">
            <Button type="primary">Back Home</Button>
          </Link>
        }
      />
    );
  }
  return (
    <div>
      <Form
        form={form}
        layout="vertical"
        name="basic"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        initialValues={[data?.getUserForTTT?.data?.[0]] || []}
        onFinish={handleSubmit}
        autoComplete="off"
      >
        <Form.Item label="Hiring Criteria" name={"hiring_criteria"} className="capitalize">
          <Select options={hiringCriteriaOptions} />
        </Form.Item>

        <Form.Item label={"Assessed By"} name={"assessed_by"}>
          <Input maxLength={30} />
        </Form.Item>
        <Form.Item label={"Mothers Name"} name={"mothers_name"}>
          <Input maxLength={30} />
        </Form.Item>
        <Form.Item label={"Remarks"} name={"remark"}>
          <TextArea />
        </Form.Item>
      </Form>
    </div>
  );
};

export default AdminClearance;
