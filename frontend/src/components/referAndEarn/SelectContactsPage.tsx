import {
  Box,
  Center,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Spinner,
  Text,
  useToast
} from "@chakra-ui/react";
import debounce from "lodash/debounce";
import { Search, User } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import {
  useGenerateReferralCodeQuery,
  useGetContactsOfTheUserFrAdminQuery,
  useSendRefferalInviteMutation
} from "../../__generated__";
import Loading from "../Loading";
import { ReferConfirmModal } from "./ReferralConfirmModal";

interface Contact {
  id: string;
  name: string;
  phone: string;
}

// Using the generated types from Apollo

const ContactsPage: React.FC = () => {
  const [searchInput, setSearchInput] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const toast = useToast();
  const { userId } = useParams();
  const { data: referral } = useGenerateReferralCodeQuery();
  const [sendRefferalInvite, { loading: sendRefferalInviteLoading }] =
    useSendRefferalInviteMutation();

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchQuery(value);
    }, 500),
    []
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchInput(value);
    debouncedSearch(value);
  };

  const {
    loading,
    error,
    data: contactsData,
    refetch
  } = useGetContactsOfTheUserFrAdminQuery({
    variables: {
      filter: { search: searchQuery },
      userId: Number(userId)
    },
    fetchPolicy: "network-only"
  });

  const contacts: Contact[] =
    contactsData?.getContactsOfTheUserFrAdmin?.data?.map((item) => ({
      id: item?.contact?.id || "",
      name: item?.contact?.name || "",
      phone: item?.contact?.phone || ""
    })) || [];

  useEffect(() => {
    refetch({ filter: { search: searchQuery } });
  }, [searchQuery, refetch]);

  const filteredContacts = contacts.filter(
    (contact) =>
      contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.phone.includes(searchQuery)
  );

  const handleRefer = (selectedContact: Contact) => {
    sendRefferalInvite({
      variables: {
        data: {
          name: selectedContact.name,
          phone_number: selectedContact.phone,
          meta: {},
          referral_code: referral?.generateReferralCodeFrUser || ""
        }
      },
      onCompleted: () => {
        toast({
          position: "top",
          title: t("success_referral"),
          status: "success"
        });
        setSelectedContact(null);
        navigate("/profile");
      },
      onError: (error) => {
        if (error.message.includes("Referral invite already sent")) {
          toast({
            position: "top",
            title: t("error_contact_already_referred"),
            status: "error"
          });
        } else {
          toast({
            position: "top",
            title: error.message,
            status: "error"
          });
        }
        setSelectedContact(null);
        return;
      }
    });
  };

  if (sendRefferalInviteLoading) {
    return <Loading />;
  }

  return (
    <Box bg="white" rounded={"lg"}>
      <Box
        position="sticky"
        top="0"
        bg="white"
        borderBottom="1px solid"
        borderColor="gray.200"
        zIndex="10"
        roundedTop={"xl"}
        overflow={"hidden"}
      >
        <Flex align="center" gap={3} p={4} marginLeft={"8px"}>
          <Text fontSize="2xl" fontWeight="semibold" color={"1f2937"}>
            {t("referral_select_contact")}
          </Text>
        </Flex>

        <Box px={4} pb={4}>
          <InputGroup>
            <InputLeftElement pointerEvents="none">
              <Search size={20} color="gray" />
            </InputLeftElement>
            <Input
              placeholder={t("referral_search_contacts")}
              value={searchInput}
              onChange={handleSearchChange}
              pl="10"
              pr="4"
              py="2"
              borderRadius="lg"
              focusBorderColor="blue.500"
            />
          </InputGroup>
        </Box>
      </Box>

      <Box pb={20} maxHeight={"100vh"} overflowY={"auto"}>
        {loading ? (
          <Center py={10}>
            <Spinner size="lg" color="blue.500" />
          </Center>
        ) : error ? (
          <Center py={10}>
            <Text color="red.500">{error.message}</Text>
          </Center>
        ) : filteredContacts.length === 0 ? (
          <Center py={10}>
            <Text color="gray.500">{t("referral_no_contacts_found")}</Text>
          </Center>
        ) : (
          filteredContacts.map((contact) => (
            <Flex
              as="button"
              key={contact.id}
              onClick={() => setSelectedContact(contact)}
              w="full"
              align="center"
              gap={3}
              p={4}
              borderBottom="1px solid"
              borderColor="gray.100"
              _hover={{ bg: "gray.50" }}
              transition="all 0.2s"
              bg={selectedContact?.id === contact.id ? "blue.50" : "transparent"}
            >
              <Flex
                w="10"
                h="10"
                bg="blue.50"
                rounded="full"
                align="center"
                justify="center"
                flexShrink={0}
              >
                <User size={20} color="#2563eb" />
              </Flex>
              <Box textAlign="left">
                <Text fontWeight="medium">{contact.name}</Text>
                <Text fontSize="sm" color="gray.600">
                  {contact.phone}
                </Text>
              </Box>
            </Flex>
          ))
        )}
      </Box>
      <ReferConfirmModal
        contact={selectedContact}
        isOpen={selectedContact !== null}
        onClose={() => setSelectedContact(null)}
        onConfirm={() => {
          if (selectedContact) {
            handleRefer(selectedContact);
          }
        }}
      />
    </Box>
  );
};

export default ContactsPage;
