import { Button, Divider, notification, Select } from "antd";
import { useEffect, useState } from "react";
import { OnboardingStage, useUpdateUserAdminMutation } from "../../__generated__";

interface Pops {
  stageValue: OnboardingStage;
  userId: number;
  refetch: () => void;
}

const OnboardingStageDropdown: React.FC<Pops> = ({ stageValue, userId, refetch }) => {
  const [onboardingStage, setOnboardingStage] = useState<OnboardingStage>(stageValue);
  const [openStageDropdown, setOpenStageDropdown] = useState(false);

  //Graphql Mutation
  const [updateUser, { loading: user_update_loading }] = useUpdateUserAdminMutation();

  const handleSaveClick = () => {
    setOpenStageDropdown(true);
    updateUser({
      variables: {
        userId: userId,
        data: {
          onboarding_stage: onboardingStage as OnboardingStage
        }
      }
    })
      .then(() => {
        refetch();
        notification.success({
          message: "Stage updated successfully"
        });
      })
      .catch((error) => {
        notification.error({
          message: error.message
        });
      })
      .finally(() => {
        setOpenStageDropdown(false);
      });
  };

  useEffect(() => {
    setOnboardingStage(stageValue);
  }, [stageValue, openStageDropdown]);

  return (
    <div className={`${openStageDropdown ? "w-[300px]" : "w-auto"}`}>
      <Select
        className="w-full"
        open={openStageDropdown}
        defaultValue={stageValue}
        value={onboardingStage}
        loading={user_update_loading}
        disabled={user_update_loading}
        onClick={() => {
          setOpenStageDropdown((prev) => !prev);
        }}
        onDropdownVisibleChange={(open) => {
          if (!open) {
            setOpenStageDropdown(false);
          }
        }}
        onSelect={(value) => {
          setOnboardingStage(value as OnboardingStage);
        }}
        dropdownRender={(menu) => (
          <div
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            <div>{menu}</div>
            {stageValue !== onboardingStage && (
              <>
                <Divider className="my-1" />
                <div>
                  <Button
                    type="primary"
                    loading={user_update_loading}
                    className="w-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSaveClick();
                    }}
                  >
                    Update
                  </Button>
                </div>
              </>
            )}
          </div>
        )}
      >
        {onboardingStageOptions(OnboardingStage).map((option) => {
          return (
            <Select.Option key={option.value} value={option.value}>
              {option.label}
            </Select.Option>
          );
        })}
      </Select>
    </div>
  );
};

export default OnboardingStageDropdown;

export const onboardingStageOptions = (onboardingStage: typeof OnboardingStage) => {
  const orderedStages = [
    onboardingStage.Dashboard,
    onboardingStage.Documents,
    onboardingStage.Interview,
    onboardingStage.ScreeningTest,
    onboardingStage.TrainingAndTests,
    onboardingStage.Onboard,
    onboardingStage.TransferredToTms,
    onboardingStage.Verification
  ];

  return Object.entries(onboardingStage)
    .filter(
      ([, value]) =>
        !["NOT_STARTED", "SIGN_UP", "GENERAL_DETAILS", "SKILLS", "WORK_EXPERIENCE"].includes(value)
    )
    .map(([key, value]) => ({
      label:
        value === "DASHBOARD"
          ? "Sign Up Completed"
          : value === "SCREENING_TEST"
          ? "App Test"
          : value === "TRAINING_AND_TESTS"
          ? "Training"
          : value === "TRANSFERRED_TO_TMS"
          ? "Onboarded"
          : key.split(/(?=[A-Z])/).join(" "),
      value
    }))
    .sort((a, b) => {
      const aIndex = orderedStages.indexOf(a.value);
      const bIndex = orderedStages.indexOf(b.value);
      return aIndex - bIndex;
    });
};
