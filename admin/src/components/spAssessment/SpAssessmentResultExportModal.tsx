import { DatePicker, Form, Modal, notification } from "antd";
import { useForm } from "antd/es/form/Form";
import { Dayjs } from "dayjs";
import React, { useState } from "react";
import { AssignmentUserType, useExportAssessmentResultLazyQuery } from "../../__generated__";
import { date_range_presets } from "../../utils/utils";

interface ExportModalProps {
  open: boolean;
  closeModal: () => void;
}
const SpAssessmentResultExportModal: React.FC<ExportModalProps> = ({ open, closeModal }) => {
  //Hooks
  const [disableUserCreationDate, setDisableUserCreationDate] = useState(false);
  const [disableAssignmentCompletionDate, setDisableAssignmentCompletionDate] = useState(false);

  //form
  const [form] = useForm();

  //GraphQl Query
  const [exportAssignmentResult, { loading: exportLoading }] = useExportAssessmentResultLazyQuery();

  const handleFormOnFinish = (val: any) => {
    exportAssignmentResult({
      variables: {
        filter: {
          assignment_completion_date: {
            start_date: val.assignment_completion_date?.[0] || undefined,
            end_date: val.assignment_completion_date?.[1] || undefined
          },
          user_creation_date: {
            start_date: val.user_creation_date?.[0] || undefined,
            end_date: val.user_creation_date?.[1] || undefined
          },
          assign_to: AssignmentUserType.ServiceProvider
        }
      },
      fetchPolicy: "cache-and-network",
      onCompleted(data) {
        notification.success({ message: data?.exportAssessmentResult?.message });
        closeModal();
      },
      onError(error) {
        notification.error({ message: error.message });
      }
    });
  };

  return (
    <>
      <Modal
        closeIcon={false}
        centered
        open={open}
        onOk={form.submit}
        okText="Send"
        onCancel={() => {
          closeModal(), form.resetFields();
        }}
        okButtonProps={{ loading: exportLoading }}
      >
        <Form preserve form={form} onFinish={(val) => handleFormOnFinish(val)} layout="vertical">
          <Form.Item
            label="User Creation Date"
            style={{ marginBottom: 24 }}
            name="user_creation_date"
          >
            <DatePicker.RangePicker
              presets={date_range_presets}
              onChange={(dates: null | (Dayjs | null)[]) => {
                dates &&
                  form.setFieldsValue({
                    user_creation_date: { start_date: dates[0], end_date: dates[1] }
                  });
                //disable assignment_completion_date if user_creation_date is filled
                dates?.[0]
                  ? setDisableAssignmentCompletionDate(true)
                  : setDisableAssignmentCompletionDate(false);
              }}
              disabled={disableUserCreationDate}
            />
          </Form.Item>
          <Form.Item
            label="Assignment Completion Date"
            style={{ marginBottom: 0 }}
            name="assignment_completion_date"
          >
            <DatePicker.RangePicker
              presets={date_range_presets}
              onChange={(dates: null | (Dayjs | null)[]) => {
                dates &&
                  form.setFieldsValue({
                    assignment_completion_date: { start_date: dates[0], end_date: dates[1] }
                  });
                dates?.[0] ? setDisableUserCreationDate(true) : setDisableUserCreationDate(false);
              }}
              //disable user_creation_date if assignment_completion_date is filled
              disabled={disableAssignmentCompletionDate}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default SpAssessmentResultExportModal;
