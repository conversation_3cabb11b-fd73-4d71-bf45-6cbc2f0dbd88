-- DropForeign<PERSON>ey
ALTER TABLE "app_installation_user_map" DROP CONSTRAINT "app_installation_user_map_device_id_fkey";

-- DropForeignKey
ALTER TABLE "app_installation_user_map" DROP CONSTRAINT "app_installation_user_map_user_id_fkey";

-- DropForeignKey
ALTER TABLE "device_activity_daily" DROP CONSTRAINT "device_activity_daily_app_installation_id_fkey";

-- DropForeignKey
ALTER TABLE "user_session_screen_recording" DROP CONSTRAINT "user_session_screen_recording_session_id_fkey";

-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';

-- AddForeignKey
ALTER TABLE "app_installation_user_map" ADD CONSTRAINT "app_installation_user_map_device_id_fkey" FOREIGN KEY ("device_id") REFERENCES "app_installation"("device_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "app_installation_user_map" ADD CONSTRAINT "app_installation_user_map_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_session_screen_recording" ADD CONSTRAINT "user_session_screen_recording_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "user_activity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "device_activity_daily" ADD CONSTRAINT "device_activity_daily_app_installation_id_fkey" FOREIGN KEY ("app_installation_id") REFERENCES "app_installation"("id") ON DELETE CASCADE ON UPDATE CASCADE;
