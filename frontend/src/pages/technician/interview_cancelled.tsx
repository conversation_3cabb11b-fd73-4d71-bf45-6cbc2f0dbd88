import { Box, Button, Text } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { LuPhoneCall } from "react-icons/lu";
import HeaderImageAndChildContainer from "../../components/HeaderImageAndChildContainer";

const InterviewCancelled: React.FC = () => {
  const { t } = useTranslation();
  return (
    <Box p={3} className="flex justify-center h-[85vh] items-center">
      <Box>
        <HeaderImageAndChildContainer image="/icons/interview_rejected.png">
          <Text textColor={"#F76E11"} fontSize={"2xl"} fontWeight={"bold"} mt={10}>
            {t("interview_cancelled")}
          </Text>
          <Box mt={4} width={"full"}>
            <Button
              display={"flex"}
              gap={"4"}
              width={"100%"}
              backgroundColor="#fd8087"
              textColor={"#fff"}
              px={1}
              onClick={() => (window.location.href = "tel:+91 8669449671")}
            >
              <LuPhoneCall /> {t("interview_rej_details")}
            </Button>
          </Box>
        </HeaderImageAndChildContainer>{" "}
      </Box>
    </Box>
  );
};

export default InterviewCancelled;
