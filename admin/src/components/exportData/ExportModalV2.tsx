import { Checkbox, Collapse, Modal, Spin } from "antd";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { CheckboxValueType } from "antd/es/checkbox/Group";
import dayjs from "dayjs";
import isEmpty from "is-empty";
import { useEffect, useState } from "react";
import { BiExport } from "react-icons/bi";
import { downloadXLSXFromJson } from "../../utils/XlsxDataDownloader";
import { toTitleCase } from "../../utils/utils";

interface ExportModalProps {
  openModal: boolean;
  onClose: () => void;
  data: [any];
  exportDataLoading: boolean;
}

const ExportModalV2: React.FC<ExportModalProps> = ({
  onClose,
  openModal,
  data,
  exportDataLoading
}) => {
  //antd Panel
  const { Panel } = Collapse;
  //antd Checkbox
  const CheckboxGroup = Checkbox.Group;

  //Hooks
  const [exportData, setExportData] = useState<Array<any>>([]);
  const [downloadableData, setDownloadableData] = useState<Array<any>>();
  const [finalChecklist, setFinalChecklist] = useState<any>([]);
  const [checkAll, setCheckAll] = useState(false);

  //Handle Modal Close
  const handleClose = () => {
    onClose();
  };

  //Optimize data according to the requested export
  const filterData = (data: any) => {
    try {
      const checkList: any = [];
      const userDetails = data?.[0];
      //userDetails undefined in some cases hence check before doing entries
      if (!isEmpty(userDetails)) {
        //Takes first object and saves in array to show available export check list
        for (const [key] of Object.entries(userDetails)) {
          switch (key) {
            case "__typename":
              break;
            case "user_expertise_map":
              break;
            case "user_onboarding_data":
              //0th user onboarding data can be empty also hence push keys as these are anyways needed
              checkList.push(
                "mothers_name",
                "fathers_name",
                "martial_status",
                "salary_offered",
                "date_of_joining",
                "salary_vp",
                "transfer_to_tms_date"
              );
              break;
            case "location":
              checkList.push("city", "district_taluka");
              break;
            default:
              checkList.push(key);
          }
        }
      }
      setDownloadableData(data);
      setExportData(checkList);
    } catch (error) {
      console.error(error);
    }
  };

  //Handle Excel Export //TODO : make it more efficient
  const handleExcelExport = () => {
    let id = 1;
    const result = downloadableData?.map((obj: any) => {
      if (obj?.onboarding_stage == "DASHBOARD") {
        //spread the existing obj and the new stage
        obj = { ...obj, onboarding_stage: "SIGN UP COMPLETED" };
      }
      if (obj?.designation) {
        obj = { ...obj, designation: obj.designation.name };
      }

      const newObj: any = {};
      const user_onboarding_data = obj?.user_onboarding_data?.[0];
      newObj.id = id;
      id++;
      finalChecklist.forEach((item: any) => {
        if (Object.prototype.hasOwnProperty.call(obj, item)) {
          if (Array.isArray(obj[item])) {
            newObj[item] = JSON.stringify(obj[item]);
          } else {
            item === "created_at"
              ? (newObj[item] = obj[item] && dayjs(obj[item]).format("DD-MM-YYYY"))
              : (newObj[item] = obj[item]);
          }
        }

        // for location data
        if (item === "district_taluka") {
          newObj[item] = obj?.location?.district_taluka;
        }

        if (item === "city") {
          newObj[item] = obj?.location?.city;
        }

        //for user onboarding data
        if (
          [
            "martial_status",
            "salary_offered",
            "mothers_name",
            "fathers_name",
            "date_of_joining",
            "salary_vp",
            "transfer_to_tms_date"
          ].includes(item)
        ) {
          for (const key in user_onboarding_data) {
            //download only the fields that are present in finalCheckList
            if (item === key) {
              item === "date_of_joining"
                ? (newObj[item] =
                    user_onboarding_data[key] &&
                    dayjs(user_onboarding_data[key]).format("DD-MM-YYYY"))
                : (newObj[item] = user_onboarding_data[key]);
            }
            if (item === "transfer_to_tms_date") {
              newObj[item] = user_onboarding_data.meta.transfer_to_tms_date
                ? dayjs(user_onboarding_data.meta.transfer_to_tms_date).format("DD-MM-YYYY")
                : "-";
            }
          }
        }
      });
      return newObj;
    });
    //Download to Excel file
    downloadXLSXFromJson(
      result || [],
      dayjs().format("MMMM D, YYYY").replace(/,/g, "").replace(/\s/g, "_") + "_" + "Data" + ".xlsx"
    );
    handleClose();
  };

  const onChangeCheckBox = (list: CheckboxValueType[]) => {
    setFinalChecklist(list);
    setCheckAll(list.length === exportData?.length);
  };

  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    setFinalChecklist(e.target.checked ? exportData : []);
    setCheckAll(e.target.checked);
  };

  useEffect(() => {
    filterData(data);
  }, [data]);

  return (
    <>
      <Modal
        centered
        destroyOnClose
        open={openModal}
        onCancel={handleClose}
        onOk={handleExcelExport}
      >
        <div className="mx-2 my-2">
          <div className="text-center text-purple-600 text-xl mb-4">
            <BiExport />
            <span className="mx-2">Export To Excel</span>
          </div>
          <Collapse defaultActiveKey={["1"]}>
            <Panel
              className="relative"
              header={
                <span>
                  <Checkbox
                    onChange={onCheckAllChange}
                    onClick={(e) => {
                      e.stopPropagation(); // prevent Collapse
                    }}
                    checked={checkAll}
                  />
                  <span className="mx-2 text-md font-medium">User Data</span>
                </span>
              }
              key="1"
            >
              {exportDataLoading ? (
                <div className="flex justify-center items-center">
                  <Spin tip="Loading..." />
                </div>
              ) : (
                <CheckboxGroup
                  className={`grid md:grid-cols-2 gap-2 `}
                  options={exportData.map((value) => {
                    return {
                      label:
                        value === "transfer_to_tms_date"
                          ? "Transfer To TMS Date"
                          : toTitleCase(value),
                      value: value
                    };
                  })}
                  value={finalChecklist}
                  onChange={onChangeCheckBox}
                />
              )}
              {/* </p> */}
            </Panel>
          </Collapse>
        </div>
      </Modal>
    </>
  );
};

export default ExportModalV2;
