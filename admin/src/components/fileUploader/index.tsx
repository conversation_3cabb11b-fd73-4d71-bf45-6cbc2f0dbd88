import { Button, Form, Input, Modal, Progress, notification } from "antd";
import { useForm } from "antd/es/form/Form";
import axios from "axios";
import { useState } from "react";
import { useDropzone } from "react-dropzone";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  DocNumberValidationType,
  Document,
  GetDocumentsOfUserFrSpOrTeamMemberDocument,
  GetSingleUserDocDocument,
  useAddDocumentMutation
} from "../../__generated__";
import useDocumentStore from "../../stores/documentStore";

export interface FormValues {
  doc_num: string;
}

const UploadToS3: React.FC = () => {
  //states
  const [file, setFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [showModal, setShowModal] = useState<boolean>(true);
  const { currentDocument, addDocumentToCurrentDoc, deleteCurrentDocument } = useDocumentStore();
  const [imageUrl, setImageUrl] = useState("");

  const navigate = useNavigate();
  //Hooks
  const [form] = useForm();

  const [searchParams] = useSearchParams({
    type_id: "-1",
    user_id: "-1"
  });

  //graphql
  const [addDocument] = useAddDocumentMutation();

  const uploadFileToS3 = async (file: File): Promise<string> => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`,
        {
          file_type: file.type
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("adminToken")}`
          }
        }
      );

      const { status, data: signedUrl, Key } = response.data;

      if (status) {
        await axios.put(signedUrl, file, {
          headers: {
            "Content-Type": file.type
          },
          onUploadProgress: (progressEvent) => {
            const { loaded } = progressEvent;
            const percent = Math.floor((loaded * 100) / file.size);
            setUploadProgress(percent);
          }
        });

        const url = import.meta.env.VITE_S3_URL + "/" + Key;

        return url;
      } else {
        console.error(response);
        notification.error({
          message: "There was some issue during uploading the file. Please try again later."
        });
      }
    } catch (err) {
      console.error(err);
      notification.error({
        message: "There was some issue during uploading the file. Please try again later."
      });
    }

    return "";
  };

  //dropzone state
  const { getInputProps, getRootProps, isDragActive } = useDropzone({
    maxFiles: 1,
    maxSize: 10000000,
    validator: (file) => {
      if (
        file.type !== "image/png" &&
        file.type !== "image/jpeg" &&
        file.type !== "image/webp" &&
        file.type !== "image/jpg" &&
        file.type !== "image/svg" &&
        file.type !== "application/pdf"
      ) {
        return {
          code: "file-type",
          message: "Only PNG , PDF , and JPEG files are allowed"
        };
      }
      return null;
    },
    onDropRejected() {
      notification.error({
        message: "Only PNG , PDF , and JPEG files are allowed"
      });
    },
    async onDropAccepted(files) {
      if (files.length > 0) {
        setFile(files[0]);
        try {
          const url = await uploadFileToS3(files[0]);
          if (!currentDocument?.is_doc_number_required) {
            await handleAddDocument(url, "");
          } else {
            setImageUrl(url);
          }
        } catch (err) {
          console.error(err);
          notification.error({
            message: "There was some issue during uploading the file. Please try again later."
          });
        }
      }
    }
  });

  const handleAddDocument = async (url: string, docNum: string) => {
    await addDocument({
      variables: {
        data: {
          url: url,
          type_id: parseInt(searchParams.get("type_id") || "-1"),
          user: parseInt(searchParams.get("user_id") || "-1"),
          doc_number: docNum
        }
      },
      onCompleted(data) {
        const doc_id = currentDocument?.document?.[0]?.id;
        if (doc_id === undefined) {
          addDocumentToCurrentDoc(
            parseInt(searchParams.get("type_id") || "-1"),
            data.addDocument as Document
          );
        } else {
          deleteCurrentDocument(doc_id as number); //remove the current doc as it is updated with new ID and URL
          addDocumentToCurrentDoc(
            //add the updated current doc
            parseInt(searchParams.get("type_id") || "-1"),
            data.addDocument as Document
          );
        }
        setShowModal(false);
        navigate(-1);
      },
      refetchQueries: [GetDocumentsOfUserFrSpOrTeamMemberDocument, GetSingleUserDocDocument]
    });
  };

  const handleFormSubmit = async (values: FormValues) => {
    const { doc_num } = values;
    await handleAddDocument(imageUrl, doc_num);
  };

  return (
    <Modal
      open={showModal}
      onCancel={() => navigate(-1)}
      footer={null}
      okButtonProps={{
        disabled: !file
      }}
      centered
    >
      {searchParams.get("type_id") === "-1" ||
        (searchParams.get("user_id") === "-1" && <div>Incorrect type id or user id</div>)}

      <div className="text-xl mb-2">
        {currentDocument?.is_doc_number_required ? (
          <>{uploadProgress !== 100 ? "Upload image" : "Document number is required"}</>
        ) : (
          "Upload your image"
        )}
      </div>
      {file ? (
        <div className="">
          {currentDocument?.is_doc_number_required ? (
            <>
              {uploadProgress === 100 ? (
                <>
                  <Form form={form} onFinish={handleFormSubmit} layout="vertical">
                    <Form.Item
                      name={"doc_num"}
                      required
                      label={"Document Number"}
                      rules={[
                        {
                          required: true,
                          message: "Document number is required"
                        },
                        {
                          min: currentDocument?.document_number_config?.min_length || 1,
                          message: `Minimum length is ${
                            currentDocument?.document_number_config?.min_length || 1
                          }`
                        },
                        {
                          max: currentDocument?.document_number_config?.max_length || 50,
                          message: `Maximum length is ${
                            currentDocument?.document_number_config?.max_length || 50
                          }`
                        },
                        {
                          pattern:
                            currentDocument?.document_number_config?.validation_type ===
                            DocNumberValidationType.AlphaNumeric
                              ? /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z0-9]*$/ // At least one letter, one digit, alphanumeric only
                              : /^[0-9]*$/, // Numeric only
                          message:
                            currentDocument?.document_number_config?.validation_type ===
                            DocNumberValidationType.AlphaNumeric
                              ? "Must contain at least one letter and one number, with only alphanumeric characters"
                              : "Only numeric characters are allowed"
                        }
                      ]}
                    >
                      <Input placeholder="enter document number" />
                    </Form.Item>
                    <div className="flex justify-end w-full">
                      <Button type="primary" className="" onClick={form.submit}>
                        Save
                      </Button>
                    </div>
                  </Form>
                </>
              ) : (
                <div className="p-6 flex justify-center items-center w-full">
                  <Progress type="circle" percent={uploadProgress} />
                </div>
              )}
            </>
          ) : (
            <div className="p-6 flex justify-center items-center w-full">
              <Progress type="circle" percent={uploadProgress} />
            </div>
          )}
        </div>
      ) : (
        <div
          className={`w-full my-6 flex justify-center items-center h-60 border-2 border-dashed ${
            isDragActive ? "border-blue-500" : "border-gray-400"
          } flex-col`}
          {...getRootProps()}
        >
          <img width={130} src="/images/upload_image.png" alt="upload image" />
          <div className="text-xl mt-2">Please click or drag and drop here</div>
          <input accept="image/*" type="file" {...getInputProps()} />
        </div>
      )}
    </Modal>
  );
};

export default UploadToS3;
