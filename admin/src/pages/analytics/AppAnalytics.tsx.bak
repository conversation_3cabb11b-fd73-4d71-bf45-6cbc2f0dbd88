import { <PERSON><PERSON>, DatePicker, Radio, Spin, Tabs } from "antd";
import {
  ArcElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LineElement,
  LinearScale,
  PointElement,
  Title,
  Tooltip
} from "chart.js";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { ArrowDownToLine, BarChart, Globe, LogIn, Smartphone, UserMinus, Zap } from "lucide-react";
import React, { useEffect, useState } from "react";
import { Line } from "react-chartjs-2";
import { MdRocketLaunch } from "react-icons/md";
import { useLocation, useNavigate } from "react-router-dom";
import { useGetAppAnalyticsQuery } from "../../__generated__";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const { RangePicker } = DatePicker;

const AppAnalytics: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);

  // Parse date ranges from URL or set defaults
  const getInitialDateRange = (): [Dayjs, Dayjs] => {
    const startDate = queryParams.get("startDate");
    const endDate = queryParams.get("endDate");

    if (startDate && endDate) {
      return [dayjs(startDate), dayjs(endDate)];
    }
    return [dayjs(), dayjs()];
  };

  const [activeTab, setActiveTab] = useState<"app" | "web">(
    (queryParams.get("tab") as "app" | "web") || "app"
  );
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(getInitialDateRange());
  const [appliedDateRange, setAppliedDateRange] = useState<[Dayjs, Dayjs] | null>(
    queryParams.has("startDate") && queryParams.has("endDate") ? getInitialDateRange() : null
  );

  // Add state for chart view
  const [chartView, setChartView] = useState<"weekly" | "monthly" | "yearly">("weekly");

  // Update URL when tab or applied date range changes
  useEffect(() => {
    const params = new URLSearchParams();

    if (activeTab !== "app") {
      params.set("tab", activeTab);
    }

    if (appliedDateRange) {
      params.set("startDate", appliedDateRange[0].toISOString());
      params.set("endDate", appliedDateRange[1].toISOString());
    }

    const search = params.toString();
    navigate({ search }, { replace: true });
  }, [activeTab, appliedDateRange, navigate]);

  const { data: analyticsData, loading } = useGetAppAnalyticsQuery({
    variables: {
      filter: {
        startDate: appliedDateRange ? appliedDateRange[0].toISOString() : undefined,
        endDate: appliedDateRange ? appliedDateRange[1].toISOString() : undefined
      }
    }
  });
  const data = analyticsData?.getAppAnalytics;

  const formatDate = (date: Dayjs): string => {
    return date.format("MMM D, YYYY");
  };

  // Prevent selecting future dates
  const disableFutureDates = (current: Dayjs) => {
    // Can't select days after today
    return current && current > dayjs().endOf("day");
  };

  const handleDateChange = (dates: [Dayjs | null, Dayjs | null] | null) => {
    if (!dates || !dates[0] || !dates[1]) {
      clearDateFilter();
      return;
    }
    setDateRange([dates[0], dates[1]]);
  };

  const applyDateRange = () => {
    setAppliedDateRange(dateRange);
  };

  const clearDateFilter = () => {
    setAppliedDateRange(null);
    setDateRange(null);
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value as "app" | "web");
  };

  // Display date range label based on whether a filter is applied
  const getDateRangeLabel = () => {
    if (appliedDateRange) {
      return `${formatDate(appliedDateRange[0])} - ${formatDate(appliedDateRange[1])}`;
    }
    return "Today";
  };

  // Static data for weekly chart (days of the week)
  const weeklyChartData = {
    labels: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    datasets: [
      {
        label: "Total Installations",
        data: [25, 30, 28, 35, 40, 20, 15],
        borderColor: "rgba(132, 0, 255, 1)",
        backgroundColor: "rgba(132, 0, 255, 0.1)",
        borderWidth: 2,
        fill: true
      },
      {
        label: "Technician",
        data: [15, 18, 16, 20, 24, 12, 8],
        borderColor: "rgba(0, 204, 255, 1)",
        backgroundColor: "rgba(0, 204, 255, 0.1)",
        borderWidth: 2,
        fill: true
      },
      {
        label: "Service Provider",
        data: [10, 12, 12, 15, 16, 8, 7],
        borderColor: "rgba(255, 153, 0, 1)",
        backgroundColor: "rgba(255, 153, 0, 0.1)",
        borderWidth: 2,
        fill: true
      }
    ]
  };

  // Static data for monthly chart (days of the month)
  const monthlyChartData = {
    labels: Array.from({ length: 31 }, (_, i) => (i + 1).toString()), // 1-31
    datasets: [
      {
        label: "Total Installations",
        data: [
          8, 10, 7, 9, 12, 15, 11, 8, 7, 10, 14, 12, 9, 8, 10, 13, 15, 12, 9, 11, 14, 16, 13, 10, 8,
          7, 9, 12, 14, 11, 9
        ],
        borderColor: "rgba(132, 0, 255, 1)",
        backgroundColor: "rgba(132, 0, 255, 0.1)",
        borderWidth: 2,
        fill: true
      },
      {
        label: "Technician",
        data: [
          5, 6, 4, 5, 7, 9, 7, 5, 4, 6, 8, 7, 5, 5, 6, 8, 9, 7, 5, 6, 8, 10, 8, 6, 5, 4, 5, 7, 8, 7,
          5
        ],
        borderColor: "rgba(0, 204, 255, 1)",
        backgroundColor: "rgba(0, 204, 255, 0.1)",
        borderWidth: 2,
        fill: true
      },
      {
        label: "Service Provider",
        data: [
          3, 4, 3, 4, 5, 6, 4, 3, 3, 4, 6, 5, 4, 3, 4, 5, 6, 5, 4, 5, 6, 6, 5, 4, 3, 3, 4, 5, 6, 4,
          4
        ],
        borderColor: "rgba(255, 153, 0, 1)",
        backgroundColor: "rgba(255, 153, 0, 0.1)",
        borderWidth: 2,
        fill: true
      }
    ]
  };

  // Static data for yearly chart (months of the year)
  const yearlyChartData = {
    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    datasets: [
      {
        label: "Total Installations",
        data: [65, 78, 90, 110, 135, 162, 190, 210, 232, 245, 265, 285],
        borderColor: "rgba(132, 0, 255, 1)",
        backgroundColor: "rgba(132, 0, 255, 0.1)",
        borderWidth: 2,
        fill: true
      },
      {
        label: "Technician",
        data: [42, 50, 58, 68, 80, 95, 112, 125, 140, 148, 160, 172],
        borderColor: "rgba(0, 204, 255, 1)",
        backgroundColor: "rgba(0, 204, 255, 0.1)",
        borderWidth: 2,
        fill: true
      },
      {
        label: "Service Provider",
        data: [23, 28, 32, 42, 55, 67, 78, 85, 92, 97, 105, 113],
        borderColor: "rgba(255, 153, 0, 1)",
        backgroundColor: "rgba(255, 153, 0, 0.1)",
        borderWidth: 2,
        fill: true
      }
    ]
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "bottom" as const,
        labels: {
          boxWidth: 12,
          usePointStyle: true,
          pointStyle: "circle"
        }
      },
      tooltip: {
        mode: "index" as const,
        intersect: false,
        backgroundColor: "rgba(255, 255, 255, 0.9)",
        titleColor: "#111827",
        bodyColor: "#4B5563",
        borderColor: "#E5E7EB",
        borderWidth: 1,
        padding: 10,
        boxPadding: 6
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0,
          color: "#6B7280"
        },
        grid: {
          color: "rgba(229, 231, 235, 0.5)"
        }
      },
      x: {
        ticks: {
          color: "#6B7280"
        },
        grid: {
          display: false
        }
      }
    },
    elements: {
      line: {
        tension: 0.4 // Adds a slight curve to the line
      },
      point: {
        radius: 3,
        hoverRadius: 5
      }
    }
  };

  // Function to get chart data based on selected view
  const getChartData = () => {
    switch (chartView) {
      case "weekly":
        return weeklyChartData;
      case "monthly":
        return monthlyChartData;
      case "yearly":
        return yearlyChartData;
      default:
        return weeklyChartData;
    }
  };

  // Helper for loading states
  const LoadingValue = ({ size = "base" }: { size?: "base" | "large" }) => (
    <div className={`flex items-center justify-center h-${size === "large" ? 10 : 6}`}>
      <Spin size={size === "large" ? "default" : "small"} />
    </div>
  );

  // Helper for metric display
  const MetricCard = ({
    title,
    subtitle,
    value,
    icon,
    iconBgColor,
    iconColor,
    isLoading,
    className = ""
  }: {
    title: string;
    subtitle: string;
    value: number | undefined;
    icon: React.ReactNode;
    iconBgColor: string;
    iconColor: string;
    isLoading: boolean;
    className?: string;
  }) => (
    <div
      className={`bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 ${className}`}
    >
      <div className="flex items-center justify-between mb-4">
        <div>
          <h4 className="text-lg font-medium text-gray-800 mb-1">{title}</h4>
          <p className="text-sm text-gray-500">{subtitle}</p>
        </div>
        <div className={`${iconBgColor} p-3 rounded-full`}>
          {React.cloneElement(icon as React.ReactElement, { className: `w-5 h-5 ${iconColor}` })}
        </div>
      </div>
      <div className="text-3xl font-bold text-gray-900">
        {isLoading ? <LoadingValue size="large" /> : value?.toLocaleString() || 0}
      </div>
    </div>
  );

  // Helper for metric breakdown
  const MetricBreakdown = ({
    title,
    subtitle,
    value,
    breakdowns,
    icon,
    iconBgColor,
    iconColor,
    isLoading
  }: {
    title: string;
    subtitle: string;
    value: number | undefined;
    breakdowns: { label: string; value: number | undefined; bgColor: string; textColor: string }[];
    icon: React.ReactNode;
    iconBgColor: string;
    iconColor: string;
    isLoading: boolean;
  }) => (
    <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h4 className="text-lg font-medium text-gray-800 mb-1">{title}</h4>
          <p className="text-sm text-gray-500">{subtitle}</p>
        </div>
        <div className={`${iconBgColor} p-3 rounded-full`}>
          {React.cloneElement(icon as React.ReactElement, { className: `w-5 h-5 ${iconColor}` })}
        </div>
      </div>
      <div className="text-3xl font-bold text-gray-900 mb-6">
        {isLoading ? <LoadingValue size="large" /> : value?.toLocaleString() || 0}
      </div>
      <div className="grid grid-cols-2 gap-4">
        {breakdowns.map((breakdown, index) => (
          <div
            key={index}
            className={`${breakdown.bgColor} rounded-xl p-4 hover:bg-opacity-80 transition-colors duration-200`}
          >
            <div className="text-sm text-gray-700 mb-1">{breakdown.label}</div>
            <div className={`text-xl font-semibold ${breakdown.textColor}`}>
              {isLoading ? <LoadingValue /> : breakdown.value?.toLocaleString() || 0}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="min-h-[calc(100vh-64px)] bg-gray-50 overflow-y-auto pb-12">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-1">Analytics Dashboard</h1>
            <p className="text-gray-500">
              Monitor your application performance and user engagement
            </p>
          </div>
          {loading && (
            <div className="bg-purple-50 text-purple-700 px-3 py-2 rounded-lg flex items-center">
              <Spin className="mr-2" /> Loading analytics data
            </div>
          )}
        </div>

        {/* Main content */}
        <div className="space-y-6">
          {/* Total installations card */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <MetricCard
              title="Total App Installations"
              subtitle="All time app installations"
              value={data?.android_installations}
              icon={<BarChart />}
              iconBgColor="bg-purple-50"
              iconColor="text-purple-600"
              isLoading={loading}
              className="lg:col-span-3"
            />
          </div>

          {/* Installation Trends Chart */}
          <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-1">Installation Trends</h3>
                <p className="text-gray-500">Installation activity over time</p>
              </div>
              <Radio.Group
                value={chartView}
                onChange={(e) => setChartView(e.target.value)}
                optionType="button"
                buttonStyle="solid"
                className="mt-3 sm:mt-0"
              >
                <Radio.Button value="weekly">Weekly</Radio.Button>
                <Radio.Button value="monthly">Monthly</Radio.Button>
                <Radio.Button value="yearly">Yearly</Radio.Button>
              </Radio.Group>
            </div>

            <div className="h-[350px] w-full">
              <Line options={chartOptions} data={getChartData()} />
            </div>
          </div>

          {/* App/Web Analytics Tabs */}
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="p-4 border-b flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
              <Tabs
                activeKey={activeTab}
                onChange={handleTabChange}
                type="card"
                className="analytics-tabs mb-0"
                items={[
                  {
                    key: "app",
                    label: (
                      <span className="flex items-center gap-2">
                        <Smartphone className="w-4 h-4" />
                        App Activity
                      </span>
                    ),
                    children: null
                  },
                  {
                    key: "web",
                    label: (
                      <span className="flex items-center gap-2">
                        <Globe className="w-4 h-4" />
                        Web Activity
                      </span>
                    ),
                    children: null
                  }
                ]}
              />

              <div className="flex items-center gap-2">
                <div className="text-sm text-gray-500 hidden sm:block">{getDateRangeLabel()}</div>
                <div className="flex flex-wrap sm:flex-nowrap items-center gap-2">
                  <div className="relative">
                    <RangePicker
                      value={dateRange}
                      onChange={handleDateChange}
                      className="border border-gray-200 rounded-lg hover:border-purple-400 focus:border-purple-500 shadow-sm"
                      allowClear={true}
                      format="MMM D, YYYY"
                      placement="bottomRight"
                      disabledDate={disableFutureDates}
                      presets={[
                        {
                          label: "Yesterday",
                          value: [
                            dayjs().subtract(1, "day").startOf("day"),
                            dayjs().subtract(1, "day").endOf("day")
                          ]
                        },
                        { label: "Last 7 days", value: [dayjs().subtract(6, "days"), dayjs()] },
                        {
                          label: "Last week",
                          value: [
                            dayjs().subtract(1, "week").startOf("week"),
                            dayjs().subtract(1, "week").endOf("week")
                          ]
                        },
                        { label: "Last 30 days", value: [dayjs().subtract(30, "days"), dayjs()] },
                        { label: "Last 6 months", value: [dayjs().subtract(6, "months"), dayjs()] },
                        {
                          label: "This month",
                          value: [dayjs().startOf("month"), dayjs().endOf("month")]
                        },
                        {
                          label: "Last month",
                          value: [
                            dayjs().subtract(1, "month").startOf("month"),
                            dayjs().subtract(1, "month").endOf("month")
                          ]
                        },
                        {
                          label: "This year",
                          value: [dayjs().startOf("year"), dayjs()]
                        }
                      ]}
                    />
                  </div>
                  <Button
                    type="primary"
                    onClick={applyDateRange}
                    className="bg-purple-600 hover:bg-purple-700 border-none shadow-sm h-8 flex items-center"
                    disabled={!dateRange}
                  >
                    Apply
                  </Button>
                  {appliedDateRange && (
                    <Button
                      onClick={clearDateFilter}
                      className="h-8 flex items-center border-gray-300 hover:text-purple-600 hover:border-purple-600"
                    >
                      Reset
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {activeTab === "app" ? (
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <MetricBreakdown
                    title="App Installations"
                    subtitle="Installations in selected period"
                    value={data?.android_installations_by_date_range}
                    icon={<ArrowDownToLine />}
                    iconBgColor="bg-green-50"
                    iconColor="text-green-600"
                    isLoading={loading}
                    breakdowns={[
                      {
                        label: "Technician",
                        value: data?.android_installations_by_technician,
                        bgColor: "bg-purple-50",
                        textColor: "text-purple-700"
                      },
                      {
                        label: "Service Provider",
                        value: data?.android_installations_by_service_provider,
                        bgColor: "bg-blue-50",
                        textColor: "text-blue-700"
                      }
                    ]}
                  />

                  <MetricBreakdown
                    title="Active Devices"
                    subtitle="Currently active devices"
                    value={data?.active_devices}
                    icon={<Zap />}
                    iconBgColor="bg-yellow-50"
                    iconColor="text-yellow-600"
                    isLoading={loading}
                    breakdowns={[
                      {
                        label: "Technician",
                        value: data?.active_devices_by_technician,
                        bgColor: "bg-yellow-50",
                        textColor: "text-yellow-700"
                      },
                      {
                        label: "Service Provider",
                        value: data?.active_devices_by_service_provider,
                        bgColor: "bg-amber-50",
                        textColor: "text-amber-700"
                      }
                    ]}
                  />

                  <MetricBreakdown
                    title="App Sign Ups"
                    subtitle="Total app registrations"
                    value={data?.total_user_sign_up_android}
                    icon={<LogIn />}
                    iconBgColor="bg-indigo-50"
                    iconColor="text-indigo-600"
                    isLoading={loading}
                    breakdowns={[
                      {
                        label: "Technician",
                        value: data?.total_user_sign_up_android_by_technician,
                        bgColor: "bg-indigo-50",
                        textColor: "text-indigo-700"
                      },
                      {
                        label: "Service Provider",
                        value: data?.total_user_sign_up_android_by_service_provider,
                        bgColor: "bg-indigo-50",
                        textColor: "text-indigo-700"
                      }
                    ]}
                  />

                  <MetricCard
                    title="Drop Off"
                    subtitle="Users who left without completing registration"
                    value={data?.drop_off_user}
                    icon={<UserMinus />}
                    iconBgColor="bg-red-50"
                    iconColor="text-red-600"
                    isLoading={loading}
                  />
                </div>
              </div>
            ) : (
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <MetricBreakdown
                    title="Web Sign Ups"
                    subtitle="Total website registrations"
                    value={data?.total_user_sign_up_web}
                    icon={<LogIn />}
                    iconBgColor="bg-indigo-50"
                    iconColor="text-indigo-600"
                    isLoading={loading}
                    breakdowns={[
                      {
                        label: "Technician",
                        value: data?.total_user_sign_up_web_by_technician,
                        bgColor: "bg-indigo-50",
                        textColor: "text-indigo-700"
                      },
                      {
                        label: "Service Provider",
                        value: data?.total_user_sign_up_web_by_service_provider,
                        bgColor: "bg-cyan-50",
                        textColor: "text-cyan-700"
                      }
                    ]}
                  />

                  <MetricBreakdown
                    title="Unique Visitors"
                    subtitle="Total unique website visitors"
                    value={data?.unique_visitors}
                    icon={<MdRocketLaunch />}
                    iconBgColor="bg-orange-50"
                    iconColor="text-orange-600"
                    isLoading={loading}
                    breakdowns={[
                      {
                        label: "Individual",
                        value: data?.unique_visitors_by_technician,
                        bgColor: "bg-orange-50",
                        textColor: "text-orange-700"
                      },
                      {
                        label: "Service Provider",
                        value: data?.unique_visitors_by_service_provider,
                        bgColor: "bg-amber-50",
                        textColor: "text-amber-700"
                      }
                    ]}
                  />

                  <MetricCard
                    title="Drop Off"
                    subtitle="Users who left without action"
                    value={data?.drop_off_user}
                    icon={<UserMinus />}
                    iconBgColor="bg-red-50"
                    iconColor="text-red-600"
                    isLoading={loading}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppAnalytics;
