import { OnboardingStage, Prisma } from '@prisma/client'
import dayjs from 'dayjs'
import { GraphQLError } from 'graphql'
import DataProvider from '../../data'
import { logHistory } from '../../services/history.service'
import { sendPolicyTrackingDataToAdmin } from '../../services/policy.service'
import { validateAdmin, validateUser } from '../../utils/authValidators'
import { MAIN_ORG_DATA } from '../../utils/envVars'
import { EventType } from '../../utils/eventTypes'
import { Context } from '../../utils/types'
import { Pagination } from '../__generated__/types'

const db = DataProvider.getDbInstance()

const getPoliciesWithUserStatus = async (
    _: any,
    args: { user_id?: number; search?: string; pagination: Pagination },
    ctx: Context
) => {
    try {
        let user

        if (ctx.admin) {
            validateAdmin(ctx)
        } else {
            user = validateUser(ctx)
        }

        // Get all active policies
        const policies = await db.policy.findMany({
            where: {
                is_active: true,
            },
            select: {
                id: true,
                policy_name: true,
                policy_name_hindi: true,
                url: true,
                is_active: true,
                created_at: true,
                updated_at: true,
                user_policy_tracking: {
                    where: {
                        user_id: user
                            ? user.id
                            : args.user_id
                            ? args?.user_id
                            : undefined,
                    },

                    select: {
                        accepted: true,
                        created_at: true,
                    },
                },
            },
            orderBy: {
                created_at: 'desc',
            },
        })

        return {
            result: true,
            message: 'Policies fetched successfully',
            data: policies,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getAllPolicies = async (_: any, args: any, ctx: Context) => {
    try {
        if (ctx.admin) {
            validateAdmin(ctx)
        } else {
            validateUser(ctx)
        }

        const policies = await db.policy.findMany({
            where: {
                is_active: true,
            },
            orderBy: {
                created_at: 'desc',
            },
        })

        return {
            result: true,
            message: 'All policies fetched successfully',
            data: policies,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const updatePolicyAcceptance = async (
    _: unknown,
    args: { data: { policy_id: string; accepted: boolean } },
    ctx: Context
) => {
    try {
        const { user } = ctx

        if (!user) {
            throw new GraphQLError('User not authenticated')
        }

        const { policy_id, accepted } = args.data

        // Check if policy exists
        const policy = await db.policy.findUnique({
            where: {
                id: policy_id,
            },
        })

        if (!policy) {
            throw new GraphQLError('Policy not found')
        }

        // Check if user has already accepted this policy
        await db.user_policy_tracking.findFirst({
            where: {
                user_id: user.id,
                policy_id: policy_id,
            },
        })

        const userPolicyTracking = await db.user_policy_tracking.upsert({
            where: {
                user_id_policy_id: {
                    user_id: user.id,
                    policy_id: policy_id,
                },
            },
            update: {
                accepted: accepted || true,
                created_at: accepted ? new Date() : undefined,
            },
            create: {
                user_id: user.id,
                policy_id: policy_id,
                accepted: accepted || true, //todo change this
                created_at: accepted ? new Date() : undefined,
            },
        })

        // Log history
        await logHistory({
            entity_module: 'user_policy_tracking',
            entity_id: userPolicyTracking.id,
            event_title: accepted
                ? EventType.USER_POLICY_ACCEPTED
                : EventType.USER_POLICY_DECLINED,
            creator_id: user.id,
            creator_type: 'USER',
            new_values: {
                policy_id,
                accepted,
                created_at: userPolicyTracking.created_at,
            },
            meta: {
                user_id: user.id,
                user_name: user.name,
            },
        })

        return {
            result: true,
            message: accepted
                ? 'Policy accepted successfully'
                : 'Policy declined successfully',
            data: {
                ...policy,
                user_accepted: accepted,
                created_at: userPolicyTracking.created_at,
            },
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getPolicyById = async (_: any, args: { id: string }, ctx: Context) => {
    try {
        validateAdmin(ctx)

        const policy = await db.policy.findUnique({
            where: {
                id: args.id,
            },
        })

        if (!policy) {
            throw new GraphQLError('Policy not found')
        }

        return {
            result: true,
            message: 'Policy fetched successfully',
            data: policy,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

/**
 * Admin: Get policy with user acceptance status
 */
const getPolicyWithUserStatus = async (
    _: any,
    args: { policy_id: string },
    ctx: Context
) => {
    try {
        validateAdmin(ctx)

        const policy = await db.policy.findUnique({
            where: {
                id: args.policy_id,
            },
            include: {
                user_policy_tracking: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        })

        if (!policy) {
            throw new GraphQLError('Policy not found')
        }

        // Transform the data to match the expected response format
        const policyWithUserStatus = {
            ...policy,
            user_status: policy.user_policy_tracking.map((tracking) => ({
                user_id: tracking.user_id,
                user_name: tracking.user.name,
                accepted: tracking.accepted,
                accepted_on: tracking.created_at,
            })),
        }

        return {
            result: true,
            message: 'Policy with user status fetched successfully',
            data: [policyWithUserStatus],
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}
interface UserPolicyTrackingData {
    id: number
    name: string
    onboarded_date: Date
    policies_accepted: number
    total_policies: number
    last_accepted_date: Date | null
    all_accepted: boolean
    user_onboarding_data: any[]
}

/**
 * Get policy tracking data for all users
 * This includes users who have been transferred to TMS and their policy acceptance status
 */

const getUserPolicyTracking = async (
    _: any,
    args: {
        userType?: string
        search: string
        pagination: { skip: number; take: number }
        filter?: {
            startDate?: string
            endDate?: string
            allAccepted?: boolean
        }
    },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)

        const { search, pagination, filter } = args
        const { skip, take } = pagination || { skip: 0, take: 10 }

        // Get all active policies
        const totalPoliciesCount = await db.policy.count({
            where: {
                is_active: true,
            },
            orderBy: {
                created_at: 'desc',
            },
        })

        // Build search filter for users
        const userWhere: Prisma.userWhereInput = {
            // Only include users who have been transferred to TMS
            ...(args.userType
                ? { user_type: args.userType }
                : { user_type: 'TECHNICIAN' }),
            // Search filter
            ...(search
                ? {
                      OR: [
                          {
                              name: {
                                  contains: search,
                                  mode: 'insensitive',
                              },
                          },
                      ],
                  }
                : {}),
            // Date filter
            ...(filter?.startDate && filter?.endDate
                ? {
                      user_onboarding_data: {
                          some: {
                              meta: {
                                  path: ['transfer_to_tms_date'],
                                  gte: new Date(filter.startDate).toISOString(),
                                  lte: new Date(filter.endDate).toISOString(),
                              },
                          },
                      },
                  }
                : {}),
            isActive: true,
            user_type: 'TECHNICIAN',
            onboarding_stage: OnboardingStage.ONBOARD,
            organization: {
                id: admin?.active_org_id || -1,
            },
            transfer_to_tms_status: true,
        }

        // Get total count of users matching the search criteria
        const totalCount = await db.user.count({
            where: {
                ...userWhere,
            },
        })

        // Get total count of onboarded users (transferred to TMS)
        const totalOnboardedUsers = await db.user.count({
            where: {
                transfer_to_tms_status: true,
                isActive: true,
                user_type: 'TECHNICIAN',
                onboarding_stage: OnboardingStage.ONBOARD,
                organization: {
                    id: admin?.active_org_id || -1,
                },
            },
        })

        // Get users with pagination
        const users = await db.user.findMany({
            where: userWhere,
            orderBy: {
                created_at: 'desc',
            },
            select: {
                id: true,
                name: true,
                created_at: true,
                user_onboarding_data: {
                    select: {
                        meta: true,
                    },
                },
                user_policy_tracking: {
                    select: {
                        accepted: true,
                        created_at: true,
                    },
                    orderBy: {
                        created_at: 'desc',
                    },
                },
            },
            skip: skip * take,
            take: take,
        })

        const totalAccepted = await db.user.findMany({
            where: {
                ...(args.userType
                    ? { user_type: args.userType }
                    : { user_type: 'TECHNICIAN' }),
                isActive: true,
                user_type: 'TECHNICIAN',
                onboarding_stage: OnboardingStage.ONBOARD,
                organization: {
                    id: admin?.active_org_id || -1,
                },
                transfer_to_tms_status: true,
            },
            select: {
                id: true,
                name: true,
                created_at: true,
                user_onboarding_data: {
                    select: {
                        meta: true,
                    },
                },
                user_policy_tracking: {
                    select: {
                        accepted: true,
                        created_at: true,
                    },
                    orderBy: {
                        created_at: 'desc',
                    },
                },
            },
        })
        console.log(totalAccepted.length, 'totalAccepted')

        const usersWithAllPoliciesAccepted = totalAccepted.reduce(
            (count, user) => {
                const acceptedCount = user.user_policy_tracking.filter(
                    (tracking) => tracking.accepted
                ).length

                return count + (acceptedCount === totalPoliciesCount ? 1 : 0)
            },
            0
        )

        // Process user data to include policy acceptance information
        const policyUsers =
            filter?.allAccepted && !filter.endDate && !filter.startDate
                ? totalAccepted
                : users

        console.log(
            filter?.allAccepted && !filter.endDate && !filter.startDate
                ? 'yes'
                : 'no'
        )
        const userPolicyDataWithNulls = await Promise.all(
            policyUsers.map(async (user) => {
                // Get all policy acceptances for this user
                const acceptedPolicies = user.user_policy_tracking.filter(
                    (tracking) => tracking.accepted
                )

                // Calculate if user has accepted all policies
                const policiesAccepted = acceptedPolicies.length
                const allAccepted = policiesAccepted === totalPoliciesCount

                // Skip users that don't match the allAccepted filter if it's provided
                if (
                    filter?.allAccepted !== undefined &&
                    allAccepted !== filter.allAccepted
                ) {
                    return null
                }

                // Get the last acceptance date
                const lastAcceptedDate =
                    acceptedPolicies.length > 0
                        ? acceptedPolicies[0].created_at
                        : null

                return {
                    id: user?.id,
                    name: user?.name,
                    onboarded_date: user?.created_at,
                    policies_accepted: policiesAccepted,
                    total_policies: totalPoliciesCount,
                    last_accepted_date: lastAcceptedDate,
                    all_accepted: allAccepted,
                    user_onboarding_data: user?.user_onboarding_data,
                }
            })
        )

        // Filter out null values
        const userPolicyData: UserPolicyTrackingData[] =
            userPolicyDataWithNulls.filter(
                (user): user is UserPolicyTrackingData => user !== null
            )

        // Count users who have accepted all policies

        return {
            result: true,
            message: 'User policy tracking data fetched successfully',
            data: {
                users: userPolicyData,
                totalCount:
                    filter?.allAccepted && !filter.endDate && !filter.startDate
                        ? usersWithAllPoliciesAccepted
                        : totalCount,
                totalOnboardedUsers,
                usersWithAllPoliciesAccepted,
            },
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getSpPolicyTracking = async (
    _: any,
    args: {
        userType?: string
        search: string
        pagination: { skip: number; take: number }
        filter?: {
            startDate?: string
            endDate?: string
            allAccepted?: boolean
        }
    },
    ctx: Context
) => {
    try {
        validateAdmin(ctx)

        const { search, pagination, filter } = args
        const { skip, take } = pagination || { skip: 0, take: 10 }

        // Get all active policies
        const totalPoliciesCount = await db.policy.count({
            where: {
                is_active: true,
            },
            orderBy: {
                created_at: 'desc',
            },
        })

        // Build search filter for users
        const userWhere: Prisma.organizationWhereInput = {
            // Only include users who have been transferred to TMS
            // Search filter

            // Date filter
            org_owner: {
                ...(search
                    ? {
                          name: {
                              contains: search,
                              mode: 'insensitive',
                          },
                      }
                    : {}),
                user_onboarding_data: {
                    ...(filter?.startDate && filter?.endDate
                        ? {
                              //org owner meta in user onboarded table transfer to tms date based filter

                              some: {
                                  meta: {
                                      path: ['transfer_to_tms_date'],
                                      gte: new Date(
                                          filter.startDate
                                      ).toISOString(),
                                      lte: new Date(
                                          filter.endDate
                                      ).toISOString(),
                                  },
                              },
                          }
                        : {}),
                },
                isActive: true,
                meta: {
                    path: ['user_onboarded'],
                    equals: true,
                },
            },
            id: {
                not: Number(MAIN_ORG_DATA.ORG_ID), //main org
            },
            is_active: true,
        }

        // Get total count of users matching the search criteria
        const totalCount = await db.organization.count({
            where: {
                ...userWhere,
            },
        })

        // Get total count of onboarded users (transferred to TMS)
        const totalOnboardedUsers = await db.organization.count({
            where: {
                id: {
                    not: Number(MAIN_ORG_DATA.ORG_ID), //main org
                },
                is_active: true,
                org_owner: {
                    isActive: true,
                    meta: {
                        path: ['user_onboarded'],
                        equals: true,
                    },
                },
            },
        })

        // Get users with pagination
        const users = await db.organization.findMany({
            where: userWhere,
            orderBy: {
                org_owner: {
                    created_at: 'desc',
                },
            },
            select: {
                org_owner: {
                    select: {
                        id: true,
                        name: true,
                        created_at: true,
                        user_onboarding_data: {
                            select: {
                                meta: true,
                            },
                        },
                        user_policy_tracking: {
                            select: {
                                accepted: true,
                                created_at: true,
                            },
                            orderBy: {
                                created_at: 'desc',
                            },
                        },
                    },
                },
            },
            take: take,
            skip: skip * take,
        })
        //todo: find a better way to handle pagination and accepted users
        const totalAccepted = await db.organization.findMany({
            where: {
                org_owner: {
                    isActive: true,
                    meta: {
                        path: ['user_onboarded'],
                        equals: true,
                    },
                },
                id: {
                    not: Number(MAIN_ORG_DATA.ORG_ID), //main org
                },
                is_active: true,
            },
            select: {
                org_owner: {
                    select: {
                        id: true,
                        name: true,
                        created_at: true,
                        user_onboarding_data: {
                            select: {
                                meta: true,
                            },
                        },
                        user_policy_tracking: {
                            select: {
                                accepted: true,
                                created_at: true,
                            },
                            orderBy: {
                                created_at: 'desc',
                            },
                        },
                    },
                },
            },
        })
        const usersWithAllPoliciesAccepted = totalAccepted.reduce(
            (count, user) => {
                const acceptedCount =
                    user?.org_owner?.user_policy_tracking.filter(
                        (tracking) => tracking.accepted
                    ).length

                return count + (acceptedCount === totalPoliciesCount ? 1 : 0)
            },
            0
        )

        const policyUsers =
            filter?.allAccepted && !filter.endDate && !filter.startDate
                ? totalAccepted
                : users

        // Process user data to include policy acceptance information
        const userPolicyDataWithNulls = await Promise.all(
            policyUsers.map(async (userOrg) => {
                const user = userOrg.org_owner
                // Get all policy acceptances for this user
                const acceptedPolicies = user?.user_policy_tracking.filter(
                    (tracking) => tracking.accepted
                )

                // Calculate if user has accepted all policies
                const policiesAccepted = acceptedPolicies?.length
                const allAccepted = policiesAccepted === totalPoliciesCount

                // Skip users that don't match the allAccepted filter if it's provided
                if (
                    filter?.allAccepted !== undefined &&
                    allAccepted !== filter.allAccepted
                ) {
                    return null
                }

                // Get the last acceptance date
                const lastAcceptedDate =
                    acceptedPolicies?.length && acceptedPolicies?.length > 0
                        ? acceptedPolicies?.[0]?.created_at
                        : null

                return {
                    id: user?.id,
                    name: user?.name,
                    onboarded_date: user?.created_at,
                    policies_accepted: policiesAccepted,
                    total_policies: totalPoliciesCount,
                    last_accepted_date: lastAcceptedDate,
                    all_accepted: allAccepted,
                    user_onboarding_data: user?.user_onboarding_data,
                }
            })
        )

        // Filter out null values
        const userPolicyData: UserPolicyTrackingData[] =
            userPolicyDataWithNulls.filter(
                (user): user is UserPolicyTrackingData => user !== null
            )

        return {
            result: true,
            message: 'User policy tracking data fetched successfully',
            data: {
                users: userPolicyData,
                totalCount:
                    filter?.allAccepted && !filter.endDate && !filter.startDate
                        ? usersWithAllPoliciesAccepted
                        : totalCount,
                totalOnboardedUsers,
                usersWithAllPoliciesAccepted,
            },
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getExportUserPolicyTracking = async (
    _: any,
    args: {
        userType?: string
        search: string
        filter?: {
            startDate?: string
            endDate?: string
            allAccepted?: boolean
        }
    },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)

        const { search, filter } = args

        // Get all active policies
        const totalPoliciesCount = await db.policy.count({
            where: {
                is_active: true,
            },
            orderBy: {
                created_at: 'desc',
            },
        })

        // Build search filter for users
        const userWhere: Prisma.userWhereInput = {
            // Only include users who have been transferred to TMS
            ...(args.userType
                ? { user_type: args.userType }
                : { user_type: 'TECHNICIAN' }),
            // Search filter
            ...(search
                ? {
                      OR: [
                          {
                              name: {
                                  contains: search,
                                  mode: 'insensitive',
                              },
                          },
                      ],
                  }
                : {}),
            // Date filter
            ...(filter?.startDate && filter?.endDate
                ? {
                      user_onboarding_data: {
                          some: {
                              meta: {
                                  path: ['transfer_to_tms_date'],
                                  gte: new Date(filter.startDate).toISOString(),
                                  lte: new Date(filter.endDate).toISOString(),
                              },
                          },
                      },
                  }
                : {}),
            isActive: true,
            user_type: 'TECHNICIAN',
            onboarding_stage: OnboardingStage.ONBOARD,
            organization: {
                id: admin?.active_org_id || -1,
            },
            transfer_to_tms_status: true,
        }

        // Get users with pagination
        const users = await db.user.findMany({
            where: userWhere,
            orderBy: {
                created_at: 'desc',
            },
            select: {
                id: true,
                name: true,
                created_at: true,
                user_onboarding_data: {
                    select: {
                        meta: true,
                    },
                },
                user_policy_tracking: {
                    select: {
                        accepted: true,
                        created_at: true,
                    },
                    orderBy: {
                        created_at: 'desc',
                    },
                },
            },
        })

        // Process user data to include policy acceptance information
        const userPolicyDataWithNulls = await Promise.all(
            users.map(async (user) => {
                // Get all policy acceptances for this user
                const acceptedPolicies = user.user_policy_tracking.filter(
                    (tracking) => tracking.accepted
                )

                // Calculate if user has accepted all policies
                const policiesAccepted = acceptedPolicies.length
                const allAccepted = policiesAccepted === totalPoliciesCount

                // Skip users that don't match the allAccepted filter if it's provided
                if (
                    filter?.allAccepted !== undefined &&
                    allAccepted !== filter.allAccepted
                ) {
                    return null
                }

                // Get the last acceptance date
                const lastAcceptedDate =
                    acceptedPolicies.length > 0
                        ? acceptedPolicies[0].created_at
                        : null

                return {
                    id: user.id,
                    name: user.name,
                    onboarded_date: user.created_at,
                    policies_accepted: policiesAccepted,
                    total_policies: totalPoliciesCount,
                    last_accepted_date: lastAcceptedDate,
                    all_accepted: allAccepted,
                    user_onboarding_data: user.user_onboarding_data,
                }
            })
        )

        // Filter out null values
        const userPolicyData: UserPolicyTrackingData[] =
            userPolicyDataWithNulls.filter(
                (user): user is UserPolicyTrackingData => user !== null
            )

        const processedUsers = userPolicyData
            ?.filter((user): user is NonNullable<typeof user> => user !== null)
            .map((user) => ({
                id: user.id,
                name: user.name,
                user_code: user?.user_onboarding_data?.length
                    ? user?.user_onboarding_data[0]?.meta?.onboarding_data
                          ?.user_code
                    : '-',
                onboarded_date: user?.user_onboarding_data?.length
                    ? dayjs(
                          user?.user_onboarding_data[0]?.meta
                              ?.transfer_to_tms_date
                      ).format('DD MMM YYYY')
                    : '-',
                policies_accepted: user.policies_accepted,
                total_policies: user.total_policies,
                last_accepted_date: user.last_accepted_date
                    ? dayjs(user.last_accepted_date).format('DD MMM YYYY')
                    : null,
                all_accepted: user.all_accepted,
            }))
        //sort the users based on onboarded date
        const sortedUsers = processedUsers.sort((a, b) => {
            if (a.onboarded_date === '-' && b.onboarded_date === '-') {
                return 0
            } else if (a.onboarded_date === '-') {
                return 1
            } else if (b.onboarded_date === '-') {
                return -1
            } else {
                return dayjs(b.onboarded_date).diff(dayjs(a.onboarded_date))
            }
        })

        const formattedExportUserData = sortedUsers?.map((user) => ({
            Id: user.id,
            Name: user.name,
            'User Code': user.user_code,
            'Onboarded Date':
                typeof user.onboarded_date === 'string'
                    ? user.onboarded_date
                    : null,
            'Policies Accepted': user.policies_accepted,
            'Total Policies': user.total_policies,
            'Last Accepted Date': user.last_accepted_date
                ? typeof user.last_accepted_date === 'string'
                    ? user.last_accepted_date
                    : null
                : null,
            'All Accepted': user.all_accepted,
        }))
        if (formattedExportUserData?.length === 0) {
            throw new GraphQLError('No contacts to download')
        }

        const sheet_id = await sendPolicyTrackingDataToAdmin(
            formattedExportUserData,
            ctx
        )
        return {
            result: true,
            message: 'Policy Tracking data exported successfully',
            sheet_id,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getExportSpPolicyTracking = async (
    _: any,
    args: {
        userType?: string
        search: string
        filter?: {
            startDate?: string
            endDate?: string
            allAccepted?: boolean
        }
    },
    ctx: Context
) => {
    try {
        validateAdmin(ctx)

        const { search, filter } = args

        // Get all active policies
        const totalPoliciesCount = await db.policy.count({
            where: {
                is_active: true,
            },
            orderBy: {
                created_at: 'desc',
            },
        })

        // Build search filter for users
        const userWhere: Prisma.organizationWhereInput = {
            // Only include users who have been transferred to TMS
            // Search filter

            // Date filter
            org_owner: {
                ...(search
                    ? {
                          name: {
                              contains: search,
                              mode: 'insensitive',
                          },
                      }
                    : {}),
                user_onboarding_data: {
                    ...(filter?.startDate && filter?.endDate
                        ? {
                              //org owner meta in user onboarded table transfer to tms date based filter

                              some: {
                                  meta: {
                                      path: ['transfer_to_tms_date'],
                                      gte: new Date(
                                          filter.startDate
                                      ).toISOString(),
                                      lte: new Date(
                                          filter.endDate
                                      ).toISOString(),
                                  },
                              },
                          }
                        : {}),
                },
                isActive: true,
                meta: {
                    path: ['user_onboarded'],
                    equals: true,
                },
            },
            id: {
                not: Number(MAIN_ORG_DATA.ORG_ID), //main org
            },
            is_active: true,
        }

        // Get users with pagination
        const users = await db.organization.findMany({
            where: userWhere,
            orderBy: {
                org_owner: {
                    created_at: 'desc',
                },
            },
            select: {
                org_owner: {
                    select: {
                        id: true,
                        name: true,
                        created_at: true,
                        user_onboarding_data: {
                            select: {
                                meta: true,
                            },
                        },
                        user_policy_tracking: {
                            select: {
                                accepted: true,
                                created_at: true,
                            },
                            orderBy: {
                                created_at: 'desc',
                            },
                        },
                    },
                },
            },
        })

        // Process user data to include policy acceptance information
        const userPolicyDataWithNulls = await Promise.all(
            users.map(async (userOrg) => {
                const user = userOrg.org_owner
                // Get all policy acceptances for this user
                const acceptedPolicies = user?.user_policy_tracking.filter(
                    (tracking) => tracking.accepted
                )

                // Calculate if user has accepted all policies
                const policiesAccepted = acceptedPolicies?.length
                const allAccepted = policiesAccepted === totalPoliciesCount

                // Skip users that don't match the allAccepted filter if it's provided
                if (
                    filter?.allAccepted !== undefined &&
                    allAccepted !== filter.allAccepted
                ) {
                    return null
                }

                // Get the last acceptance date
                const lastAcceptedDate =
                    acceptedPolicies?.length && acceptedPolicies?.length > 0
                        ? acceptedPolicies?.[0]?.created_at
                        : null

                return {
                    id: user?.id,
                    name: user?.name,
                    onboarded_date: user?.created_at,
                    policies_accepted: policiesAccepted,
                    total_policies: totalPoliciesCount,
                    last_accepted_date: lastAcceptedDate,
                    all_accepted: allAccepted,
                    user_onboarding_data: user?.user_onboarding_data,
                }
            })
        )

        // Filter out null values
        const userPolicyData: UserPolicyTrackingData[] =
            userPolicyDataWithNulls.filter(
                (user): user is UserPolicyTrackingData => user !== null
            )

        // Count users who have accepted all policies

        const processedUsers = userPolicyData
            ?.filter((user): user is NonNullable<typeof user> => user !== null)
            .map((user) => ({
                id: user.id,
                name: user.name,
                user_code: user?.user_onboarding_data?.length
                    ? user?.user_onboarding_data[0]?.meta?.onboarding_data
                          ?.user_code
                    : '-',
                onboarded_date: user?.user_onboarding_data?.length
                    ? dayjs(
                          user?.user_onboarding_data[0]?.meta
                              ?.transfer_to_tms_date
                      ).format('DD MMM YYYY')
                    : '-',
                policies_accepted: user.policies_accepted,
                total_policies: user.total_policies,
                last_accepted_date: user.last_accepted_date
                    ? dayjs(user.last_accepted_date).format('DD MMM YYYY')
                    : null,
                all_accepted: user.all_accepted,
            }))
        //sort the users based on onboarded date
        const sortedUsers = processedUsers.sort((a, b) => {
            if (a.onboarded_date === '-' && b.onboarded_date === '-') {
                return 0
            } else if (a.onboarded_date === '-') {
                return 1
            } else if (b.onboarded_date === '-') {
                return -1
            } else {
                return dayjs(b.onboarded_date).diff(dayjs(a.onboarded_date))
            }
        })

        const formattedExportUserData = sortedUsers?.map((user) => ({
            Id: user.id,
            Name: user.name,
            'User Code': user.user_code,
            'Onboarded Date':
                typeof user.onboarded_date === 'string'
                    ? user.onboarded_date
                    : null,
            'Policies Accepted': user.policies_accepted,
            'Total Policies': user.total_policies,
            'Last Accepted Date': user.last_accepted_date
                ? typeof user.last_accepted_date === 'string'
                    ? user.last_accepted_date
                    : null
                : null,
            'All Accepted': user.all_accepted,
        }))

        if (formattedExportUserData?.length === 0) {
            throw new GraphQLError('No contacts to download')
        }

        const sheet_id = await sendPolicyTrackingDataToAdmin(
            formattedExportUserData,
            ctx
        )

        return {
            result: true,
            message: 'Policy Tracking data exported successfully',
            sheet_id,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const exportUserPolicyTrackingData = async (
    _: any,
    args: any,
    ctx: Context
) => {
    try {
        validateAdmin(ctx)

        const { data } = args
        if (data?.data?.length === 0) {
            throw new GraphQLError('No contacts to download')
        }

        const sheet_id = await sendPolicyTrackingDataToAdmin(data?.data, ctx)
        return {
            result: true,
            message: 'Policy Tracking data exported successfully',
            sheet_id,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const policyResolver = {
    Query: {
        getPoliciesWithUserStatus,
        getAllPolicies,
        getPolicyById,
        getPolicyWithUserStatus,
        getUserPolicyTracking,
        getExportUserPolicyTracking,
        getSpPolicyTracking,
        getExportSpPolicyTracking,
    },
    Mutation: {
        updatePolicyAcceptance,
        exportUserPolicyTrackingData,
    },
}

export default policyResolver
