/**

This is the TransferToTMSModal component which allows the user to go through the transfer process for a technician.
It displays three tabs with forms for Admin Clearance, HR Clearance, and Transfer information.
The user can save and continue to the next tab or transfer the technician to TMS if all the tabs are filled correctly.
@returns {React.FC} A React functional component
*/

import type { TabsProps } from "antd";
import { Button, Modal, notification, Result, Tabs } from "antd";
import { useForm } from "antd/es/form/Form";
import isEmpty from "is-empty";
import { useState } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import {
  GetUserForTttDocument,
  useCreateOrUpdateUserBankDataMutation,
  useCreateOrUpdateUsersOnboardingDataMutation
} from "../../__generated__";
import AdminClearance from "./AdminClearance";
import HRClearance from "./HRClearance";
import TransferToTms from "./TransferToTms";

interface TransferToTMSModalProps {
  open?: boolean;
  onClose?: () => void;
  isFromUsersComponent?: boolean;
}

const TransferToTMSModal: React.FC<TransferToTMSModalProps> = ({
  open,
  onClose,
  isFromUsersComponent
}) => {
  //Hooks
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [adminForm] = useForm();
  const [hrForm] = useForm();
  const [tmsForm] = useForm();
  const [loading, setLoading] = useState(false);
  const location = useLocation();

  //Graphql
  const [updateUserData] = useCreateOrUpdateUsersOnboardingDataMutation();
  const [updateUserBankData] = useCreateOrUpdateUserBankDataMutation();

  // Variables
  const tab = Number(searchParams.get("tab")) || 1;
  const userId = Number(searchParams.get("user_id")) || -1;

  // Functions
  const onChange = (key: string) => {
    setSearchParams({
      tab: key,
      user_id: searchParams.get("user_id") || "-1"
    });
  };

  const handleSubmit = async (data: any) => {
    const { bank_name, account_number, ifsc, ...userData } = data;

    try {
      await updateUserData({
        variables: {
          userOnboardingData: {
            ...userData,
            user_id: userId
          }
        },
        refetchQueries: [GetUserForTttDocument]
      });

      const bankData: any = {};
      if (bank_name) bankData.bank_name = bank_name;
      if (account_number) bankData.account_number = account_number;
      if (ifsc) bankData.ifsc = ifsc;

      if (Object.keys(bankData).length > 0) {
        await updateUserBankData({
          variables: {
            userBankData: {
              ...bankData,
              user_id: userId
            }
          },
          refetchQueries: [GetUserForTttDocument]
        });
      }
    } catch (error) {
      notification.error({ message: "Failed To Update User Data" });
      console.error(error);
    }
  };

  const saveAndContinue = () => {
    if (tab === 1) {
      adminForm.submit();
    } else if (tab === 2) {
      hrForm.submit();
    } else if (tab === 3) {
      tmsForm.submit();
    }
    if (tab < 3) {
      isFromUsersComponent
        ? navigate(`${location.pathname}?tab=${tab + 1}&user_id=${userId}`)
        : navigate(`/dashboard/transfer_to_tms/add_technician?tab=${tab + 1}&user_id=${userId}`);
    }
  };
  //Tabs Props
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: `Admin`,
      children: <AdminClearance form={adminForm} handleSubmit={handleSubmit} />
    },
    {
      key: "2",
      label: `HR Section`,
      children: <HRClearance form={hrForm} handleSubmit={handleSubmit} />
    },
    {
      key: "3",
      label: `Transfer`,
      children: (
        <TransferToTms form={tmsForm} setCreateUserInTmsLoading={setLoading} onClose={onClose} />
      )
    }
  ];

  return (
    <Modal
      open={open ?? true}
      onCancel={() => {
        if (isFromUsersComponent) {
          if (onClose) onClose();
          setSearchParams();
        } else {
          navigate(`/dashboard/transfer_to_tms`);
        }
      }}
      title="Transfer Procedure"
      centered
      width={1200}
      okText={Number(searchParams.get("tab")) === 3 ? "Transfer To TMS" : "Save and Continue"}
      onOk={() => saveAndContinue()}
      okButtonProps={{
        loading: loading
      }}
      destroyOnClose={true}
      closable={false}
    >
      {parseInt(searchParams.get("tab") || "-1") > 3 ||
      parseInt(searchParams.get("tab") || "-1") < 1 ||
      isEmpty(searchParams.get("user_id")) ? (
        <div>
          <Result
            status="warning"
            title="Check your Tab ID BUDDY !! 😉"
            extra={
              <Button
                type="primary"
                key="console"
                onClick={() => navigate("/dashboard/transfer_to_tms")}
              >
                Go back
              </Button>
            }
          />
        </div>
      ) : (
        <Tabs activeKey={searchParams.get("tab") || "1"} items={items} onChange={onChange} />
      )}
    </Modal>
  );
};

export default TransferToTMSModal;
