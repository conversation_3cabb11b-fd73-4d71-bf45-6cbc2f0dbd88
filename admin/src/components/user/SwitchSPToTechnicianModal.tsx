import { Button, Form, Input, Modal, Select, message } from "antd";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  useCheckIfUserExistsLazyQuery,
  useGetUserExpertiseQuery,
  useSwitchSpToTechnicianMutation
} from "../../__generated__";
import useBasePath from "../../hooks/useBasePath";

interface SwitchSPToTechnicianModalProps {
  userId: number;
  userName: string;
  isOpen: boolean;
  userDetails: any;
  onClose: () => void;
  onSuccess: () => void;
}

const SwitchSPToTechnicianModal: React.FC<SwitchSPToTechnicianModalProps> = ({
  userId,
  userName,
  userDetails,
  isOpen,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const navigate = useNavigate();
  const [showRedirectConfirm, setShowRedirectConfirm] = useState(false);
  const { setBasePath } = useBasePath();

  // Get expertise/skills
  const { data: expertiseData, loading: expertiseLoading } = useGetUserExpertiseQuery();
  const [checkIfUserExists] = useCheckIfUserExistsLazyQuery();
  // Handle redirection
  const handleRedirect = () => {
    form.resetFields();
    onClose();
    navigate("/dashboard/users/");
    setBasePath("");
  };
  // Switch mutation
  const [switchSPToTechnician, { loading: switchLoading }] = useSwitchSpToTechnicianMutation({
    onCompleted: (data) => {
      if (data?.switchSPToTechnician?.result) {
        setShowRedirectConfirm(true);
        onSuccess();
      } else {
        message.error(data?.switchSPToTechnician?.message || "Failed to switch user type");
      }
      setConfirmLoading(false);
    },
    // refetchQueries:
    onError: (error: any) => {
      message.error(error.message || "Failed to switch user type");
      setConfirmLoading(false);
    }
  });

  useEffect(() => {
    if (isOpen) {
      form.setFieldsValue({
        name: userName,
        email: userDetails?.email,
        phone: userDetails?.phone
      });

      // Check if user is organization owner
    }
  }, [isOpen, userName, userDetails, userId, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      setConfirmLoading(true);

      const switchData: any = {
        name: values.name,
        phone: values.phone,
        user_id: userId,
        skills: values.skills,
        city: values.city,
        email: values.email,
        source: values.source,
        poc: values.poc
      };

      switchSPToTechnician({
        variables: {
          data: switchData
        }
      });
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  return (
    <>
      <Modal
        title="Move Service Provider to Individual"
        open={isOpen}
        onCancel={onClose}
        confirmLoading={confirmLoading || switchLoading}
        closable={false}
        width={600}
        footer={[
          <Button key="cancel" onClick={onClose}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={confirmLoading || switchLoading}
            onClick={handleSubmit}
          >
            Move to Individual
          </Button>
        ]}
      >
        <Form
          form={form}
          className="max-h-[70vh] overflow-y-auto"
          layout="vertical"
          initialValues={{
            name: userName,
            new_owner_type: "existing"
          }}
        >
          <Form.Item
            name="name"
            label="Name"
            rules={[
              { required: true, message: "Enter name" },
              {
                validator: (_: any, value: any) => {
                  // Check if the value contains only whitespace
                  if (value && value.startsWith(" ")) {
                    return Promise.reject("Whitespace is not allowed at start");
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="Enter name" allowClear />
          </Form.Item>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              {
                required: true,
                message: "Enter a valid email id",
                pattern:
                  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
              },
              {
                validator: async (_: any, value: any) => {
                  if (
                    value &&
                    value.length > 0 &&
                    value.match(
                      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
                    )
                  ) {
                    const { data } = await checkIfUserExists({
                      variables: {
                        data: {
                          email: value,
                          exclude_email: userDetails?.email
                        }
                      }
                    });
                    if (data?.checkIfUserExists) {
                      return Promise.reject("Email already exists");
                    }
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="Enter email" allowClear />
          </Form.Item>
          <Form.Item
            name="poc"
            label="POC"
            rules={[
              {
                validator: (_: any, value: any) => {
                  // Check if the value contains only whitespace
                  if (value && value.startsWith(" ")) {
                    return Promise.reject("Whitespace is not allowed at start");
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="Enter POC" allowClear />
          </Form.Item>
          <Form.Item
            name="skills"
            label="Skills"
            rules={[{ required: true, message: "Please select at least one skill" }]}
          >
            <Select
              mode="multiple"
              placeholder="Select skills"
              loading={expertiseLoading}
              options={
                expertiseData?.expertise?.data?.map((expertise: any) => ({
                  label: expertise.name,
                  value: expertise.id
                })) || []
              }
            />
          </Form.Item>

          <Form.Item
            name="phone"
            label="Phone"
            rules={[
              { required: true, message: "Please enter phone number" },
              {
                pattern: /^[1-9][0-9]{9}$/,
                message: "Invalid phone number"
              },
              { len: 10, message: "Phone number must be 10 digits" },
              {
                validator: async (_: any, value: any) => {
                  if (value && value.charAt(0) === "0") {
                    return Promise.reject("Phone number should not start with 0");
                  }
                  if (value && value.length === 10) {
                    const { data } = await checkIfUserExists({
                      variables: {
                        data: {
                          phone: value,
                          exclude_phone: userDetails?.phone
                        }
                      }
                    });
                    if (data?.checkIfUserExists) {
                      return Promise.reject("Phone number already exists");
                    }
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="Enter phone" allowClear />
          </Form.Item>
          <Form.Item
            name="source"
            label="Source"
            rules={[
              { required: true, message: "Enter source" },
              {
                validator: (_: any, value: any) => {
                  // Check if the value contains only whitespace
                  if (value && value.startsWith(" ")) {
                    return Promise.reject("Whitespace is not allowed at start");
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="Enter source" allowClear />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title="Redirect to Individual Users"
        className="top-[30%]"
        open={showRedirectConfirm}
        onCancel={() => {
          setShowRedirectConfirm(false);
          onClose();
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              form.resetFields();
              setShowRedirectConfirm(false);
              navigate("/dashboard/sp/service_providers");
              onClose();
            }}
          >
            No
          </Button>,
          <Button key="submit" type="primary" onClick={handleRedirect}>
            Yes
          </Button>
        ]}
      >
        <p>SP has been successfully moved to Individual users.</p>
        <p>Would you like to go to the Individual Users page?</p>
      </Modal>
    </>
  );
};

export default SwitchSPToTechnicianModal;
