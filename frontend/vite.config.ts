import react from "@vitejs/plugin-react-swc";
import { copyFileSync, existsSync, mkdirSync } from "fs";
import { resolve } from "path";
import { defineConfig } from "vite";

// Custom plugin to ensure offline.html is copied
const copyOfflineHtml = () => {
  const copyFile = () => {
    const src = resolve(__dirname, "public/offline.html");
    const dest = resolve(__dirname, "dist/offline.html");

    if (existsSync(src)) {
      // Ensure dist directory exists
      const distDir = resolve(__dirname, "dist");
      if (!existsSync(distDir)) {
        mkdirSync(distDir, { recursive: true });
      }

      copyFileSync(src, dest);
      console.log("✓ Copied offline.html to dist directory");
      return true;
    } else {
      console.warn("⚠ offline.html not found in public directory");
      return false;
    }
  };

  return {
    name: "copy-offline-html",
    // Copy during build start
    buildStart() {
      copyFile();
    },
    // Copy after bundle is written
    writeBundle() {
      copyFile();
    },
    // Copy when files change in dev mode
    handleHotUpdate({ file }) {
      if (file.includes("offline.html")) {
        copyFile();
      }
    }
  };
};

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), copyOfflineHtml()],
  // Ensure public directory files are copied to dist
  publicDir: "public",
  build: {
    // Copy all files from public directory to dist
    copyPublicDir: true,
    // Ensure the file is preserved during build
    emptyOutDir: true
  }
});
