/*
  Warnings:

  - A unique constraint covering the columns `[device_id]` on the table `app_installation` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';

-- CreateTable
CREATE TABLE "app_installation_user_map" (
    "id" TEXT NOT NULL,
    "device_id" TEXT NOT NULL,
    "user_id" INTEGER NOT NULL,

    CONSTRAINT "app_installation_user_map_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "app_installation_user_map_device_id_user_id_key" ON "app_installation_user_map"("device_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "app_installation_device_id_key" ON "app_installation"("device_id");

-- AddForeignKey
ALTER TABLE "app_installation_user_map" ADD CONSTRAINT "app_installation_user_map_device_id_fkey" FOREIGN KEY ("device_id") REFERENCES "app_installation"("device_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "app_installation_user_map" ADD CONSTRAINT "app_installation_user_map_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
