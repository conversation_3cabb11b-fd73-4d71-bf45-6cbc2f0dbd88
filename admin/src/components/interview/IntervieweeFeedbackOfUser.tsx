import { <PERSON><PERSON>, Card, Form, Spin } from "antd";
import Form<PERSON>uilder, { FieldType } from "antd-form-builder";
import { useForm } from "antd/es/form/Form";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { useGetIntervieweeFeedbackFormDataQuery } from "../../__generated__";
import { createAntdFormMeta } from "../../utils/formbuilder.helper";

const IntervieweeFeedbackOfUser: React.FC<{ feedback_form_id: string }> = ({
  feedback_form_id
}) => {
  // GraphQL Query
  const { data, loading, error } = useGetIntervieweeFeedbackFormDataQuery({
    variables: {
      templateFormId: feedback_form_id
    }
  });

  const [meta, setMeta] = useState<FieldType[]>([]);
  const [form] = useForm();
  const feedbackData = data?.getIntervieweeFeedbackFormData;
  const formMeta = feedbackData?.template?.interviewee_feedback_meta;
  const formData = feedbackData?.interviewee_feedback_result;

  useEffect(() => {
    if (formData && formMeta) {
      try {
        const meta = createAntdFormMeta(formMeta) as FieldType[];
        setMeta(meta);
        form.setFieldsValue(formData);
      } catch (error) {
        console.error("Error parsing interviewee feedback template:", error);
      }
    }
  }, [form, formData, formMeta]);

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center p-10">
          <Spin tip="Loading feedback data..." />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="Error loading feedback"
          description="There was a problem loading the interviewee feedback data."
          type="error"
          showIcon
        />
      </Card>
    );
  }

  if (!feedbackData) {
    return (
      <Card>
        <Alert
          message="No feedback data"
          description="No interviewee feedback data is available for this interview."
          type="info"
          showIcon
        />
      </Card>
    );
  }

  return (
    <Card>
      <div className="mb-4">
        <div className="flex items-center mb-2">
          <span className="mr-2 font-medium">Interviewee:</span>
          <span>{feedbackData?.user?.name || "Unknown"}</span>
        </div>
        {feedbackData?.meta?.interviewee_feedback_submitted_at && (
          <div className="flex items-center mb-2">
            <span className="mr-2 font-medium">Feedback Submitted:</span>
            <span>
              {dayjs(feedbackData.meta.interviewee_feedback_submitted_at).format(
                "MMMM D, YYYY h:mm A"
              )}
            </span>
          </div>
        )}
        {feedbackData?.meta?.interview_date && (
          <div className="flex items-center">
            <span className="mr-2 font-medium">Interview Date & Time:</span>
            <span>{dayjs(feedbackData.meta.interview_date).format("MMMM D, YYYY h:mm A")}</span>
          </div>
        )}
      </div>
      <div className="max-h-[600px] overflow-y-auto p-6">
        <Form form={form} layout="vertical" disabled>
          <FormBuilder meta={meta} form={form} disabled />
        </Form>
      </div>
    </Card>
  );
};

export default IntervieweeFeedbackOfUser;
