import { CloseCircleOutlined } from "@ant-design/icons";
import { Card, Empty, List, Tag } from "antd";
import ErrorBoundary from "antd/es/alert/ErrorBoundary";
import Search from "antd/es/input/Search";
import isEmpty from "is-empty";
import { useCallback, useEffect, useState } from "react";
import { GrLocationPin } from "react-icons/gr";
import { LuPhone } from "react-icons/lu";
import { useParams, useSearchParams } from "react-router-dom";
import { Expertise, User, useSearchUsersLazyQuery } from "../../__generated__";
import Loading from "../../components/loading/Loading";
import { debounce, getRandomTagBgColor } from "../../utils/utils";
import { SelectedUserType } from "./AssignConfigToUserModal";

export interface UsersListUIProps {
  user: SelectedUserType;
}

interface AssignManualAssignmentProps {
  selectedUsers: SelectedUserType[];
  setSelectedUsers: (user: any) => void;
  searchTerm: string;
  setSearchTerm: (searchTerm: string) => void;
}

const UsersListUI: React.FC<UsersListUIProps> = ({ user }) => {
  return (
    <div className="mb-2">
      <div>
        <span className="font-semibold text-lg mr-5">{user?.name}</span>
        {user?.location?.city && (
          <span className="mr-5">
            <span className="mr-1  text-purple-500">
              <GrLocationPin />
            </span>
            {user?.location?.city}
          </span>
        )}
        <span className="mr-5">
          <span className=" mr-1 text-purple-500">
            <LuPhone />
          </span>
          {user?.phone}
        </span>
      </div>
      <div>
        {user.expertise?.map((e: Expertise) => (
          <Tag color={getRandomTagBgColor(e?.name || "")}>{e?.name}</Tag>
        ))}
      </div>
    </div>
  );
};

const AssignManualAssignment: React.FC<AssignManualAssignmentProps> = ({
  selectedUsers,
  setSelectedUsers,
  searchTerm,
  setSearchTerm
}) => {
  //Hooks
  const [selectableUsers, setSelectableUsers] = useState<Array<any>>([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const { assignment_config_id } = useParams();

  //Graphql query
  const [searchUsers, { data: users, loading: usersLoading, error: usersError }] =
    useSearchUsersLazyQuery();

  //Handle Debounce Search
  const debounceSearch = useCallback(
    debounce((val: string) => {
      setSearchParams({ search: val });
    }, 500),
    []
  );

  // Function to check if a user should be disabled
  const isUserDisabled = (user: User) => {
    return (
      !isEmpty(assignment_config_id) &&
      user?.manual_assignments?.some(
        (assignment: any) => assignment?.assignment_config_id === assignment_config_id
      )
    );
  };

  const addSelectedUser = (user: SelectedUserType) => {
    const selected_users = localStorage.getItem("selected_users");
    const userToAdd = {
      id: user?.id,
      name: user?.name,
      location: {
        city: user?.location?.city
      },
      phone: user?.phone,
      expertise: user?.expertise
    };

    //add user in selected users list
    setSelectedUsers([...selectedUsers, userToAdd]);
    //remove user from selectable users list
    setSelectableUsers((prevSelectableUsers) =>
      prevSelectableUsers.filter((selectableUser) => selectableUser.id !== user.id)
    );
    //if present in local storage then push else set local storage
    if (selected_users) {
      const parsed_selected_users = JSON.parse(selected_users);
      parsed_selected_users.push(userToAdd);
      localStorage.setItem("selected_users", JSON.stringify(parsed_selected_users));
    } else {
      localStorage.setItem("selected_users", JSON.stringify([userToAdd]));
    }
  };

  const removeSelectedUser = (user: SelectedUserType) => {
    const userToAdd = {
      id: user?.id,
      name: user?.name,
      location: {
        city: user?.location?.city
      },
      phone: user?.phone,
      expertise: user?.expertise
    };

    // Remove user from selected users list
    setSelectedUsers((prevSelectedUsers: SelectedUserType[]) =>
      prevSelectedUsers.filter((selectedUser: SelectedUserType) => selectedUser.id !== user.id)
    );
    //add user back to selectable users list if search is not empty
    if (searchParams.get("search") !== "") {
      setSelectableUsers([userToAdd, ...selectableUsers]);
    }

    // Remove user from local storage
    const selected_users = localStorage.getItem("selected_users");
    if (selected_users) {
      const parsed_selected_users = JSON.parse(selected_users);
      const updated_selected_users = parsed_selected_users.filter(
        (selectedUser: SelectedUserType) => selectedUser.id !== user.id
      );
      localStorage.setItem("selected_users", JSON.stringify(updated_selected_users));
    }
  };

  useEffect(() => {
    const searched_text = searchParams.get("search");
    const selected_users = localStorage.getItem("selected_users");
    const parsed_selected_users = selected_users ? JSON.parse(selected_users) : undefined;

    searchUsers({
      variables: {
        search: searched_text,
        userIds: parsed_selected_users
          ? parsed_selected_users?.map((user: SelectedUserType) => user?.id)
          : []
      },
      onCompleted(data) {
        setSelectableUsers(data?.searchUser);
      }
    });

    if (parsed_selected_users) {
      setSelectedUsers(parsed_selected_users);
    }
  }, [searchParams.get("search")]);

  return (
    <div className="flex w-full">
      <Card className="w-6/12 " style={{ whiteSpace: "nowrap" }}>
        <div className="text-xl mb-3 font-bold">Search Users for Assignment</div>
        <div className="flex-grow">
          <Search
            allowClear
            value={searchTerm}
            autoFocus
            onChange={(e) => {
              setSearchTerm(e.target.value);
              debounceSearch(e.target.value);
            }}
            placeholder="search user by name"
          />
        </div>
        {usersError ? (
          <ErrorBoundary />
        ) : usersLoading ? (
          <Loading tip="Searching ..." />
        ) : (
          <div className="flex flex-col gap-3 max-h-[400px] overflow-auto  mt-6">
            {users?.searchUser?.length ? (
              selectableUsers?.map((user) => (
                <List.Item
                  key={user?.id}
                  onClick={() => addSelectedUser(user)}
                  className="cursor-pointer"
                  style={
                    isUserDisabled(user)
                      ? {
                          backgroundColor: "#f5f5f5",
                          color: "#333333", // Darker gray color
                          pointerEvents: "none",
                          padding: "1rem 0.5rem",
                          opacity: 0.6, // Faded look
                          borderRadius: "4px", // Rounded corners
                          border: "1px solid #d9d9d9" // Light gray border
                        }
                      : {
                          padding: "1rem 0.5rem"
                        }
                  }
                >
                  <UsersListUI user={user} />
                </List.Item>
              ))
            ) : (
              <div className="mt-20">
                <Empty />
              </div>
            )}
          </div>
        )}
      </Card>
      <Card className="w-6/12 ml-3 " style={{ whiteSpace: "nowrap" }}>
        <div className="text-xl font-bold">Selected Users</div>
        <div className="flex flex-col gap-3 max-h-[435px] overflow-auto mt-8">
          {selectedUsers?.length > 0 ? (
            selectedUsers?.map((user: SelectedUserType) => (
              <List.Item
                key={user?.id}
                style={{
                  borderBottom: "1px solid #d1d5db",
                  padding: "0.5rem 0",
                  marginBottom: "5px"
                }}
              >
                <div className="flex">
                  <CloseCircleOutlined
                    onClick={() => removeSelectedUser(user)}
                    className="mr-2 text-red-500"
                  />
                  <UsersListUI user={user} />
                </div>
              </List.Item>
            ))
          ) : (
            <div className="mt-32">
              <Empty />
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default AssignManualAssignment;
