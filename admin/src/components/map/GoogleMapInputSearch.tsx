import { Autocomplete, GoogleMap } from "@react-google-maps/api";
import { Input } from "antd";
import React, { useEffect, useState } from "react";

interface SelectLocationFromGoogleMapInputProps {
  setLocation: (address: google.maps.GeocoderResult) => void;
  lat?: number;
  lng?: number;
  address?: string;
}

const SelectLocationFromGoogleMapInput: React.FC<SelectLocationFromGoogleMapInputProps> = ({
  setLocation,
  address
}) => {
  // Hooks
  const [searchInputValue, setSearchInputValue] = useState("");

  const handleInputChange = (e: { target: { value: React.SetStateAction<string> } }) => {
    setSearchInputValue(e.target.value);
  };

  const getAddressFrmLatLng = (latLng: google.maps.LatLng) => {
    const geocoder = new google.maps.Geocoder();
    geocoder.geocode({ location: latLng }, (results, status) => {
      if (status === "OK") {
        if (results && results[0]) {
          setLocation(results[0]);
        } else {
          console.log("No results found");
        }
      } else {
        console.log("Geocoder failed due to: " + status);
      }
    });
  };

  const updateMarkerPosition = (newPosition: google.maps.LatLng) => {
    getAddressFrmLatLng(newPosition);
  };

  useEffect(() => {
    if (address) {
      setSearchInputValue(address);
    }
  }, []);

  return (
    <div>
      <Autocomplete
        onLoad={(autocomplete) => {
          autocomplete.addListener("place_changed", () => {
            setSearchInputValue("");
            const place = autocomplete.getPlace();
            if (!place.geometry || !place.geometry.location) {
              return;
            }
            const locality = place?.address_components?.find((place) =>
              place?.types?.includes("locality")
            );
            if (!locality) return;
            updateMarkerPosition(place.geometry.location);
          });
        }}
        children={
          <Input value={searchInputValue} onChange={handleInputChange} placeholder="Search" />
        }
        restrictions={{
          // Restrict to addresses within India
          country: "in"
        }}
      ></Autocomplete>
      <GoogleMap></GoogleMap>
    </div>
  );
};

export default SelectLocationFromGoogleMapInput;
