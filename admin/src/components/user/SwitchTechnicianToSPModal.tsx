import { Button, Form, Input, Modal, Select, message } from "antd";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  GetUsersByOrganizationDocument,
  useCheckIfUserExistsLazyQuery,
  useGetServiceTypesQuery,
  useGetUserExpertiseQuery,
  useSwitchTechnicianToSpMutation
} from "../../__generated__";
import { sp_cities_list } from "../../utils/utils";

interface SwitchTechnicianToSPModalProps {
  userId: number;
  userName: string;
  userDetails: any;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const SwitchTechnicianToSPModal: React.FC<SwitchTechnicianToSPModalProps> = ({
  userId,
  userName,
  userDetails,
  isOpen,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [showRedirectConfirm, setShowRedirectConfirm] = useState(false);

  // Get service types
  const { data: serviceTypesData, loading: serviceTypesLoading } = useGetServiceTypesQuery();

  // Get expertise/skills
  const { data: expertiseData, loading: expertiseLoading } = useGetUserExpertiseQuery();
  const [checkIfUserExists] = useCheckIfUserExistsLazyQuery();

  // Handle redirection
  const handleRedirect = () => {
    form.resetFields();
    onClose();
    navigate("/dashboard/sp/service_providers");
    localStorage.setItem("base_path", "sp");

    // Dispatch a custom event to notify DashboardContainer to update basePath
    window.dispatchEvent(new Event("basePathChanged"));
  };

  // Switch mutation
  const [switchTechnicianToSP, { loading: switchLoading }] = useSwitchTechnicianToSpMutation({
    onCompleted: (data) => {
      if (data?.switchTechnicianToSP?.result) {
        setShowRedirectConfirm(true);
        onSuccess();

        // Show confirmation modal instead of immediate redirect
      } else {
        message.error(data?.switchTechnicianToSP?.message || "Failed to switch user type");
      }
      setConfirmLoading(false);
    },

    onError: (error: any) => {
      message.error(error.message || "Failed to switch user type");
      setConfirmLoading(false);
    }
  });

  useEffect(() => {
    if (isOpen) {
      form.setFieldsValue({
        owner_name: userName
      });
    }
  }, [isOpen, userName, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setConfirmLoading(true);

      switchTechnicianToSP({
        variables: {
          data: {
            user_id: userId,
            company_name: values.company_name,
            company_poc_name: values.company_poc_name,
            owner_name: values.owner_name,
            skills: values.skills,
            city: values.city,
            email: values.email,
            service_type_id: values.service_type_id
          }
        },
        refetchQueries: [GetUsersByOrganizationDocument]
      });
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  return (
    <>
      <Modal
        title="Move Individual User to Service Provider"
        open={isOpen}
        onCancel={onClose}
        closable={false}
        confirmLoading={confirmLoading || switchLoading}
        footer={[
          <Button key="cancel" onClick={onClose}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={confirmLoading || switchLoading}
            onClick={handleSubmit}
          >
            Move to Service Provider
          </Button>
        ]}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="company_name"
            label="Company Name"
            rules={[
              { required: true, message: "Please enter the company name" },
              {
                validator: (_: any, value: any) => {
                  // Check if the value contains only whitespace
                  if (value && value.startsWith(" ")) {
                    return Promise.reject("Whitespace is not allowed at start");
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="Enter company name" allowClear />
          </Form.Item>
          <Form.Item
            name="owner_name"
            label="Owner Name"
            rules={[
              { required: true, message: "Please enter the Owner Name" },
              {
                validator: (_: any, value: any) => {
                  // Check if the value contains only whitespace
                  if (value && value.startsWith(" ")) {
                    return Promise.reject("Whitespace is not allowed at start");
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="Enter Owner name" allowClear />
          </Form.Item>
          <Form.Item
            name="email"
            label="Email"
            initialValue={userDetails?.email}
            rules={[
              {
                required: true,
                message: "Enter a valid email id",
                pattern:
                  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
              },
              {
                validator: async (_: any, value: any) => {
                  if (
                    value &&
                    value.length > 0 &&
                    value.match(
                      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
                    )
                  ) {
                    const { data } = await checkIfUserExists({
                      variables: {
                        data: {
                          email: value,
                          exclude_email: userDetails?.email
                        }
                      }
                    });
                    if (data?.checkIfUserExists) {
                      return Promise.reject("Email already exists");
                    }
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="Enter email" allowClear />
          </Form.Item>
          <Form.Item
            name="company_poc_name"
            label="POC"
            rules={[
              { required: true, message: "Please enter the company POC name" },
              {
                validator: (_: any, value: any) => {
                  // Check if the value contains only whitespace
                  if (value && value.startsWith(" ")) {
                    return Promise.reject("Whitespace is not allowed at start");
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder="Enter POC name" allowClear />
          </Form.Item>

          <Form.Item
            name="skills"
            label="Skills"
            rules={[{ required: true, message: "Please select at least one skill" }]}
          >
            <Select
              mode="multiple"
              placeholder="Select skills"
              loading={expertiseLoading}
              options={
                expertiseData?.expertise?.data?.map((expertise: any) => ({
                  label: expertise.name,
                  value: expertise.id
                })) || []
              }
            />
          </Form.Item>

          <Form.Item
            name="city"
            label="City"
            rules={[{ required: true, message: "Please enter the city" }]}
          >
            <Select
              placeholder="Enter city"
              options={sp_cities_list
                ?.map((primary_city: any) => {
                  return { value: primary_city, label: primary_city };
                })
                .filter((option) => option !== undefined)}
            />
          </Form.Item>

          <Form.Item
            name="service_type_id"
            label="Service Type"
            rules={[{ required: true, message: "Please select a service type" }]}
          >
            <Select
              mode="multiple"
              placeholder="Select service type"
              loading={serviceTypesLoading}
              options={
                serviceTypesData?.getServiceTypes?.map((type: any) => ({
                  label: type.name,
                  value: type.id
                })) || []
              }
            />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title="Redirect to Service Providers"
        className="top-[30%]"
        width={500}
        open={showRedirectConfirm}
        onCancel={() => {
          setShowRedirectConfirm(false);
          onClose();
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setShowRedirectConfirm(false);
              navigate("/dashboard/users");
              form.resetFields();
              onClose();
            }}
          >
            No
          </Button>,
          <Button key="submit" type="primary" onClick={handleRedirect}>
            Yes
          </Button>
        ]}
      >
        <p>Individual user has been successfully moved to Service Provider.</p>
        <p>Would you like to go to the Service Providers page?</p>
      </Modal>

      {/* Confirmation Modal for Redirection */}
    </>
  );
};

export default SwitchTechnicianToSPModal;
