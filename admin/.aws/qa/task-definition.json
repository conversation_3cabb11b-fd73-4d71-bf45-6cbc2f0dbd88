{"ipcMode": null, "executionRoleArn": "arn:aws:iam::194153695972:role/ecsTaskExecutionRole", "containerDefinitions": [{"dnsSearchDomains": null, "environmentFiles": null, "logConfiguration": {"logDriver": "awslogs", "secretOptions": null, "options": {"awslogs-group": "/ecs/qa-on-admin", "awslogs-region": "ap-south-1", "awslogs-stream-prefix": "ecs"}}, "entryPoint": [], "portMappings": [{"hostPort": 80, "protocol": "tcp", "containerPort": 80}], "command": [], "linuxParameters": null, "cpu": 0, "environment": [], "resourceRequirements": null, "ulimits": null, "dnsServers": null, "mountPoints": [], "workingDirectory": null, "secrets": null, "dockerSecurityOptions": null, "memory": null, "memoryReservation": null, "volumesFrom": [], "stopTimeout": null, "image": "194153695972.dkr.ecr.ap-south-1.amazonaws.com/qa-onbding-admin:latest", "startTimeout": null, "firelensConfiguration": null, "dependsOn": null, "disableNetworking": null, "interactive": null, "healthCheck": null, "essential": true, "links": null, "hostname": null, "extraHosts": null, "pseudoTerminal": null, "user": null, "readonlyRootFilesystem": null, "dockerLabels": null, "systemControls": null, "privileged": null, "name": "qa-on-admin"}], "placementConstraints": [], "memory": "1024", "taskRoleArn": "arn:aws:iam::194153695972:role/Onboarding-task-exe-Role", "compatibilities": ["EC2", "FARGATE"], "taskDefinitionArn": "arn:aws:ecs:ap-south-1:194153695972:task-definition/qa-on-admin:1", "family": "qa-on-admin", "requiresAttributes": [{"targetId": null, "targetType": null, "value": null, "name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"targetId": null, "targetType": null, "value": null, "name": "ecs.capability.execution-role-awslogs"}, {"targetId": null, "targetType": null, "value": null, "name": "com.amazonaws.ecs.capability.ecr-auth"}, {"targetId": null, "targetType": null, "value": null, "name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"targetId": null, "targetType": null, "value": null, "name": "com.amazonaws.ecs.capability.task-iam-role"}, {"targetId": null, "targetType": null, "value": null, "name": "ecs.capability.execution-role-ecr-pull"}, {"targetId": null, "targetType": null, "value": null, "name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"targetId": null, "targetType": null, "value": null, "name": "ecs.capability.task-eni"}], "pidMode": null, "requiresCompatibilities": ["FARGATE"], "networkMode": "awsvpc", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": null}, "cpu": "512", "revision": 1, "status": "ACTIVE", "inferenceAccelerators": null, "proxyConfiguration": null, "volumes": []}