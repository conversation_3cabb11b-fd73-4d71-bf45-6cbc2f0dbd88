export const technicianFeedbackForm = {
  originalFields: [
    {
      id: "interview_completed",
      key: "interview_completed",
      element: "RadioButtons",
      field: "Interview",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "d5gq5",
            text: "Did you attend the interview ?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "6318d3f7-12c5-4dc3-8fa1-43750b8bb23d",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "d09a9d95-bad8-49de-9111-6397023307ff",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "document_verification",
      element: "RadioButtons",
      field: "Document Verification",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "f6jpv",
            text: "Did the onboarding team check your documents (<PERSON><PERSON>har card, bank statement, etc.) ?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 13,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 13,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 13,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 13,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "79782bbc-3711-43da-85c0-5f2979a86354",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "94e6036b-7482-4f96-82e0-5405d86cbef0",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "app_assessment",
      element: "RadioButtons",
      field: "App Assessment",
      wrapperClassName: "disable-inline-flex",
      required: true,
      label: {
        blocks: [
          {
            key: "9lp8d",
            text: "Did you complete the test on the Wify Partner app ?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 14,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 14,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 14,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 14,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "17209ef4-4c7c-42cf-a6c6-e8ec43058a53",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "6ee083c5-578e-466e-b3eb-a1693d244e4e",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "training_completed",
      element: "RadioButtons",
      field: "Training",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "1ahht",
            text: " Did you attend any training sessions ?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 8,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 8,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 8,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 8,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "4f447ad1-a1df-4b7c-ae7a-70eae711ce97",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "b7b7bbf8-6926-4640-9afb-a7235db3c4ac",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "soft_skills_training",
      element: "RadioButtons",
      field: "Soft Skills Training",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "8dqrv",
            text: "Did you get soft skills training (like how to talk to customers) ?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 20,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 20,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 20,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 20,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "7d6441b5-7e47-4aa9-b06e-39cd26a4224e",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "0c49ae28-fb0a-462d-a53f-124699e64f28",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "tshirt_bag_handed",
      element: "RadioButtons",
      field: "T-Shirt and Bag Handed Over",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "8fqd1",
            text: "Did you receive the company T-Shirt and bag ?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 27,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 27,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 27,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 27,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "11e6bc63-70c2-4b7b-8844-047cc3c5dc4c",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "124cd854-4ebc-4236-9261-0e3efe214bfe",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "toolkit_assigned",
      element: "RadioButtons",
      field: "Toolkit Assigned",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "4eshs",
            text: "Did you get your work tools (toolkit) ?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 16,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 16,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 16,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 16,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "efcd7c50-10b7-43e8-aa37-71840c30f18c",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "fc38d79e-ceb8-4afa-bba8-c974fcddab4f",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "app_training",
      element: "RadioButtons",
      field: "App Training",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "8suc9",
            text: "Did someone show you how to use the app for Jobs and leaves ?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 12,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 12,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 12,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 12,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "67045ea1-a114-4c86-ac47-ce020bf60cd4",
          label: "Yes ",
          value: "true",
          checked: false
        },
        {
          id: "ad2cc452-d9af-4654-911f-02c62bc991bb",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "onboarding-rating",
      element: "Rating",
      field: "Rating",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "4johh",
            text: "How was your entire joining experience ?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 40,
                style: "BOLD"
              },
              {
                offset: 0,
                length: 40,
                style: "fontsize-18"
              },
              {
                offset: 0,
                length: 14,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 14,
                style: "bgcolor-rgb(249,250,251)"
              },

              {
                offset: 0,
                length: 14,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      value: 0,
      numberOfStars: 5,
      initialText: "",
      lambdaArn: ""
    }
  ],
  translatedFields: [
    {
      key: "interview_completed",
      required: true,
      field: "Interview",
      label: "Did you attend the interview ?",
      widget: "radio-group",
      wrapperClassName: "disable-inline-flex",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "document_verification",
      required: true,
      field: "Document Verification",
      label: "Did the onboarding team check your documents (Aadhar card, bank statement, etc.) ?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "app_assessment",
      required: true,
      field: "App Assessment",
      label: "Did you complete the test on the Wify Partner app ?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "training_completed",
      field: "Training",
      required: true,
      label: "Did you attend any training sessions ?",
      widget: "radio-group",
      wrapperClassName: "disable-inline-flex",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "soft_skills_training",
      required: true,
      field: "Soft Skills Training",
      label: "Did you get soft skills training (like how to talk to customers) ?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "tshirt_bag_handed",
      required: true,
      field: "T-Shirt and Bag Handed Over",
      label: "Did you receive the company T-Shirt and bag ?",
      widget: "radio-group",
      wrapperClassName: "disable-inline-flex",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "toolkit_assigned",
      required: true,
      field: "Toolkit Assigned",
      label: "Did you get your work tools (toolkit) ?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "app_training",
      required: true,
      field: "App Training",
      label: "Did someone show you how to use the app for Jobs and leaves ?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "onboarding-rating",
      field: "Rating",
      required: true,
      label: "How was your entire joining experience ?",
      wrapperClassName: "disable-inline-flex",
      cust_widget: "Rating",
      widgetProps: {
        count: 5
      }
    }
  ]
};

export const technicianFeedbackFormHinglish = {
  originalFields: [
    {
      id: "interview_completed",
      key: "interview_completed",
      element: "RadioButtons",
      field: "Interview",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "d5gq5",
            text: "Kya aap interview mein aaye the?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "6318d3f7-12c5-4dc3-8fa1-43750b8bb23d",
          label: "Haan",
          value: "true",
          checked: false
        },
        {
          id: "d09a9d95-bad8-49de-9111-6397023307ff",
          label: "Nahi",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "document_verification",
      element: "RadioButtons",
      field: "Document Verification",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "f6jpv",
            text: "Onboarding team ne aapke documents (Aadhar, bank statement, etc.) check kiye the?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "79782bbc-3711-43da-85c0-5f2979a86354",
          label: "Haan",
          value: "true",
          checked: false
        },
        {
          id: "94e6036b-7482-4f96-82e0-5405d86cbef0",
          label: "Nahi",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "app_assessment",
      element: "RadioButtons",
      field: "App Assessment",
      wrapperClassName: "disable-inline-flex",
      required: true,
      label: {
        blocks: [
          {
            key: "9lp8d",
            text: "Kya aapne Wify Partner app par test diya tha?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "17209ef4-4c7c-42cf-a6c6-e8ec43058a53",
          label: "Haan",
          value: "true",
          checked: false
        },
        {
          id: "6ee083c5-578e-466e-b3eb-a1693d244e4e",
          label: "Nahi",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "training_completed",
      element: "RadioButtons",
      field: "Training",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "1ahht",
            text: "Kya aapne training session attend kiya tha?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "4f447ad1-a1df-4b7c-ae7a-70eae711ce97",
          label: "Haan",
          value: "true",
          checked: false
        },
        {
          id: "b7b7bbf8-6926-4640-9afb-a7235db3c4ac",
          label: "Nahi",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "soft_skills_training",
      element: "RadioButtons",
      field: "Soft Skills Training",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "8dqrv",
            text: "Kya aapko soft skills training mili thi (jaise customer se baat kaise kare)?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "7d6441b5-7e47-4aa9-b06e-39cd26a4224e",
          label: "Haan",
          value: "true",
          checked: false
        },
        {
          id: "0c49ae28-fb0a-462d-a53f-124699e64f28",
          label: "Nahi",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "tshirt_bag_handed",
      element: "RadioButtons",
      field: "T-Shirt and Bag Handed Over",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "8fqd1",
            text: "Kya aapko company ka T-Shirt aur bag mila tha?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "11e6bc63-70c2-4b7b-8844-047cc3c5dc4c",
          label: "Haan",
          value: "true",
          checked: false
        },
        {
          id: "124cd854-4ebc-4236-9261-0e3efe214bfe",
          label: "Nahi",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "toolkit_assigned",
      element: "RadioButtons",
      field: "Toolkit Assigned",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "4eshs",
            text: "Kya aapko kaam ke tools (toolkit) mil gaye the?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "efcd7c50-10b7-43e8-aa37-71840c30f18c",
          label: "Haan",
          value: "true",
          checked: false
        },
        {
          id: "fc38d79e-ceb8-4afa-bba8-c974fcddab4f",
          label: "Nahi",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "app_training",
      element: "RadioButtons",
      field: "App Training",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "8suc9",
            text: "Kya kisi ne aapko app ka use karna sikhaya tha (Jobs/leaves ke liye)?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "67045ea1-a114-4c86-ac47-ce020bf60cd4",
          label: "Haan",
          value: "true",
          checked: false
        },
        {
          id: "ad2cc452-d9af-4654-911f-02c62bc991bb",
          label: "Nahi",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "onboarding-rating",
      element: "Rating",
      field: "Rating",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "4johh",
            text: "Aapka poora joining experience kaisa tha?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      value: 0,
      numberOfStars: 5,
      initialText: "",
      lambdaArn: ""
    }
  ],
  translatedFields: [
    {
      key: "interview_completed",
      required: true,
      field: "Interview",
      label: "Kya aap interview mein aaye the?",
      widget: "radio-group",
      wrapperClassName: "disable-inline-flex",
      forwardRef: true,
      options: [
        {
          label: "Haan",
          value: true
        },
        {
          label: "Nahi",
          value: false
        }
      ]
    },
    {
      key: "document_verification",
      required: true,
      field: "Document Verification",
      label: "Onboarding team ne aapke documents (Aadhar, bank statement, etc.) check kiye the?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "Haan",
          value: true
        },
        {
          label: "Nahi",
          value: false
        }
      ]
    },
    {
      key: "app_assessment",
      required: true,
      field: "App Assessment",
      label: "Kya aapne Wify Partner app par test diya tha?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "Haan",
          value: true
        },
        {
          label: "Nahi",
          value: false
        }
      ]
    },
    {
      key: "training_completed",
      field: "Training",
      required: true,
      label: "Kya aapne training session attend kiya tha?",
      widget: "radio-group",
      wrapperClassName: "disable-inline-flex",
      forwardRef: true,
      options: [
        {
          label: "Haan",
          value: true
        },
        {
          label: "Nahi",
          value: false
        }
      ]
    },
    {
      key: "soft_skills_training",
      required: true,
      field: "Soft Skills Training",
      label: "Kya aapko soft skills training mili thi (jaise customer se baat kaise kare)?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "Haan",
          value: true
        },
        {
          label: "Nahi",
          value: false
        }
      ]
    },
    {
      key: "tshirt_bag_handed",
      required: true,
      field: "T-Shirt and Bag Handed Over",
      label: "Kya aapko company ka T-Shirt aur bag mila tha?",
      widget: "radio-group",
      wrapperClassName: "disable-inline-flex",
      forwardRef: true,
      options: [
        {
          label: "Haan",
          value: true
        },
        {
          label: "Nahi",
          value: false
        }
      ]
    },
    {
      key: "toolkit_assigned",
      required: true,
      field: "Toolkit Assigned",
      label: "Kya aapko kaam ke tools (toolkit) mil gaye the?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "Haan",
          value: true
        },
        {
          label: "Nahi",
          value: false
        }
      ]
    },
    {
      key: "app_training",
      required: true,
      field: "App Training",
      label: "Kya kisi ne aapko app ka use karna sikhaya tha (नौकरी/अवकाश के लिए)?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "Haan",
          value: true
        },
        {
          label: "Nahi",
          value: false
        }
      ]
    },
    {
      key: "onboarding-rating",
      field: "Rating",
      required: true,
      label: "Aapka poora joining experience kaisa tha?",
      wrapperClassName: "disable-inline-flex",
      cust_widget: "Rating",
      widgetProps: {
        count: 5
      }
    }
  ]
};

export const technicianFeedbackFormPureHindi = {
  originalFields: [
    {
      id: "interview_completed",
      key: "interview_completed",
      element: "RadioButtons",
      field: "इंटरव्यू",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "d5gq5",
            text: "क्या आपका इंटरव्यू हुआ था?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "6318d3f7-12c5-4dc3-8fa1-43750b8bb23d",
          label: "हाँ",
          value: "true",
          checked: false
        },
        {
          id: "d09a9d95-bad8-49de-9111-6397023307ff",
          label: "नहीं",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "document_verification",
      element: "RadioButtons",
      field: "डॉक्युमेंट वेरिफिकेशन",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "f6jpv",
            text: "क्या ऑनबोर्डिंग टीम ने आपके डॉक्युमेंट्स (आधार, बैंक स्टेटमेंट आदि) चेक किया था?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "79782bbc-3711-43da-85c0-5f2979a86354",
          label: "हाँ",
          value: "true",
          checked: false
        },
        {
          id: "94e6036b-7482-4f96-82e0-5405d86cbef0",
          label: "नहीं",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "app_assessment",
      element: "RadioButtons",
      field: "ऐप असेसमेंट",
      wrapperClassName: "disable-inline-flex",
      required: true,
      label: {
        blocks: [
          {
            key: "9lp8d",
            text: "क्या आपने Wify Partner app par test diya tha?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "17209ef4-4c7c-42cf-a6c6-e8ec43058a53",
          label: "हाँ",
          value: "true",
          checked: false
        },
        {
          id: "6ee083c5-578e-466e-b3eb-a1693d244e4e",
          label: "नहीं",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "training_completed",
      element: "RadioButtons",
      field: "ट्रेनिंग",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "1ahht",
            text: "क्या आपने कोई ट्रेनिंग सेशन अटेंड किया था?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "4f447ad1-a1df-4b7c-ae7a-70eae711ce97",
          label: "हाँ",
          value: "true",
          checked: false
        },
        {
          id: "b7b7bbf8-6926-4640-9afb-a7235db3c4ac",
          label: "नहीं",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "soft_skills_training",
      element: "RadioButtons",
      field: "सॉफ्ट स्किल्स ट्रेनिंग",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "8dqrv",
            text: "क्या आपको सॉफ्ट स्किल्स ट्रेनिंग (जैसे कस्टमर से बात करना) मिला था?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "7d6441b5-7e47-4aa9-b06e-39cd26a4224e",
          label: "हाँ",
          value: "true",
          checked: false
        },
        {
          id: "0c49ae28-fb0a-462d-a53f-124699e64f28",
          label: "नहीं",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "tshirt_bag_handed",
      element: "RadioButtons",
      field: "टी-शर्ट और बैग",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "8fqd1",
            text: "क्या आपको कंपनी का टी-शर्ट और बैग मिला था?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "11e6bc63-70c2-4b7b-8844-047cc3c5dc4c",
          label: "हाँ",
          value: "true",
          checked: false
        },
        {
          id: "124cd854-4ebc-4236-9261-0e3efe214bfe",
          label: "नहीं",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "toolkit_assigned",
      element: "RadioButtons",
      field: "टूलकिट",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "4eshs",
            text: "क्या आपको काम के टूल्स मिले हैं?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "efcd7c50-10b7-43e8-aa37-71840c30f18c",
          label: "हाँ",
          value: "true",
          checked: false
        },
        {
          id: "fc38d79e-ceb8-4afa-bba8-c974fcddab4f",
          label: "नहीं",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "app_training",
      element: "RadioButtons",
      field: "ऐप ट्रेनिंग",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "8suc9",
            text: "क्या किसी ने आपको दिखाया कि जॉब्स और लीव्स के लिए ऐप का इस्तेमाल कैसे करना है?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "67045ea1-a114-4c86-ac47-ce020bf60cd4",
          label: "हाँ",
          value: "true",
          checked: false
        },
        {
          id: "ad2cc452-d9af-4654-911f-02c62bc991bb",
          label: "नहीं",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "onboarding-rating",
      element: "Rating",
      field: "जॉइनिंग एक्सपीरियंस",
      required: true,
      wrapperClassName: "disable-inline-flex",
      label: {
        blocks: [
          {
            key: "4johh",
            text: "आपका WIFY परिवार से जुड़ने का अनुभव कैसा रहा?",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      value: 0,
      numberOfStars: 5,
      initialText: "",
      lambdaArn: ""
    }
  ],
  translatedFields: [
    {
      key: "interview_completed",
      required: true,
      field: "इंटरव्यू",
      label: "क्या आपका इंटरव्यू हुआ था?",
      widget: "radio-group",
      wrapperClassName: "disable-inline-flex",
      forwardRef: true,
      options: [
        {
          label: "हाँ",
          value: true
        },
        {
          label: "नहीं",
          value: false
        }
      ]
    },
    {
      key: "document_verification",
      required: true,
      field: "डॉक्युमेंट वेरिफिकेशन",
      label: "क्या ऑनबोर्डिंग टीम ने आपके डॉक्युमेंट्स (आधार, बैंक स्टेटमेंट आदि) चेक किया था?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "हाँ",
          value: true
        },
        {
          label: "नहीं",
          value: false
        }
      ]
    },
    {
      key: "app_assessment",
      required: true,
      field: "ऐप असेसमेंट",
      label: "क्या आपने Wify Partner ऐप पर टेस्ट दिया था?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "हाँ",
          value: true
        },
        {
          label: "नहीं",
          value: false
        }
      ]
    },
    {
      key: "training_completed",
      field: "ट्रेनिंग",
      required: true,
      label: "क्या आपने कोई ट्रेनिंग सेशन अटेंड किया था?",
      widget: "radio-group",
      wrapperClassName: "disable-inline-flex",
      forwardRef: true,
      options: [
        {
          label: "हाँ",
          value: true
        },
        {
          label: "नहीं",
          value: false
        }
      ]
    },
    {
      key: "soft_skills_training",
      required: true,
      field: "सॉफ्ट स्किल्स ट्रेनिंग",
      label: "क्या आपको सॉफ्ट स्किल्स ट्रेनिंग (जैसे कस्टमर से बात करना) मिला था?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "हाँ",
          value: true
        },
        {
          label: "नहीं",
          value: false
        }
      ]
    },
    {
      key: "tshirt_bag_handed",
      required: true,
      field: "टी-शर्ट और बैग",
      label: "क्या आपको कंपनी का टी-शर्ट और बैग मिला था?",
      widget: "radio-group",
      wrapperClassName: "disable-inline-flex",
      forwardRef: true,
      options: [
        {
          label: "हाँ",
          value: true
        },
        {
          label: "नहीं",
          value: false
        }
      ]
    },
    {
      key: "toolkit_assigned",
      required: true,
      field: "टूलकिट",
      label: "क्या आपको काम के टूल्स मिले हैं?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "हाँ",
          value: true
        },
        {
          label: "नहीं",
          value: false
        }
      ]
    },
    {
      key: "app_training",
      required: true,
      field: "ऐप ट्रेनिंग",
      label: "क्या किसी ने आपको दिखाया कि जॉब्स और लीव्स के लिए ऐप का इस्तेमाल कैसे करना है?",
      widget: "radio-group",
      forwardRef: true,
      wrapperClassName: "disable-inline-flex",
      options: [
        {
          label: "हाँ",
          value: true
        },
        {
          label: "नहीं",
          value: false
        }
      ]
    },
    {
      key: "onboarding-rating",
      field: "जॉइनिंग एक्सपीरियंस",
      required: true,
      label: "आपका WIFY परिवार से जुड़ने का अनुभव कैसा रहा?",
      wrapperClassName: "disable-inline-flex",
      cust_widget: "Rating",
      widgetProps: {
        count: 5
      }
    }
  ]
};
