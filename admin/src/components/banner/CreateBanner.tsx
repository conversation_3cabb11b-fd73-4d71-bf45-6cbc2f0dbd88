import { BackwardOutlined } from "@ant-design/icons";
import { Button, Checkbox, Form, Input, Radio, Select, notification } from "antd";
import { useForm } from "antd/es/form/Form";
import { isEmpty } from "lodash";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { BannerConfigsDocument, useCreateBannerConfigMutation } from "../../__generated__";
import { banner_images, banner_links } from "../../utils/utils";
import ImageUploaderS3 from "../fileUploader/ImageUploadS3";
import Banner from "./Banner";

function CreateBanner() {
  const [banner_form] = useForm();
  const [showCTALink, setShowCTALink] = useState<boolean>(true);
  const [preview, setPreviewContentData] = useState({
    title: "",
    description: "",
    hero_image: banner_images[0].url,
    image: "",
    cta_link: ""
  });

  //navigate
  const navigate = useNavigate();

  //GraphQL Query
  const [createBanner, { loading: createLoading }] = useCreateBannerConfigMutation();

  const handleFormSubmit = async () => {
    try {
      await createBanner({
        variables: {
          data: {
            title: banner_form.getFieldValue("title"),
            description: banner_form.getFieldValue("description"),
            hero_image: isEmpty(banner_form.getFieldValue("hero_image"))
              ? banner_images[0].url
              : banner_form.getFieldValue("hero_image"),
            cta_required: !showCTALink,
            cta_link: banner_form.getFieldValue("cta_link"),
            image: banner_form.getFieldValue("image"),
            cta_text: banner_form.getFieldValue("cta_text") || undefined
          }
        },
        onCompleted: () => {
          navigate("/dashboard/banners");
          notification.success({
            message: "Banner Created Successfully"
          });
        },
        refetchQueries: [BannerConfigsDocument],
        onError: (error) => {
          notification.error({
            message: "Error while creating banner \n" + error.message
          });
        }
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handlePreview = async () => {
    try {
      banner_form.setFieldsValue({
        title: banner_form.getFieldValue("title"),
        description: banner_form.getFieldValue("description"),
        hero_image: banner_form.getFieldValue("hero_image"),
        image: banner_form.getFieldValue("image")
      });

      // rerender the preview content
      setPreviewContentData({
        title: banner_form.getFieldValue("title"),
        description: banner_form.getFieldValue("description"),
        hero_image: banner_form.getFieldValue("hero_image"),
        image: banner_form.getFieldValue("image"),
        cta_link: banner_form.getFieldValue("cta_link")
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleGetCtaTextFromLink = () => {
    if (banner_form.getFieldValue("cta_link")) {
      const cta_text = banner_links.find(
        (link) => link.value === banner_form.getFieldValue("cta_link")
      )?.label;
      if (cta_text) {
        banner_form.setFieldsValue({
          cta_text
        });
      }
    }
  };
  return (
    <div>
      <div className="p-8">
        <div className="">
          <div className="flex items-center gap-3 bg-grey border-purple-400 shadow-sm p-2 rounded-md border border-solid border-l-4 border-r-0 border-b-0 border-t-0 mb-4">
            <img src={"/images/vector.png"} alt="icon" />
            <div className="text-2xl">Create Banner </div>
          </div>
        </div>
        <Button
          onClick={() => {
            const form = Object.values(banner_form.getFieldsValue()).some(
              (val) => val !== undefined
            );

            if (form) {
              if (!window.confirm("You have unsaved changes, are you sure you want to leave?")) {
                return;
              }
            }
            navigate("/dashboard/banners");
          }}
          className="mb-2 text-colorPrimary"
          icon={<BackwardOutlined />}
        >
          Go back
        </Button>
        <div className="grid grid-cols-6 gap-4 h-[80vh]">
          <div className="col-span-3 lg:col-span-4 border border-solid border-slate-200 p-4 rounded bg-white h-full overflow-auto overflow-x-hidden overflow-y-auto ">
            <Form
              layout="vertical"
              form={banner_form}
              onFinish={handleFormSubmit}
              onChange={handlePreview}
              className="mt-5"
            >
              <Form.Item
                name={"title"}
                label={"Banner Title"}
                rules={[{ required: true, message: "Enter Banner Title" }]}
                extra="Limit to 100 characters"
              >
                <Input maxLength={100} placeholder="Enter Banner Title" />
              </Form.Item>
              <Form.Item name={"hero_image"}>
                <div className="grid grid-cols-4 gap-3 ">
                  <Radio.Group
                    value={banner_form.getFieldValue("hero_image")}
                    defaultValue={banner_images[0].url}
                  >
                    <div className="flex  gap-2 ">
                      {banner_images.map((img) => (
                        <label key={img.id} className=" items-center cursor-pointer relative ">
                          <Radio
                            value={img.url}
                            onChange={(e) => {
                              banner_form.setFieldsValue({ hero_image: e.target.value });
                              handlePreview();
                            }}
                            name="image"
                            className="absolute top-0 right-0 "
                          />
                          <div className="border border-solid border-slate-200 p-2 rounded ">
                            <img
                              className="object-cover rounded-md  w-[160px] h-[90px]"
                              src={img.url}
                              alt={"no image"}
                              width={64}
                              height={64}
                            />
                          </div>
                        </label>
                      ))}
                    </div>
                  </Radio.Group>
                </div>
              </Form.Item>
              <Form.Item
                name={"description"}
                label={"Banner Content"}
                rules={[{ required: true, message: "Enter Banner Description " }]}
              >
                <Input placeholder="Write your description" />
              </Form.Item>
              <Form.Item name={"image"}>
                <ImageUploaderS3
                  setImageUrl={(url) => {
                    if (url === "deleted") {
                      banner_form.setFieldValue("image", undefined);
                      handlePreview();
                    } else {
                      banner_form.setFieldValue("image", url);
                      handlePreview();
                    }
                  }}
                  text={"Upload an image"}
                  imgUrl={banner_form.getFieldValue("image")}
                />
              </Form.Item>
              <Form.Item>
                <Checkbox
                  onChange={(e) => {
                    setShowCTALink(!showCTALink);
                    if (e.target.value === false) {
                      banner_form.setFieldsValue({ cta_link: "" });
                    }
                  }}
                >
                  CTA Required
                </Checkbox>
              </Form.Item>
              <Form.Item
                name={"cta_link"}
                rules={[{ required: !showCTALink, message: "CTA Link is required" }]}
              >
                <Select
                  onChange={() => {
                    handlePreview();

                    handleGetCtaTextFromLink();
                  }}
                  disabled={showCTALink}
                  options={banner_links}
                  placeholder="Select CTA"
                />
              </Form.Item>
            </Form>
            <div className="flex justify-center">
              <Button
                className="mx-auto"
                type="primary"
                loading={createLoading}
                onClick={() => {
                  banner_form.submit();
                }}
              >
                Save as Draft
              </Button>
            </div>
          </div>
          <div className="col-span-2 flex justify-center items-center ">
            <Banner
              bgUrl={preview.hero_image}
              title={preview.description}
              sideImgUrl={preview.image}
              ctaLink={preview.cta_link}
              ctaRequired={!showCTALink}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default CreateBanner;
