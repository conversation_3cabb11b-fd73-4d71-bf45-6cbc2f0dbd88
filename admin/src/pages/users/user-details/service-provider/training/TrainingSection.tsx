import { LuGraduationCap } from "react-icons/lu";
import TrainingCard from "./TrainingCard";

interface TrainingSectionProps {
  trainings: Training[];
  setActiveKey: (key: string) => void;
}
export type TrainingStatus = "PASS" | "NEED_MORE_TRAINING" | "FAIL";

export interface Training {
  id: number;
  name: string;
  durationDays: number;
  location: string;
  assessor: string;
  startDate: string;
  endDate: string;
  status: TrainingStatus;
  feedback?: string;
}

export const getStatusColor = (status: TrainingStatus): string => {
  switch (status) {
    case "PASS":
      return "bg-green-100 text-green-800";
    case "NEED_MORE_TRAINING":
      return "bg-yellow-100 text-yellow-800";
    case "FAIL":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const TrainingSection = ({ trainings }: TrainingSectionProps) => {
  return (
    <div className="bg-white rounded-lg shadow">
      <div
        style={{
          borderBottom: "1px solid #e5e7eb",
          borderBottomWidth: "1px",
          borderBottomColor: "#e5e7eb",
          borderBottomStyle: "solid"
        }}
        className="px-6 py-5 border-b border-gray-200"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <LuGraduationCap className="h-6 w-6 text-indigo-600" />
            <div className="text-xl font-semibold text-gray-900">Training</div>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="overflow-x-auto pb-4">
          <div className="inline-flex gap-4 min-w-full">
            {trainings.map((training) => (
              <div key={training.id} className="min-w-[400px]">
                <TrainingCard training={training} />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
export default TrainingSection;
