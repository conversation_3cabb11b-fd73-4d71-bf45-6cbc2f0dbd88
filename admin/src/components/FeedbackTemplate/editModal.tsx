import { FormOutlined, InfoCircleOutlined, LoadingOutlined } from "@ant-design/icons";
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  Modal,
  Popover,
  Row,
  Spin,
  Switch,
  Tabs,
  Typography
} from "antd";
import FormBuilder from "antd-form-builder";
import { useForm } from "antd/es/form/Form";
import TextArea from "antd/es/input/TextArea";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useGetSingleFeedbackTemplateQuery } from "../../__generated__";
import { createAntdFormMeta, validateForm<PERSON>son } from "../../utils/formbuilder.helper";
import Loading from "../loading/Loading";

const { Title, Text } = Typography;
const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

// Define a type for the FormBuilder meta prop that matches antd-form-builder's expected shape
interface FieldType {
  key: string;
  label?: string;
  widget?: string | React.ComponentType<any>;
  widgetProps?: Record<string, any>;
  options?: Array<{ label: string; value: string | number }>;
  colSpan?: number;
  required?: boolean;
  [key: string]: any;
}

interface EditModalProps {
  openModal: boolean;
  setOpenModal: (open: boolean) => void;
  handleUpdateTemplate: (data: FormData, includeOverallRating: boolean) => void;
  template_id: string;
  updateLoading: boolean;
}

export interface FormData {
  id: string;
  title: string;
  status: boolean;
  interviewer_feedback_enabled: boolean;
  interviewer_feedback_meta: string;
  interviewee_feedback_enabled: boolean;
  interviewee_feedback_meta: string;
}

const EditTemplateModal: React.FC<EditModalProps> = ({
  openModal,
  setOpenModal,
  handleUpdateTemplate,
  template_id,
  updateLoading
}) => {
  const [form] = useForm();
  const [previewForm] = Form.useForm();
  const { data, loading } = useGetSingleFeedbackTemplateQuery({
    variables: {
      templateId: parseInt(template_id)
    },
    skip: !template_id || !openModal,
    fetchPolicy: "network-only",
    notifyOnNetworkStatusChange: true
  });

  // State for interviewer tab
  const [jsonInput, setJsonInput] = useState("");
  const [previewData, setPreviewData] = useState<FieldType[]>([]);
  const [jsonError, setJsonError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("1");
  const [processingJson, setProcessingJson] = useState(false);
  const [fadeIn, setFadeIn] = useState(false);
  const [enableOverallRating, setEnableOverallRating] = useState<boolean>(false);

  // State for interviewee section
  const [intervieweeJsonInput, setIntervieweeJsonInput] = useState("");
  const [intervieweePreviewData, setIntervieweePreviewData] = useState<FieldType[]>([]);
  const [intervieweeJsonError, setIntervieweeJsonError] = useState<string | null>(null);
  const [intervieweeActiveTab, setIntervieweeActiveTab] = useState("1");
  const [intervieweeProcessingJson, setIntervieweeProcessingJson] = useState(false);
  const [intervieweeFadeIn, setIntervieweeFadeIn] = useState(false);
  const [enableIntervieweeSection, setEnableIntervieweeSection] = useState(false);

  const feedbackTemplate = data;

  // Main tab state
  const [mainTab, setMainTab] = useState("interviewer");

  useEffect(() => {
    const fetchSection = async () => {
      if (!feedbackTemplate) return;

      const {
        title,
        interviewee_feedback_enabled,
        interviewee_feedback_meta,
        interviewer_feedback_enabled,
        interviewer_feedback_meta,
        status,
        meta
      } = feedbackTemplate.getSingleFeedbackTemplate;

      // Check if there's a includeOverallRating flag in meta and set state
      const shouldIncludeOverallRating = meta?.includeOverallRating === true;
      setEnableOverallRating(shouldIncludeOverallRating);

      // Set form values
      form.setFieldsValue({
        title: title,
        status: status,
        interviewer_feedback_enabled: interviewer_feedback_enabled,
        interviewer_feedback_meta: interviewer_feedback_meta,
        interviewee_feedback_enabled: interviewee_feedback_enabled,
        interviewee_feedback_meta: interviewee_feedback_meta
      });

      // Update state variables
      if (interviewer_feedback_meta) {
        const jsonData = interviewer_feedback_meta || "";
        setJsonInput(jsonData);
        processJsonInputWithRating(jsonData, shouldIncludeOverallRating);
      }

      // Store interviewee JSON data even if the section is disabled
      if (interviewee_feedback_meta) {
        setIntervieweeJsonInput(interviewee_feedback_meta);
        processIntervieweeJsonInput(interviewee_feedback_meta);
      }
      setEnableIntervieweeSection(interviewee_feedback_enabled || false);
    };

    if (template_id && openModal) {
      fetchSection();
    }
  }, [template_id, data, openModal, form]);

  // Add a useEffect that resets form state when template_id changes
  useEffect(() => {
    // Reset all state variables when the template_id changes
    if (template_id) {
      setMainTab("interviewer");
      setJsonInput("");
      setPreviewData([]);
      setJsonError(null);
      setActiveTab("1");
      setFadeIn(false);
      setIntervieweeJsonInput("");
      setIntervieweePreviewData([]);
      setIntervieweeJsonError(null);
      setIntervieweeActiveTab("1");
      setIntervieweeFadeIn(false);
      setEnableIntervieweeSection(false);
      setEnableOverallRating(false);

      // Reset form
      form.resetFields();
      previewForm.resetFields();
    }
  }, [template_id, form, previewForm]);

  // Add a new function that takes the rating flag as a parameter
  const processJsonInputWithRating = (input: string, shouldAddRating: boolean) => {
    if (!input) {
      setPreviewData([]);
      setJsonError(null);
      return;
    }

    setProcessingJson(true);

    // Simulate processing animation
    setTimeout(() => {
      try {
        // Validate JSON structure
        const validation = validateFormJson(input);
        if (!validation.isValid) {
          setJsonError(validation.message || "Invalid JSON format");
          setPreviewData([]);
          setProcessingJson(false);
          return;
        }

        let processedInput = input;

        // Parse the input once to avoid multiple parses
        let parsedJson = JSON.parse(input);

        // First, let's remove any existing overall rating fields to avoid duplicates
        try {
          // Remove existing rating fields based on the JSON structure
          if (
            Array.isArray(parsedJson) &&
            parsedJson.length > 0 &&
            parsedJson[0].translatedFields
          ) {
            // Check if we already have a rating field
            const hasRatingField = parsedJson[0].translatedFields.some(
              (field: any) => field.key === "overall-rating" || field.label === "Overall Rating"
            );

            if (hasRatingField) {
              // Clone to avoid mutation
              const newInput = JSON.parse(input);
              // Filter out any fields with the key "overall-rating"
              newInput[0].translatedFields = newInput[0].translatedFields.filter(
                (field: any) => field.key !== "overall-rating" && field.label !== "Overall Rating"
              );
              if (newInput[0].originalFields) {
                newInput[0].originalFields = newInput[0].originalFields.filter(
                  (field: any) =>
                    field.id !== "overall-rating" &&
                    (!field.label ||
                      !field.label.blocks ||
                      !field.label.blocks.some((block: any) => block.text === "Overall Rating"))
                );
              }
              processedInput = JSON.stringify(newInput);
              // Update the parsed input for further processing
              parsedJson = JSON.parse(processedInput);
            }
          } else if (parsedJson.translatedFields) {
            // Check if we already have a rating field
            const hasRatingField = parsedJson.translatedFields.some(
              (field: any) => field.key === "overall-rating" || field.label === "Overall Rating"
            );

            if (hasRatingField) {
              // Clone to avoid mutation
              const newInput = JSON.parse(input);
              // Filter out any fields with the key "overall-rating"
              newInput.translatedFields = newInput.translatedFields.filter(
                (field: any) => field.key !== "overall-rating" && field.label !== "Overall Rating"
              );
              if (newInput.originalFields) {
                newInput.originalFields = newInput.originalFields.filter(
                  (field: any) =>
                    field.id !== "overall-rating" &&
                    (!field.label ||
                      !field.label.blocks ||
                      !field.label.blocks.some((block: any) => block.text === "Overall Rating"))
                );
              }
              processedInput = JSON.stringify(newInput);
              // Update the parsed input for further processing
              parsedJson = JSON.parse(processedInput);
            }
          }
        } catch (error) {
          console.error("Edit modal - Error removing existing rating field:", error);
        }

        // Add rating field based on the passed flag, not the state
        if (shouldAddRating) {
          try {
            // Create rating field metadata
            const originalMeta = {
              id: "overall-rating",
              element: "Rating",
              required: true,
              label: {
                blocks: [
                  {
                    key: "30rh4",
                    text: "Overall Rating",
                    type: "unstyled",
                    depth: 0
                  }
                ]
              }
            };

            const translatedMeta = {
              key: "overall-rating",
              required: true,
              label: "Overall Rating",
              cust_widget: "Rating",
              widgetProps: {
                count: 5
              }
            };

            // Check the structure to determine how to add the rating
            if (Array.isArray(parsedJson)) {
              // If it's an array with at least one object that has translatedFields
              if (parsedJson.length > 0 && parsedJson[0].translatedFields) {
                console.log("Array structure with translatedFields");
                // Use the already parsed input - no need to parse again
                const newInput = { ...parsedJson };
                newInput[0].translatedFields.push(translatedMeta);
                if (newInput[0].originalFields) {
                  newInput[0].originalFields.push(originalMeta);
                }
                processedInput = JSON.stringify(newInput);
              }
              // If it's just a plain array, add properly
              else {
                console.log("Plain array structure");
                const newInput = {
                  translatedFields: [...parsedJson, translatedMeta],
                  originalFields: [originalMeta]
                };
                processedInput = JSON.stringify(newInput);
              }
            }
            // If it has translatedFields directly (not in an array)
            else if (parsedJson.translatedFields) {
              console.log("Object with translatedFields");
              const newInput = { ...parsedJson };
              newInput.translatedFields.push(translatedMeta);
              if (newInput.originalFields) {
                newInput.originalFields.push(originalMeta);
              }
              processedInput = JSON.stringify(newInput);
            }
            // Otherwise treat it as a new structure
            else {
              console.log("Unknown structure, creating new format");
              const newInput = {
                translatedFields: [parsedJson, translatedMeta],
                originalFields: [originalMeta]
              };
              processedInput = JSON.stringify(newInput);
            }

            // Update the jsonInput state and form field value
            setJsonInput(processedInput);
            form.setFieldValue("interviewer_feedback_meta", processedInput);
          } catch (error) {
            console.error("Edit modal - Error adding rating to JSON:", error);
            // Continue with original input if there's an error
            processedInput = input;
          }
        } else {
          // Since we've already removed any rating fields earlier, just update the form
          setJsonInput(processedInput);
          form.setFieldValue("interviewer_feedback_meta", processedInput);
        }

        // Generate preview data from the processed input
        console.log("Edit modal - Final input for createAntdFormMeta:", processedInput);
        const formMeta = createAntdFormMeta(processedInput) as FieldType[];
        console.log("Edit modal - Generated form meta:", formMeta);

        // Animation for updating preview - fade out, update, fade in
        setFadeIn(false);
        setTimeout(() => {
          setPreviewData(formMeta);
          setJsonError(null);
          setProcessingJson(false);

          // Auto-switch to preview tab for valid JSON with fields
          if (formMeta.length > 0) {
            setActiveTab("2");
            // Fade in effect after tab change
            setTimeout(() => setFadeIn(true), 100);
          }
        }, 300);
      } catch (error) {
        console.error("Edit modal - Error processing JSON:", error);
        setJsonError("Invalid JSON format");
        setPreviewData([]);
        setProcessingJson(false);
      }
    }, 300); // Simulate processing time
  };

  // Handle input change
  const handleJsonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const input = e.target.value;
    setJsonInput(input);

    // Always update form value - fix field name
    form.setFieldValue("interviewer_feedback_meta", input);

    // Validate and update the field
    if (input) {
      try {
        const validation = validateFormJson(input);
        if (validation.isValid) {
          form.setFields([
            {
              name: "interviewer_feedback_meta",
              errors: []
            }
          ]);
        }
      } catch (error) {
        // Silent catch - errors will be handled by form validator
      }
    }

    // Only process if we have substantial input (at least 20 chars)
    if (input.length > 20) {
      // Use the current enableOverallRating state
      processJsonInputWithRating(input, enableOverallRating);
    }
  };

  // Process interviewee JSON input
  const processIntervieweeJsonInput = (input: string) => {
    if (!input) {
      setIntervieweePreviewData([]);
      setIntervieweeJsonError(null);
      return;
    }

    setIntervieweeProcessingJson(true);

    // Simulate processing animation
    setTimeout(() => {
      try {
        // Validate JSON structure
        const validation = validateFormJson(input);
        if (!validation.isValid) {
          setIntervieweeJsonError(validation.message || "Invalid JSON format");
          setIntervieweePreviewData([]);
          setIntervieweeProcessingJson(false);
          return;
        }

        // Generate preview data
        const formMeta = createAntdFormMeta(input) as FieldType[];

        console.log("Edit modal - Generating interviewee form preview");

        // Animation for updating preview - fade out, update, fade in
        setIntervieweeFadeIn(false);
        setTimeout(() => {
          setIntervieweePreviewData(formMeta);
          setIntervieweeJsonError(null);
          setIntervieweeProcessingJson(false);

          // Auto-switch to preview tab for valid JSON with fields
          if (formMeta.length > 0) {
            setIntervieweeActiveTab("2");
            // Fade in effect after tab change
            setTimeout(() => setIntervieweeFadeIn(true), 100);
          }
        }, 300);
      } catch (error) {
        setIntervieweeJsonError("Invalid JSON format");
        setIntervieweePreviewData([]);
        setIntervieweeProcessingJson(false);
      }
    }, 300); // Simulate processing time
  };

  // Handle input change for interviewee JSON
  const handleIntervieweeJsonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const input = e.target.value;
    setIntervieweeJsonInput(input);

    // Always update form value - fix field name
    form.setFieldValue("interviewee_feedback_meta", input);

    // Validate and update the field
    if (input) {
      try {
        const validation = validateFormJson(input);
        if (validation.isValid) {
          form.setFields([
            {
              name: "interviewee_feedback_meta",
              errors: []
            }
          ]);
        }
      } catch (error) {
        // Silent catch - errors will be handled by form validator and processIntervieweeJsonInput
      }
    }

    // Only process if we have substantial input (at least 20 chars)
    if (input.length > 20) {
      processIntervieweeJsonInput(input);
    }
  };

  // Handle toggle of interviewee feedback section
  const handleEnableIntervieweeFeedback = (checked: boolean) => {
    setEnableIntervieweeSection(checked);
    form.setFieldValue("interviewee_feedback_enabled", checked);

    // If enabling, switch to interviewee tab and initialize if needed
    if (checked) {
      setMainTab("interviewee");

      // Get the existing interviewee JSON data from form if available
      const existingJson = form.getFieldValue("interviewee_feedback_meta");
      if (existingJson && !intervieweeJsonInput) {
        setIntervieweeJsonInput(existingJson);
        processIntervieweeJsonInput(existingJson);
      }
    } else {
      // If disabling, switch back to interviewer tab
      setMainTab("interviewer");
    }
  };

  // Effect to handle fade in animation when tab changes to preview
  useEffect(() => {
    if (activeTab === "2" && previewData.length > 0) {
      setTimeout(() => setFadeIn(true), 100);
    } else {
      setFadeIn(false);
    }
  }, [activeTab, previewData]);

  // Effect to handle fade in animation for interviewee preview
  useEffect(() => {
    if (intervieweeActiveTab === "2" && intervieweePreviewData.length > 0) {
      setTimeout(() => setIntervieweeFadeIn(true), 100);
    } else {
      setIntervieweeFadeIn(false);
    }
  }, [intervieweeActiveTab, intervieweePreviewData]);

  const renderPreview = () => {
    if (processingJson) {
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            padding: "48px",
            textAlign: "center"
          }}
        >
          <Spin indicator={antIcon} />
          <Text style={{ marginTop: "16px" }}>Processing JSON...</Text>
        </div>
      );
    }

    if (jsonError) {
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            padding: "48px",
            textAlign: "center"
          }}
        >
          <InfoCircleOutlined
            style={{ fontSize: "48px", color: "#f5222d", marginBottom: "16px" }}
          />
          <Title level={4} style={{ margin: "8px 0", color: "#f5222d" }}>
            JSON Error
          </Title>
          <Text type="secondary">{jsonError}</Text>
          <Button type="primary" onClick={() => setActiveTab("1")} style={{ marginTop: "16px" }}>
            Back to Editor
          </Button>
        </div>
      );
    }

    if (previewData.length === 0) {
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            padding: "48px",
            textAlign: "center"
          }}
        >
          <FormOutlined style={{ fontSize: "48px", color: "#8c8c8c", marginBottom: "16px" }} />
          <Title level={4} style={{ margin: "8px 0" }}>
            No Preview Available
          </Title>
          <Text type="secondary">Please enter valid JSON to see a preview of your form.</Text>
        </div>
      );
    }

    return (
      <div style={{ height: "100%" }}>
        <div
          style={{
            background: "white",
            borderRadius: "8px",
            border: "1px solid #f0f0f0",
            overflow: "hidden",
            height: "100%",
            opacity: fadeIn ? 1 : 0,
            transition: "opacity 0.3s ease-in-out"
          }}
        >
          <div
            style={{ padding: "16px", borderBottom: "1px solid #f0f0f0", background: "#f5f5f5" }}
          >
            <Title level={4} style={{ margin: 0 }}>
              Form Preview
            </Title>
          </div>
          <div style={{ padding: "24px", overflowY: "auto", maxHeight: "490px" }}>
            <Form form={previewForm} layout="vertical">
              <FormBuilder form={previewForm} meta={previewData} />
            </Form>
          </div>
        </div>
      </div>
    );
  };

  // Render interviewee preview
  const renderIntervieweePreview = () => {
    if (intervieweeProcessingJson) {
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            padding: "48px",
            textAlign: "center"
          }}
        >
          <Spin indicator={antIcon} />
          <Text style={{ marginTop: "16px" }}>Processing JSON...</Text>
        </div>
      );
    }

    if (intervieweeJsonError) {
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            padding: "48px",
            textAlign: "center"
          }}
        >
          <InfoCircleOutlined
            style={{ fontSize: "48px", color: "#f5222d", marginBottom: "16px" }}
          />
          <Title level={4} style={{ margin: "8px 0", color: "#f5222d" }}>
            JSON Error
          </Title>
          <Text type="secondary">{intervieweeJsonError}</Text>
          <Button
            type="primary"
            onClick={() => setIntervieweeActiveTab("1")}
            style={{ marginTop: "16px" }}
          >
            Back to Editor
          </Button>
        </div>
      );
    }

    if (intervieweePreviewData.length === 0) {
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            padding: "48px",
            textAlign: "center"
          }}
        >
          <FormOutlined style={{ fontSize: "48px", color: "#8c8c8c", marginBottom: "16px" }} />
          <Title level={4} style={{ margin: "8px 0" }}>
            No Preview Available
          </Title>
          <Text type="secondary">Please enter valid JSON to see a preview of your form.</Text>
        </div>
      );
    }

    return (
      <div style={{ height: "100%" }}>
        <div
          style={{
            background: "white",
            borderRadius: "8px",
            border: "1px solid #f0f0f0",
            overflow: "hidden",
            height: "100%",
            opacity: intervieweeFadeIn ? 1 : 0,
            transition: "opacity 0.3s ease-in-out"
          }}
        >
          <div
            style={{ padding: "16px", borderBottom: "1px solid #f0f0f0", background: "#f5f5f5" }}
          >
            <Title level={4} style={{ margin: 0 }}>
              Interviewee Form Preview
            </Title>
            <Text type="secondary">This is how your form will appear to interviewees</Text>
          </div>
          <div style={{ padding: "24px", overflowY: "auto", maxHeight: "490px" }}>
            <Form form={previewForm} layout="vertical">
              <FormBuilder form={previewForm} meta={intervieweePreviewData} />
            </Form>
          </div>
        </div>
      </div>
    );
  };

  // Update the JSON help content to use the new function
  const jsonHelpContent = (_unused = false) => (
    <div style={{ maxWidth: 450 }}>
      <div>Your JSON should follow this structure to define form fields:</div>
      <div style={{ background: "#f6f8fa", padding: 12, borderRadius: 6, marginBottom: 12 }}>
        <pre style={{ margin: 0, fontSize: 12, lineHeight: 1.5 }}>
          {`[
  {
    "translatedFields": [
      {
        "key": "unique_id",
        "label": "Field Label",
        "required": true|false,
        "widget": "Input|Rate|etc",
        "widgetProps": { /* widget properties */ }
      },
      // Additional fields...
    ]
  }
]`}
        </pre>
      </div>
    </div>
  );

  if (loading) {
    return <Loading tip="Please wait ..." />;
  }

  return (
    <>
      <Modal
        open={openModal}
        onCancel={() => {
          setMainTab("interviewer");
          setOpenModal(false);
        }}
        cancelButtonProps={{
          hidden: true
        }}
        confirmLoading={updateLoading}
        onOk={() => form.submit()}
        width="70%"
        title={"Edit Template"}
        bodyStyle={{ padding: "12px 20px" }}
        okText="Update Template"
        maskClosable={false}
        destroyOnClose={true}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={(values) => {
            // Only continue if there are changes
            if (!form.isFieldsTouched()) return;

            // Prepare the data
            const formData: FormData = {
              id: template_id,
              title: values.title,
              status: values.status,
              interviewer_feedback_enabled: true,
              interviewer_feedback_meta: values.interviewer_feedback_meta,
              interviewee_feedback_enabled: values.interviewee_feedback_enabled || false,
              interviewee_feedback_meta: values.interviewee_feedback_meta || ""
            };

            // Send to the update handler with the overall rating flag
            handleUpdateTemplate(formData, enableOverallRating);
          }}
          preserve={true}
        >
          <div>
            <div style={{ marginBottom: "16px" }}>
              <Row gutter={16} align="middle">
                <Col span={16}>
                  <Form.Item
                    label={<span style={{ fontSize: "15px", fontWeight: 500 }}>Template Name</span>}
                    name="title"
                    rules={[{ required: true, message: "Template name is required" }]}
                    style={{ marginBottom: "8px" }}
                  >
                    <Input
                      placeholder="Enter template name..."
                      size="middle"
                      style={{ fontSize: "14px" }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "8px",
                      padding: "10px 12px",
                      background: "#f9f9f9",
                      borderRadius: "6px",
                      boxShadow: "0 1px 2px rgba(0,0,0,0.05)"
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        background: "transparent",
                        padding: "4px 8px",
                        borderRadius: "4px"
                      }}
                    >
                      <span style={{ fontSize: "13px", fontWeight: 500, marginRight: "12px" }}>
                        Active status
                      </span>
                      <Form.Item
                        name="status"
                        valuePropName="checked"
                        initialValue={true}
                        style={{ margin: "0", width: "65px" }}
                      >
                        <Switch
                          checkedChildren="Active"
                          unCheckedChildren="Inactive"
                          size="small"
                        />
                      </Form.Item>
                    </div>

                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        background: "transparent",
                        padding: "4px 8px",
                        borderRadius: "4px",
                        transition: "background-color 0.3s ease"
                      }}
                    >
                      <span style={{ fontSize: "13px", fontWeight: 500 }}>
                        Interviewee template
                      </span>
                      <Form.Item
                        name="interviewee_feedback_enabled"
                        valuePropName="checked"
                        initialValue={false}
                        style={{ margin: "0", width: "65px" }}
                      >
                        <Switch
                          checkedChildren="Enabled"
                          unCheckedChildren="Disabled"
                          onChange={handleEnableIntervieweeFeedback}
                          size="small"
                        />
                      </Form.Item>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>

            {/* Main tabs for Interviewer/Interviewee sections */}
            <Tabs
              activeKey={mainTab}
              onChange={setMainTab}
              size="small"
              tabBarStyle={{ marginBottom: "8px" }}
              items={[
                {
                  key: "interviewer",
                  label: (
                    <span style={{ fontSize: "14px", padding: "0 4px" }}>
                      <FormOutlined /> Interviewer Template
                    </span>
                  ),
                  children: (
                    <div style={{ padding: "8px 0" }}>
                      <Row gutter={16}>
                        {/* Left side - JSON Editor */}
                        <Col span={12}>
                          <div style={{ position: "relative", height: "100%" }}>
                            <div
                              style={{
                                position: "absolute",
                                top: "6px",
                                right: "6px",
                                zIndex: 10,
                                background: "rgba(255, 255, 255, 0.9)",
                                padding: "3px",
                                borderRadius: "4px",
                                boxShadow: "0 1px 4px rgba(0,0,0,0.08)"
                              }}
                            >
                              <div style={{ display: "flex", gap: "6px" }}>
                                <Button
                                  size="small"
                                  type="primary"
                                  href="https://wify-tms-demo.wify.co.in/fields-creator?edit="
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  style={{
                                    display: "flex",
                                    alignItems: "center",
                                    fontSize: "12px",
                                    padding: "0 8px",
                                    height: "24px"
                                  }}
                                >
                                  <FormOutlined style={{ marginRight: "4px" }} /> Open JSON Creator
                                </Button>
                                <Popover
                                  content={jsonHelpContent(false)}
                                  title="JSON Template Format"
                                  trigger="click"
                                  placement="bottomRight"
                                >
                                  <Button
                                    hidden
                                    size="small"
                                    style={{
                                      display: "flex",
                                      alignItems: "center",
                                      fontSize: "12px",
                                      padding: "0 8px",
                                      height: "24px"
                                    }}
                                  >
                                    <InfoCircleOutlined style={{ marginRight: "4px" }} /> Help
                                  </Button>
                                </Popover>
                              </div>
                            </div>

                            <div style={{ marginBottom: "10px" }}>
                              <Checkbox
                                checked={enableOverallRating}
                                onChange={(e) => {
                                  const newValue = e.target.checked;
                                  console.log("Edit modal - Checkbox changed to:", newValue);

                                  // Use a functional update to ensure latest state
                                  setEnableOverallRating(newValue);

                                  // Use the callback pattern to ensure we use the new value
                                  setTimeout(() => {
                                    console.log(
                                      "Edit modal - Processing form with new value:",
                                      newValue
                                    );
                                    // Pass the new value directly to avoid state timing issues
                                    processJsonInputWithRating(jsonInput, newValue);
                                  }, 10);
                                }}
                              >
                                Include Overall Rating
                              </Checkbox>
                            </div>

                            <Form.Item
                              name="interviewer_feedback_meta"
                              label={
                                <div className="flex justify-between w-full">
                                  <span>JSON Template</span>
                                  {jsonInput.length > 0 && (
                                    <Button
                                      type="link"
                                      size="small"
                                      onClick={() =>
                                        handleJsonChange({
                                          target: { value: "" }
                                        } as React.ChangeEvent<HTMLTextAreaElement>)
                                      }
                                    >
                                      Clear
                                    </Button>
                                  )}
                                </div>
                              }
                              rules={[
                                { required: true, message: "JSON input is required" },
                                {
                                  validator: (_, value) => {
                                    if (!value) return Promise.resolve();
                                    try {
                                      const validation = validateFormJson(value);
                                      if (validation.isValid) {
                                        return Promise.resolve();
                                      }
                                      return Promise.reject(
                                        validation.message || "Invalid JSON format"
                                      );
                                    } catch (error) {
                                      return Promise.reject("Invalid JSON format");
                                    }
                                  }
                                }
                              ]}
                              style={{ height: "100%", marginBottom: "0" }}
                              validateTrigger="onChange"
                            >
                              <TextArea
                                placeholder="Paste your JSON template here..."
                                onChange={handleJsonChange}
                                value={jsonInput}
                                readOnly={jsonInput.length > 0}
                                onPaste={(_e) => {
                                  // Clear the input first to allow new paste
                                  if (jsonInput.length > 0) {
                                    // If there's already content, clear it first
                                    handleJsonChange({
                                      target: { value: "" }
                                    } as React.ChangeEvent<HTMLTextAreaElement>);
                                  }
                                }}
                                style={{
                                  fontFamily: "monospace",
                                  height: "450px",
                                  resize: "none",
                                  padding: "12px",
                                  lineHeight: 1.5,
                                  fontSize: "13px",
                                  backgroundColor: jsonInput.length > 0 ? "#f5f5f5" : "white"
                                }}
                              />
                            </Form.Item>

                            {processingJson && (
                              <div
                                style={{
                                  position: "absolute",
                                  bottom: "12px",
                                  right: "12px",
                                  background: "rgba(0, 0, 0, 0.5)",
                                  color: "white",
                                  padding: "3px 10px",
                                  borderRadius: "12px",
                                  display: "flex",
                                  alignItems: "center",
                                  gap: "6px",
                                  fontSize: "12px"
                                }}
                              >
                                <LoadingOutlined style={{ fontSize: 12 }} />
                                <span>Processing...</span>
                              </div>
                            )}
                          </div>
                        </Col>

                        {/* Right side - Preview */}
                        <Col span={12}>{renderPreview()}</Col>
                      </Row>
                    </div>
                  )
                },
                {
                  key: "interviewee",
                  label: (
                    <span style={{ fontSize: "14px", padding: "0 4px" }}>
                      <FormOutlined /> Interviewee Template
                    </span>
                  ),
                  disabled: !enableIntervieweeSection,
                  children: (
                    <div style={{ padding: "8px 0" }}>
                      <Row gutter={16}>
                        {/* Left side - JSON Editor */}
                        <Col span={12}>
                          <div style={{ position: "relative", height: "100%" }}>
                            <div
                              style={{
                                position: "absolute",
                                top: "6px",
                                right: "6px",
                                zIndex: 10,
                                background: "rgba(255, 255, 255, 0.9)",
                                padding: "3px",
                                borderRadius: "4px",
                                boxShadow: "0 1px 4px rgba(0,0,0,0.08)"
                              }}
                            >
                              <div style={{ display: "flex", gap: "6px" }}>
                                <Button
                                  size="small"
                                  type="primary"
                                  href="https://wify-tms-demo.wify.co.in/fields-creator?edit="
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  style={{
                                    display: "flex",
                                    alignItems: "center",
                                    fontSize: "12px",
                                    padding: "0 8px",
                                    height: "24px"
                                  }}
                                >
                                  <FormOutlined style={{ marginRight: "4px" }} /> Open JSON Creator
                                </Button>
                                <Popover
                                  content={jsonHelpContent(true)}
                                  title="JSON Template Format"
                                  trigger="click"
                                  placement="bottomRight"
                                >
                                  <Button
                                    hidden
                                    size="small"
                                    style={{
                                      display: "flex",
                                      alignItems: "center",
                                      fontSize: "12px",
                                      padding: "0 8px",
                                      height: "24px"
                                    }}
                                  >
                                    <InfoCircleOutlined style={{ marginRight: "4px" }} /> Help
                                  </Button>
                                </Popover>
                              </div>
                            </div>

                            <div style={{ marginBottom: "10px" }}>
                              <Form.Item
                                name="interviewee_feedback_meta"
                                label={
                                  <div className="flex justify-between w-full">
                                    <span>JSON Template</span>
                                    {intervieweeJsonInput.length > 0 && (
                                      <Button
                                        type="link"
                                        size="small"
                                        onClick={() =>
                                          handleIntervieweeJsonChange({
                                            target: { value: "" }
                                          } as React.ChangeEvent<HTMLTextAreaElement>)
                                        }
                                      >
                                        Clear
                                      </Button>
                                    )}
                                  </div>
                                }
                                rules={[
                                  {
                                    required: enableIntervieweeSection,
                                    message: "JSON input is required"
                                  },
                                  {
                                    validator: (_, value) => {
                                      if (!value || !enableIntervieweeSection)
                                        return Promise.resolve();
                                      try {
                                        const validation = validateFormJson(value);
                                        if (validation.isValid) {
                                          return Promise.resolve();
                                        }
                                        return Promise.reject(
                                          validation.message || "Invalid JSON format"
                                        );
                                      } catch (error) {
                                        return Promise.reject("Invalid JSON format");
                                      }
                                    }
                                  }
                                ]}
                                style={{ height: "100%", marginBottom: "0" }}
                                validateTrigger="onChange"
                              >
                                <TextArea
                                  placeholder="Paste your interviewee JSON template here..."
                                  onChange={handleIntervieweeJsonChange}
                                  value={intervieweeJsonInput}
                                  readOnly={intervieweeJsonInput.length > 0}
                                  onPaste={(_e) => {
                                    // Clear the input first to allow new paste
                                    if (intervieweeJsonInput.length > 0) {
                                      // If there's already content, clear it first
                                      handleIntervieweeJsonChange({
                                        target: { value: "" }
                                      } as React.ChangeEvent<HTMLTextAreaElement>);
                                    }
                                  }}
                                  style={{
                                    fontFamily: "monospace",
                                    height: "450px",
                                    resize: "none",
                                    padding: "12px",
                                    lineHeight: 1.5,
                                    fontSize: "13px",
                                    backgroundColor:
                                      intervieweeJsonInput.length > 0 ? "#f5f5f5" : "white"
                                  }}
                                />
                              </Form.Item>

                              {intervieweeProcessingJson && (
                                <div
                                  style={{
                                    position: "absolute",
                                    bottom: "12px",
                                    right: "12px",
                                    background: "rgba(0, 0, 0, 0.5)",
                                    color: "white",
                                    padding: "3px 10px",
                                    borderRadius: "12px",
                                    display: "flex",
                                    alignItems: "center",
                                    gap: "6px",
                                    fontSize: "12px"
                                  }}
                                >
                                  <LoadingOutlined style={{ fontSize: 12 }} />
                                  <span>Processing...</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </Col>

                        {/* Right side - Preview */}
                        <Col span={12}>{renderIntervieweePreview()}</Col>
                      </Row>
                    </div>
                  )
                }
              ]}
            />
          </div>

          {/* Creator and Update Information */}
          {feedbackTemplate && (
            <div className="bg-purple-100 w-full h-20 border-solid border rounded-md mb-2 border-purple-500 flex justify-center p-4 flex-col">
              <div className="">
                Last updated :{" "}
                <span className="font-semibold">
                  {(feedbackTemplate.getSingleFeedbackTemplate.meta as any)?.updated_at
                    ? dayjs(
                        (feedbackTemplate.getSingleFeedbackTemplate.meta as any)?.updated_at
                      ).format("MMM-DD-YYYY h:mm A")
                    : "Not Available"}
                </span>
                {`, by ${
                  (feedbackTemplate.getSingleFeedbackTemplate.meta as any)?.updated_by_name ||
                  "Not available"
                }`}
              </div>
              <div className="mt-2 ">
                Created on :{" "}
                <span className="font-semibold">
                  {(feedbackTemplate.getSingleFeedbackTemplate.meta as any)?.created_at
                    ? dayjs(
                        (feedbackTemplate.getSingleFeedbackTemplate.meta as any)?.created_at
                      ).format("MMM-DD-YYYY h:mm A")
                    : "Not Available"}
                </span>
                {`, by ${
                  (feedbackTemplate.getSingleFeedbackTemplate.meta as any)?.created_by_name ||
                  "Not available"
                }`}
              </div>
            </div>
          )}
        </Form>
      </Modal>
    </>
  );
};

export default EditTemplateModal;
