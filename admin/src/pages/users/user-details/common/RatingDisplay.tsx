import { Tooltip } from "antd";
import { LuStar, LuStarHalf } from "react-icons/lu";

interface RatingDisplayProps {
  rating: number;
  size?: "sm" | "md" | "lg";
}

const RatingDisplay = ({ rating, size = "md" }: RatingDisplayProps) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;

  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  };

  return (
    <div className="flex items-center">
      <Tooltip title={rating ? "" : "No Rating Found"} placement="right">
        {[...Array(5)].map((_, i) => {
          if (i < fullStars) {
            return (
              <LuStar key={i} className={`${sizeClasses[size]} text-yellow-400 fill-current`} />
            );
          }
          if (i === fullStars && hasHalfStar) {
            return (
              <LuStarHalf key={i} className={`${sizeClasses[size]} text-yellow-400 fill-current`} />
            );
          }
          return (
            <LuStar
              key={i}
              className={`${sizeClasses[size]} ${
                rating === undefined ? "opacity-40 fill-current  " : ""
              }text-gray-300 `}
            />
          );
        })}
      </Tooltip>
    </div>
  );
};
export default RatingDisplay;
