import { Spin } from "antd";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";

dayjs.extend(isBetween);

interface TimeSlot {
  start: string;
  end: string;
  title: string;
}

interface InterviewerTimelineProps {
  busySlots: TimeSlot[];
  isLoading?: boolean;
  selectedDate?: dayjs.Dayjs;
}

const InterviewerTimeline: React.FC<InterviewerTimelineProps> = ({
  busySlots,
  isLoading = false,
  selectedDate = dayjs()
}) => {
  const startTime = selectedDate.hour(10).minute(0).second(0);
  const endTime = selectedDate.hour(19).minute(0).second(0);
  const totalMinutes = endTime.diff(startTime, "minute");
  const pixelsPerMinute = 540 / totalMinutes;

  const renderHourMarkers = () => {
    const markers = [];
    let currentTime = startTime;

    while (currentTime.isBefore(endTime)) {
      const top = currentTime.diff(startTime, "minute") * pixelsPerMinute;
      markers.push(
        <div
          key={currentTime.format("HH:mm")}
          className="absolute left-0 w-[60px] p-2 text-right text-gray-500 text-xs border-r border-gray-200 bg-gray-50"
          style={{ top }}
        >
          {currentTime.format("h A")}
        </div>
      );
      currentTime = currentTime.add(1, "hour");
    }

    return markers;
  };

  const renderBusyBlocks = () => {
    return busySlots.map((slot, index) => {
      const start = dayjs(slot.start);
      const end = dayjs(slot.end);
      const top = start.diff(startTime, "minute") * pixelsPerMinute;
      const height = end.diff(start, "minute") * pixelsPerMinute;

      return (
        <div
          key={index}
          className="absolute left-[60px] right-0 bg-blue-500 text-white p-1 rounded-md mx-2 text-xs flex items-center cursor-pointer transition-colors hover:bg-blue-600"
          style={{ top, height }}
        >
          {start.format("h:mm A")} - {end.format("h:mm A")} - {slot.title}
        </div>
      );
    });
  };

  return (
    <div className="relative h-[540px] border border-gray-200 rounded-md bg-white">
      {isLoading ? (
        <div className="flex justify-center items-center h-full">
          <Spin />
        </div>
      ) : (
        <>
          {renderHourMarkers()}
          {renderBusyBlocks()}
        </>
      )}
    </div>
  );
};

export default InterviewerTimeline;
