import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  useDisclosure
} from "@chakra-ui/react";
import { ArrowLeft, CheckCircle2, ChevronRight, FileText, Search, X } from "lucide-react";
import { useMemo, useState } from "react";
import {
  Policy,
  useGetPoliciesWithUserStatusQuery,
  useUserDetailsQuery
} from "../../__generated__";
import PdfViewerWithNav from "../../components/PDFViewerWithPageNav";

const PolicyList: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);
  const [showAcknowledge, setShowAcknowledge] = useState(false);

  const { data: userDetails, loading: userLoading } = useUserDetailsQuery();
  const user_id = userDetails?.userDetail?.data?.id;

  const {
    data: policies,
    loading,
    error
  } = useGetPoliciesWithUserStatusQuery({
    variables: user_id ? { userId: +user_id } : undefined,
    fetchPolicy: "network-only",
    skip: !user_id
  });

  const filteredPolicies = useMemo(() => {
    const query = searchQuery.toLowerCase();

    return policies?.getPoliciesWithUserStatus?.data?.filter(
      (policy) =>
        policy?.policy_name.toLowerCase().includes(query) ||
        policy?.policy_name_hindi?.includes(query)
    );
  }, [searchQuery, policies]);

  if (loading || userLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }
  return (
    <>
      {" "}
      <div className="">
        <div className="flex  mt-2 items-center gap-3 mb-1" id="div-top">
          <button
            onClick={() => console.log("")}
            className="  p-3 ml-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="hidden h-5 w-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-800">Company Policies</h1>
            <p className="text-sm text-gray-500 mt-0.5">Access and view all company policies</p>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-sm overflow-hidden border border-gray-100 ">
          <div className="p-4 bg-gray-50 border-b border-gray-100">
            <div className="relative">
              <input
                type="text"
                placeholder="Search policies..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-10 py-2.5 bg-white rounded-xl text-sm border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              />
              <Search className="absolute left-3.5 top-3 h-4 w-4 text-gray-400" />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery("")}
                  className="absolute right-3 top-3 p-0.5 rounded-full hover:bg-gray-100"
                >
                  <X className="h-4 w-4 text-gray-400" />
                </button>
              )}
            </div>
          </div>

          <div className="divide-y divide-gray-100">
            {filteredPolicies?.map((policy) => (
              <div
                key={policy?.id}
                onClick={() => {
                  setSelectedPolicy(policy || null);
                  onOpen();
                }}
                className="flex items-center gap-3 p-4 hover:bg-gray-50 transition-colors cursor-pointer group"
              >
                <div className="w-10 h-10 bg-blue-50 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-blue-100 transition-colors">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <h3 className="text-gray-800 font-medium mb-1 truncate">
                      {policy?.policy_name}
                    </h3>
                    {policy?.user_policy_tracking?.[0]?.accepted && (
                      <CheckCircle2 className="h-4 w-4 text-green-500 flex-shrink-0" />
                    )}
                  </div>
                  <p className="text-gray-500 text-sm">{policy?.policy_name_hindi}</p>
                </div>
                <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
              </div>
            ))}

            {filteredPolicies?.length === 0 && (
              <div className="py-12 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="h-8 w-8 text-gray-400" />
                </div>
                <p className="text-gray-500 font-medium">No policies found</p>
                <p className="text-gray-400 text-sm mt-1">Try adjusting your search terms</p>
              </div>
            )}
          </div>
        </div>
      </div>
      <Box p={4} backgroundColor={"#fff"} mx={"2"} mb="10" rounded={"lg"}>
        <Modal
          isOpen={isOpen}
          onClose={() => {
            if (!selectedPolicy?.user_policy_tracking?.[0]?.accepted) {
              setShowAcknowledge(true);
            }
          }}
          size={"full"}
        >
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>{selectedPolicy?.policy_name} </ModalHeader>
            {/* <ModalCloseButton /> */}

            <ModalBody>
              <Box className="w-full">
                {/* <PdfViewer pdfUrl={selectedPolicy?.url || ""} /> */}
                <PdfViewerWithNav
                  pdfUrl={selectedPolicy?.url || ""}
                  showAcknowledge={showAcknowledge}
                  policyId={selectedPolicy?.id || ""}
                  onClose={() => {
                    setShowAcknowledge(false);
                    onClose();
                  }}
                />
              </Box>
            </ModalBody>

            <ModalFooter>
              <Button
                colorScheme="blue"
                mr={3}
                onClick={() => {
                  if (!selectedPolicy?.user_policy_tracking?.[0]?.accepted) {
                    setShowAcknowledge(true);
                  } else {
                    onClose();
                  }
                }}
              >
                Close
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </Box>
    </>
  );
};

export default PolicyList;
