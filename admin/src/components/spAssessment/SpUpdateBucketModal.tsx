import { Form, Input, Modal, notification } from "antd";
import { useForm } from "antd/es/form/Form";
import FormItem from "antd/es/form/FormItem";
import { useNavigate, useParams } from "react-router-dom";
import {
  GetAllBucketsDocument,
  useGetSingleBucketQuery,
  useUpdateBucketNameMutation
} from "../../__generated__";

interface FormValues {
  name: string;
}

const SpUpdateBucketModal: React.FC = () => {
  //Form
  const [form] = useForm();
  //Params
  const params = useParams();
  //Navigate
  const navigate = useNavigate();
  //Mutation
  const [updateBucket, { loading }] = useUpdateBucketNameMutation();

  useGetSingleBucketQuery({
    variables: {
      getSingleBucketId: params?.bucket_id || ""
    },
    fetchPolicy: "no-cache",
    onCompleted(data) {
      if (data?.getSingleBucket?.data) {
        form.setFieldsValue({
          name: data?.getSingleBucket?.data?.name
        });
      }
    }
  });

  const onFormSubmit = async (values: FormValues) => {
    try {
      await updateBucket({
        variables: {
          updateBucketNameId: params?.bucket_id || "",
          name: values?.name
        },
        onCompleted() {
          notification.success({
            message: "Bucket updated successfully"
          });
          navigate(-1);
        },
        refetchQueries: [GetAllBucketsDocument]
      });
    } catch (err) {
      notification.error({
        message: "There was something wrong during updating your bucket"
      });
    }
  };

  return (
    <Modal
      centered
      closeIcon={false}
      onOk={() => form.submit()}
      onCancel={() => navigate(-1)}
      open
      okButtonProps={{
        loading: loading
      }}
      okText={"Update"}
      title={"Update bucket"}
    >
      <Form form={form} onFinish={onFormSubmit}>
        <FormItem
          rules={[
            {
              required: true,
              message: "Bucket name is required"
            }
          ]}
          name={"name"}
          label={"Name"}
        >
          <Input maxLength={50} />
        </FormItem>
      </Form>
    </Modal>
  );
};

export default SpUpdateBucketModal;
