import { OnboardingStage, Prisma } from '@prisma/client'
import dayjs from 'dayjs'
import { GraphQLError } from 'graphql'
import DataProvider from '../../data'
import { followUpTmsLeads } from '../../queue/scheduler/follow-up'
import {
    filterContacts,
    sendContactsToAdmin,
} from '../../services/contacts.service'
import { validateAdmin, validateUser } from '../../utils/authValidators'
import { MASTER_ORG_ID } from '../../utils/constants'
import { Context } from '../../utils/types'
import { createUserTimestamp } from '../../utils/user'
import { formatString } from '../../utils/utils'
import {
    MutationResolvers,
    QueryResolvers,
    UserRole,
} from '../__generated__/types'

const db = DataProvider.getDbInstance()
type MutationTypes = MutationResolvers<Context>

const syncContacts = async (contacts: any[], user: any) => {
    try {
        const formatted_contacts = filterContacts(contacts, user)

        if (formatted_contacts?.length) {
            // Upsert contacts one by one in a sequential manner avoid promise.all and transaction
            const upsertedContacts = []

            for (const contact of formatted_contacts) {
                const upsertedContact = await db.contact.upsert({
                    where: {
                        unique_name_phone: {
                            name:
                                typeof contact.name === 'object'
                                    ? contact.name.display
                                    : contact.name,
                            phone: contact.phone,
                        },
                    },
                    update: {},
                    create: {
                        ...contact,
                        name:
                            typeof contact.name === 'object'
                                ? contact.name.display
                                : contact.name,
                    },
                })
                upsertedContacts.push(upsertedContact)
            }

            // Extract the IDs of the upserted contacts
            const contactIds = upsertedContacts.map((contact) => contact.id)

            // Create user_contact_map entries
            await db.user_contact_map.createMany({
                data: contactIds.map((contactId) => ({
                    contact_id: contactId,
                    user_id: user.id,
                })),
                skipDuplicates: true,
            })
        }
    } catch (error) {
        console.error(`❌ Error in processing chunk for user ${user.id}`, error)
    }
}

const createContacts: MutationTypes['createContacts'] = async (
    _,
    args,
    ctx
) => {
    try {
        const { user } = ctx
        const { contacts } = args
        const contact_data = JSON.parse(contacts || '')
        await db.user.update({
            where: {
                id: user.id,
            },
            data: {
                last_contacts_synced_at: new Date(), //this will be used to show the date on leads table
            },
        })

        await syncContacts(contact_data, user)

        return {
            result: true,
            message: 'Contacts are getting created',
        }
    } catch (error) {
        throw new GraphQLError('Failed to create contacts: ' + error.message)
    }
}

const getLeadContactsFrAdmin: QueryResolvers['getLeadContactsFrAdmin'] = async (
    _,
    args,
    ctx
) => {
    try {
        //TODO: add pagination logic also
        validateAdmin(ctx)
        /**
         * this will get all the users who have some user contact mapped to them
         * return the count of user contact map
         */

        const { filter } = args

        //TODO: here need to add show active_user_contact_map only
        const user_where: Prisma.userWhereInput = {
            user_contacts: {
                some: {},
            }, // Ensures that the user has at least one contact
        }

        if (filter?.search) {
            user_where.name = {
                contains: `%${filter.search}%`,
                mode: 'insensitive',
            }
        }

        const users_with_contact_count = await db.user.findMany({
            where: user_where,
            include: {
                _count: {
                    select: {
                        user_contacts: {
                            where: { is_active: true },
                        },
                    }, // Counts only the active contacts for each user
                },
            },
            orderBy: {
                last_contacts_synced_at: 'desc',
            },
        })

        const total_count = await db.user.count({
            where: user_where,
        })

        return {
            data: users_with_contact_count.map((user) => {
                return {
                    user: user,
                    total_contacts_synced: user._count.user_contacts,
                }
            }),
            total_count: total_count,
        } as any //TODO: remove this any later
    } catch (error) {
        throw new GraphQLError(
            'Failed to fetch lead contacts: ' + error.message
        )
    }
}

//write query for getContactsOfTheUserFrAdmin
const getContactsOfTheUserFrAdmin: QueryResolvers['getContactsOfTheUserFrAdmin'] =
    async (_, args, ctx) => {
        try {
            const { user_id, filter } = args

            let user
            if (ctx.admin) {
                validateAdmin(ctx)
            } else {
                user = validateUser(ctx)
                if (user_id !== user.id) {
                    throw new GraphQLError(
                        'You are not authorized to access this data'
                    )
                }
            }

            const user_contact_where: Prisma.user_contact_mapWhereInput = {
                user_id: user_id || -1,
                is_active: true,
                contact: {
                    AND: [
                        {
                            OR: [
                                {
                                    name: {
                                        contains: filter?.search || undefined,
                                        mode: 'insensitive',
                                    },
                                },
                                {
                                    phone: {
                                        contains: filter?.search || undefined,
                                        mode: 'insensitive',
                                    },
                                },
                            ],
                        },
                        filter?.moved_to_users_status
                            ? {
                                  moved_to_users:
                                      filter.moved_to_users_status === 'unmoved'
                                          ? false
                                          : true,
                              }
                            : {},
                    ],
                },
            }

            const user_contacts = await db.user_contact_map.findMany({
                where: user_contact_where,
                include: {
                    contact: true,
                },
            })

            const total_count = await db.user_contact_map.count({
                where: {
                    user_id: user_id || -1,
                    is_active: true,
                },
            })

            const total_moved_contacts_count = await db.user_contact_map.count({
                where: {
                    user_id: user_id || -1,
                    is_active: true,
                    contact: {
                        moved_to_users: true,
                    },
                },
            })

            return {
                data: user_contacts,
                total_count,
                total_moved_contacts_count,
            } as any //TODO:remove this any
        } catch (error) {
            throw new GraphQLError(error)
        }
    }

//write mutation for deleteContactOfUserFrAdmin
const deleteContactOfUserFrAdmin: MutationTypes['deleteContactOfUserFrAdmin'] =
    async (_, args, ctx) => {
        try {
            validateAdmin(ctx) //FIXME: this not authorizing even valid token
            const { contact_id, user_id } = args

            await db.user_contact_map.updateMany({
                where: {
                    user_id: user_id,
                    contact_id: {
                        in: contact_id,
                    },
                },
                data: {
                    is_active: false,
                },
            })

            return {
                result: true,
                message: 'Contact deleted successfully',
            }
        } catch (error) {
            throw new GraphQLError(error)
        }
    }

//write mutation for moveContactsToUserFrAdmin
const moveContactsToUserFrAdmin: MutationTypes['moveContactsToUserFrAdmin'] =
    async (_, args, ctx) => {
        //TODO: even this should happen in queue when move all is clicked it breaks transaction
        try {
            validateAdmin(ctx) // Ensure this function correctly authorizes the admin

            const { contact_id, user_id } = args

            const contacts = await db.user_contact_map.findMany({
                where: {
                    user_id: user_id,
                    contact_id: {
                        in: contact_id,
                    },
                },
                include: {
                    contact: true,
                },
            })

            const createUserPromises = contacts?.map(async (contactDetails) => {
                const { contact } = contactDetails
                const createdUser = await db.user.upsert({
                    where: {
                        phone: contact.phone, // Assuming phone is a unique field
                    },
                    create: {
                        name: contact.name,
                        phone: contact.phone,
                        organization_id: MASTER_ORG_ID,
                        onboarding_stage: OnboardingStage.GENERAL_DETAILS,
                        updated_at: new Date(),
                        user_type: UserRole.Technician, //once lead contacts is enabled for SP will handle it dynamically
                    },
                    update: {
                        name: contact.name,
                        organization_id: MASTER_ORG_ID,
                        onboarding_stage: OnboardingStage.GENERAL_DETAILS,
                        updated_at: new Date(),
                    },
                })

                await db.contact.updateMany({
                    where: {
                        phone: contact.phone,
                    },
                    data: {
                        moved_to_users: true,
                        meta: {
                            ...(contact.meta && typeof contact.meta === 'object'
                                ? contact.meta
                                : {}),
                            moved_by_admin_id: ctx.admin.id,
                        },
                    },
                })

                await createUserTimestamp({
                    user_id: createdUser.id,
                    admin_id: ctx.admin.id,
                })
            })

            await Promise.all(createUserPromises)

            await followUpTmsLeads()

            return {
                result: true,
                message: 'Contact moved successfully',
            }
        } catch (error) {
            throw new GraphQLError(error)
        }
    }
const exportContacts: QueryResolvers['exportContacts'] = async (
    _,
    args,
    ctx
) => {
    try {
        validateAdmin(ctx)
        const { filter } = args
        const user_where: Prisma.userWhereInput = {
            user_contacts: {
                some: {},
            },
        }

        if (filter?.expertise) {
            user_where.user_expertise_map = {
                some: {
                    expertise: {
                        id: {
                            in: filter.expertise,
                        },
                    },
                },
            }
        }

        if ((filter as any)?.city) {
            user_where.OR = [
                {
                    meta: {
                        path: ['city'],
                        equals: (filter as any).city,
                    },
                },
                {
                    meta: {
                        path: ['primary_city'],
                        equals: (filter as any).city,
                    },
                },
            ]
        }

        const user_order_by: Prisma.userOrderByWithRelationAndSearchRelevanceInput =
            {
                last_contacts_synced_at: 'desc',
            }
        if (filter?.start_date && filter?.end_date) {
            const startDate = new Date(filter.start_date)
            startDate.setUTCHours(
                startDate.getUTCHours() + 5,
                startDate.getUTCMinutes() + 30
            ) // Convert UTC to IST
            startDate.setUTCHours(0, 0, 0, 0) // Start of the day (00:00:00.000 IST)

            const endDate = new Date(filter.end_date)
            endDate.setUTCHours(
                endDate.getUTCHours() + 5,
                endDate.getUTCMinutes() + 30
            ) // Convert UTC to IST
            endDate.setUTCHours(23, 59, 59, 999) // End of the day (23:59:59.999 IST)

            user_where.last_contacts_synced_at = {
                gte: startDate,
                lte: endDate,
            }

            user_order_by.last_contacts_synced_at = 'asc'
        }

        const users_with_contact_count = await db.user.findMany({
            select: {
                name: true,
                phone: true,
                meta: true,
                onboarding_stage: true,
                user_expertise_map: {
                    select: {
                        expertise: {
                            select: {
                                name: true,
                            },
                        },
                    },
                },
                last_contacts_synced_at: true,
                user_contacts: {
                    select: {
                        contact: {
                            select: {
                                name: true,
                                phone: true,
                            },
                        },
                    },
                },
            },
            where: user_where,
            orderBy: user_order_by,
        })
        if (users_with_contact_count.length === 0) {
            throw new GraphQLError('No contacts to download')
        }
        //User Name , City , Skills , Date Synced , Onboarding Stage , Contact Name , Contact Number
        const users_with_contact_info = users_with_contact_count.map((user) => {
            const users_with_contact_info = []
            for (const contact of user.user_contacts) {
                users_with_contact_info.push({
                    'User Name': user.name,
                    City: (() => {
                        if (
                            typeof user.meta !== 'object' ||
                            user.meta === null
                        ) {
                            return 'Not available'
                        }
                        const meta = user.meta as any
                        return meta.primary_city || meta.city || 'Not available'
                    })(),
                    Skills:
                        user.user_expertise_map
                            .map((expertise) => expertise.expertise.name)
                            .join(', ') || 'Not available',
                    'Date Synced': user.last_contacts_synced_at
                        ? dayjs(user.last_contacts_synced_at).format(
                              'DD-MM-YYYY'
                          )
                        : 'Not available',
                    'Onboarding Stage':
                        user.onboarding_stage === OnboardingStage.DASHBOARD
                            ? 'Sign Up Completed'
                            : formatString(user.onboarding_stage),
                    'Contact Name': contact.contact.name,
                    'Contact Number': contact.contact.phone,
                })
            }
            return users_with_contact_info
        })
        //add all the users_with_contact_info in the single array
        const all_users_with_contact_info = users_with_contact_info.flat()

        const sheet_id = await sendContactsToAdmin(
            all_users_with_contact_info,
            ctx
        )
        return {
            result: true,
            message: 'Contacts exported successfully',
            sheet_id,
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const leadContactsResolver = {
    Mutation: {
        createContacts,
        deleteContactOfUserFrAdmin,
        moveContactsToUserFrAdmin,
    },
    Query: {
        getLeadContactsFrAdmin,
        exportContacts,
        getContactsOfTheUserFrAdmin,
    },
}

export default leadContactsResolver
