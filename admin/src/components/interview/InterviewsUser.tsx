import { BackwardOutlined } from "@ant-design/icons";
import { Avatar, Button, Popconfirm, Result, Space, Tooltip, notification } from "antd";
import ButtonGroup from "antd/es/button/button-group";
import isEmpty from "is-empty";
import React, { useEffect, useState } from "react";
import { BsStarFill } from "react-icons/bs";
import { FcApproval, FcInfo, FcVoicePresentation } from "react-icons/fc";
import { MdDoneAll } from "react-icons/md";
import { Outlet, useLocation, useNavigate, useParams } from "react-router-dom";
import {
  FeedBackState,
  Gender,
  GetUserInterviewsQuery,
  InterviewState,
  useApproveUserForTrainingMutation,
  useGetUserInterviewsQuery,
  useRejectUserForTrainingMutation,
  useUserDetailsQueryQuery
} from "../../__generated__";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import { getUserProfileSrc } from "../../utils/utils";
import Loading from "../loading/Loading";
import InterviewScheduledList from "./InterviewScheduledList";

const InterviewPage: React.FC = () => {
  //Hooks
  const [activeInterviewsCount, setActiveInterviewsCount] = useState(0);
  const [completedFeedbackCount, setCompletedFeedbackCount] = useState(0);
  const [completedInterviewsCount, setCompletedInterviewsCount] = useState(0);
  const [totalFeedbackCount, setTotalFeedbackCount] = useState(0);
  const [isApproved, setIsApproved] = useState<boolean | undefined>(false);
  const [isRejected, setIsRejected] = useState<boolean | undefined>(false);
  const [averageRating, setAverageRating] = useState<number | undefined>(0);
  const location = useLocation();
  const fromInterviewDetails = location.pathname.startsWith("/dashboard/interviews/details/");

  const nav = useNavigate();

  const { user_id } = useParams();

  //Graphql Query
  const { data: interviewsResponse } = useGetUserInterviewsQuery({
    fetchPolicy: "no-cache",
    variables: {
      userId: Number(user_id)
    }
  });
  const { data: userDataResponse, loading: userDataLoading } = useUserDetailsQueryQuery({
    variables: {
      userId: Number(user_id)
    }
  });
  const userData = userDataResponse?.userDetailsForAdmin;

  //Graphql Mutations
  const [approveUserForTraining, { loading: approveConfirmLoading }] =
    useApproveUserForTrainingMutation();
  const [rejectUserForTraining, { loading: rejectConfirmLoading }] =
    useRejectUserForTrainingMutation();

  //handle User Approve for Training
  const handleApproveUserForTraining = () => {
    approveUserForTraining({
      variables: {
        userId: Number(user_id)
      },
      onCompleted() {
        notification.success({ message: "User approved for training successfully" });
      },
      onError() {
        notification.error({ message: "Something went Wrong, Please Contact Admin " });
      }
    });
  };
  //handle USer Reject for Training
  const handleRejectUserForTraining = () => {
    rejectUserForTraining({
      variables: {
        userId: Number(user_id)
      },
      onCompleted() {
        notification.success({ message: "User Rejected from training successfully" });
      },
      onError() {
        notification.error({ message: "Something went Wrong, Please Contact Admin " });
      }
    });
  };

  const handleInterviewDataCards = (data: GetUserInterviewsQuery) => {
    setActiveInterviewsCount(0);
    setCompletedInterviewsCount(0);
    setCompletedFeedbackCount(0);
    //Check Total Feedback Count 👈
    setTotalFeedbackCount(data.getUserInterviews?.length || 0);
    const interviewData = data?.getUserInterviews?.map((interview) => {
      if (
        interview?.status === InterviewState.Rescheduled ||
        interview?.status === InterviewState.Scheduled
      ) {
        //Check User Active Interviews 👈
        setActiveInterviewsCount((data) => data + 1);
      }
      if (interview?.feedback?.interviewer_feedback_state === FeedBackState.Completed) {
        //Check User Completed Feedback Interviews 👈
        setCompletedFeedbackCount((data) => data + 1);
      }
      if (interview?.status === InterviewState.Completed) {
        setCompletedInterviewsCount((data) => data + 1);
      }
    });
    if (isEmpty(interviewData)) {
      setIsApproved(true);
      setIsRejected(true);
    }
  };

  const handleIfUserIsRejectedOrApprovedForTraining = (data: GetUserInterviewsQuery) => {
    // Check if the user has any interviews
    if (!data.getUserInterviews?.length) {
      setIsApproved(false);
      setIsRejected(false);
      return;
    }

    // Check if the user is Approved for Training
    const isApproved = data.getUserInterviews?.every(
      (interview) => interview?.feedback?.interviewer_feedback_state === FeedBackState.Completed
    );
    //calculate the average rating of the user
    let totalRating = 0;
    let ratingCount = 0;

    data.getUserInterviews?.forEach((interview) => {
      const result = interview?.feedback?.interviewer_feedback_result || {};
      if (result?.["overall-rating"]) {
        totalRating += Number(result["overall-rating"]);
        ratingCount++;
      }
    });

    // Calculate the average only if there are ratings
    const averageRating = ratingCount > 0 ? (totalRating / ratingCount).toFixed(1) : 0;

    setAverageRating(Number(averageRating));

    // Check if the user is Rejected for Training
    const isRejected = data.getUserInterviews?.every(
      (interview) => interview?.status === InterviewState.Rejected
    );
    setIsApproved(isApproved);
    setIsRejected(isRejected);
  };

  useEffect(() => {
    interviewsResponse && handleIfUserIsRejectedOrApprovedForTraining(interviewsResponse);
    interviewsResponse && handleInterviewDataCards(interviewsResponse);
  }, [interviewsResponse, isApproved, isRejected]);

  const GeneralInterviewData = () => {
    const data = [
      {
        title: "Active interview",
        tooltip: "Interviews that are currently active that is scheduled for future",
        value: activeInterviewsCount,
        icon: <FcVoicePresentation size={48} />
      },
      {
        title: "Interviews given",
        tooltip: "Interviews that are given by the user",
        value: completedInterviewsCount,
        card_color: "#deb3ff",
        icon: <FcApproval size={48} />
      },
      {
        title: "Average rating so far",
        tooltip: "Average rating of the user so far in the assessment",
        value: averageRating ? `${averageRating}/5` : "0/5",
        icon: <BsStarFill color="#FFD700" size={42} />
      },
      {
        title: "Interviewer Completed Feedback",
        tooltip: "No of feedback that are completed by the interviewer",
        value: `${completedFeedbackCount + " / " + totalFeedbackCount}`,

        icon: <MdDoneAll color="#800080" size={34} />
      }
    ];
    return (
      <div className="grid grid-cols-4 gap-3 ">
        {data.map((d) => (
          <div className="rounded-md shadow-md p-3 flex items-start gap-3 ">
            <div>{d.icon}</div>
            <div className="flex flex-col justify-center w-full">
              <div className="flex items-center gap-2 justify-between w-full">
                <div className="font-bold text-gray-500">{d.title}</div>
                <Tooltip title={d.tooltip}>
                  <FcInfo size={17} />
                </Tooltip>
              </div>
              <div className="text-3xl">{d.value}</div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <ProtectedRoute disablePageLevelAuth>
      {({ permissions }) => (
        <div>
          {fromInterviewDetails && (
            <Button
              onClick={() => nav("/dashboard/interviews")}
              className="mb-2"
              icon={<BackwardOutlined />}
            >
              Go back
            </Button>
          )}
          {userDataLoading ? (
            <Loading tip="Loading user data. Please wait ..." />
          ) : (
            <div>
              {!user_id || !userData ? (
                <Result
                  status={404}
                  title="User not found. Please verify the user exists."
                  extra={
                    <Button type="primary" onClick={() => nav(-1)}>
                      Go back
                    </Button>
                  }
                />
              ) : (
                <div className="space-y-5 ">
                  {fromInterviewDetails && (
                    <div className="flex justify-between px-3">
                      <div className="flex items-start">
                        <Avatar
                          className="w-16 h-16 mr-4"
                          src={getUserProfileSrc(
                            userData?.gender || Gender.NotSpecified,
                            userData?.photoUrl || ""
                          )}
                        />
                        <Space direction="vertical">
                          <div>
                            <div className="flex space-y-3 space-x-8">
                              <div className="text-3xl">{userData?.name}</div>
                              {/* {!isApproved || !isRejected ? (
                                <div>
                                  <Tag color={!isApproved ? "green" : !isRejected ? "red" : ""}>
                                    {!isApproved ? "Approved" : !isRejected ? "red" : ""}
                                  </Tag>
                                </div>
                              ) : null} */}
                            </div>
                            <div className="text-colorPrimary font-bold">Technician</div>
                          </div>
                          <div>
                            <b>{userData.phone ? "Phone" : "Email"} </b> -{" "}
                            {userData?.phone || userData?.email}
                          </div>
                        </Space>
                      </div>
                      <div className="mr-20 ">
                        <ButtonGroup>
                          <Popconfirm
                            placement="left"
                            disabled={isApproved || !permissions.update}
                            title={<p>Are you sure to approve user for training </p>}
                            showCancel={false}
                            okText={"Approve"}
                            onConfirm={handleApproveUserForTraining}
                          >
                            <Button
                              disabled={isApproved || !permissions.update}
                              loading={approveConfirmLoading}
                              type="primary"
                            >
                              {" "}
                              Approve
                            </Button>
                          </Popconfirm>{" "}
                          <Popconfirm
                            placement="bottomLeft"
                            disabled={isRejected || !permissions.update}
                            title={<p>Are you sure to reject user from training </p>}
                            showCancel={false}
                            onConfirm={handleRejectUserForTraining}
                            okText={"Reject"}
                          >
                            <Button
                              danger
                              disabled={isRejected || !permissions.update}
                              loading={rejectConfirmLoading}
                              color="red"
                            >
                              {" "}
                              Reject
                            </Button>
                          </Popconfirm>{" "}
                        </ButtonGroup>
                      </div>
                    </div>
                  )}
                  <GeneralInterviewData />
                  <div className="flex overflow-y-scroll max-h-[calc(100vh-500px)]">
                    <div className="w-2/6 ">
                      <InterviewScheduledList permissions={permissions} />
                    </div>
                    <div className="w-3/5 ml-10">
                      <Outlet />
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </ProtectedRoute>
  );
};

export default InterviewPage;
