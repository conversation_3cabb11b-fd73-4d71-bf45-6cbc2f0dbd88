import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
import * as ApolloReactHooks from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
  JSON: { input: any; output: any; }
};

export type AddInterviewerData = {
  city?: InputMaybe<Scalars['String']['input']>;
  district_taluka?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  landmark?: InputMaybe<Scalars['String']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name: Scalars['String']['input'];
  pincode: Scalars['String']['input'];
  state: Scalars['String']['input'];
  work_address?: InputMaybe<Scalars['String']['input']>;
};

export type Admin = {
  __typename?: 'Admin';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type AdminCreateInput = {
  admin_role_id?: InputMaybe<Scalars['Int']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  parent_id?: InputMaybe<Scalars['Int']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  profile_photo?: InputMaybe<Scalars['String']['input']>;
};

export type AdminData = {
  __typename?: 'AdminData';
  active_org_id?: Maybe<Scalars['String']['output']>;
  admin_on_organization?: Maybe<Array<Maybe<AdminOrg>>>;
  admin_role_id?: Maybe<Scalars['Int']['output']>;
  doc_verify?: Maybe<Array<Maybe<User>>>;
  email?: Maybe<Scalars['String']['output']>;
  email_verified?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  organizations?: Maybe<Array<Maybe<Organization>>>;
  parent_id?: Maybe<Scalars['Int']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  phone_verified?: Maybe<Scalars['Boolean']['output']>;
  profile_photo?: Maybe<Scalars['String']['output']>;
  type?: Maybe<AdminType>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type AdminForgetPwdInput = {
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type AdminLoginInput = {
  password?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type AdminName = {
  __typename?: 'AdminName';
  name?: Maybe<Scalars['String']['output']>;
};

export type AdminOnOrg = {
  __typename?: 'AdminOnOrg';
  assessment?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  dashboard?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  document_type?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  documents?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  interviews?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  lead_contacts?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  lead_management?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  name?: Maybe<Scalars['String']['output']>;
  new_releases?: Maybe<Array<Maybe<ReleaseNote>>>;
  referral?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  reject_user?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  skills?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  training_and_test?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  transfer_to_tms?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  users?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type AdminOrg = {
  __typename?: 'AdminOrg';
  permission_id?: Maybe<Scalars['Int']['output']>;
};

export type AdminProtectedFeature = {
  created_at?: InputMaybe<Scalars['DateTime']['input']>;
  feature_id: Scalars['ID']['input'];
  icon_name?: InputMaybe<Scalars['String']['input']>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  is_module?: InputMaybe<Scalars['Boolean']['input']>;
  module_id?: InputMaybe<Scalars['Int']['input']>;
  module_name?: InputMaybe<Scalars['String']['input']>;
  parent_module_id?: InputMaybe<Scalars['Int']['input']>;
  parent_module_name?: InputMaybe<Scalars['String']['input']>;
  section_id?: InputMaybe<Scalars['String']['input']>;
  sub_module_name?: InputMaybe<Scalars['String']['input']>;
  sub_module_url_sp?: InputMaybe<Scalars['String']['input']>;
  sub_module_url_user?: InputMaybe<Scalars['String']['input']>;
  updated_at?: InputMaybe<Scalars['DateTime']['input']>;
};

export type AdminProtectedFeatureData = {
  __typename?: 'AdminProtectedFeatureData';
  created_at: Scalars['DateTime']['output'];
  feature_id: Scalars['ID']['output'];
  icon_name: Scalars['String']['output'];
  is_active: Scalars['Boolean']['output'];
  is_module?: Maybe<Scalars['Boolean']['output']>;
  module_id?: Maybe<Scalars['Int']['output']>;
  module_name?: Maybe<Scalars['String']['output']>;
  parent_module_id?: Maybe<Scalars['Int']['output']>;
  parent_module_name?: Maybe<Scalars['String']['output']>;
  section_id?: Maybe<Scalars['String']['output']>;
  sub_module_name?: Maybe<Scalars['String']['output']>;
  sub_module_url_sp?: Maybe<Scalars['String']['output']>;
  sub_module_url_user?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type AdminPwdChangeInput = {
  password?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['String']['input']>;
};

export type AdminResult = {
  __typename?: 'AdminResult';
  active_org_id?: Maybe<Scalars['String']['output']>;
  admin_on_organization?: Maybe<Array<Maybe<AdminOnOrg>>>;
  admin_role?: Maybe<AdminRoleData>;
  doc_verify?: Maybe<Array<Maybe<User>>>;
  email?: Maybe<Scalars['String']['output']>;
  email_verified?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  last_release_version_seen?: Maybe<Scalars['String']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  organizations?: Maybe<Array<Maybe<Organization>>>;
  parent_id?: Maybe<Scalars['Int']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  phone_verified?: Maybe<Scalars['Boolean']['output']>;
  profile_photo?: Maybe<Scalars['String']['output']>;
  type?: Maybe<AdminType>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type AdminRoleByIdResponse = {
  __typename?: 'AdminRoleByIdResponse';
  data: AdminRoleData;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type AdminRoleData = {
  __typename?: 'AdminRoleData';
  admin_role_id: Scalars['ID']['output'];
  admin_role_permission?: Maybe<Array<AdminRolePermission>>;
  can_accept: Scalars['Boolean']['output'];
  can_ban: Scalars['Boolean']['output'];
  can_download: Scalars['Boolean']['output'];
  can_reject: Scalars['Boolean']['output'];
  created_at: Scalars['DateTime']['output'];
  creator_id: Scalars['ID']['output'];
  description?: Maybe<Scalars['String']['output']>;
  has_admin_privileges: Scalars['Boolean']['output'];
  is_root_admin: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  updated_by?: Maybe<Scalars['ID']['output']>;
};

export type AdminRoleInput = {
  can_accept?: InputMaybe<Scalars['Boolean']['input']>;
  can_ban?: InputMaybe<Scalars['Boolean']['input']>;
  can_download?: InputMaybe<Scalars['Boolean']['input']>;
  can_reject?: InputMaybe<Scalars['Boolean']['input']>;
  creator_id?: InputMaybe<Scalars['Int']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  has_admin_privileges?: InputMaybe<Scalars['Boolean']['input']>;
  is_root_admin?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  role_permission_array?: InputMaybe<Array<InputMaybe<RolePermissionData>>>;
};

export type AdminRolePermission = {
  __typename?: 'AdminRolePermission';
  admin_protected_feature?: Maybe<AdminProtectedFeatureData>;
  admin_role_id?: Maybe<Scalars['Int']['output']>;
  feature_id?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  permission_for_sp?: Maybe<Scalars['JSON']['output']>;
  permission_for_user?: Maybe<Scalars['JSON']['output']>;
};

export type AdminRoleResponse = {
  __typename?: 'AdminRoleResponse';
  data: Array<AdminRoleData>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export enum AdminType {
  Admin = 'ADMIN',
  SuperUser = 'SUPER_USER'
}

export type AdminUpdateInput = {
  active_org_id?: InputMaybe<Scalars['Int']['input']>;
  admin_role_id?: InputMaybe<Scalars['Int']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  email_verified?: InputMaybe<Scalars['Boolean']['input']>;
  id?: InputMaybe<Scalars['Int']['input']>;
  last_release_version_seen?: InputMaybe<Scalars['String']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  organization?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  parent_id?: InputMaybe<Scalars['Int']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  phone_verified?: InputMaybe<Scalars['Boolean']['input']>;
  profile_photo?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<AdminType>;
  updated_at?: InputMaybe<Scalars['DateTime']['input']>;
};

export type AllAdminResult = {
  __typename?: 'AllAdminResult';
  data1?: Maybe<Array<Maybe<AdminData>>>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type AllUserReferralsResponse = {
  __typename?: 'AllUserReferralsResponse';
  data: Array<Maybe<ReferredUsersData>>;
  total_count: Scalars['Int']['output'];
};

export enum AllowedOperator {
  Equals = 'equals',
  In = 'in',
  LessThan = 'less_than',
  MoreThan = 'more_than',
  NotEquals = 'not_equals'
}

export type AnalyticsResponse = {
  __typename?: 'AnalyticsResponse';
  data?: Maybe<Scalars['JSON']['output']>;
  message: Scalars['String']['output'];
  success: Scalars['Boolean']['output'];
};

export enum AnalyticsType {
  AppAnalytics = 'APP_ANALYTICS',
  UserActivity = 'USER_ACTIVITY'
}

export type AppAnalytics = {
  __typename?: 'AppAnalytics';
  android_installations_by_date_range: Scalars['Int']['output'];
  android_installations_by_service_provider: Scalars['Int']['output'];
  android_installations_by_technician: Scalars['Int']['output'];
  drop_off_user: Scalars['Int']['output'];
  total_user_sign_up_android: Scalars['Int']['output'];
  total_user_sign_up_android_by_service_provider: Scalars['Int']['output'];
  total_user_sign_up_android_by_technician: Scalars['Int']['output'];
  total_user_sign_up_web: Scalars['Int']['output'];
  total_user_sign_up_web_by_service_provider: Scalars['Int']['output'];
  total_user_sign_up_web_by_technician: Scalars['Int']['output'];
  unique_visitors: Scalars['Int']['output'];
  unique_visitors_by_service_provider: Scalars['Int']['output'];
  unique_visitors_by_technician: Scalars['Int']['output'];
};

export type AppAnalyticsFilter = {
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
};

export type AppInstallation = {
  __typename?: 'AppInstallation';
  city?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  device_id?: Maybe<Scalars['String']['output']>;
  device_info?: Maybe<Scalars['JSON']['output']>;
  id: Scalars['ID']['output'];
  installation_date: Scalars['DateTime']['output'];
  ip_address?: Maybe<Scalars['String']['output']>;
  platform_type: AppPlatformType;
  referrer?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  status: AppInstallationStatus;
  uninstallation_date?: Maybe<Scalars['DateTime']['output']>;
  updated_at: Scalars['DateTime']['output'];
  user_agent?: Maybe<Scalars['String']['output']>;
};

export enum AppInstallationPeriod {
  CurrentMonth = 'CURRENT_MONTH',
  Monthly = 'MONTHLY',
  Weekly = 'WEEKLY',
  Yearly = 'YEARLY'
}

export enum AppInstallationStatus {
  Installed = 'INSTALLED',
  Uninstalled = 'UNINSTALLED'
}

export enum AppPlatformType {
  Android = 'ANDROID',
  Web = 'WEB'
}

export type AssignContentInputData = {
  assign_to_all: Scalars['Boolean']['input'];
  content_id: Scalars['String']['input'];
  user_id?: InputMaybe<Scalars['Int']['input']>;
};

export type AssignContentToAllUsers = {
  content_id: Scalars['String']['input'];
};

export type AssignCourseBulkInputData = {
  course_id: Scalars['String']['input'];
};

export type AssignCourseInputData = {
  assign: Scalars['Boolean']['input'];
  course_id: Scalars['String']['input'];
  user_id: Scalars['Int']['input'];
};

export type AssignedUser = {
  __typename?: 'AssignedUser';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type Assignment = {
  __typename?: 'Assignment';
  status: Scalars['String']['output'];
};

export type AssignmentConfig = {
  __typename?: 'AssignmentConfig';
  assignment?: Maybe<Array<Maybe<AssignmentData>>>;
  config_assignment_rules?: Maybe<Array<AutoAssignmentGenRules>>;
  config_assignment_type: AssignmentType;
  creator: AdminData;
  creator_id: Scalars['Int']['output'];
  draft_mode: Scalars['Boolean']['output'];
  general_instructions_md: Scalars['String']['output'];
  id: Scalars['String']['output'];
  is_active: Scalars['Boolean']['output'];
  meta: Scalars['JSON']['output'];
  name: Scalars['String']['output'];
  no_of_questions: Scalars['Int']['output'];
  questions_assignment_rules?: Maybe<Array<AutoAssignmentGenRules>>;
  questions_assignment_type?: Maybe<AssignmentType>;
  updated_at: Scalars['Int']['output'];
};

export type AssignmentConfigCreateUpdateParams = {
  config_assignment_type?: InputMaybe<AssignmentType>;
  draft_mode?: InputMaybe<Scalars['String']['input']>;
  general_instructions_md: Scalars['String']['input'];
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name: Scalars['String']['input'];
  no_of_questions: Scalars['Int']['input'];
  questions_assignment_type?: InputMaybe<AssignmentType>;
};

export type AssignmentConfigData = {
  __typename?: 'AssignmentConfigData';
  assigned_to_only_team_members?: Maybe<Scalars['Boolean']['output']>;
  config_assignment_rules?: Maybe<AutoAssignmentGenRules>;
  config_assignment_type: AssignmentType;
  draft_mode: Scalars['Boolean']['output'];
  easy_time?: Maybe<Scalars['Int']['output']>;
  general_instructions_md: Scalars['String']['output'];
  hard_time?: Maybe<Scalars['Int']['output']>;
  id: Scalars['String']['output'];
  is_active: Scalars['Boolean']['output'];
  medium_time?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  no_of_questions: Scalars['Int']['output'];
  on_basis_of: Array<Maybe<Scalars['String']['output']>>;
  questions?: Maybe<Array<Maybe<Question>>>;
  questions_assignment_rules?: Maybe<AutoAssignmentGenRules>;
  questions_assignment_type?: Maybe<AssignmentType>;
  time_bounded: Scalars['Boolean']['output'];
  total_count?: Maybe<Scalars['Int']['output']>;
  updated_at: Scalars['DateTime']['output'];
};

export type AssignmentConfigDataFrSingleAssignment = {
  __typename?: 'AssignmentConfigDataFrSingleAssignment';
  config_assignment_type: AssignmentType;
  easy_question_time?: Maybe<Scalars['String']['output']>;
  hard_question_time?: Maybe<Scalars['String']['output']>;
  medium_question_time?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  no_of_questions: Scalars['Int']['output'];
  questions_assignment_type: AssignmentType;
  time_bounded: Scalars['Boolean']['output'];
};

export type AssignmentConfigFilter = {
  assign_to?: InputMaybe<AssignmentUserType>;
  config_assignment_type?: InputMaybe<AssignmentType>;
  draft?: InputMaybe<Scalars['Boolean']['input']>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  question_assignment_type?: InputMaybe<AssignmentType>;
  search?: InputMaybe<Scalars['String']['input']>;
};

export type AssignmentConfigUpdate = {
  assigned_to_only_team_members?: InputMaybe<Scalars['Boolean']['input']>;
  config_assignment_type?: InputMaybe<AssignmentType>;
  draft_mode?: InputMaybe<Scalars['Boolean']['input']>;
  easy_question_time?: InputMaybe<Scalars['Int']['input']>;
  general_instructions_md?: InputMaybe<Scalars['String']['input']>;
  hard_question_time?: InputMaybe<Scalars['Int']['input']>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  is_time_bounded?: InputMaybe<Scalars['Boolean']['input']>;
  medium_question_time?: InputMaybe<Scalars['Int']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  no_of_questions?: InputMaybe<Scalars['Int']['input']>;
  on_basis_of?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  questions_assignment_type?: InputMaybe<AssignmentType>;
  rules?: InputMaybe<Scalars['JSON']['input']>;
};

export type AssignmentConfigWithIdAndName = {
  __typename?: 'AssignmentConfigWithIdAndName';
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type AssignmentConfigWithName = {
  __typename?: 'AssignmentConfigWithName';
  id?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
};

export type AssignmentData = {
  __typename?: 'AssignmentData';
  finished_at?: Maybe<Scalars['DateTime']['output']>;
  status: AssignmentStatus;
};

export type AssignmentDataFrUser = {
  __typename?: 'AssignmentDataFrUser';
  assignment_configration: AssignmentConfigWithIdAndName;
  assignment_configuration_id: Scalars['String']['output'];
  created_at: Scalars['DateTime']['output'];
  finished_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['String']['output'];
  result?: Maybe<GeneralAssignmentResult>;
  status: AssignmentStatus;
};

export type AssignmentResult = {
  __typename?: 'AssignmentResult';
  correct_answer: Scalars['Int']['output'];
  created_at: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  total_time_taken: Scalars['Int']['output'];
  unattempted_questions: Scalars['Int']['output'];
  updated_at: Scalars['DateTime']['output'];
  wrong_answer: Scalars['Int']['output'];
};

export enum AssignmentResultStatus {
  Average = 'AVERAGE',
  Excellent = 'EXCELLENT',
  Fail = 'FAIL'
}

export type AssignmentRulesParams = {
  assign_to?: InputMaybe<AssignmentUserType>;
  name: Scalars['String']['input'];
  rules: Array<InputMaybe<Rule>>;
};

export enum AssignmentStatus {
  Completed = 'COMPLETED',
  NotStarted = 'NOT_STARTED',
  OnGoing = 'ON_GOING'
}

export enum AssignmentType {
  Automatic = 'AUTOMATIC',
  Manual = 'MANUAL'
}

export enum AssignmentUserType {
  ServiceProvider = 'SERVICE_PROVIDER',
  Technician = 'TECHNICIAN'
}

export type AssignmentsFrCourseFilter = {
  search?: InputMaybe<Scalars['String']['input']>;
};

export type AuthResult = {
  __typename?: 'AuthResult';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
  token?: Maybe<Scalars['String']['output']>;
};

export type AutoAssignmentGenRules = {
  __typename?: 'AutoAssignmentGenRules';
  created_at: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  rules: Scalars['JSON']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type AutoAssignmentGenRulesWithCount = {
  __typename?: 'AutoAssignmentGenRulesWithCount';
  data?: Maybe<Array<Maybe<AutoAssignmentGenRules>>>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export enum AutoAssignmentOperations {
  DoesNotExists = 'DOES_NOT_EXISTS',
  Equal = 'EQUAL',
  Exists = 'EXISTS',
  In = 'IN',
  LessThan = 'LESS_THAN',
  MoreThan = 'MORE_THAN',
  NotEqual = 'NOT_EQUAL'
}

export type BankData = {
  encrypted?: InputMaybe<UserBankDetailsInput>;
  user_id?: InputMaybe<Scalars['Int']['input']>;
};

export type BankDetails = {
  __typename?: 'BankDetails';
  encrypted?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['Int']['output']>;
};

export type Banner = {
  __typename?: 'Banner';
  banner_config?: Maybe<BannerConfigFrBanner>;
  expiry_time?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['String']['output']>;
};

export type BannerConfig = {
  __typename?: 'BannerConfig';
  banner?: Maybe<Array<Banner>>;
  created_at: Scalars['DateTime']['output'];
  cta_link?: Maybe<Scalars['String']['output']>;
  cta_required: Scalars['Boolean']['output'];
  description: Scalars['String']['output'];
  hero_image: Scalars['String']['output'];
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  is_active: Scalars['Boolean']['output'];
  is_deleted: Scalars['Boolean']['output'];
  is_published: Scalars['Boolean']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  title: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type BannerConfigFilterInput = {
  banner_config_id?: InputMaybe<Scalars['String']['input']>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  is_published?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type BannerConfigFrBanner = {
  __typename?: 'BannerConfigFrBanner';
  cta_link?: Maybe<Scalars['String']['output']>;
  cta_required?: Maybe<Scalars['Boolean']['output']>;
  cta_text?: Maybe<Scalars['String']['output']>;
  description: Scalars['String']['output'];
  hero_image: Scalars['String']['output'];
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  is_published?: Maybe<Scalars['Boolean']['output']>;
  title: Scalars['String']['output'];
};

export type BannerConfigsWithCount = {
  __typename?: 'BannerConfigsWithCount';
  data: Array<BannerConfig>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type BannerFilterInput = {
  order: Scalars['Int']['input'];
};

export type Bucket = {
  __typename?: 'Bucket';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  created_by?: Maybe<AdminData>;
  creator_id?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  questions?: Maybe<Array<Maybe<Question>>>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type BucketDetails = {
  __typename?: 'BucketDetails';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  creator_id?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type BucketResponse = {
  __typename?: 'BucketResponse';
  data?: Maybe<BucketDetails>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type BulkAdditionUserAdded = {
  __typename?: 'BulkAdditionUserAdded';
  message?: Maybe<Scalars['String']['output']>;
  totalAddedUsers?: Maybe<Scalars['Int']['output']>;
};

export type BulkAdditionUserAlreadyPresent = {
  __typename?: 'BulkAdditionUserAlreadyPresent';
  existingUsers?: Maybe<User>;
  message?: Maybe<Scalars['String']['output']>;
};

export type BulkAdditionUserResult = BulkAdditionUserAdded | BulkAdditionUserAlreadyPresent;

export type BulkUserData = {
  city?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  org_id?: InputMaybe<Scalars['Int']['input']>;
  phone: Scalars['String']['input'];
  pincode?: InputMaybe<Scalars['String']['input']>;
  poc?: InputMaybe<Scalars['String']['input']>;
  skills?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  source?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  work_address?: InputMaybe<Scalars['String']['input']>;
  work_experience?: InputMaybe<Array<InputMaybe<WorkExperienceInput>>>;
};

export enum BusinessVertical {
  Doors = 'DOORS',
  LooseFurnitureAndAmazon = 'LOOSE_FURNITURE_AND_AMAZON',
  MkwAndMeasurement = 'MKW_AND_MEASUREMENT',
  SmartLocks = 'SMART_LOCKS',
  WarrantyAndElectric = 'WARRANTY_AND_ELECTRIC',
  Water = 'WATER'
}

export type CalendarEvent = {
  __typename?: 'CalendarEvent';
  created_by?: Maybe<Scalars['Int']['output']>;
  created_for?: Maybe<User>;
  data?: Maybe<Scalars['JSON']['output']>;
  google_event_id?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  type?: Maybe<CalendarEventType>;
};

export enum CalendarEventType {
  InterviewOffline = 'INTERVIEW_OFFLINE',
  InterviewOnline = 'INTERVIEW_ONLINE'
}

export type CalendarEvents = {
  __typename?: 'CalendarEvents';
  name?: Maybe<Scalars['String']['output']>;
  type?: Maybe<CalendarEventType>;
};

export type CalendarFreeBusyResponse = {
  __typename?: 'CalendarFreeBusyResponse';
  end?: Maybe<Scalars['DateTime']['output']>;
  start?: Maybe<Scalars['DateTime']['output']>;
};

export type Chapter = {
  __typename?: 'Chapter';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  duration?: Maybe<Scalars['Int']['output']>;
  id: Scalars['String']['output'];
  lesson: Array<LessonInputFrCourseChapter>;
  order?: Maybe<Scalars['Int']['output']>;
  title: Scalars['String']['output'];
  user_chapter_progress_track: Array<UserChapterProgressTrack>;
};

export type ChapterAndLessonInputData = {
  lessons?: InputMaybe<Array<InputMaybe<Lesson>>>;
  name: Scalars['String']['input'];
};

export type ChartDataset = {
  __typename?: 'ChartDataset';
  backgroundColor: Scalars['String']['output'];
  borderColor: Scalars['String']['output'];
  borderWidth: Scalars['Int']['output'];
  data: Array<Scalars['Int']['output']>;
  fill: Scalars['Boolean']['output'];
  label: Scalars['String']['output'];
};

export type CheckUnregisteredReferralResponse = {
  __typename?: 'CheckUnregisteredReferralResponse';
  is_referred: Scalars['Boolean']['output'];
  referrers: Array<ReferrerInfo>;
};

export type Checklist = {
  __typename?: 'Checklist';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type ChecklistHistoryData = {
  __typename?: 'ChecklistHistoryData';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  creator?: Maybe<ChecklistHistoryPerson>;
  last_editor?: Maybe<ChecklistHistoryPerson>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type ChecklistHistoryPerson = {
  __typename?: 'ChecklistHistoryPerson';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type ChecklistResponse = {
  __typename?: 'ChecklistResponse';
  data?: Maybe<Array<Maybe<Checklist>>>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export enum CompanyType {
  LimitedLiabilityCompany = 'LIMITED_LIABILITY_COMPANY',
  NotSpecified = 'NOT_SPECIFIED',
  Other = 'OTHER',
  PartnershipFirm = 'PARTNERSHIP_FIRM',
  PrivateCompany = 'PRIVATE_COMPANY',
  SoleProprietorship = 'SOLE_PROPRIETORSHIP'
}

export type CompleteUserAssignmentResult = {
  __typename?: 'CompleteUserAssignmentResult';
  data: AssignmentResultStatus;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type Contact = {
  __typename?: 'Contact';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  image_url?: Maybe<Scalars['String']['output']>;
  invitations?: Maybe<Array<TeamMemberInvitation>>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  is_deleted?: Maybe<Scalars['Boolean']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  moved_to_users?: Maybe<Scalars['Boolean']['output']>;
  name: Scalars['String']['output'];
  phone: Scalars['String']['output'];
  phone_contact_id?: Maybe<Scalars['String']['output']>;
  timeline?: Maybe<Scalars['JSON']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type ContactEmail = {
  address?: InputMaybe<Scalars['String']['input']>;
  isPrimary?: InputMaybe<Scalars['Boolean']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type ContactName = {
  display?: InputMaybe<Scalars['String']['input']>;
  family?: InputMaybe<Scalars['String']['input']>;
  given?: InputMaybe<Scalars['String']['input']>;
  middle?: InputMaybe<Scalars['String']['input']>;
  suffix?: InputMaybe<Scalars['String']['input']>;
};

export type ContactOrganization = {
  company?: InputMaybe<Scalars['String']['input']>;
};

export type ContactPhone = {
  isPrimary?: InputMaybe<Scalars['Boolean']['input']>;
  label?: InputMaybe<Scalars['String']['input']>;
  number?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type ContactWithInvitationStatus = {
  __typename?: 'ContactWithInvitationStatus';
  contact: Contact;
  status: TeamMemberInvitationStatus;
};

export type Contacts = {
  contactId?: InputMaybe<Scalars['String']['input']>;
  emails?: InputMaybe<Array<InputMaybe<ContactEmail>>>;
  name?: InputMaybe<ContactName>;
  organization?: InputMaybe<ContactOrganization>;
  phones?: InputMaybe<Array<InputMaybe<ContactPhone>>>;
};

export type ContactsFilters = {
  city?: InputMaybe<Scalars['String']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  expertise?: InputMaybe<Array<Scalars['Int']['input']>>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};

export type Content = {
  __typename?: 'Content';
  content_designation_map: Array<CourseDesignation>;
  created_at: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  expertise_content_map: Array<Maybe<ContentExpertise>>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  internal_content_id: Scalars['String']['output'];
  is_active: Scalars['Boolean']['output'];
  is_deleted: Scalars['Boolean']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  published: Scalars['Boolean']['output'];
  published_on?: Maybe<Scalars['DateTime']['output']>;
  title: Scalars['String']['output'];
  type: ContentType;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  video_content: Video_Content;
};

export type ContentExpertise = {
  __typename?: 'ContentExpertise';
  expertise?: Maybe<Expertise>;
};

export type ContentFilterInput = {
  date?: InputMaybe<DateRange>;
  designation?: InputMaybe<Array<Scalars['String']['input']>>;
  expertise?: InputMaybe<Array<Scalars['Int']['input']>>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  published?: InputMaybe<Scalars['Boolean']['input']>;
  type?: InputMaybe<ContentType>;
};

export type ContentFrLesson = {
  __typename?: 'ContentFrLesson';
  title: Scalars['String']['output'];
  video_content: VideoContent;
};

export type ContentResultData = {
  __typename?: 'ContentResultData';
  count?: Maybe<Scalars['Int']['output']>;
  data: Array<Content>;
};

export enum ContentType {
  Image = 'IMAGE',
  Video = 'VIDEO'
}

export type ContentUserMap = {
  __typename?: 'ContentUserMap';
  content_id?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['Int']['output']>;
};

export type CoreResultsData = {
  __typename?: 'CoreResultsData';
  approved_for_training: Scalars['Int']['output'];
  assignments_in_progress: Scalars['Int']['output'];
  completed_assignments: Scalars['Int']['output'];
  denied_for_training: Scalars['Int']['output'];
  pending_for_approval: Scalars['Int']['output'];
  total_assignments: Scalars['Int']['output'];
};

export type Course = {
  __typename?: 'Course';
  _count?: Maybe<CourseUserMapCount>;
  business_vertical: BusinessVertical;
  chapter: Array<Chapter>;
  course_assessment_map?: Maybe<Array<CourseAssignmentMap>>;
  course_designation_map?: Maybe<Array<CourseDesignation>>;
  description: Scalars['String']['output'];
  duration: Scalars['Int']['output'];
  expertise_course_map?: Maybe<Array<CourseExpertiseMap>>;
  id?: Maybe<Scalars['String']['output']>;
  image: Scalars['String']['output'];
  internal_course_id?: Maybe<Scalars['String']['output']>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  is_deleted?: Maybe<Scalars['Boolean']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  method: CourseMethod;
  name: Scalars['String']['output'];
  objective: Scalars['String']['output'];
  published: Scalars['Boolean']['output'];
  published_on?: Maybe<Scalars['DateTime']['output']>;
  rating?: Maybe<Scalars['Float']['output']>;
  status: CourseStatus;
  type: CourseType;
  user_course?: Maybe<Array<Maybe<CourseUserMap>>>;
};

export type CourseAssignmentMap = {
  __typename?: 'CourseAssignmentMap';
  assignment_config?: Maybe<AssignmentConfigWithName>;
  assignment_config_id: Scalars['String']['output'];
};

export type CourseChapters = {
  duration?: InputMaybe<Scalars['Int']['input']>;
  lessons?: InputMaybe<Array<InputMaybe<Lessons>>>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type CourseDesignation = {
  __typename?: 'CourseDesignation';
  designation: Designation;
};

export type CourseDesignationMap = {
  __typename?: 'CourseDesignationMap';
  id: Scalars['String']['output'];
};

export type CourseExpertiseMap = {
  __typename?: 'CourseExpertiseMap';
  expertise?: Maybe<ExpertiseWithIdNameIcon>;
};

export type CourseFilterInput = {
  business_vertical?: InputMaybe<BusinessVertical>;
  date?: InputMaybe<DateRange>;
  designation?: InputMaybe<Array<Scalars['String']['input']>>;
  expertise?: InputMaybe<Array<Scalars['Int']['input']>>;
  internal_course_id?: InputMaybe<Scalars['String']['input']>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  method?: InputMaybe<CourseMethod>;
  published?: InputMaybe<Scalars['Boolean']['input']>;
  type?: InputMaybe<CourseType>;
};

export enum CourseMethod {
  Practical = 'PRACTICAL',
  Theoretical = 'THEORETICAL'
}

export enum CourseStatus {
  Active = 'ACTIVE',
  Draft = 'DRAFT',
  Inactive = 'INACTIVE'
}

export enum CourseType {
  Soft = 'SOFT',
  Technical = 'TECHNICAL'
}

export type CourseUserAssignmentMap = {
  __typename?: 'CourseUserAssignmentMap';
  assignment?: Maybe<UserCourseAssignmentData>;
  assignment_config?: Maybe<AssignmentConfigWithName>;
};

export type CourseUserMap = {
  __typename?: 'CourseUserMap';
  completed_percentage: Scalars['String']['output'];
  course: Course;
  course_completed: Scalars['Boolean']['output'];
  course_id?: Maybe<Scalars['String']['output']>;
  course_user_assignment_map?: Maybe<Array<CourseUserAssignmentMap>>;
  id: Scalars['String']['output'];
  progress_status: UserCourseProgressStatus;
  training_completed: Scalars['Boolean']['output'];
  user_id?: Maybe<Scalars['String']['output']>;
};

export type CourseUserMapCount = {
  __typename?: 'CourseUserMapCount';
  chapter?: Maybe<Scalars['Int']['output']>;
  course_assessment_map?: Maybe<Scalars['Int']['output']>;
  expertise_course_map?: Maybe<Scalars['Int']['output']>;
  user_chapter_progress_track?: Maybe<Scalars['Int']['output']>;
  user_course?: Maybe<Scalars['Int']['output']>;
};

export type CoursesForUserResult = {
  __typename?: 'CoursesForUserResult';
  business_vertical: BusinessVertical;
  course_designation_map?: Maybe<Array<CourseDesignation>>;
  expertise_course_map?: Maybe<Array<CourseExpertiseMap>>;
  id?: Maybe<Scalars['String']['output']>;
  internal_course_id?: Maybe<Scalars['String']['output']>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  is_deleted?: Maybe<Scalars['Boolean']['output']>;
  method: CourseMethod;
  name: Scalars['String']['output'];
  published: Scalars['Boolean']['output'];
  status: CourseStatus;
  type: CourseType;
  user_course?: Maybe<Array<Maybe<CourseUserMap>>>;
};

export type CoursesResponseResultWithData = {
  __typename?: 'CoursesResponseResultWithData';
  count?: Maybe<Scalars['Int']['output']>;
  data: Array<Course>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type CreateAdminData = {
  __typename?: 'CreateAdminData';
  message?: Maybe<Scalars['String']['output']>;
  moduleToken?: Maybe<Scalars['String']['output']>;
  permissionToken?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
  token?: Maybe<Scalars['String']['output']>;
};

export type CreateAssignmentConfigInput = {
  assign_to?: InputMaybe<AssignmentUserType>;
  assigned_to_only_team_members?: InputMaybe<Scalars['Boolean']['input']>;
  buckets?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  config_assignment_type: AssignmentType;
  config_auto_assignment_gen_rules_id?: InputMaybe<Scalars['String']['input']>;
  difficulty?: InputMaybe<Array<InputMaybe<QuestionDifficulty>>>;
  easy_time?: InputMaybe<Scalars['Int']['input']>;
  general_instructions_md: Scalars['String']['input'];
  hard_time?: InputMaybe<Scalars['Int']['input']>;
  medium_time?: InputMaybe<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
  no_of_questions: Scalars['Int']['input'];
  on_basis_of?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  questions_assignment_type: AssignmentType;
  time_bounded: Scalars['Boolean']['input'];
};

export type CreateAssignmentResultType = {
  __typename?: 'CreateAssignmentResultType';
  id: Scalars['String']['output'];
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type CreateBannerConfigInput = {
  cta_link?: InputMaybe<Scalars['String']['input']>;
  cta_required: Scalars['Boolean']['input'];
  cta_text?: InputMaybe<Scalars['String']['input']>;
  description: Scalars['String']['input'];
  hero_image: Scalars['String']['input'];
  image?: InputMaybe<Scalars['String']['input']>;
  title: Scalars['String']['input'];
};

export type CreateBannerInput = {
  banner_config_id: Scalars['String']['input'];
  order: Scalars['Int']['input'];
};

export type CreateCalendarEventData = {
  date?: InputMaybe<Scalars['DateTime']['input']>;
  end?: InputMaybe<Scalars['DateTime']['input']>;
  interviewer_id?: InputMaybe<Scalars['Int']['input']>;
  offline_interview_location?: InputMaybe<Scalars['String']['input']>;
  start?: InputMaybe<Scalars['DateTime']['input']>;
  template_id?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<CalendarEventType>;
  user_id?: InputMaybe<Scalars['Int']['input']>;
};

export type CreateChecklistInput = {
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
};

export type CreateContentInputData = {
  content_type?: InputMaybe<ContentType>;
  description: Scalars['String']['input'];
  designation: Array<Scalars['String']['input']>;
  duration: Scalars['Int']['input'];
  expertise: Array<Scalars['Int']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  is_deleted?: InputMaybe<Scalars['Boolean']['input']>;
  title: Scalars['String']['input'];
  type?: InputMaybe<ContentType>;
  updated_at?: InputMaybe<Scalars['DateTime']['input']>;
  video_url: Scalars['String']['input'];
};

export type CreateCourseInputData = {
  assessments?: InputMaybe<Array<Scalars['String']['input']>>;
  business_vertical?: InputMaybe<BusinessVertical>;
  chapters?: InputMaybe<Array<ChapterAndLessonInputData>>;
  description?: InputMaybe<Scalars['String']['input']>;
  designation?: InputMaybe<Array<Scalars['String']['input']>>;
  expertise?: InputMaybe<Array<Scalars['Int']['input']>>;
  image?: InputMaybe<Scalars['String']['input']>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  method?: InputMaybe<CourseMethod>;
  name: Scalars['String']['input'];
  objective?: InputMaybe<Scalars['String']['input']>;
  published?: InputMaybe<Scalars['Boolean']['input']>;
  type?: InputMaybe<CourseType>;
};

export type CreateDesignationInput = {
  expertise: Array<Scalars['Int']['input']>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  level: Scalars['Int']['input'];
  name: Scalars['String']['input'];
};

export type CreateExpertiseResult = {
  __typename?: 'CreateExpertiseResult';
  data?: Maybe<Expertise>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type CreateFeedbackSection = {
  instruction?: InputMaybe<Scalars['String']['input']>;
  max_score?: InputMaybe<Scalars['Int']['input']>;
  template_id?: InputMaybe<Scalars['Int']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type CreateInterviewFeedbackInput = {
  feedback_state?: InputMaybe<FeedBackStateInput>;
  interviewer_id?: InputMaybe<Scalars['String']['input']>;
  summary?: InputMaybe<Scalars['String']['input']>;
  template_id?: InputMaybe<Scalars['String']['input']>;
  user_id?: InputMaybe<Scalars['String']['input']>;
};

export type CreateLeadInput = {
  current_work?: InputMaybe<Scalars['String']['input']>;
  image_url?: InputMaybe<Scalars['String']['input']>;
  location?: InputMaybe<Scalars['String']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  token?: InputMaybe<Scalars['String']['input']>;
};

export type CreateOrganizationData = {
  org_name: Scalars['String']['input'];
  user_name: Scalars['String']['input'];
  user_password: Scalars['String']['input'];
  user_phone: Scalars['String']['input'];
};

export type CreatePolicyInput = {
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  policy_name: Scalars['String']['input'];
  policy_name_hindi?: InputMaybe<Scalars['String']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
};

export type CreateProviderOrgInput = {
  orgDetails: OrgCreateInput;
  userDetails: ProviderCreateInput;
};

export type CreateProviderOrgInputBulk = {
  orgDetails: OrgCreateInputBulk;
  userDetails: ProviderCreateInputBulk;
};

export type CreateRoleData = {
  name: Scalars['String']['input'];
  permission: Scalars['JSON']['input'];
};

export type CreateUnregisteredReferralInput = {
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
};

export type DailyAnalytics = {
  __typename?: 'DailyAnalytics';
  average_session_time: Scalars['Float']['output'];
  bounce_rate: Scalars['Float']['output'];
  created_at: Scalars['DateTime']['output'];
  date: Scalars['DateTime']['output'];
  funnel_metrics: Scalars['JSON']['output'];
  id: Scalars['ID']['output'];
  language_breakdown: Scalars['JSON']['output'];
  platform_breakdown: Scalars['JSON']['output'];
  total_logins: Scalars['Int']['output'];
  total_signups: Scalars['Int']['output'];
  total_visitors: Scalars['Int']['output'];
  unique_visitors: Scalars['Int']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type DashboardData = {
  __typename?: 'DashboardData';
  locations?: Maybe<UsersWithLocationAndCount>;
  skills?: Maybe<Array<Maybe<ExpertiseCount>>>;
  stages?: Maybe<UsersCountByType>;
};

export type DashboardFilter = {
  city?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  expertise?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  fulfillment_type?: InputMaybe<FulfillmentType>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};

export enum DashboardStates {
  Applicants = 'APPLICANTS',
  Interview = 'INTERVIEW',
  Onboard = 'ONBOARD',
  Rejected = 'REJECTED',
  Training = 'TRAINING'
}

export type DateFilter = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};

export type DateRange = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  start_date: Scalars['DateTime']['input'];
};

export type DeleteDocumentType = {
  __typename?: 'DeleteDocumentType';
  deletedDoc?: Maybe<Document>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type DeleteResult = {
  __typename?: 'DeleteResult';
  count?: Maybe<Scalars['Int']['output']>;
};

export type DeleteRoleResult = DeleteRoleSuccess | DeletionFailedDueToRelation;

export type DeleteRoleSuccess = {
  __typename?: 'DeleteRoleSuccess';
  message?: Maybe<Scalars['String']['output']>;
};

export type DeleteWorkExpResult = {
  __typename?: 'DeleteWorkExpResult';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type DeletionData = {
  __typename?: 'DeletionData';
  user?: Maybe<PartialUserData>;
};

export type DeletionFailedDueToRelation = {
  __typename?: 'DeletionFailedDueToRelation';
  data?: Maybe<DeletionData>;
  message?: Maybe<Scalars['String']['output']>;
};

export type Designation = {
  __typename?: 'Designation';
  admin?: Maybe<Admin>;
  course_designation_map?: Maybe<Array<CourseDesignationMap>>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  designation_expertise_map?: Maybe<Array<Maybe<DesignationExpertise>>>;
  id?: Maybe<Scalars['String']['output']>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  is_deleted?: Maybe<Scalars['Boolean']['output']>;
  level?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  user?: Maybe<Array<Maybe<AssignedUser>>>;
};

export type DesignationExpertise = {
  __typename?: 'DesignationExpertise';
  expertise?: Maybe<Expertise>;
};

export type DesignationFilter = {
  expertise?: InputMaybe<Array<Scalars['Int']['input']>>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  level?: InputMaybe<Array<Scalars['Int']['input']>>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type DesignationWithCount = {
  __typename?: 'DesignationWithCount';
  data?: Maybe<Array<Maybe<Designation>>>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type DeviceAnalytics = {
  __typename?: 'DeviceAnalytics';
  daily_active_devices: Scalars['Int']['output'];
  monthly_active_devices: Scalars['Int']['output'];
  total_active_devices: Scalars['Int']['output'];
};

export type DocCompletionFailed = {
  __typename?: 'DocCompletionFailed';
  message?: Maybe<Scalars['String']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
};

export type DocCompletionResult = DocCompletionFailed | DocCompletionSuccess;

export type DocCompletionSuccess = {
  __typename?: 'DocCompletionSuccess';
  message?: Maybe<Scalars['String']['output']>;
};

export enum DocNumberValidationType {
  AlphaNumeric = 'ALPHA_NUMERIC',
  Numeric = 'NUMERIC'
}

export type DocUnderVerificationResult = {
  __typename?: 'DocUnderVerificationResult';
  data?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export enum DocVerificationStatus {
  Completed = 'COMPLETED',
  Failed = 'FAILED',
  NotSubmitted = 'NOT_SUBMITTED',
  Pending = 'PENDING',
  ReUpload = 'RE_UPLOAD'
}

export type Document = {
  __typename?: 'Document';
  admin?: Maybe<Admin>;
  creator_type?: Maybe<Scalars['String']['output']>;
  doc_number?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  type?: Maybe<DocumentType>;
  type_id?: Maybe<Scalars['Int']['output']>;
  url?: Maybe<Scalars['String']['output']>;
  user?: Maybe<User>;
  user_id?: Maybe<Scalars['Int']['output']>;
  verification_message?: Maybe<Scalars['String']['output']>;
  verification_status?: Maybe<Scalars['String']['output']>;
  verified?: Maybe<Scalars['Boolean']['output']>;
};

export type DocumentData = {
  doc_number?: InputMaybe<Scalars['String']['input']>;
  type_id: Scalars['Int']['input'];
  url?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['Int']['input']>;
};

export type DocumentFilter = {
  createdBy?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<TypeFilter>;
  type_id?: InputMaybe<Scalars['Int']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
  verificationFilter?: InputMaybe<VerificationFilter>;
};

export type DocumentFilterAdmin = {
  id?: InputMaybe<Scalars['Int']['input']>;
  verification_status?: InputMaybe<DocVerificationStatus>;
  verified?: InputMaybe<Scalars['Boolean']['input']>;
};

export type DocumentNumberConfig = {
  __typename?: 'DocumentNumberConfig';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  creator_id?: Maybe<Scalars['Int']['output']>;
  document_type_id?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  max_length?: Maybe<Scalars['Int']['output']>;
  min_length?: Maybe<Scalars['Int']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  validation_type?: Maybe<DocNumberValidationType>;
};

export enum DocumentStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE'
}

export type DocumentType = {
  __typename?: 'DocumentType';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  creator?: Maybe<Admin>;
  document?: Maybe<Array<Maybe<Document>>>;
  document_number_config?: Maybe<DocumentNumberConfig>;
  id?: Maybe<Scalars['Int']['output']>;
  instructions?: Maybe<Scalars['JSON']['output']>;
  is_doc_number_required?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  required?: Maybe<Scalars['Boolean']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  updator?: Maybe<Admin>;
  visible_to_service_provider_technicians?: Maybe<Scalars['Boolean']['output']>;
};

export type DocumentTypeInputData = {
  doc_num_max_length?: InputMaybe<Scalars['Int']['input']>;
  doc_num_min_length?: InputMaybe<Scalars['Int']['input']>;
  doc_num_validation_type?: InputMaybe<DocNumberValidationType>;
  instructions?: InputMaybe<Scalars['JSON']['input']>;
  is_doc_number_required?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
  required: Scalars['Boolean']['input'];
  status: DocumentStatus;
  user_type?: InputMaybe<UserTypeDocument>;
  visible_to_service_provider_technicians?: InputMaybe<Scalars['Boolean']['input']>;
};

export type DocumentUpdateResult = {
  __typename?: 'DocumentUpdateResult';
  data?: Maybe<Document>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type DocumentUploadCount = {
  __typename?: 'DocumentUploadCount';
  status?: Maybe<Scalars['String']['output']>;
  total?: Maybe<Scalars['Int']['output']>;
  uploaded_count?: Maybe<Scalars['Int']['output']>;
};

export type DocumentsResult = {
  __typename?: 'DocumentsResult';
  data?: Maybe<Array<Maybe<User>>>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export enum Event_Type {
  InterviewOffline = 'INTERVIEW_OFFLINE',
  InterviewOnline = 'INTERVIEW_ONLINE'
}

export type ExistingAndNewCategory = {
  existing_category?: InputMaybe<Array<TeamSizeAsPerSkill>>;
  new_category?: InputMaybe<Array<InputMaybe<NewCategoryBySp>>>;
};

export type ExistingAndNewSkills = {
  existing_skills?: InputMaybe<Array<Scalars['Int']['input']>>;
  new_skills?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type Expertise = {
  __typename?: 'Expertise';
  icon?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  organization?: Maybe<Organization>;
  organization_id?: Maybe<Scalars['Int']['output']>;
};

export type ExpertiseCount = {
  __typename?: 'ExpertiseCount';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  round?: Maybe<Scalars['Int']['output']>;
};

export type ExpertiseMap = {
  __typename?: 'ExpertiseMap';
  id?: Maybe<Scalars['Int']['output']>;
  user?: Maybe<User>;
};

export type ExpertiseResult = {
  __typename?: 'ExpertiseResult';
  data?: Maybe<Array<Maybe<Expertise>>>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type ExpertiseResults = {
  __typename?: 'ExpertiseResults';
  data?: Maybe<Array<Maybe<Expertise>>>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type ExpertiseUserResult = {
  __typename?: 'ExpertiseUserResult';
  data?: Maybe<Array<Maybe<ExpertiseMap>>>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type ExpertiseWithIdAndName = {
  __typename?: 'ExpertiseWithIdAndName';
  icon?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type ExpertiseWithIdNameIcon = {
  __typename?: 'ExpertiseWithIdNameIcon';
  icon?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type ExportAssessmentResFilter = {
  assign_to?: InputMaybe<AssignmentUserType>;
  assignment_completion_date?: InputMaybe<DateFilter>;
  user_creation_date?: InputMaybe<DateFilter>;
};

export type ExportPolicyTrackingResponse = {
  __typename?: 'ExportPolicyTrackingResponse';
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
  sheet_id?: Maybe<Scalars['String']['output']>;
};

export type ExportUserPolicyTrackingInput = {
  data?: InputMaybe<Array<InputMaybe<UserPolicyData>>>;
};

export enum ExportableDataType {
  Individual = 'INDIVIDUAL',
  Referred = 'REFERRED'
}

export type ExportableReferredData = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  type?: InputMaybe<ExportableDataType>;
};

export enum FeedBackState {
  Completed = 'COMPLETED',
  Expired = 'EXPIRED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export type FeedBackStateInput = {
  COMPLETED?: InputMaybe<Scalars['String']['input']>;
  EXPIRED?: InputMaybe<Scalars['String']['input']>;
  PENDING?: InputMaybe<Scalars['String']['input']>;
};

export type Feedback = {
  __typename?: 'Feedback';
  interviewer_feedback_state: FeedBackState;
};

export type FeedbackFormFilter = {
  feedback_state?: InputMaybe<Scalars['String']['input']>;
};

export type FeedbackFormStatusResponse = {
  __typename?: 'FeedbackFormStatusResponse';
  language?: Maybe<Scalars['JSON']['output']>;
  message: Scalars['String']['output'];
  onboarded: Scalars['Boolean']['output'];
  result: Scalars['Boolean']['output'];
  submitted: Scalars['Boolean']['output'];
  user_id?: Maybe<Scalars['Int']['output']>;
};

export type FeedbackResult = {
  __typename?: 'FeedbackResult';
  comments?: Maybe<Scalars['String']['output']>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  score?: Maybe<Scalars['Int']['output']>;
};

export type FeedbackResults = {
  __typename?: 'FeedbackResults';
  comments?: Maybe<Scalars['String']['output']>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  score?: Maybe<Scalars['Int']['output']>;
};

export type FeedbackResultsFrUser = {
  __typename?: 'FeedbackResultsFrUser';
  feedback_form_id?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  interview_state?: Maybe<InterviewState>;
  interviewee_feedback_result?: Maybe<Scalars['JSON']['output']>;
  interviewee_feedback_state?: Maybe<FeedBackState>;
  interviewer_feedback_state?: Maybe<FeedBackState>;
  template?: Maybe<Template>;
};

export type FeedbackSection = {
  __typename?: 'FeedbackSection';
  id?: Maybe<Scalars['Int']['output']>;
  instruction?: Maybe<Scalars['String']['output']>;
  max_score?: Maybe<Scalars['Int']['output']>;
  required?: Maybe<Scalars['Boolean']['output']>;
  template?: Maybe<Template>;
  title?: Maybe<Scalars['String']['output']>;
};

export type FeedbackSectionInput = {
  id?: InputMaybe<Scalars['Int']['input']>;
  instruction?: InputMaybe<Scalars['String']['input']>;
  max_score?: InputMaybe<Scalars['Int']['input']>;
  required?: InputMaybe<Scalars['Boolean']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export enum FeedbackState {
  Completed = 'COMPLETED',
  Expired = 'EXPIRED',
  Pending = 'PENDING'
}

export type Filter = {
  email?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Gender>;
  id?: InputMaybe<Scalars['Int']['input']>;
  isActive?: InputMaybe<Scalars['Boolean']['input']>;
  location_id?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  onboarding_stage?: InputMaybe<OnboardingStage>;
  phone?: InputMaybe<Scalars['String']['input']>;
  phoneVerified?: InputMaybe<Scalars['Boolean']['input']>;
  phone_verified?: InputMaybe<Scalars['Boolean']['input']>;
};

export enum FilterType {
  Config = 'CONFIG',
  Question = 'QUESTION'
}

export type FilterUsers = {
  gender?: InputMaybe<Scalars['String']['input']>;
  isActive?: InputMaybe<Scalars['Boolean']['input']>;
  role?: InputMaybe<Scalars['Int']['input']>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
};

export enum FulfillmentType {
  Contractor = 'CONTRACTOR',
  InHouse = 'IN_HOUSE'
}

export type FunnelData = {
  __typename?: 'FunnelData';
  count: Scalars['Int']['output'];
  stage: OnboardingStage;
};

export enum Gender {
  Female = 'FEMALE',
  Male = 'MALE',
  NotSpecified = 'NOT_SPECIFIED'
}

export type GeneralAssignmentResult = {
  __typename?: 'GeneralAssignmentResult';
  correct_answer: Scalars['Int']['output'];
  created_at: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  total_questions: Scalars['Int']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type GeneralResult = {
  __typename?: 'GeneralResult';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type GetAllContentInputData = {
  content_id: Scalars['String']['input'];
};

export type GetAllCourseInputData = {
  id: Scalars['String']['input'];
};

export type GetProviderDashboardCardsResult = {
  __typename?: 'GetProviderDashboardCardsResult';
  active_service_providers: Scalars['Int']['output'];
  active_technician: Scalars['Int']['output'];
  assessment: Scalars['Int']['output'];
  genera_training: Scalars['Int']['output'];
  inactive_technician: Scalars['Int']['output'];
  sign_up: Scalars['Int']['output'];
  sp_inactive: Scalars['Int']['output'];
  training: Scalars['Int']['output'];
  user_onboard: Scalars['Int']['output'];
};

export type GetSingleUserTrainingPerformanceResults = {
  __typename?: 'GetSingleUserTrainingPerformanceResults';
  average_marks_percentage: Scalars['Float']['output'];
  completed_chapter: Scalars['Int']['output'];
  completed_course: Scalars['Int']['output'];
  completed_lessons: Scalars['Int']['output'];
  course_name: Scalars['String']['output'];
  total_chapter: Scalars['Int']['output'];
  total_course: Scalars['Int']['output'];
  total_course_test_marks_percentage: Scalars['Float']['output'];
  total_lessons: Scalars['Int']['output'];
};

export type GetUserCoursesDataFilter = {
  course_user_map_id?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Array<UserCourseProgressStatus>>;
};

export type GetUserSingleAssignmentResultData = {
  __typename?: 'GetUserSingleAssignmentResultData';
  _count: QuestionOnAssignmentCount;
  assignment_configration: AssignmentConfigDataFrSingleAssignment;
  assignment_duration_seconds: Scalars['Int']['output'];
  created_at: Scalars['DateTime']['output'];
  finished_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['String']['output'];
  question_on_assignment?: Maybe<Array<Maybe<QuestionOnAssignment>>>;
  result?: Maybe<AssignmentResult>;
  status: AssignmentStatus;
};

export type GetUserTttInputFilter = {
  transfer_to_tms_status?: InputMaybe<Scalars['Boolean']['input']>;
};

export type GetUsers = {
  __typename?: 'GetUsers';
  data?: Maybe<Array<Maybe<User>>>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type GoogleAuthLink = {
  __typename?: 'GoogleAuthLink';
  link?: Maybe<Scalars['String']['output']>;
};

export enum HiringCriteria {
  OnsitePracticalTestAndTheoreticalTest = 'ONSITE_PRACTICAL_TEST_AND_THEORETICAL_TEST',
  TechnicalRoundAndPastExperienceOfModular = 'TECHNICAL_ROUND_AND_PAST_EXPERIENCE_OF_MODULAR',
  TrainingAssessmentTest = 'TRAINING_ASSESSMENT_TEST'
}

export type InputLocation = {
  city?: InputMaybe<Scalars['String']['input']>;
  landmark?: InputMaybe<Scalars['String']['input']>;
  pincode?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  work_address?: InputMaybe<Scalars['String']['input']>;
};

export type InstallationBreakdownChart = {
  __typename?: 'InstallationBreakdownChart';
  datasets: Array<ChartDataset>;
  labels: Array<Scalars['String']['output']>;
};

export type InstallationBreakdownResponse = {
  __typename?: 'InstallationBreakdownResponse';
  data: InstallationBreakdownChart;
  success: Scalars['Boolean']['output'];
};

export type InterviewData = {
  __typename?: 'InterviewData';
  attempts?: Maybe<Scalars['Int']['output']>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  end_time?: Maybe<Scalars['DateTime']['output']>;
  event_id?: Maybe<Scalars['String']['output']>;
  feedback?: Maybe<Feedback>;
  google_meet_link?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  interview_type?: Maybe<Scalars['String']['output']>;
  interviewer?: Maybe<Interviewer>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  scheduled_by?: Maybe<ScheduledBy>;
  start_time?: Maybe<Scalars['DateTime']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type InterviewDetails = {
  __typename?: 'InterviewDetails';
  created_at: Scalars['DateTime']['output'];
  id?: Maybe<Scalars['Int']['output']>;
  note?: Maybe<Scalars['String']['output']>;
};

export type InterviewFeedBack = {
  __typename?: 'InterviewFeedBack';
  feedback_form_id?: Maybe<Scalars['String']['output']>;
  feedback_result?: Maybe<Array<Maybe<FeedbackResult>>>;
  feedback_state?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  interview_state?: Maybe<Scalars['String']['output']>;
  interviewee_feedback_enabled?: Maybe<Scalars['Boolean']['output']>;
  interviewer?: Maybe<Interviewer>;
  template?: Maybe<Template>;
  user?: Maybe<User>;
};

export type InterviewFeedback = {
  __typename?: 'InterviewFeedback';
  created_at: Scalars['DateTime']['output'];
  feedback_form_id?: Maybe<Scalars['String']['output']>;
  feedback_result?: Maybe<Array<Maybe<FeedbackResult>>>;
  feedback_state?: Maybe<FeedBackState>;
  id: Scalars['Int']['output'];
  interview: Array<Maybe<InterviewDetails>>;
  interview_state?: Maybe<Scalars['String']['output']>;
  interviewer?: Maybe<Interviewer>;
  interviewer_feedback_result: Scalars['JSON']['output'];
  interviewer_feedback_state: FeedBackState;
  meta?: Maybe<Scalars['JSON']['output']>;
  summary?: Maybe<Scalars['String']['output']>;
  template?: Maybe<Template>;
  user?: Maybe<User>;
};

export type InterviewFeedbackResults = {
  __typename?: 'InterviewFeedbackResults';
  comments?: Maybe<Scalars['String']['output']>;
  feedback?: Maybe<InterviewFeedback>;
  id?: Maybe<Scalars['Int']['output']>;
  score?: Maybe<Scalars['Int']['output']>;
  section?: Maybe<FeedbackSection>;
};

export type InterviewModeData = {
  interview_id: Scalars['Int']['input'];
  is_online: Scalars['Boolean']['input'];
};

export type InterviewScheduleFor = {
  __typename?: 'InterviewScheduleFor';
  name: Scalars['String']['output'];
};

export type InterviewScheduledBy = {
  __typename?: 'InterviewScheduledBy';
  name: Scalars['String']['output'];
};

export enum InterviewState {
  Cancelled = 'CANCELLED',
  Completed = 'COMPLETED',
  Expired = 'EXPIRED',
  Rejected = 'REJECTED',
  Rescheduled = 'RESCHEDULED',
  Scheduled = 'SCHEDULED'
}

export enum InterviewStateExtended {
  Cancelled = 'CANCELLED',
  Completed = 'COMPLETED',
  Expired = 'EXPIRED',
  NotScheduled = 'NOT_SCHEDULED',
  Rejected = 'REJECTED',
  Rescheduled = 'RESCHEDULED',
  Scheduled = 'SCHEDULED'
}

export enum InterviewType {
  InterviewOffline = 'INTERVIEW_OFFLINE',
  InterviewOnline = 'INTERVIEW_ONLINE'
}

export type InterviewWitInterviewData = {
  __typename?: 'InterviewWitInterviewData';
  interviewer?: Maybe<Interviewer>;
  status?: Maybe<InterviewState>;
};

export type IntervieweeFeedbackForm = {
  __typename?: 'IntervieweeFeedbackForm';
  interviewee_feedback_result: Scalars['JSON']['output'];
  interviewee_feedback_state: FeedBackState;
  interviewer: Interviewer;
  meta?: Maybe<Scalars['JSON']['output']>;
  template: IntervieweeFeedbackTemplate;
  user: User;
};

export type IntervieweeFeedbackFormInput = {
  feedback_form_id: Scalars['String']['input'];
  interviewee_feedback_result: Scalars['JSON']['input'];
};

export type IntervieweeFeedbackTemplate = {
  __typename?: 'IntervieweeFeedbackTemplate';
  interviewee_feedback_meta: Scalars['JSON']['output'];
};

export type Interviewer = {
  __typename?: 'Interviewer';
  active?: Maybe<Scalars['Boolean']['output']>;
  calendar_event?: Maybe<Array<Maybe<CalendarEvents>>>;
  created_on?: Maybe<Scalars['DateTime']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  location?: Maybe<Location>;
  meta?: Maybe<Scalars['JSON']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  updated_on?: Maybe<Scalars['DateTime']['output']>;
};

export type InterviewerFeedbackForm = {
  __typename?: 'InterviewerFeedbackForm';
  interviewer: Interviewer;
  interviewer_feedback_result: Scalars['JSON']['output'];
  interviewer_feedback_state: FeedBackState;
  template: InterviewerFeedbackTemplate;
  user: User;
};

export type InterviewerFeedbackFormInput = {
  feedback_form_id: Scalars['String']['input'];
  interviewer_feedback_result: Scalars['JSON']['input'];
};

export type InterviewerFeedbackResults = {
  __typename?: 'InterviewerFeedbackResults';
  created_at: Scalars['DateTime']['output'];
  id?: Maybe<Scalars['Int']['output']>;
  interviewer: Interviewer;
  interviewer_feedback_result: Scalars['JSON']['output'];
  interviewer_feedback_state: FeedBackState;
  template: InterviewerFeedbackTemplate;
  user: User;
};

export type InterviewerFeedbackTemplate = {
  __typename?: 'InterviewerFeedbackTemplate';
  interviewer_feedback_meta: Scalars['JSON']['output'];
};

export type InterviewerWithCount = {
  __typename?: 'InterviewerWithCount';
  data?: Maybe<Array<Maybe<Interviewer>>>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type InvitedUser = {
  __typename?: 'InvitedUser';
  assignment?: Maybe<Array<TeamMemberAssignmentStatus>>;
  doc_verification_state?: Maybe<TeamMemberDocVerificationStatus>;
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  name: Scalars['String']['output'];
  phone: Scalars['String']['output'];
  user_onboarding_data?: Maybe<Array<TeamMemberOnboardingData>>;
};

export type KeyLabel = {
  __typename?: 'KeyLabel';
  label: Scalars['String']['output'];
  option_audio: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type Lead = {
  __typename?: 'Lead';
  current_work?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phone_number?: Maybe<Scalars['String']['output']>;
  status?: Maybe<LeadHandlingStatus>;
};

export type LeadContacts = {
  __typename?: 'LeadContacts';
  total_contacts_synced?: Maybe<Scalars['Int']['output']>;
  user?: Maybe<User>;
};

export type LeadContactsFilter = {
  search?: InputMaybe<Scalars['String']['input']>;
};

export type LeadContactsWithCount = {
  __typename?: 'LeadContactsWithCount';
  data?: Maybe<Array<Maybe<LeadContacts>>>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type LeadFilters = {
  current_work?: InputMaybe<Scalars['String']['input']>;
  location?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<LeadHandlingStatus>;
  text_search?: InputMaybe<Scalars['String']['input']>;
};

export type LeadGenerationResult = {
  __typename?: 'LeadGenerationResult';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export enum LeadHandlingStatus {
  Handled = 'HANDLED',
  Pending = 'PENDING'
}

export type Lesson = {
  content_id?: InputMaybe<Scalars['String']['input']>;
  duration?: InputMaybe<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
  objective?: InputMaybe<Scalars['String']['input']>;
};

export type LessonInputFrCourseChapter = {
  __typename?: 'LessonInputFrCourseChapter';
  content?: Maybe<ContentFrLesson>;
  content_id: Scalars['String']['output'];
  duration: Scalars['Int']['output'];
  id?: Maybe<Scalars['String']['output']>;
  objective: Scalars['String']['output'];
  title: Scalars['String']['output'];
  user_lesson_progress_track: Array<UserLessonProgressTrack>;
};

export type LessonProgress = {
  __typename?: 'LessonProgress';
  chapter_progress_id?: Maybe<Scalars['String']['output']>;
  is_completed: Scalars['Boolean']['output'];
  time_spent: Scalars['Int']['output'];
};

export type Lessons = {
  content_id?: InputMaybe<Scalars['String']['input']>;
  duration?: InputMaybe<Scalars['Int']['input']>;
  objective?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type Location = {
  __typename?: 'Location';
  city?: Maybe<Scalars['String']['output']>;
  district_taluka?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  landmark?: Maybe<Scalars['String']['output']>;
  pincode?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  work_address?: Maybe<Scalars['String']['output']>;
};

export type LocationFilter = {
  id?: InputMaybe<Scalars['Int']['input']>;
  pincode?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
};

export type ManualAssignment = {
  __typename?: 'ManualAssignment';
  assignment?: Maybe<AssignmentConfig>;
  assignment_config_id?: Maybe<Scalars['String']['output']>;
  created_on?: Maybe<Scalars['DateTime']['output']>;
  total_count?: Maybe<Scalars['Int']['output']>;
  user?: Maybe<User>;
};

export type ManualAssignmentsFilter = {
  search?: InputMaybe<Scalars['String']['input']>;
};

export enum MartialStatus {
  Married = 'MARRIED',
  Unmarried = 'UNMARRIED'
}

export enum MascotNotificationPriority {
  High = 'HIGH',
  Low = 'LOW',
  Medium = 'MEDIUM'
}

export enum MascotSectionType {
  LaunchScreen = 'LAUNCH_SCREEN',
  Training = 'TRAINING'
}

export type Message = {
  __typename?: 'Message';
  message?: Maybe<Scalars['String']['output']>;
};

export type MinMaxPercentage = {
  max: Scalars['Int']['input'];
  min: Scalars['Int']['input'];
};

export type Mutation = {
  __typename?: 'Mutation';
  DeleteUser?: Maybe<UserDeletedData>;
  UpdateUser?: Maybe<UpdateUserData>;
  activeAssignmentConfig: ResponseResult;
  addDocument?: Maybe<Document>;
  addInterviewer?: Maybe<Interviewer>;
  addQuestionsToAssignmentConfig: ResponseResult;
  approveForTraining: ResponseResult;
  approveInterviewCandidate?: Maybe<Message>;
  approveUserForTraining?: Maybe<Scalars['Boolean']['output']>;
  assignAssignmentToUsers: ResponseResult;
  assignContentToAllUser: ResponseResult;
  assignContentToUser: ResponseResult;
  assignCourseBulkUsers: ResponseResult;
  assignCourseToUser: ResponseResult;
  assignExpertise?: Maybe<GeneralResult>;
  assignMultipleCoursesToUser: ResponseResult;
  assignRoleToAdmin: ResponseResult;
  autoFillHiring: UserChecklistDataResponse;
  autoFillTechnician: UserChecklistDataResponse;
  completeDocVerification?: Maybe<DocCompletionResult>;
  completeUserAssignment: CompleteUserAssignmentResult;
  createAdmin?: Maybe<CreateAdminData>;
  createAdminRole?: Maybe<ResponseResult>;
  createAssignmentConfig: CreateAssignmentResultType;
  createAssignmentRule: ResponseResult;
  createBanner: ResponseResult;
  createBannerConfig: ResponseResult;
  createBucket?: Maybe<ResponseResult>;
  createChecklist: ChecklistResponse;
  createContacts: ResponseResult;
  createContent: ResponseResult;
  createCourse: ResponseResult;
  createDesignation?: Maybe<GeneralResult>;
  createDocumentTypeFrAdmin: ResponseResult;
  createExpertise?: Maybe<GeneralResult>;
  createFeedbackSection?: Maybe<FeedbackSection>;
  createFeedbackTemplate: ResponseResult;
  createInterviewFeedback?: Maybe<InterviewFeedback>;
  createLead?: Maybe<LeadGenerationResult>;
  createNewModule: ResponseResult;
  createNewPage: ResponseResult;
  createNewSection: ResponseResult;
  createOrUpdateUserBankData?: Maybe<Result>;
  createOrUpdateUsersOnboardingData?: Maybe<Result>;
  createPermission?: Maybe<Result>;
  createProfessionalExperience?: Maybe<Scalars['Boolean']['output']>;
  createProviderBulk: ResponseResult;
  createProviderOrg: ResponseResult;
  createQuestion?: Maybe<ResponseResult>;
  createQuestionsBulk: ResponseResult;
  createReferralConfigAdmin?: Maybe<ReferralConfig>;
  createRole?: Maybe<Role>;
  createServiceType: ResponseResult;
  createTeamMemberInvitation: ResponseResult;
  createUnregisteredReferral: UnregisteredReferral;
  createUserBulk?: Maybe<BulkAdditionUserResult>;
  createWorkExp?: Maybe<WorkExpResult>;
  deactivateAssignmentConfig: ResponseResult;
  deleteAdminRole: ResponseResult;
  deleteAssignmentRule?: Maybe<ResponseResult>;
  deleteBannerConfig: ResponseResult;
  deleteBucket?: Maybe<ResponseResult>;
  deleteContactOfUserFrAdmin: ResponseResult;
  deleteContent: ResponseResult;
  deleteCourse: ResponseResult;
  deleteDesignation?: Maybe<GeneralResult>;
  deleteDocument?: Maybe<DeleteDocumentType>;
  deleteDocumentType?: Maybe<DocumentUpdateResult>;
  deleteExpertise?: Maybe<GeneralResult>;
  deleteInterviewer?: Maybe<Result>;
  deleteQuestion: ResponseResult;
  deleteRole?: Maybe<DeleteRoleResult>;
  deleteUnregisteredReferral: Scalars['Boolean']['output'];
  deleteUserAdmin?: Maybe<Scalars['Boolean']['output']>;
  deleteWorkExperience?: Maybe<DeleteWorkExpResult>;
  exportUserPolicyTrackingData: ExportPolicyTrackingResponse;
  finalSaveAndPublishAssignmentConfig: ResponseResult;
  finalizeVerification?: Maybe<Result>;
  finalizeVerificationFrSPOrTeamMembers: ResponseResult;
  forgetPassword?: Maybe<VerifyAdminData>;
  forgetPwdChange?: Maybe<VerifyAdminData>;
  forgetPwdOTPVerify?: Maybe<VerifiedOtpData>;
  generateAssignmentForUser: ResponseResultWithData;
  moveContactsToUserFrAdmin: ResponseResult;
  publishModule: ResponseResult;
  referralCodeValidate?: Maybe<ReferralCodeValidationResult>;
  registerUser: RegisterData;
  rejectUserForTraining?: Maybe<Scalars['Boolean']['output']>;
  scheduleInterview?: Maybe<CalendarEvent>;
  sendFcm: SendFcmResponse;
  sendOtp?: Maybe<SendOtpResult>;
  sendRefferalInvite: UnregisteredReferral;
  signInAdmin?: Maybe<CreateAdminData>;
  signInOrganization?: Maybe<AuthResult>;
  signUpOrganization?: Maybe<AuthResult>;
  submitAnswerForSingleQuestion: ResponseResult;
  switchSPToTechnician?: Maybe<SwitchUserTypeResult>;
  switchTechnicianToSP?: Maybe<SwitchUserTypeResult>;
  trackAnalytics: AnalyticsResponse;
  transferTeamMemberToTMS: ResponseResult;
  unassignAssignmentToUser: ResponseResult;
  updateAdmin?: Maybe<AllAdminResult>;
  updateAdminOnOrganization?: Maybe<AllAdminResult>;
  updateAdminRole: ResponseResult;
  updateAssignmentConfig: ResponseResult;
  updateBanner: ResponseResult;
  updateBannerConfig: ResponseResult;
  updateBucketName: ResponseResult;
  updateChecklist: ChecklistResponse;
  updateContent: ResponseResult;
  updateCourse: ResponseResult;
  updateDesignation?: Maybe<GeneralResult>;
  updateDocumentAdmin?: Maybe<DocumentUpdateResult>;
  updateDocumentType?: Maybe<DocumentUpdateResult>;
  updateExpertise?: Maybe<GeneralResult>;
  updateExpertiseOrder?: Maybe<GeneralResult>;
  updateFeedbackSection?: Maybe<FeedbackSection>;
  updateFeedbackTemplate: ResponseResult;
  updateInterview?: Maybe<Scalars['Boolean']['output']>;
  updateInterviewMode?: Maybe<Scalars['Boolean']['output']>;
  updateInterviewResult?: Maybe<Message>;
  updateIntervieweeFeedbackForm: Result;
  updateInterviewer?: Maybe<Interviewer>;
  updateInterviewerFeedbackForm: Result;
  updateLeadStatus?: Maybe<LeadGenerationResult>;
  updateNotification: ResponseResult;
  updateOrganization?: Maybe<UpdateOrganizationResult>;
  updatePermission?: Maybe<Result>;
  updatePolicyAcceptance: PolicyUpdateResponse;
  updateProvider: ResponseResult;
  updateProviderDetailsAdmin: ResponseResult;
  updateQuestion: ResponseResult;
  updateReferralConfigurations?: Maybe<Scalars['Boolean']['output']>;
  updateRejectandBanUser?: Maybe<UserData>;
  updateRole?: Maybe<Role>;
  updateTeamMember: ResponseResult;
  updateTeamMemberDetailsFrAdmin: ResponseResult;
  updateTeamMemberTmsData: ResponseResult;
  updateTechnicianFeedbackData: SingleUserChecklistDataResponse;
  updateUnregisteredReferral: UnregisteredReferral;
  updateUserAdmin?: Maybe<User>;
  updateUserChecklistData: SingleUserChecklistDataResponse;
  updateUserCourseStatus: ResponseResult;
  updateUserMascotAlert: ResponseResult;
  updateUserMeta?: Maybe<Scalars['Boolean']['output']>;
  updateUserPreferredLanguage?: Maybe<Scalars['Boolean']['output']>;
  updateUserType: UpdateUserTypeResult;
  updateUserWatchProgress: ResponseResult;
  updateUsersOnboardingData?: Maybe<Scalars['Boolean']['output']>;
  updateVideoWatchTime: UserVideoProgressResultData;
  verifyOtpAdmin?: Maybe<CreateAdminData>;
  verifyPhoneNumber?: Maybe<VerifyPhoneResult>;
};


export type MutationUpdateUserArgs = {
  data?: InputMaybe<UpdateUserInput>;
};


export type MutationActiveAssignmentConfigArgs = {
  id: Scalars['String']['input'];
};


export type MutationAddDocumentArgs = {
  data?: InputMaybe<DocumentData>;
};


export type MutationAddInterviewerArgs = {
  data: AddInterviewerData;
};


export type MutationAddQuestionsToAssignmentConfigArgs = {
  assignment_config_id: Scalars['String']['input'];
  question_ids: Array<InputMaybe<Scalars['String']['input']>>;
};


export type MutationApproveForTrainingArgs = {
  status: TrainingApprovalStatus;
  user_id: Scalars['Int']['input'];
};


export type MutationApproveInterviewCandidateArgs = {
  id?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationApproveUserForTrainingArgs = {
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationAssignAssignmentToUsersArgs = {
  assignment_config_id: Scalars['String']['input'];
  user_ids: Array<Scalars['Int']['input']>;
};


export type MutationAssignContentToAllUserArgs = {
  data: AssignContentToAllUsers;
};


export type MutationAssignContentToUserArgs = {
  data: AssignContentInputData;
  filter?: InputMaybe<UserContentFilter>;
};


export type MutationAssignCourseBulkUsersArgs = {
  data: AssignCourseBulkInputData;
  filter?: InputMaybe<UserCourseFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type MutationAssignCourseToUserArgs = {
  data: AssignCourseInputData;
};


export type MutationAssignExpertiseArgs = {
  expertise?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};


export type MutationAssignMultipleCoursesToUserArgs = {
  assign_course_ids: Array<InputMaybe<Scalars['String']['input']>>;
  unassign_course_ids: Array<InputMaybe<Scalars['String']['input']>>;
  user_id: Scalars['Int']['input'];
};


export type MutationAssignRoleToAdminArgs = {
  admin_id: Scalars['Int']['input'];
  admin_role_id: Scalars['Int']['input'];
};


export type MutationAutoFillHiringArgs = {
  user_id: Scalars['Int']['input'];
};


export type MutationAutoFillTechnicianArgs = {
  user_id: Scalars['Int']['input'];
};


export type MutationCompleteDocVerificationArgs = {
  doc_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationCompleteUserAssignmentArgs = {
  assignment_id: Scalars['String']['input'];
};


export type MutationCreateAdminArgs = {
  data?: InputMaybe<AdminCreateInput>;
};


export type MutationCreateAdminRoleArgs = {
  data?: InputMaybe<AdminRoleInput>;
};


export type MutationCreateAssignmentConfigArgs = {
  data: CreateAssignmentConfigInput;
};


export type MutationCreateAssignmentRuleArgs = {
  data: AssignmentRulesParams;
};


export type MutationCreateBannerArgs = {
  data: CreateBannerInput;
};


export type MutationCreateBannerConfigArgs = {
  data: CreateBannerConfigInput;
};


export type MutationCreateBucketArgs = {
  assign_to?: InputMaybe<AssignmentUserType>;
  name: Scalars['String']['input'];
};


export type MutationCreateChecklistArgs = {
  data: CreateChecklistInput;
};


export type MutationCreateContactsArgs = {
  contacts?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateContentArgs = {
  data: CreateContentInputData;
};


export type MutationCreateCourseArgs = {
  data: CreateCourseInputData;
};


export type MutationCreateDesignationArgs = {
  data: CreateDesignationInput;
};


export type MutationCreateDocumentTypeFrAdminArgs = {
  data: DocumentTypeInputData;
};


export type MutationCreateExpertiseArgs = {
  icon?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateFeedbackSectionArgs = {
  data?: InputMaybe<CreateFeedbackSection>;
};


export type MutationCreateFeedbackTemplateArgs = {
  data: TemplateInput;
};


export type MutationCreateInterviewFeedbackArgs = {
  data?: InputMaybe<CreateInterviewFeedbackInput>;
};


export type MutationCreateLeadArgs = {
  data?: InputMaybe<CreateLeadInput>;
};


export type MutationCreateNewModuleArgs = {
  features: Array<InputMaybe<NewFeatureData>>;
  module: NewModuleData;
};


export type MutationCreateNewPageArgs = {
  module_id: Scalars['Int']['input'];
  page: Array<InputMaybe<NewFeatureData>>;
};


export type MutationCreateNewSectionArgs = {
  moduleId: Scalars['Int']['input'];
  sections: Array<SectionInput>;
  submoduleId: Scalars['Int']['input'];
};


export type MutationCreateOrUpdateUserBankDataArgs = {
  userBankData?: InputMaybe<UserBankDetailsInput>;
};


export type MutationCreateOrUpdateUsersOnboardingDataArgs = {
  userOnboardingData?: InputMaybe<UserOnboardingDataInput>;
};


export type MutationCreatePermissionArgs = {
  data?: InputMaybe<PermissionInput>;
};


export type MutationCreateProfessionalExperienceArgs = {
  data?: InputMaybe<UserProfessionalExperienceArgs>;
};


export type MutationCreateProviderBulkArgs = {
  data: Array<CreateProviderOrgInputBulk>;
};


export type MutationCreateProviderOrgArgs = {
  data: CreateProviderOrgInput;
};


export type MutationCreateQuestionArgs = {
  data: QuestionCreateUpdateParams;
};


export type MutationCreateQuestionsBulkArgs = {
  data: Array<QuestionCreateUpdateParams>;
};


export type MutationCreateReferralConfigAdminArgs = {
  data?: InputMaybe<ReferralConfigInput>;
};


export type MutationCreateRoleArgs = {
  data?: InputMaybe<CreateRoleData>;
};


export type MutationCreateServiceTypeArgs = {
  name: Array<Scalars['String']['input']>;
};


export type MutationCreateTeamMemberInvitationArgs = {
  data: TeamMemberInvitationInput;
};


export type MutationCreateUnregisteredReferralArgs = {
  data: CreateUnregisteredReferralInput;
};


export type MutationCreateUserBulkArgs = {
  data?: InputMaybe<Array<InputMaybe<BulkUserData>>>;
};


export type MutationCreateWorkExpArgs = {
  data?: InputMaybe<Array<InputMaybe<WorkExpInput>>>;
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationDeactivateAssignmentConfigArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteAdminRoleArgs = {
  admin_role_id: Scalars['Int']['input'];
};


export type MutationDeleteAssignmentRuleArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteBannerConfigArgs = {
  banner_config_id: Scalars['String']['input'];
};


export type MutationDeleteBucketArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteContactOfUserFrAdminArgs = {
  contact_id: Array<Scalars['String']['input']>;
  user_id: Scalars['Int']['input'];
};


export type MutationDeleteContentArgs = {
  internal_content_id: Scalars['String']['input'];
};


export type MutationDeleteCourseArgs = {
  internal_course_id: Scalars['String']['input'];
};


export type MutationDeleteDesignationArgs = {
  designation_id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteDocumentArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteDocumentTypeArgs = {
  document_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationDeleteExpertiseArgs = {
  expertise_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationDeleteInterviewerArgs = {
  interviewer_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationDeleteQuestionArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteRoleArgs = {
  role_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationDeleteUnregisteredReferralArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteUserAdminArgs = {
  user_id: Scalars['Int']['input'];
};


export type MutationDeleteWorkExperienceArgs = {
  exp_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationExportUserPolicyTrackingDataArgs = {
  data?: InputMaybe<ExportUserPolicyTrackingInput>;
};


export type MutationFinalSaveAndPublishAssignmentConfigArgs = {
  assignment_config_id: Scalars['String']['input'];
};


export type MutationFinalizeVerificationArgs = {
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationFinalizeVerificationFrSpOrTeamMembersArgs = {
  user_id: Scalars['Int']['input'];
};


export type MutationForgetPasswordArgs = {
  data?: InputMaybe<AdminForgetPwdInput>;
};


export type MutationForgetPwdChangeArgs = {
  data?: InputMaybe<AdminPwdChangeInput>;
};


export type MutationForgetPwdOtpVerifyArgs = {
  data?: InputMaybe<VerifyOtp>;
};


export type MutationGenerateAssignmentForUserArgs = {
  assignment_config_id: Scalars['String']['input'];
};


export type MutationMoveContactsToUserFrAdminArgs = {
  contact_id: Array<Scalars['String']['input']>;
  user_id: Scalars['Int']['input'];
};


export type MutationPublishModuleArgs = {
  module_id: Scalars['Int']['input'];
  role_permission: Array<RolePermissionData>;
};


export type MutationReferralCodeValidateArgs = {
  data?: InputMaybe<ReferralInput>;
};


export type MutationRegisterUserArgs = {
  data: RegisterInput;
};


export type MutationRejectUserForTrainingArgs = {
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationScheduleInterviewArgs = {
  data?: InputMaybe<CreateCalendarEventData>;
};


export type MutationSendFcmArgs = {
  input: SendFcmInput;
};


export type MutationSendOtpArgs = {
  phone?: InputMaybe<Scalars['String']['input']>;
};


export type MutationSendRefferalInviteArgs = {
  data: SendRefferalInviteInput;
};


export type MutationSignInAdminArgs = {
  data?: InputMaybe<AdminLoginInput>;
};


export type MutationSignInOrganizationArgs = {
  otp: Scalars['String']['input'];
  password: Scalars['String']['input'];
  phoneNumber: Scalars['String']['input'];
};


export type MutationSignUpOrganizationArgs = {
  data?: InputMaybe<CreateOrganizationData>;
};


export type MutationSubmitAnswerForSingleQuestionArgs = {
  data?: InputMaybe<SubmitAnswerForSingleQuestionInput>;
};


export type MutationSwitchSpToTechnicianArgs = {
  data?: InputMaybe<SwitchSpToTechnicianInput>;
};


export type MutationSwitchTechnicianToSpArgs = {
  data?: InputMaybe<SwitchTechnicianToSpInput>;
};


export type MutationTrackAnalyticsArgs = {
  input: TrackAnalyticsInput;
};


export type MutationTransferTeamMemberToTmsArgs = {
  org_id: Scalars['Int']['input'];
};


export type MutationUnassignAssignmentToUserArgs = {
  assignment_config_id: Scalars['String']['input'];
  user_id: Scalars['Int']['input'];
};


export type MutationUpdateAdminArgs = {
  data?: InputMaybe<AdminUpdateInput>;
};


export type MutationUpdateAdminOnOrganizationArgs = {
  data?: InputMaybe<AdminUpdateInput>;
};


export type MutationUpdateAdminRoleArgs = {
  data: UpdateAdminRoleInput;
};


export type MutationUpdateAssignmentConfigArgs = {
  data: AssignmentConfigUpdate;
  id: Scalars['String']['input'];
};


export type MutationUpdateBannerArgs = {
  data?: InputMaybe<UpdateBannerInput>;
};


export type MutationUpdateBannerConfigArgs = {
  data: UpdateBannerConfigInput;
};


export type MutationUpdateBucketNameArgs = {
  id: Scalars['String']['input'];
  name: Scalars['String']['input'];
};


export type MutationUpdateChecklistArgs = {
  data: UpdateChecklistInput;
};


export type MutationUpdateContentArgs = {
  data: UpdateContentInputData;
};


export type MutationUpdateCourseArgs = {
  data: UpdateCourseInputData;
};


export type MutationUpdateDesignationArgs = {
  data?: InputMaybe<UpdateDesignationInput>;
  designation_id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateDocumentAdminArgs = {
  data?: InputMaybe<UpdateDocumentInput>;
  id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationUpdateDocumentTypeArgs = {
  data?: InputMaybe<UpdateDocumentTypeInput>;
};


export type MutationUpdateExpertiseArgs = {
  expertise_id?: InputMaybe<Scalars['Int']['input']>;
  icon?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateExpertiseOrderArgs = {
  dragged_expertise_id?: InputMaybe<Scalars['Int']['input']>;
  target_expertise_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationUpdateFeedbackSectionArgs = {
  data?: InputMaybe<FeedbackSectionInput>;
};


export type MutationUpdateFeedbackTemplateArgs = {
  data: UpdateFeedbackTemplateInput;
};


export type MutationUpdateInterviewArgs = {
  data?: InputMaybe<UpdateCalendarEventArgsType>;
};


export type MutationUpdateInterviewModeArgs = {
  data?: InputMaybe<InterviewModeData>;
};


export type MutationUpdateInterviewResultArgs = {
  id?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<FeedBackState>;
};


export type MutationUpdateIntervieweeFeedbackFormArgs = {
  data: IntervieweeFeedbackFormInput;
};


export type MutationUpdateInterviewerArgs = {
  data: UpdateInterviewerData;
};


export type MutationUpdateInterviewerFeedbackFormArgs = {
  data: InterviewerFeedbackFormInput;
};


export type MutationUpdateLeadStatusArgs = {
  lead_id: Scalars['Int']['input'];
  status: LeadHandlingStatus;
};


export type MutationUpdateNotificationArgs = {
  data?: InputMaybe<NotificationUpdateInput>;
  notification_id: Scalars['String']['input'];
};


export type MutationUpdateOrganizationArgs = {
  data?: InputMaybe<UpdateOrganizationInput>;
};


export type MutationUpdatePermissionArgs = {
  data?: InputMaybe<PermissionInput>;
  id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationUpdatePolicyAcceptanceArgs = {
  data: PolicyAcceptInput;
};


export type MutationUpdateProviderArgs = {
  data?: InputMaybe<UpdateProviderInput>;
};


export type MutationUpdateProviderDetailsAdminArgs = {
  data: UpdateProviderDetailsAdminInput;
};


export type MutationUpdateQuestionArgs = {
  data?: InputMaybe<QuestionUpdateParams>;
  question_id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateReferralConfigurationsArgs = {
  configuration_id: Scalars['Int']['input'];
  data?: InputMaybe<UpdateReferralConfigInput>;
};


export type MutationUpdateRejectandBanUserArgs = {
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationUpdateRoleArgs = {
  data?: InputMaybe<UpdateRoleData>;
  role_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationUpdateTeamMemberArgs = {
  data?: InputMaybe<UpdateTeamMemberInput>;
};


export type MutationUpdateTeamMemberDetailsFrAdminArgs = {
  data: TeamMemberDetailsFrAdminInput;
  user_id: Scalars['Int']['input'];
};


export type MutationUpdateTeamMemberTmsDataArgs = {
  onboarding_data: Scalars['JSON']['input'];
  user_id: Scalars['Int']['input'];
};


export type MutationUpdateTechnicianFeedbackDataArgs = {
  data: UpdateUserChecklistDataInput;
};


export type MutationUpdateUnregisteredReferralArgs = {
  data: CreateUnregisteredReferralInput;
  id: Scalars['ID']['input'];
};


export type MutationUpdateUserAdminArgs = {
  data?: InputMaybe<UpdateUserAdminInput>;
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationUpdateUserChecklistDataArgs = {
  data: Array<InputMaybe<UpdateUserChecklistDataInput>>;
};


export type MutationUpdateUserCourseStatusArgs = {
  data: UpdateUserCourseStatusInputData;
};


export type MutationUpdateUserMascotAlertArgs = {
  id: Scalars['String']['input'];
};


export type MutationUpdateUserMetaArgs = {
  data?: InputMaybe<UserMetaInput>;
};


export type MutationUpdateUserPreferredLanguageArgs = {
  data?: InputMaybe<UserPreferredLanguageInput>;
};


export type MutationUpdateUserTypeArgs = {
  data: UpdateUserTypeInput;
};


export type MutationUpdateUserWatchProgressArgs = {
  data?: InputMaybe<UpdateUserWatchProgressInputData>;
};


export type MutationUpdateVideoWatchTimeArgs = {
  content_id: Scalars['String']['input'];
  is_completed?: InputMaybe<Scalars['Boolean']['input']>;
  user_course_id: Scalars['String']['input'];
  video_progress_in_seconds: Scalars['Int']['input'];
};


export type MutationVerifyOtpAdminArgs = {
  data?: InputMaybe<VerifyOtp>;
};


export type MutationVerifyPhoneNumberArgs = {
  otp?: InputMaybe<Scalars['String']['input']>;
};

export type NewCategoryBySp = {
  icon: Scalars['String']['input'];
  name: Scalars['String']['input'];
  team_member_count: Scalars['Int']['input'];
};

export type NewFeatureData = {
  is_module?: InputMaybe<Scalars['Boolean']['input']>;
  sub_module_name?: InputMaybe<Scalars['String']['input']>;
  sub_module_url_sp?: InputMaybe<Scalars['String']['input']>;
  sub_module_url_user?: InputMaybe<Scalars['String']['input']>;
};

export type NewModuleData = {
  icon_name: Scalars['String']['input'];
  module_name: Scalars['String']['input'];
  visible_in_sp: Scalars['Boolean']['input'];
  visible_in_users: Scalars['Boolean']['input'];
};

export type Notification = {
  __typename?: 'Notification';
  created_at: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  is_read: Scalars['Boolean']['output'];
  message: Scalars['String']['output'];
  meta: Scalars['JSON']['output'];
  priority?: Maybe<NotificationPriority>;
  redirect_to: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type NotificationFilterInput = {
  is_read?: InputMaybe<Scalars['Boolean']['input']>;
};

export enum NotificationPriority {
  High = 'HIGH',
  Low = 'LOW',
  Medium = 'MEDIUM'
}

export type NotificationUpdateInput = {
  is_delete?: InputMaybe<Scalars['Boolean']['input']>;
  is_read?: InputMaybe<Scalars['Boolean']['input']>;
};

export enum OnboardingStage {
  Dashboard = 'DASHBOARD',
  Documents = 'DOCUMENTS',
  GeneralDetails = 'GENERAL_DETAILS',
  Interview = 'INTERVIEW',
  NotStarted = 'NOT_STARTED',
  Onboard = 'ONBOARD',
  ScreeningTest = 'SCREENING_TEST',
  SignUp = 'SIGN_UP',
  Skills = 'SKILLS',
  TrainingAndTests = 'TRAINING_AND_TESTS',
  TransferredToTms = 'TRANSFERRED_TO_TMS',
  Verification = 'VERIFICATION',
  WorkExperience = 'WORK_EXPERIENCE'
}

export type Option = {
  __typename?: 'Option';
  id?: Maybe<Scalars['String']['output']>;
  is_right_option?: Maybe<Scalars['Boolean']['output']>;
  option_text?: Maybe<Scalars['String']['output']>;
};

export type OptionInput = {
  is_right_option?: InputMaybe<Scalars['Boolean']['input']>;
  option_text?: InputMaybe<Scalars['String']['input']>;
};

export type Options = {
  __typename?: 'Options';
  is_right_option: Scalars['Boolean']['output'];
  option_text: Scalars['String']['output'];
};

export type OrgCreateInput = {
  category: Array<Scalars['Int']['input']>;
  company_name: Scalars['String']['input'];
  company_type?: InputMaybe<CompanyType>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  service_type: Array<Scalars['String']['input']>;
};

export type OrgCreateInputBulk = {
  category?: InputMaybe<Array<Scalars['Int']['input']>>;
  company_name: Scalars['String']['input'];
  company_type?: InputMaybe<CompanyType>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  service_type: Array<Scalars['String']['input']>;
};

export type OrgDataFrTeamMember = {
  __typename?: 'OrgDataFrTeamMember';
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  org_expertise_map?: Maybe<Array<OrgExpMapFrTeamMember>>;
};

export type OrgDetailsFrUser = {
  __typename?: 'OrgDetailsFrUser';
  id: Scalars['Int']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  name: Scalars['String']['output'];
  org_owner?: Maybe<OrgOwner>;
  phone?: Maybe<Scalars['String']['output']>;
};

export type OrgExpMapFrTeamMember = {
  __typename?: 'OrgExpMapFrTeamMember';
  expertise: Expertise;
};

export type OrgExpertiseMap = {
  __typename?: 'OrgExpertiseMap';
  expertise?: Maybe<Expertise>;
  id?: Maybe<Scalars['String']['output']>;
  team_member_count?: Maybe<Scalars['Int']['output']>;
};

export type OrgOwner = {
  __typename?: 'OrgOwner';
  assignment?: Maybe<Array<Assignment>>;
  doc_verification_state?: Maybe<DocVerificationStatus>;
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  name: Scalars['String']['output'];
  onboarded?: Maybe<Scalars['Boolean']['output']>;
  onboarding_stage?: Maybe<OnboardingStage>;
  phone: Scalars['String']['output'];
  photoUrl?: Maybe<Scalars['String']['output']>;
  team_member_invitation_sent?: Maybe<Array<TeamMemberInvitationSent>>;
  transfer_to_tms_status?: Maybe<Scalars['Boolean']['output']>;
  user_onboarding_data?: Maybe<Array<Maybe<UserOnboardingData>>>;
  user_professional_data?: Maybe<Array<Maybe<UserProfessionalExperienceResult>>>;
};

export type OrgSearchOrg = {
  __typename?: 'OrgSearchOrg';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type OrgUpdateInput = {
  category?: InputMaybe<ExistingAndNewCategory>;
  city?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  company_name?: InputMaybe<Scalars['String']['input']>;
  company_type?: InputMaybe<CompanyType>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  service_type?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Organization = {
  __typename?: 'Organization';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  location_id?: Maybe<Scalars['Int']['output']>;
  logo?: Maybe<Scalars['String']['output']>;
  logo_url?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  org_owner_id?: Maybe<Scalars['Int']['output']>;
  trades?: Maybe<Scalars['JSON']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type OrganizationServiceTypeMap = {
  __typename?: 'OrganizationServiceTypeMap';
  service_type?: Maybe<ServiceType>;
};

export type OrganizationsAdmin = {
  __typename?: 'OrganizationsAdmin';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type OverallAssignmentInfo = {
  __typename?: 'OverallAssignmentInfo';
  average_percentage: Scalars['Int']['output'];
  done: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type Pagination = {
  skip?: InputMaybe<Scalars['Int']['input']>;
  take?: InputMaybe<Scalars['Int']['input']>;
};

export type PartialUserData = {
  __typename?: 'PartialUserData';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onboarding_stage?: Maybe<OnboardingStage>;
  phone?: Maybe<Scalars['String']['output']>;
};

export type PerformanceDashboardTrainingResult = {
  __typename?: 'PerformanceDashboardTrainingResult';
  active_learners: Scalars['Int']['output'];
  average_course_completion_percentage: Scalars['Float']['output'];
  completed_courses_learners: Scalars['Int']['output'];
  total_learners: Scalars['Int']['output'];
};

export type PermissionInput = {
  assessment?: InputMaybe<Array<Scalars['String']['input']>>;
  assignment?: InputMaybe<Array<Scalars['String']['input']>>;
  banners?: InputMaybe<Array<Scalars['JSON']['input']>>;
  dashboard?: InputMaybe<Array<Scalars['String']['input']>>;
  document_type?: InputMaybe<Array<Scalars['String']['input']>>;
  documents?: InputMaybe<Array<Scalars['String']['input']>>;
  interviews?: InputMaybe<Array<Scalars['String']['input']>>;
  lead_management?: InputMaybe<Array<Scalars['String']['input']>>;
  name?: InputMaybe<Scalars['String']['input']>;
  referral?: InputMaybe<Array<Scalars['String']['input']>>;
  reject_user?: InputMaybe<Array<Scalars['String']['input']>>;
  settings?: InputMaybe<Array<Scalars['String']['input']>>;
  skills?: InputMaybe<Array<Scalars['String']['input']>>;
  training_and_test?: InputMaybe<Array<Scalars['String']['input']>>;
  transfer_to_tms?: InputMaybe<Array<Scalars['String']['input']>>;
  users?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type PermissionResult = {
  __typename?: 'PermissionResult';
  assessment?: Maybe<Array<Scalars['JSON']['output']>>;
  assignment?: Maybe<Array<Scalars['JSON']['output']>>;
  banners?: Maybe<Array<Scalars['JSON']['output']>>;
  dashboard?: Maybe<Array<Scalars['JSON']['output']>>;
  document_type?: Maybe<Array<Scalars['JSON']['output']>>;
  documents?: Maybe<Array<Scalars['JSON']['output']>>;
  id?: Maybe<Scalars['Int']['output']>;
  interviews?: Maybe<Array<Scalars['JSON']['output']>>;
  lead_contacts?: Maybe<Array<Scalars['JSON']['output']>>;
  lead_management?: Maybe<Array<Scalars['JSON']['output']>>;
  name?: Maybe<Scalars['String']['output']>;
  referral?: Maybe<Array<Scalars['JSON']['output']>>;
  reject_user?: Maybe<Array<Scalars['JSON']['output']>>;
  service_providers?: Maybe<Array<Scalars['JSON']['output']>>;
  settings?: Maybe<Array<Scalars['JSON']['output']>>;
  skills?: Maybe<Array<Scalars['JSON']['output']>>;
  training_and_test?: Maybe<Array<Scalars['JSON']['output']>>;
  transfer_to_tms?: Maybe<Array<Scalars['JSON']['output']>>;
  users?: Maybe<Array<Scalars['JSON']['output']>>;
  users_manage?: Maybe<Array<Scalars['JSON']['output']>>;
};

export type Permissions = {
  __typename?: 'Permissions';
  data?: Maybe<Array<Maybe<PermissionResult>>>;
};

export type Policy = {
  __typename?: 'Policy';
  created_at: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  is_active: Scalars['Boolean']['output'];
  policy_name: Scalars['String']['output'];
  policy_name_hindi?: Maybe<Scalars['String']['output']>;
  updated_at: Scalars['DateTime']['output'];
  url?: Maybe<Scalars['String']['output']>;
  user_policy_tracking?: Maybe<Array<Maybe<User_Policy_Tracking>>>;
};

export type PolicyAcceptInput = {
  policy_id: Scalars['String']['input'];
};

export type PolicyData = {
  __typename?: 'PolicyData';
  totalCount?: Maybe<Scalars['Int']['output']>;
  totalOnboardedUsers?: Maybe<Scalars['Int']['output']>;
  users?: Maybe<Array<Maybe<UserPolicyTrackingData>>>;
  usersWithAllPoliciesAccepted?: Maybe<Scalars['Int']['output']>;
};

export type PolicyResponse = {
  __typename?: 'PolicyResponse';
  data?: Maybe<Array<Maybe<Policy>>>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type PolicyTrackingFilter = {
  allAccepted?: InputMaybe<Scalars['Boolean']['input']>;
  endDate?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
};

export type PolicyTrackingResponse = {
  __typename?: 'PolicyTrackingResponse';
  data?: Maybe<PolicyData>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type PolicyUpdateResponse = {
  __typename?: 'PolicyUpdateResponse';
  data?: Maybe<Policy>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type PolicyWithUserStatus = {
  __typename?: 'PolicyWithUserStatus';
  created_at: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  is_active: Scalars['Boolean']['output'];
  policy_name: Scalars['String']['output'];
  policy_name_hindi?: Maybe<Scalars['String']['output']>;
  updated_at: Scalars['DateTime']['output'];
  url?: Maybe<Scalars['String']['output']>;
  user_status?: Maybe<Array<Maybe<UserPolicyStatus>>>;
};

export type PolicyWithUserStatusResponse = {
  __typename?: 'PolicyWithUserStatusResponse';
  data?: Maybe<Array<Maybe<PolicyWithUserStatus>>>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type ProfessionalData = {
  __typename?: 'ProfessionalData';
  highest_education?: Maybe<Scalars['String']['output']>;
  year_of_experience?: Maybe<Scalars['Int']['output']>;
};

export type ProfileCompletion = {
  __typename?: 'ProfileCompletion';
  percentage?: Maybe<Scalars['Int']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type Provider = {
  __typename?: 'Provider';
  category: Scalars['String']['output'];
  location?: Maybe<Location>;
  name: Scalars['String']['output'];
  onboarding_date?: Maybe<Scalars['DateTime']['output']>;
  org: Organization;
  owner?: Maybe<Provider>;
  phone_number: Scalars['String']['output'];
  provider_code: Scalars['String']['output'];
  sign_up_date?: Maybe<Scalars['DateTime']['output']>;
  status?: Maybe<UserStatus>;
};

export type ProviderCreateInput = {
  email?: InputMaybe<Scalars['String']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  pincode?: InputMaybe<Scalars['String']['input']>;
  skills: Array<Scalars['Int']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  years_of_experience?: InputMaybe<Scalars['Int']['input']>;
};

export type ProviderCreateInputBulk = {
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
  phone: Scalars['String']['input'];
  poc?: InputMaybe<Scalars['String']['input']>;
  primary_city?: InputMaybe<Scalars['String']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
};

export type ProviderDetails = {
  __typename?: 'ProviderDetails';
  company_type?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  org_expertise_map?: Maybe<Array<Maybe<OrgExpertiseMap>>>;
  org_owner?: Maybe<OrgOwner>;
  organization_service_type_map?: Maybe<Array<Maybe<OrganizationServiceTypeMap>>>;
  user?: Maybe<User>;
};

export type ProviderDetailsFrAdmin = {
  __typename?: 'ProviderDetailsFrAdmin';
  city?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  name: Scalars['String']['output'];
  org_expertise_map?: Maybe<Array<Maybe<OrgExpertiseMap>>>;
  org_owner?: Maybe<OrgOwner>;
  org_owner_id?: Maybe<Scalars['Int']['output']>;
  organization_service_type_map?: Maybe<Array<Maybe<OrganizationServiceTypeMap>>>;
};

export type ProviderUpdateInput = {
  email?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Gender>;
  landmark?: InputMaybe<Scalars['String']['input']>;
  location_id?: InputMaybe<Scalars['Int']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  onboarding_stage?: InputMaybe<OnboardingStage>;
  phone?: InputMaybe<Scalars['String']['input']>;
  photoUrl?: InputMaybe<Scalars['String']['input']>;
  pincode?: InputMaybe<Scalars['String']['input']>;
  skills?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  state?: InputMaybe<Scalars['String']['input']>;
  work_address?: InputMaybe<Scalars['String']['input']>;
  years_of_experience?: InputMaybe<Scalars['Int']['input']>;
};

export type ProvidersFilter = {
  city?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  expertise?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  stage?: InputMaybe<Array<InputMaybe<OnboardingStage>>>;
  user_creation_date?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Query = {
  __typename?: 'Query';
  adminDetails?: Maybe<AdminResult>;
  allAdminDetails?: Maybe<AllAdminResult>;
  allLocations?: Maybe<Array<Maybe<Location>>>;
  allUsersCount?: Maybe<TotalUsersCountResult>;
  applications: Scalars['Int']['output'];
  approvedCandidate?: Maybe<Array<Maybe<User>>>;
  banner?: Maybe<Banner>;
  bannerConfigs: BannerConfigsWithCount;
  banners: Array<Banner>;
  checkIfUserExists: Scalars['Boolean']['output'];
  checkUnregisteredReferral: CheckUnregisteredReferralResponse;
  contents: ContentResultData;
  countUsersByOnboardingStageDuration?: Maybe<Scalars['JSON']['output']>;
  courses: CoursesResponseResultWithData;
  docUnderVerification?: Maybe<DocUnderVerificationResult>;
  document?: Maybe<Document>;
  documentTypes?: Maybe<Array<DocumentType>>;
  documentTypesAdmin?: Maybe<Array<Maybe<DocumentType>>>;
  documents?: Maybe<Array<Maybe<Document>>>;
  documentsAdmin?: Maybe<DocumentsResult>;
  expertise?: Maybe<ExpertiseResult>;
  exportAllServiceProvider?: Maybe<ResponseResult>;
  exportAssessmentResult?: Maybe<ResponseResult>;
  exportContacts: ResponseResult;
  exportUnregisteredReferrals: UnregisteredReferralsResponse;
  feedbackSection?: Maybe<Array<Maybe<FeedbackSection>>>;
  findAllDuplicateUsers: Array<Maybe<UsersPhoneNumberResult>>;
  generateGoogleAuthLink?: Maybe<GoogleAuthLink>;
  generateReferralCodeFrUser?: Maybe<Scalars['String']['output']>;
  generateTokenFrAdmin?: Maybe<TokenData>;
  getAdminPermission?: Maybe<PermissionResult>;
  getAdminRoleByRoleId?: Maybe<AdminRoleByIdResponse>;
  getAllAdminRoles?: Maybe<AdminRoleResponse>;
  getAllAssignmentConfig?: Maybe<Array<Maybe<AssignmentConfigData>>>;
  getAllAssignmentsOfUser?: Maybe<Array<Maybe<AssignmentConfig>>>;
  getAllBuckets?: Maybe<QuestionBucketWithCount>;
  getAllChecklists: ChecklistResponse;
  getAllCities?: Maybe<Array<Maybe<Location>>>;
  getAllCoursesForUser: Array<Maybe<CoursesForUserResult>>;
  getAllFeedbacks?: Maybe<Array<Maybe<InterviewFeedback>>>;
  getAllPolicies: PolicyResponse;
  getAllQuestions: Array<Question>;
  getAllReferredUsersOfUser: Array<Maybe<ReferredUser>>;
  getAllRoles?: Maybe<Array<Maybe<Role>>>;
  getAllRulesModule?: Maybe<AutoAssignmentGenRulesWithCount>;
  getAllUsers?: Maybe<UsersWithCount>;
  getAllUsersCount: Scalars['Int']['output'];
  getAllUsersInterviewData?: Maybe<Array<Maybe<Users>>>;
  getAppAnalytics: AppAnalytics;
  getAppInstallations: Array<AppInstallation>;
  getAssignmentRemainingTime: ResponseResultWithData;
  getAssignmentsFrCourse?: Maybe<Array<AssignmentConfigData>>;
  getAverageInterviewFeedbackRating: Scalars['String']['output'];
  getBellNotificationCount: Scalars['Int']['output'];
  getBulkQuestionWithAI: Array<Maybe<GetBulkQuestionWithAiResult>>;
  getCalendarFreeBusy?: Maybe<Array<Maybe<CalendarFreeBusyResponse>>>;
  getCalendarTimeSlots?: Maybe<Array<Maybe<CalendarFreeBusyResponse>>>;
  getContactsOfTheUserFrAdmin?: Maybe<UserContactsWithCount>;
  getContactsWithInvitationStatus?: Maybe<Array<Contact>>;
  getCoreResultsData: CoreResultsData;
  getDailyAnalytics?: Maybe<DailyAnalytics>;
  getDashboardData?: Maybe<DashboardData>;
  getDesignations?: Maybe<DesignationWithCount>;
  getDeviceAnalytics: DeviceAnalytics;
  getDocumentTypeDataFrAdmin?: Maybe<Array<Maybe<DocumentType>>>;
  getDocumentTypesFrAdmin?: Maybe<Array<DocumentType>>;
  getDocumentsOfUserFrSpOrTeamMember?: Maybe<Array<Maybe<DocumentType>>>;
  getExpertiseUserDetail?: Maybe<ExpertiseUserResult>;
  getExportSpPolicyTracking: ExportPolicyTrackingResponse;
  getExportUserPolicyTracking: PolicyTrackingResponse;
  getExportableReferredData?: Maybe<ReferredUsersDataWithCount>;
  getFeedbackFormStatus: FeedbackFormStatusResponse;
  getFeedbackTemplates?: Maybe<Array<Template>>;
  getFunnelData?: Maybe<Array<Maybe<FunnelData>>>;
  getInstallationBreakdown: InstallationBreakdownResponse;
  getIntervieweeFeedbackFormData: IntervieweeFeedbackForm;
  getInterviewerFeedbackFormData: InterviewerFeedbackForm;
  getInterviewerFeedbackResults: InterviewerFeedbackResults;
  getInterviewsFrUser?: Maybe<Array<UserInterviewResult>>;
  getLatestReferralConfiguration?: Maybe<ReferralConfig>;
  getLeadContactsFrAdmin?: Maybe<LeadContactsWithCount>;
  getLeads?: Maybe<Array<Maybe<Lead>>>;
  getManualAssignments?: Maybe<Array<Maybe<ManualAssignment>>>;
  getMyReferrals: AllUserReferralsResponse;
  getMyUnregisteredReferrals: UnregisteredReferralsResponse;
  getOrganizationsAdmin: Array<OrganizationsAdmin>;
  getPermissions?: Maybe<Permissions>;
  getPoliciesWithUserStatus: PolicyResponse;
  getPolicyById: PolicyUpdateResponse;
  getPolicyWithUserStatus: PolicyWithUserStatusResponse;
  getProvider?: Maybe<ProviderDetails>;
  getProviderDashboardCards: GetProviderDashboardCardsResult;
  getProviderDetailsAdmin: ProviderDetailsFrAdmin;
  getQuestion?: Maybe<Question>;
  getQuestionsOfAssignmentConfig?: Maybe<Array<Maybe<QuestionsForAssignmentConfig>>>;
  getReferralConfiguration?: Maybe<Array<Maybe<ReferralConfig>>>;
  getReferralConfigurationForUser?: Maybe<ReferralConfig>;
  getReferredUsersCount?: Maybe<ReferredAndOnboardCount>;
  getRejectandBanUser?: Maybe<GetUsers>;
  getServiceProviderOnboardingAnalytics: ServiceProviderOnboardingAnalytics;
  getServiceProviders: Array<ProviderDetailsFrAdmin>;
  getServiceTypes: Array<Maybe<ServiceType>>;
  getSingleAssignmentConfig?: Maybe<AssignmentConfigData>;
  getSingleBucket?: Maybe<BucketResponse>;
  getSingleDesignation?: Maybe<Designation>;
  getSingleFeedbackTemplate: Template;
  getSingleManualAssignmentOfUser?: Maybe<ManualAssignment>;
  getSingleUserChecklistData: SingleUserChecklistDataResponse;
  getSingleUserDetailFrUserManagement: UserAndOrgDetailsAdmin;
  getSingleUserTrainingPerformance?: Maybe<Array<GetSingleUserTrainingPerformanceResults>>;
  getSpPolicyTracking: PolicyTrackingResponse;
  getSubmodules: Array<Submodule>;
  getTeamMemberDetailsFrAdmin: TeamMemberDetailsFrAdmin;
  getTeamMemberInvitations?: Maybe<Array<TeamMemberInvitation>>;
  getTeamMemberInvitationsFrAdmin?: Maybe<Array<TeamMemberInvitationFrAdmin>>;
  getTeamMembersDataForTTT?: Maybe<Array<TeamMembersDataForTtt>>;
  getTotalUsersOnWhichRulesApply?: Maybe<Scalars['Int']['output']>;
  getUnpublishedModuleById?: Maybe<UnpublishedModuleByIdResponse>;
  getUnpublishedModules?: Maybe<UnpublishedModuleResponse>;
  getUnregisteredReferrals: UnregisteredReferralsResponse;
  getUserActivity: Array<UserActivity>;
  getUserAllAssignmentResult: Array<Maybe<AssignmentDataFrUser>>;
  getUserAssignment: Array<Maybe<GetUserAssignmentResponse>>;
  getUserAssignmentResultsFrAdmin: Array<Maybe<UserResultResponse>>;
  getUserChecklistData: UserChecklistDataResponse;
  getUserChecklistHistory: UserChecklistHistoryResponse;
  getUserCourseDetails: UserCourseDetails;
  getUserCoursesData: Array<CourseUserMap>;
  getUserForTTT?: Maybe<UserOnboardingDataResultWithCount>;
  getUserInterviewDetails?: Maybe<UserInterviewCount>;
  getUserInterviewDetailsForAdmin: UserInterviewDetailsForAdminResults;
  getUserInterviews?: Maybe<Array<UserInterviewDetailsForAdminResults>>;
  getUserMascotAlert?: Maybe<UserMascotAlertData>;
  getUserNotifications: Array<Notification>;
  getUserPolicyTracking: PolicyTrackingResponse;
  getUserProfessionalExperience?: Maybe<UserProfessionalExperienceResult>;
  getUserProfileCompletionPercentage?: Maybe<UserProfileCompletionResult>;
  getUserReferralData?: Maybe<ReferredUsersData>;
  getUserSingleAssignmentResult: GetUserSingleAssignmentResultData;
  getUserTimeline?: Maybe<Array<Maybe<UserTimelineData>>>;
  getUsersByOrganization?: Maybe<Array<UserAndOrgDetailsAdmin>>;
  getUsersByType?: Maybe<Array<Maybe<User>>>;
  getUsersEnrolledOnCourse?: Maybe<Array<Maybe<UserWithCourseDetails>>>;
  getUsersForAdmin?: Maybe<Array<Maybe<User>>>;
  getUsersFrContentAssignment?: Maybe<UserWithContentDetailsAndCount>;
  getUsersFrCourseAssignment?: Maybe<Array<Maybe<UserWithCourseDetails>>>;
  getUsersResultsForAdmin: Array<Maybe<UserAssignmentResult>>;
  getUsersTrainingPerformance: Array<UsersTrainingPerformanceResult>;
  getVideoWatchTime: UserVideoProgressResultData;
  getWorkExp?: Maybe<WorkExpResult>;
  getWorkExpAdmin?: Maybe<UserExperience>;
  getexpertisesearch?: Maybe<ExpertiseResults>;
  inActive: Scalars['Int']['output'];
  interviewersDetails?: Maybe<InterviewerWithCount>;
  onBoarded: Scalars['Int']['output'];
  orgSearch?: Maybe<Array<Maybe<OrgSearchOrg>>>;
  organization?: Maybe<Organization>;
  performanceDashboardTraining: PerformanceDashboardTrainingResult;
  roles?: Maybe<Array<Maybe<Role>>>;
  searchUser: Array<User>;
  sendOtpAdmin?: Maybe<Result>;
  serviceProviderAndIndividualCount?: Maybe<ServiceProAndIndividualCount>;
  serviceProviders: Scalars['Int']['output'];
  singleUserDocument?: Maybe<Array<Maybe<DocumentType>>>;
  textSearch?: Maybe<Array<Maybe<Location>>>;
  userDesignation?: Maybe<User>;
  userDetail?: Maybe<UserData>;
  userDetailsForAdmin?: Maybe<User>;
  userDetailsForDetailsCard?: Maybe<Array<Maybe<User>>>;
  userExpertise?: Maybe<ExpertiseResult>;
};


export type QueryAllAdminDetailsArgs = {
  data?: InputMaybe<Paginations>;
};


export type QueryAllLocationsArgs = {
  filter?: InputMaybe<LocationFilter>;
};


export type QueryAllUsersCountArgs = {
  filter?: InputMaybe<Filter>;
};


export type QueryApprovedCandidateArgs = {
  data?: InputMaybe<UserActions>;
};


export type QueryBannerArgs = {
  filter?: InputMaybe<BannerFilterInput>;
};


export type QueryBannerConfigsArgs = {
  filter?: InputMaybe<BannerConfigFilterInput>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryCheckIfUserExistsArgs = {
  data: CheckIfUserExistsInputData;
};


export type QueryCheckUnregisteredReferralArgs = {
  phone_number: Scalars['String']['input'];
};


export type QueryContentsArgs = {
  data?: InputMaybe<GetAllContentInputData>;
  filter?: InputMaybe<ContentFilterInput>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryCoursesArgs = {
  data?: InputMaybe<GetAllCourseInputData>;
  filter?: InputMaybe<CourseFilterInput>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryDocumentArgs = {
  id: Scalars['Int']['input'];
};


export type QueryDocumentTypesArgs = {
  is_team_member?: InputMaybe<Scalars['Boolean']['input']>;
  user_type?: InputMaybe<UserTypeDocument>;
};


export type QueryDocumentTypesAdminArgs = {
  org_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryDocumentsArgs = {
  filter?: InputMaybe<DocumentFilter>;
};


export type QueryDocumentsAdminArgs = {
  filter?: InputMaybe<DocumentFilterAdmin>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
  user_type?: InputMaybe<UserTypeDocument>;
};


export type QueryExportAllServiceProviderArgs = {
  filter?: InputMaybe<ProvidersFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryExportAssessmentResultArgs = {
  filter?: InputMaybe<ExportAssessmentResFilter>;
};


export type QueryExportContactsArgs = {
  filter?: InputMaybe<ContactsFilters>;
};


export type QueryExportUnregisteredReferralsArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryFindAllDuplicateUsersArgs = {
  user_phone_numbers: Array<InputMaybe<Scalars['String']['input']>>;
};


export type QueryGenerateTokenFrAdminArgs = {
  id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetAdminRoleByRoleIdArgs = {
  admin_role_id: Scalars['Int']['input'];
};


export type QueryGetAllAssignmentConfigArgs = {
  filter?: InputMaybe<AssignmentConfigFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryGetAllAssignmentsOfUserArgs = {
  data?: InputMaybe<GetAllAssignmentsOfUser>;
};


export type QueryGetAllBucketsArgs = {
  data?: InputMaybe<GetAllBucketsInput>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAllChecklistsArgs = {
  user_type?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAllCoursesForUserArgs = {
  method?: InputMaybe<CourseMethod>;
  search?: InputMaybe<Scalars['String']['input']>;
  user_id: Scalars['Int']['input'];
};


export type QueryGetAllFeedbacksArgs = {
  filter?: InputMaybe<FeedbackFormFilter>;
  org_id?: InputMaybe<Scalars['Int']['input']>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAllQuestionsArgs = {
  filter?: InputMaybe<GetAllQuestionsFilter>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAllReferredUsersOfUserArgs = {
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetAllRulesModuleArgs = {
  filter?: InputMaybe<RulesFilter>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAllUsersArgs = {
  filter?: InputMaybe<UserFilter>;
  interview_status?: InputMaybe<Scalars['String']['input']>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAllUsersInterviewDataArgs = {
  filter?: InputMaybe<UserFilter>;
  interview_status?: InputMaybe<Scalars['String']['input']>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAppAnalyticsArgs = {
  filter?: InputMaybe<AppAnalyticsFilter>;
};


export type QueryGetAppInstallationsArgs = {
  platformType?: InputMaybe<AppPlatformType>;
  status?: InputMaybe<AppInstallationStatus>;
  userId?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetAssignmentRemainingTimeArgs = {
  assignment_id: Scalars['String']['input'];
};


export type QueryGetAssignmentsFrCourseArgs = {
  filter?: InputMaybe<AssignmentsFrCourseFilter>;
};


export type QueryGetAverageInterviewFeedbackRatingArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetBulkQuestionWithAiArgs = {
  data?: InputMaybe<GetBulkQuestionWithAiInput>;
};


export type QueryGetCalendarTimeSlotsArgs = {
  data?: InputMaybe<UserCalendarInputData>;
};


export type QueryGetContactsOfTheUserFrAdminArgs = {
  filter?: InputMaybe<UserContactsFilter>;
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetContactsWithInvitationStatusArgs = {
  filter?: InputMaybe<TeamMemberInvitationFilter>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetCoreResultsDataArgs = {
  filter?: InputMaybe<GetCoreResultsDataFilter>;
};


export type QueryGetDailyAnalyticsArgs = {
  date?: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryGetDashboardDataArgs = {
  filter?: InputMaybe<DashboardFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryGetDesignationsArgs = {
  filter?: InputMaybe<DesignationFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryGetDocumentTypeDataFrAdminArgs = {
  search?: InputMaybe<Scalars['String']['input']>;
  type_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetDocumentTypesFrAdminArgs = {
  document_id?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  user_type?: InputMaybe<UserTypeDocument>;
};


export type QueryGetDocumentsOfUserFrSpOrTeamMemberArgs = {
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetExpertiseUserDetailArgs = {
  expertise_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetExportSpPolicyTrackingArgs = {
  filter?: InputMaybe<PolicyTrackingFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
  userType?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetExportUserPolicyTrackingArgs = {
  filter?: InputMaybe<PolicyTrackingFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
  userType?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetExportableReferredDataArgs = {
  filter?: InputMaybe<ExportableReferredData>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetFeedbackFormStatusArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetFeedbackTemplatesArgs = {
  search?: InputMaybe<Scalars['String']['input']>;
  template_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetInstallationBreakdownArgs = {
  endDate?: InputMaybe<Scalars['String']['input']>;
  period?: InputMaybe<AppInstallationPeriod>;
  startDate?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetIntervieweeFeedbackFormDataArgs = {
  template_form_id: Scalars['String']['input'];
};


export type QueryGetInterviewerFeedbackFormDataArgs = {
  template_form_id: Scalars['String']['input'];
};


export type QueryGetInterviewerFeedbackResultsArgs = {
  id: Scalars['Int']['input'];
};


export type QueryGetLatestReferralConfigurationArgs = {
  filter: OnboardingStage;
};


export type QueryGetLeadContactsFrAdminArgs = {
  filter?: InputMaybe<LeadContactsFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryGetLeadsArgs = {
  filters?: InputMaybe<LeadFilters>;
};


export type QueryGetManualAssignmentsArgs = {
  assignment_config_id: Scalars['String']['input'];
  filter?: InputMaybe<ManualAssignmentsFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryGetMyReferralsArgs = {
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetMyUnregisteredReferralsArgs = {
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetPoliciesWithUserStatusArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetPolicyByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetPolicyWithUserStatusArgs = {
  policy_id: Scalars['String']['input'];
};


export type QueryGetProviderDetailsAdminArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetQuestionArgs = {
  question_id?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetQuestionsOfAssignmentConfigArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetReferralConfigurationArgs = {
  configuration_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetRejectandBanUserArgs = {
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetServiceProvidersArgs = {
  filter?: InputMaybe<ProvidersFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetSingleAssignmentConfigArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetSingleBucketArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetSingleDesignationArgs = {
  designation_id?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetSingleFeedbackTemplateArgs = {
  template_id: Scalars['Int']['input'];
};


export type QueryGetSingleManualAssignmentOfUserArgs = {
  assignment_config_id: Scalars['String']['input'];
};


export type QueryGetSingleUserChecklistDataArgs = {
  checklist_id: Scalars['String']['input'];
  user_id: Scalars['Int']['input'];
};


export type QueryGetSingleUserDetailFrUserManagementArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetSingleUserTrainingPerformanceArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetSpPolicyTrackingArgs = {
  filter?: InputMaybe<PolicyTrackingFilter>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
  userType?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetSubmodulesArgs = {
  moduleId: Scalars['Int']['input'];
};


export type QueryGetTeamMemberDetailsFrAdminArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetTeamMemberInvitationsArgs = {
  filter?: InputMaybe<TeamMemberInvitationFilter>;
};


export type QueryGetTeamMemberInvitationsFrAdminArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetTeamMembersDataForTttArgs = {
  data: TeamMembersDataForTttInput;
};


export type QueryGetTotalUsersOnWhichRulesApplyArgs = {
  rules: Array<Rule>;
};


export type QueryGetUnpublishedModuleByIdArgs = {
  module_id: Scalars['Int']['input'];
};


export type QueryGetUnregisteredReferralsArgs = {
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserActivityArgs = {
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  userId: Scalars['Int']['input'];
};


export type QueryGetUserAllAssignmentResultArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetUserAssignmentArgs = {
  assignment_config_id: Scalars['String']['input'];
  assignment_id: Scalars['String']['input'];
};


export type QueryGetUserAssignmentResultsFrAdminArgs = {
  filter?: InputMaybe<GetUserAssignmentResultsFrAdminFilter>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserChecklistDataArgs = {
  user_id: Scalars['Int']['input'];
  user_type?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserChecklistHistoryArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetUserCourseDetailsArgs = {
  course_user_id: Scalars['String']['input'];
};


export type QueryGetUserCoursesDataArgs = {
  filter?: InputMaybe<GetUserCoursesDataFilter>;
};


export type QueryGetUserForTttArgs = {
  filter?: InputMaybe<GetUserTttInputFilter>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetUserInterviewDetailsArgs = {
  interview_status?: InputMaybe<InterviewState>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserInterviewDetailsForAdminArgs = {
  interview_id: Scalars['Int']['input'];
};


export type QueryGetUserInterviewsArgs = {
  user_id: Scalars['Int']['input'];
};


export type QueryGetUserMascotAlertArgs = {
  type: MascotSectionType;
};


export type QueryGetUserNotificationsArgs = {
  filter?: InputMaybe<NotificationFilterInput>;
};


export type QueryGetUserPolicyTrackingArgs = {
  filter?: InputMaybe<PolicyTrackingFilter>;
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
  userType?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserSingleAssignmentResultArgs = {
  assignment_id: Scalars['String']['input'];
};


export type QueryGetUserTimelineArgs = {
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetUsersByOrganizationArgs = {
  org_id?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUsersByTypeArgs = {
  filter?: InputMaybe<DashboardFilter>;
  pagination?: InputMaybe<Pagination>;
  type: DashboardStates;
};


export type QueryGetUsersEnrolledOnCourseArgs = {
  course_id: Scalars['String']['input'];
};


export type QueryGetUsersForAdminArgs = {
  batch?: InputMaybe<Scalars['Int']['input']>;
  filter?: InputMaybe<FilterUsers>;
};


export type QueryGetUsersFrContentAssignmentArgs = {
  content_id?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<UserContentFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUsersFrCourseAssignmentArgs = {
  course_id?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<UserCourseFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUsersTrainingPerformanceArgs = {
  export?: InputMaybe<Scalars['Boolean']['input']>;
  filter?: InputMaybe<UsersTrainingPerformanceFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetVideoWatchTimeArgs = {
  content_id: Scalars['String']['input'];
};


export type QueryGetWorkExpAdminArgs = {
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetexpertisesearchArgs = {
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryInterviewersDetailsArgs = {
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryOrgSearchArgs = {
  term: Scalars['String']['input'];
};


export type QuerySearchUserArgs = {
  filter?: InputMaybe<SearchUserFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
  user_ids?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};


export type QuerySendOtpAdminArgs = {
  phoneNumber?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<SendOtpTypeOrg>;
};


export type QuerySingleUserDocumentArgs = {
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryTextSearchArgs = {
  filter?: InputMaybe<LocationFilter>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  term?: InputMaybe<Scalars['String']['input']>;
};


export type QueryUserDetailsForAdminArgs = {
  user_id?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryUserDetailsForDetailsCardArgs = {
  type?: InputMaybe<UserDetailsType>;
};

export type Question = {
  __typename?: 'Question';
  bucket?: Maybe<Bucket>;
  difficulty: QuestionDifficulty;
  id: Scalars['String']['output'];
  images?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  option: Array<Option>;
  question_text: Scalars['String']['output'];
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type QuestionBucket = {
  __typename?: 'QuestionBucket';
  created_at: Scalars['DateTime']['output'];
  creator_id: Scalars['Int']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  total_question: Scalars['Int']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type QuestionBucketWithCount = {
  __typename?: 'QuestionBucketWithCount';
  data?: Maybe<Array<Maybe<QuestionBucket>>>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type QuestionCreateUpdateParams = {
  bucket_id: Scalars['String']['input'];
  difficulty: QuestionDifficulty;
  images?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  options: Array<InputMaybe<OptionInput>>;
  question_text: Scalars['String']['input'];
};

export enum QuestionDifficulty {
  Easy = 'EASY',
  Hard = 'HARD',
  Medium = 'MEDIUM'
}

export type QuestionOnAssignment = {
  __typename?: 'QuestionOnAssignment';
  question: QuestionWithOptions;
  submited_answer?: Maybe<Options>;
};

export type QuestionOnAssignmentCount = {
  __typename?: 'QuestionOnAssignmentCount';
  question_on_assignment: Scalars['Int']['output'];
};

export type QuestionUpdateParams = {
  bucket_id?: InputMaybe<Scalars['String']['input']>;
  difficulty?: InputMaybe<QuestionDifficulty>;
  images?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  options?: InputMaybe<Array<InputMaybe<OptionInput>>>;
  question_text?: InputMaybe<Scalars['String']['input']>;
};

export type QuestionWithOptions = {
  __typename?: 'QuestionWithOptions';
  option: Array<Maybe<Options>>;
  question_text: Scalars['String']['output'];
};

export type QuestionsForAssignmentConfig = {
  __typename?: 'QuestionsForAssignmentConfig';
  difficulty: QuestionDifficulty;
  id: Scalars['String']['output'];
  question_text: Scalars['String']['output'];
};

export type ReferralCode = {
  __typename?: 'ReferralCode';
  referral_code?: Maybe<Scalars['String']['output']>;
};

export type ReferralCodeValidationResult = {
  __typename?: 'ReferralCodeValidationResult';
  message?: Maybe<Scalars['String']['output']>;
  validated?: Maybe<Scalars['Boolean']['output']>;
};

export type ReferralConfig = {
  __typename?: 'ReferralConfig';
  existing_user_points?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  new_user_points?: Maybe<Scalars['Int']['output']>;
  referral_enabled?: Maybe<Scalars['Boolean']['output']>;
  terms_and_condition?: Maybe<Scalars['JSON']['output']>;
  user_point_redeem_stage?: Maybe<OnboardingStage>;
};

export type ReferralConfigInput = {
  existing_user_points?: InputMaybe<Scalars['Int']['input']>;
  new_user_points?: InputMaybe<Scalars['Int']['input']>;
  referral_enabled?: InputMaybe<Scalars['Boolean']['input']>;
  terms_and_condition?: InputMaybe<Scalars['JSON']['input']>;
  user_point_redeem_stage?: InputMaybe<OnboardingStage>;
};

export type ReferralInput = {
  id?: InputMaybe<Scalars['Int']['input']>;
  referral_code?: InputMaybe<Scalars['String']['input']>;
};

export type ReferredAndOnboardCount = {
  __typename?: 'ReferredAndOnboardCount';
  onboard_count: Scalars['Int']['output'];
  referred_count: Scalars['Int']['output'];
};

export type ReferredTo = {
  __typename?: 'ReferredTo';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  interview?: Maybe<Array<Maybe<InterviewData>>>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onboarding_stage?: Maybe<OnboardingStage>;
  organization_id?: Maybe<Scalars['Int']['output']>;
  source?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type ReferredUser = {
  __typename?: 'ReferredUser';
  available_points?: Maybe<Scalars['Int']['output']>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  referral_code?: Maybe<Scalars['String']['output']>;
  referred_by_id?: Maybe<Scalars['Int']['output']>;
  referred_to?: Maybe<ReferredTo>;
  user_id?: Maybe<Scalars['Int']['output']>;
  user_referral_ledger_id?: Maybe<Scalars['Int']['output']>;
};

export type ReferredUsersData = {
  __typename?: 'ReferredUsersData';
  available_points?: Maybe<Scalars['Int']['output']>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  referral_code?: Maybe<Scalars['String']['output']>;
  referred_by?: Maybe<ReferrerData>;
  referred_count?: Maybe<Scalars['Int']['output']>;
  referred_id?: Maybe<Scalars['Int']['output']>;
  referred_name?: Maybe<Scalars['String']['output']>;
  referred_phone?: Maybe<Scalars['String']['output']>;
  referred_to?: Maybe<ReferrerData>;
  referrer_code?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type ReferredUsersDataWithCount = {
  __typename?: 'ReferredUsersDataWithCount';
  data?: Maybe<Array<Maybe<ReferredUsersData>>>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type ReferrerData = {
  __typename?: 'ReferrerData';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  designation?: Maybe<Designation>;
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onboarding_stage?: Maybe<OnboardingStage>;
  phone?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  user_referred_to?: Maybe<ReferralCode>;
};

export type ReferrerInfo = {
  __typename?: 'ReferrerInfo';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  referral_code?: Maybe<Scalars['String']['output']>;
};

export type RegisterData = {
  __typename?: 'RegisterData';
  is_invited?: Maybe<Scalars['Boolean']['output']>;
  is_user_journey_completed?: Maybe<Scalars['Boolean']['output']>;
  last_location?: Maybe<Scalars['String']['output']>;
  message: Scalars['String']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  registration_type?: Maybe<SendOtpType>;
  result: Scalars['Boolean']['output'];
  token: Scalars['String']['output'];
  user_type?: Maybe<Scalars['String']['output']>;
};

export type RegisterInput = {
  meta?: InputMaybe<Scalars['JSON']['input']>;
  otp: Scalars['String']['input'];
  phone: Scalars['String']['input'];
  referral_code?: InputMaybe<Scalars['String']['input']>;
};

export type ReleaseNote = {
  __typename?: 'ReleaseNote';
  body?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  published_at?: Maybe<Scalars['String']['output']>;
  tag_name?: Maybe<Scalars['String']['output']>;
};

export type ResponseResult = {
  __typename?: 'ResponseResult';
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type ResponseResultWithData = {
  __typename?: 'ResponseResultWithData';
  data: Scalars['String']['output'];
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type Result = {
  __typename?: 'Result';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type Role = {
  __typename?: 'Role';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  organization_id?: Maybe<Scalars['Int']['output']>;
  permission?: Maybe<Scalars['JSON']['output']>;
  user_id?: Maybe<Scalars['Int']['output']>;
};

export type RoleFilter = {
  id?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  user_id?: InputMaybe<Scalars['Int']['input']>;
};

export type RolePermissionData = {
  admin_role_id?: InputMaybe<Scalars['Int']['input']>;
  feature_id?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['Int']['input']>;
  is_module?: InputMaybe<Scalars['Boolean']['input']>;
  module_id?: InputMaybe<Scalars['Int']['input']>;
  permission_for_sp?: InputMaybe<Scalars['JSON']['input']>;
  permission_for_user?: InputMaybe<Scalars['JSON']['input']>;
};

export type Rule = {
  field_name: Scalars['String']['input'];
  operator: AllowedOperator;
  value: Scalars['JSON']['input'];
};

export type RulesFilter = {
  assign_to?: InputMaybe<AssignmentUserType>;
  type?: InputMaybe<FilterType>;
};

export type ScheduledBy = {
  __typename?: 'ScheduledBy';
  email?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type SectionInput = {
  section_name: Scalars['String']['input'];
  section_url_sp?: InputMaybe<Scalars['String']['input']>;
  section_url_user?: InputMaybe<Scalars['String']['input']>;
  sub_module_name: Scalars['String']['input'];
};

export type SendFcmInput = {
  action?: InputMaybe<Scalars['String']['input']>;
  body: Scalars['String']['input'];
  title: Scalars['String']['input'];
  user_id: Scalars['Int']['input'];
};

export type SendFcmResponse = {
  __typename?: 'SendFcmResponse';
  message: Scalars['String']['output'];
  success: Scalars['Boolean']['output'];
};

export enum SendOtpType {
  Login = 'LOGIN',
  SignUp = 'SIGN_UP'
}

export type SendOtpResult = {
  __typename?: 'SendOtpResult';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
  type?: Maybe<SendOtpResultType>;
};

export enum SendOtpResultType {
  AlreadyAccountExists = 'ALREADY_ACCOUNT_EXISTS',
  NoAccountFound = 'NO_ACCOUNT_FOUND',
  OtherError = 'OTHER_ERROR',
  Success = 'SUCCESS'
}

export enum SendOtpTypeOrg {
  Login = 'login',
  PhoneVerify = 'phone_verify',
  Signup = 'signup'
}

export type SendRefferalInviteInput = {
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
  referral_code: Scalars['String']['input'];
};

export type ServiceProAndIndividualCount = {
  __typename?: 'ServiceProAndIndividualCount';
  individual?: Maybe<Scalars['Int']['output']>;
  service_provider?: Maybe<Scalars['Int']['output']>;
};

export type ServiceProviderOnboardingAnalytics = {
  __typename?: 'ServiceProviderOnboardingAnalytics';
  documentUpload?: Maybe<DocumentUploadCount>;
  organization?: Maybe<Organization>;
  profileCompletion?: Maybe<ProfileCompletion>;
  testCompletion?: Maybe<TestCompletion>;
};

export type ServiceType = {
  __typename?: 'ServiceType';
  id: Scalars['String']['output'];
  is_active?: Maybe<Scalars['Boolean']['output']>;
  name: Scalars['String']['output'];
};

export type SingleUserAdminResult = {
  __typename?: 'SingleUserAdminResult';
  data?: Maybe<Array<Maybe<Document>>>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type SingleUserChecklistDataResponse = {
  __typename?: 'SingleUserChecklistDataResponse';
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type Submodule = {
  __typename?: 'Submodule';
  submodule_id: Scalars['Int']['output'];
  submodule_name: Scalars['String']['output'];
  submodule_url_sp?: Maybe<Scalars['String']['output']>;
  submodule_url_user?: Maybe<Scalars['String']['output']>;
};

export type SubmoduleData = {
  __typename?: 'SubmoduleData';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  feature_id?: Maybe<Scalars['Int']['output']>;
  icon_name?: Maybe<Scalars['String']['output']>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  is_module?: Maybe<Scalars['Boolean']['output']>;
  module_id?: Maybe<Scalars['Int']['output']>;
  module_name?: Maybe<Scalars['String']['output']>;
  parent_module_id?: Maybe<Scalars['Int']['output']>;
  parent_module_name?: Maybe<Scalars['String']['output']>;
  section_id?: Maybe<Scalars['String']['output']>;
  sub_module_name?: Maybe<Scalars['String']['output']>;
  sub_module_url_sp?: Maybe<Scalars['String']['output']>;
  sub_module_url_user?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type Subscription = {
  __typename?: 'Subscription';
  bellNotification: Scalars['Int']['output'];
  mascotNotifications?: Maybe<UserMascotAlertData>;
  showTTTModal?: Maybe<Scalars['Boolean']['output']>;
  trainingAssignmentProcessStatus: TrainingAssignmentProcessStatusResultData;
  transferredToTMS: Success;
  userTrainingStatus: UserTrainingStatusData;
};


export type SubscriptionBellNotificationArgs = {
  user_id: Scalars['Int']['input'];
};


export type SubscriptionMascotNotificationsArgs = {
  user_id: Scalars['Int']['input'];
};


export type SubscriptionShowTttModalArgs = {
  user_id: Scalars['Int']['input'];
};


export type SubscriptionTrainingAssignmentProcessStatusArgs = {
  admin_id: Scalars['Int']['input'];
};


export type SubscriptionTransferredToTmsArgs = {
  user_id: Scalars['Int']['input'];
};


export type SubscriptionUserTrainingStatusArgs = {
  user_id: Scalars['Int']['input'];
};

export type Success = {
  __typename?: 'Success';
  result: Scalars['Boolean']['output'];
};

export type SwitchSpToTechnicianInput = {
  city?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  phone: Scalars['String']['input'];
  poc?: InputMaybe<Scalars['String']['input']>;
  skills: Array<Scalars['Int']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  user_id: Scalars['Int']['input'];
};

export type SwitchTechnicianToSpInput = {
  city: Scalars['String']['input'];
  company_name: Scalars['String']['input'];
  company_poc_name: Scalars['String']['input'];
  email: Scalars['String']['input'];
  owner_name: Scalars['String']['input'];
  service_type_id: Array<Scalars['String']['input']>;
  skills: Array<Scalars['Int']['input']>;
  user_id: Scalars['Int']['input'];
};

export type SwitchUserTypeResult = {
  __typename?: 'SwitchUserTypeResult';
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
  user?: Maybe<User>;
};

export type TeamMemberAssignmentStatus = {
  __typename?: 'TeamMemberAssignmentStatus';
  status: AssignmentStatus;
};

export type TeamMemberDetailsFrAdmin = {
  __typename?: 'TeamMemberDetailsFrAdmin';
  designation?: Maybe<Designation>;
  doc_verification_state: TeamMemberDocVerificationStatus;
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isActive?: Maybe<Scalars['Boolean']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  name: Scalars['String']['output'];
  onboarded: Scalars['Boolean']['output'];
  organization?: Maybe<OrgDataFrTeamMember>;
  phone: Scalars['String']['output'];
  team_member_invitation_received?: Maybe<Array<TeamMemberInvitationWithoutContact>>;
  user_expertise_map?: Maybe<Array<UserExpMapFrTeamMember>>;
  user_professional_data?: Maybe<UserProfessionalData>;
};

export type TeamMemberDetailsFrAdminInput = {
  doc_verification_state?: InputMaybe<TeamMemberDocVerificationStatus>;
  email?: InputMaybe<Scalars['String']['input']>;
  isActive?: InputMaybe<Scalars['Boolean']['input']>;
  is_banned?: InputMaybe<Scalars['Boolean']['input']>;
  is_delete?: InputMaybe<Scalars['Boolean']['input']>;
  is_rejected?: InputMaybe<Scalars['Boolean']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  profile_approved?: InputMaybe<Scalars['Boolean']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  user_expertise_map?: InputMaybe<Array<Scalars['Int']['input']>>;
};

export enum TeamMemberDocVerificationStatus {
  Completed = 'COMPLETED',
  Failed = 'FAILED',
  NotSubmitted = 'NOT_SUBMITTED',
  Pending = 'PENDING',
  ReUpload = 'RE_UPLOAD'
}

export type TeamMemberDocumentUser = {
  __typename?: 'TeamMemberDocumentUser';
  verification_status: TeamMemberDocVerificationStatus;
};

export type TeamMemberInvitation = {
  __typename?: 'TeamMemberInvitation';
  contact: Contact;
  status: TeamMemberInvitationStatus;
};

export type TeamMemberInvitationFilter = {
  status: TeamMemberInvitationStatus;
};

export type TeamMemberInvitationFrAdmin = {
  __typename?: 'TeamMemberInvitationFrAdmin';
  contact: Contact;
  invited_user?: Maybe<InvitedUser>;
  meta?: Maybe<Scalars['JSON']['output']>;
  status: TeamMemberInvitationStatus;
};

export type TeamMemberInvitationInput = {
  contact_id?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type TeamMemberInvitationSent = {
  __typename?: 'TeamMemberInvitationSent';
  contact?: Maybe<Contact>;
  id: Scalars['String']['output'];
};

export enum TeamMemberInvitationStatus {
  Approved = 'APPROVED',
  Expired = 'EXPIRED',
  Invited = 'INVITED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export type TeamMemberInvitationWithoutContact = {
  __typename?: 'TeamMemberInvitationWithoutContact';
  status: TeamMemberInvitationStatus;
};

export type TeamMemberOnboardingData = {
  __typename?: 'TeamMemberOnboardingData';
  meta?: Maybe<Scalars['JSON']['output']>;
  user_created_in_tms: Scalars['Boolean']['output'];
};

export type TeamMemberTttDataOnboardingData = {
  __typename?: 'TeamMemberTTTDataOnboardingData';
  designation?: Maybe<Scalars['String']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  user_created_in_tms: Scalars['Boolean']['output'];
};

export type TeamMembersDataForTtt = {
  __typename?: 'TeamMembersDataForTTT';
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  name: Scalars['String']['output'];
  onboarded: Scalars['Boolean']['output'];
  phone?: Maybe<Scalars['String']['output']>;
  photoUrl?: Maybe<Scalars['String']['output']>;
  user_onboarding_data?: Maybe<Array<TeamMemberTttDataOnboardingData>>;
};

export type TeamMembersDataForTttInput = {
  organization_id: Scalars['Int']['input'];
  user_id?: InputMaybe<Scalars['Int']['input']>;
};

export type TeamSizeAsPerSkill = {
  expertise_id: Scalars['Int']['input'];
  team_member_count: Scalars['Int']['input'];
};

export type Template = {
  __typename?: 'Template';
  created_by?: Maybe<Scalars['Int']['output']>;
  feedback_section?: Maybe<Array<FeedbackSection>>;
  id: Scalars['Int']['output'];
  interviewee_feedback_enabled?: Maybe<Scalars['Boolean']['output']>;
  interviewee_feedback_meta?: Maybe<Scalars['JSON']['output']>;
  interviewer_feedback_enabled?: Maybe<Scalars['Boolean']['output']>;
  interviewer_feedback_meta?: Maybe<Scalars['JSON']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  status?: Maybe<Scalars['Boolean']['output']>;
  title: Scalars['String']['output'];
};

export type TemplateAndFeedbackSection = {
  __typename?: 'TemplateAndFeedbackSection';
  feedback_section?: Maybe<Array<Maybe<FeedbackSection>>>;
  id?: Maybe<Scalars['Int']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type TemplateInput = {
  feedback_section?: InputMaybe<Array<InputMaybe<FeedbackSectionInput>>>;
  interviewee_feedback_enabled: Scalars['Boolean']['input'];
  interviewee_feedback_meta?: InputMaybe<Scalars['JSON']['input']>;
  interviewer_feedback_enabled: Scalars['Boolean']['input'];
  interviewer_feedback_meta: Scalars['JSON']['input'];
  meta?: InputMaybe<Scalars['JSON']['input']>;
  status: Scalars['Boolean']['input'];
  title: Scalars['String']['input'];
};

export type TestCompletion = {
  __typename?: 'TestCompletion';
  completed_count?: Maybe<Scalars['Int']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  total?: Maybe<Scalars['Int']['output']>;
};

export type TokenData = {
  __typename?: 'TokenData';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
  token?: Maybe<Scalars['String']['output']>;
};

export type TotalUsersCountResult = {
  __typename?: 'TotalUsersCountResult';
  data?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type TrackAnalyticsInput = {
  activityType?: InputMaybe<Scalars['String']['input']>;
  deviceId?: InputMaybe<Scalars['String']['input']>;
  deviceInfo?: InputMaybe<Scalars['JSON']['input']>;
  endTime?: InputMaybe<Scalars['DateTime']['input']>;
  funnelStage?: InputMaybe<UserFunnelStage>;
  installationStatus?: InputMaybe<AppInstallationStatus>;
  ipAddress?: InputMaybe<Scalars['String']['input']>;
  language?: InputMaybe<Scalars['String']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  platformType?: InputMaybe<AppPlatformType>;
  referrer?: InputMaybe<Scalars['String']['input']>;
  sessionId?: InputMaybe<Scalars['String']['input']>;
  startTime?: InputMaybe<Scalars['DateTime']['input']>;
  timeSpent?: InputMaybe<Scalars['Int']['input']>;
  tookAction?: InputMaybe<Scalars['Boolean']['input']>;
  type: AnalyticsType;
  urlPath?: InputMaybe<Scalars['String']['input']>;
  userAgent?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['Int']['input']>;
};

export enum TrainingApprovalStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export type TrainingAssignmentProcessStatusResultData = {
  __typename?: 'TrainingAssignmentProcessStatusResultData';
  completed: Scalars['Int']['output'];
  percentage: Scalars['Float']['output'];
  total: Scalars['Int']['output'];
};

export type TypeFilter = {
  createdAt?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  required?: InputMaybe<Scalars['Boolean']['input']>;
  updatedAt?: InputMaybe<Scalars['String']['input']>;
};

export type UnpublishedModuleByIdResponse = {
  __typename?: 'UnpublishedModuleByIdResponse';
  data?: Maybe<UnpublishedModuleWithSubmodules>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type UnpublishedModuleData = {
  __typename?: 'UnpublishedModuleData';
  icon_name: Scalars['String']['output'];
  is_active: Scalars['Boolean']['output'];
  is_published: Scalars['Boolean']['output'];
  module_id: Scalars['Int']['output'];
  module_name: Scalars['String']['output'];
  parent_module_id?: Maybe<Scalars['Int']['output']>;
  parent_module_name?: Maybe<Scalars['String']['output']>;
  published_at?: Maybe<Scalars['DateTime']['output']>;
  published_by?: Maybe<Scalars['Int']['output']>;
  submodules?: Maybe<Array<Maybe<SubmoduleData>>>;
  visible_in_sp: Scalars['Boolean']['output'];
  visible_in_users: Scalars['Boolean']['output'];
};

export type UnpublishedModuleResponse = {
  __typename?: 'UnpublishedModuleResponse';
  data?: Maybe<Array<Maybe<UnpublishedModuleData>>>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type UnpublishedModuleWithSubmodules = {
  __typename?: 'UnpublishedModuleWithSubmodules';
  icon_name?: Maybe<Scalars['String']['output']>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  is_published?: Maybe<Scalars['Boolean']['output']>;
  module_id?: Maybe<Scalars['Int']['output']>;
  module_name?: Maybe<Scalars['String']['output']>;
  parent_module_id?: Maybe<Scalars['Int']['output']>;
  parent_module_name?: Maybe<Scalars['String']['output']>;
  published_at?: Maybe<Scalars['DateTime']['output']>;
  published_by?: Maybe<Scalars['Int']['output']>;
  submodules?: Maybe<Array<Maybe<SubmoduleData>>>;
  visible_in_sp?: Maybe<Scalars['Boolean']['output']>;
  visible_in_users?: Maybe<Scalars['Boolean']['output']>;
};

export type UnregisteredReferral = {
  __typename?: 'UnregisteredReferral';
  created_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  name: Scalars['String']['output'];
  phone_number: Scalars['String']['output'];
  referrer?: Maybe<ReferrerData>;
  referrer_id?: Maybe<Scalars['Int']['output']>;
  updated_at: Scalars['DateTime']['output'];
};

export type UnregisteredReferralsResponse = {
  __typename?: 'UnregisteredReferralsResponse';
  data: Array<UnregisteredReferral>;
  total_count: Scalars['Int']['output'];
};

export type UpdateAdminRoleInput = {
  admin_role_id?: InputMaybe<Scalars['Int']['input']>;
  admin_role_permission?: InputMaybe<Array<InputMaybe<RolePermissionData>>>;
  can_accept?: InputMaybe<Scalars['Boolean']['input']>;
  can_ban?: InputMaybe<Scalars['Boolean']['input']>;
  can_download?: InputMaybe<Scalars['Boolean']['input']>;
  can_reject?: InputMaybe<Scalars['Boolean']['input']>;
  creator_id?: InputMaybe<Scalars['Int']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  has_admin_privileges?: InputMaybe<Scalars['Boolean']['input']>;
  is_root_admin?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateBannerConfigInput = {
  banner_config_id: Scalars['String']['input'];
  cta_link?: InputMaybe<Scalars['String']['input']>;
  cta_required?: InputMaybe<Scalars['Boolean']['input']>;
  cta_text?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  hero_image?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  is_published?: InputMaybe<Scalars['Boolean']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateBannerInput = {
  expiry_time?: InputMaybe<Scalars['DateTime']['input']>;
  id: Scalars['String']['input'];
  is_deleted?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateCalendarEventArgsType = {
  date?: InputMaybe<Scalars['DateTime']['input']>;
  end?: InputMaybe<Scalars['DateTime']['input']>;
  event_id?: InputMaybe<Scalars['String']['input']>;
  google_meet_link?: InputMaybe<Scalars['String']['input']>;
  interview_status?: InputMaybe<InterviewState>;
  interviewer_id?: InputMaybe<Scalars['Int']['input']>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  note?: InputMaybe<Scalars['String']['input']>;
  start?: InputMaybe<Scalars['DateTime']['input']>;
  template_id?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Event_Type>;
  update_type?: InputMaybe<UpdateInterviewType>;
  user_id?: InputMaybe<Scalars['Int']['input']>;
};

export type UpdateChecklistInput = {
  id: Scalars['String']['input'];
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateContentInputData = {
  content_id: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  designation?: InputMaybe<Array<Scalars['String']['input']>>;
  duration?: InputMaybe<Scalars['Int']['input']>;
  expertise?: InputMaybe<Array<Scalars['Int']['input']>>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  is_deleted?: InputMaybe<Scalars['Boolean']['input']>;
  published?: InputMaybe<Scalars['Boolean']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<ContentType>;
  video_url?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateCourseInputData = {
  assessments?: InputMaybe<Array<Scalars['String']['input']>>;
  business_vertical?: InputMaybe<BusinessVertical>;
  chapters?: InputMaybe<Array<ChapterAndLessonInputData>>;
  description?: InputMaybe<Scalars['String']['input']>;
  designation?: InputMaybe<Array<Scalars['String']['input']>>;
  expertise?: InputMaybe<Array<Scalars['Int']['input']>>;
  image?: InputMaybe<Scalars['String']['input']>;
  internal_course_id: Scalars['String']['input'];
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  is_deleted?: InputMaybe<Scalars['Boolean']['input']>;
  method?: InputMaybe<CourseMethod>;
  name?: InputMaybe<Scalars['String']['input']>;
  objective?: InputMaybe<Scalars['String']['input']>;
  published?: InputMaybe<Scalars['Boolean']['input']>;
  status?: InputMaybe<UserCourseProgressStatus>;
  title?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<CourseType>;
  video_url?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateDesignationInput = {
  expertise?: InputMaybe<Array<Scalars['Int']['input']>>;
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  level?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateDocumentTypeInput = {
  doc_num_max_length?: InputMaybe<Scalars['Int']['input']>;
  doc_num_min_length?: InputMaybe<Scalars['Int']['input']>;
  doc_num_validation_type?: InputMaybe<DocNumberValidationType>;
  document_id?: InputMaybe<Scalars['Int']['input']>;
  instructions?: InputMaybe<Scalars['JSON']['input']>;
  is_doc_number_required?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  required?: InputMaybe<Scalars['Boolean']['input']>;
  status?: InputMaybe<DocumentStatus>;
  visible_to_service_provider_technicians?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateFeedbackTemplateInput = {
  id: Scalars['Int']['input'];
  interviewee_feedback_enabled?: InputMaybe<Scalars['Boolean']['input']>;
  interviewee_feedback_meta?: InputMaybe<Scalars['JSON']['input']>;
  interviewer_feedback_enabled?: InputMaybe<Scalars['Boolean']['input']>;
  interviewer_feedback_meta?: InputMaybe<Scalars['JSON']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  status?: InputMaybe<Scalars['Boolean']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export enum UpdateInterviewType {
  Cancel = 'CANCEL',
  Update = 'UPDATE'
}

export type UpdateInterviewerData = {
  active: Scalars['Boolean']['input'];
  city?: InputMaybe<Scalars['String']['input']>;
  district_taluka?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  id: Scalars['Int']['input'];
  landmark?: InputMaybe<Scalars['String']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name: Scalars['String']['input'];
  pincode: Scalars['String']['input'];
  state: Scalars['String']['input'];
  work_address?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateOrganizationInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  location_id?: InputMaybe<Scalars['Int']['input']>;
  logo?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  trades?: InputMaybe<Scalars['JSON']['input']>;
};

export type UpdateOrganizationResult = {
  __typename?: 'UpdateOrganizationResult';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type UpdatePolicyInput = {
  id: Scalars['String']['input'];
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  policy_name?: InputMaybe<Scalars['String']['input']>;
  policy_name_hindi?: InputMaybe<Scalars['String']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateProviderInput = {
  orgDetails?: InputMaybe<OrgUpdateInput>;
  userDetails?: InputMaybe<ProviderUpdateInput>;
};

export type UpdateReferralConfigInput = {
  existing_user_points?: InputMaybe<Scalars['Int']['input']>;
  new_user_points?: InputMaybe<Scalars['Int']['input']>;
  referral_enabled?: InputMaybe<Scalars['Boolean']['input']>;
  terms_and_condition?: InputMaybe<Scalars['JSON']['input']>;
  user_point_redeem_stage?: InputMaybe<OnboardingStage>;
};

export type UpdateRoleData = {
  name?: InputMaybe<Scalars['String']['input']>;
  permission?: InputMaybe<Scalars['JSON']['input']>;
};

export type UpdateTeamMemberInput = {
  email?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Gender>;
  landmark?: InputMaybe<Scalars['String']['input']>;
  location_id?: InputMaybe<Scalars['Int']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  onboarding_stage?: InputMaybe<OnboardingStage>;
  phone?: InputMaybe<Scalars['String']['input']>;
  photoUrl?: InputMaybe<Scalars['String']['input']>;
  pincode?: InputMaybe<Scalars['String']['input']>;
  skills?: InputMaybe<ExistingAndNewSkills>;
  state?: InputMaybe<Scalars['String']['input']>;
  user_type?: InputMaybe<UserRole>;
  work_address?: InputMaybe<Scalars['String']['input']>;
  years_of_experience?: InputMaybe<Scalars['Int']['input']>;
};

export type UpdateUserAdminInput = {
  city?: InputMaybe<Scalars['String']['input']>;
  designation?: InputMaybe<Scalars['String']['input']>;
  district_taluka?: InputMaybe<Scalars['String']['input']>;
  doc_verification_state?: InputMaybe<DocVerificationStatus>;
  email?: InputMaybe<Scalars['String']['input']>;
  fulfillment_type?: InputMaybe<FulfillmentType>;
  gender?: InputMaybe<Gender>;
  landmark?: InputMaybe<Scalars['String']['input']>;
  location_id?: InputMaybe<Scalars['Int']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  onboarding_stage?: InputMaybe<OnboardingStage>;
  phone?: InputMaybe<Scalars['String']['input']>;
  photoUrl?: InputMaybe<Scalars['String']['input']>;
  pincode?: InputMaybe<Scalars['String']['input']>;
  poc?: InputMaybe<Scalars['String']['input']>;
  remark?: InputMaybe<Scalars['String']['input']>;
  skills?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  source?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  user_onboarding_data?: InputMaybe<UserOnboardingDataUpdateInput>;
  user_state?: InputMaybe<Scalars['String']['input']>;
  work_address?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateUserChecklistDataInput = {
  checklist_id?: InputMaybe<Scalars['String']['input']>;
  hiring_completed?: InputMaybe<Scalars['Boolean']['input']>;
  language?: InputMaybe<Scalars['String']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  technician_completed?: InputMaybe<Scalars['Boolean']['input']>;
  user_id: Scalars['Int']['input'];
};

export type UpdateUserCourseStatusInputData = {
  chapter: UserChapterStatusInputData;
  content_id: Scalars['String']['input'];
  course_id: Scalars['String']['input'];
  lesson: UserLessonStatusInputData;
  user_course_id: Scalars['String']['input'];
  video_progress_in_seconds?: InputMaybe<Scalars['Int']['input']>;
};

export type UpdateUserData = {
  __typename?: 'UpdateUserData';
  data?: Maybe<User>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type UpdateUserInput = {
  city?: InputMaybe<Scalars['String']['input']>;
  doc_verification_state?: InputMaybe<DocVerificationStatus>;
  email?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Gender>;
  landmark?: InputMaybe<Scalars['String']['input']>;
  location_id?: InputMaybe<Scalars['Int']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  onboarding_stage?: InputMaybe<OnboardingStage>;
  phone?: InputMaybe<Scalars['String']['input']>;
  photoUrl?: InputMaybe<Scalars['String']['input']>;
  pincode?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  user_type?: InputMaybe<UserRole>;
  work_address?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateUserTypeInput = {
  user_type: Scalars['String']['input'];
};

export type UpdateUserTypeResult = {
  __typename?: 'UpdateUserTypeResult';
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type User = {
  __typename?: 'User';
  designation?: Maybe<Designation>;
  doc_verification_state?: Maybe<DocVerificationStatus>;
  email?: Maybe<Scalars['String']['output']>;
  expertise?: Maybe<Array<Maybe<Expertise>>>;
  fulfillment_type: FulfillmentType;
  gender?: Maybe<Gender>;
  id?: Maybe<Scalars['Int']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  last_contacts_synced_at?: Maybe<Scalars['DateTime']['output']>;
  location?: Maybe<Location>;
  location_id?: Maybe<Scalars['Int']['output']>;
  manual_assignments?: Maybe<Array<Maybe<ManualAssignment>>>;
  meta?: Maybe<Scalars['JSON']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onboarded?: Maybe<Scalars['Boolean']['output']>;
  onboarding_stage?: Maybe<OnboardingStage>;
  organization?: Maybe<Organization>;
  organization_id?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  phoneVerified?: Maybe<Scalars['Boolean']['output']>;
  phone_verified?: Maybe<Scalars['Boolean']['output']>;
  photoUrl?: Maybe<Scalars['String']['output']>;
  poc?: Maybe<Scalars['String']['output']>;
  remark?: Maybe<Scalars['String']['output']>;
  roles?: Maybe<Array<Maybe<Role>>>;
  source?: Maybe<Scalars['String']['output']>;
  supported_designations?: Maybe<Array<Maybe<Designation>>>;
  transfer_to_tms_done: Scalars['Boolean']['output'];
  user_onboarding_data?: Maybe<Array<Maybe<UserOnboardingData>>>;
  user_professional_data?: Maybe<Array<Maybe<UserProfessionalExperienceResult>>>;
  user_state?: Maybe<Scalars['String']['output']>;
  user_training_approval_status?: Maybe<TrainingApprovalStatus>;
  user_type?: Maybe<Scalars['String']['output']>;
};

export type UserActions = {
  batch?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  size?: InputMaybe<Scalars['Int']['input']>;
};

export type UserActivity = {
  __typename?: 'UserActivity';
  activity_type: Scalars['String']['output'];
  created_at: Scalars['DateTime']['output'];
  end_time?: Maybe<Scalars['DateTime']['output']>;
  funnel_stage?: Maybe<UserFunnelStage>;
  id: Scalars['ID']['output'];
  ip_address?: Maybe<Scalars['String']['output']>;
  language: Scalars['String']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  platform_type: AppPlatformType;
  session_id: Scalars['String']['output'];
  start_time: Scalars['DateTime']['output'];
  time_spent?: Maybe<Scalars['Int']['output']>;
  took_action: Scalars['Boolean']['output'];
  updated_at: Scalars['DateTime']['output'];
  url_path?: Maybe<Scalars['String']['output']>;
  user_agent?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['Int']['output']>;
};

export type UserAdminChecklistData = {
  __typename?: 'UserAdminChecklistData';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  created_by?: Maybe<Scalars['Int']['output']>;
  creator?: Maybe<Admin>;
  edited_by?: Maybe<Scalars['Int']['output']>;
  editor?: Maybe<Admin>;
  hiring_completed?: Maybe<Scalars['Boolean']['output']>;
  hiring_completed_at?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  technician_completed?: Maybe<Scalars['Boolean']['output']>;
  technician_completed_at?: Maybe<Scalars['DateTime']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  user?: Maybe<Admin>;
};

export type UserAndOrgDetailsAdmin = {
  __typename?: 'UserAndOrgDetailsAdmin';
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onboarded?: Maybe<Scalars['Boolean']['output']>;
  onboarding_stage?: Maybe<OnboardingStage>;
  organization: OrgDetailsFrUser;
  phone?: Maybe<Scalars['String']['output']>;
  photoUrl?: Maybe<Scalars['String']['output']>;
  transfer_to_tms_status?: Maybe<Scalars['Boolean']['output']>;
  user_expertise_map?: Maybe<Array<UsersExpertiseMap>>;
  user_onboarding_data?: Maybe<Array<Maybe<UserOnboardingData>>>;
  user_professional_data?: Maybe<Array<Maybe<UserProfessionalExperienceResult>>>;
};

export type UserAssignmentResult = {
  __typename?: 'UserAssignmentResult';
  assignments: Array<Maybe<UserResultAssignments>>;
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  overall_assignment_info: OverallAssignmentInfo;
};

export type UserBankDetails = {
  __typename?: 'UserBankDetails';
  account_number?: Maybe<Scalars['String']['output']>;
  bank_name?: Maybe<Scalars['String']['output']>;
  ifsc?: Maybe<Scalars['String']['output']>;
};

export type UserBankDetailsInput = {
  account_number?: InputMaybe<Scalars['String']['input']>;
  bank_name?: InputMaybe<Scalars['String']['input']>;
  ifsc?: InputMaybe<Scalars['String']['input']>;
  user_id: Scalars['Int']['input'];
};

export type UserCalendarInputData = {
  interview_date?: InputMaybe<Scalars['DateTime']['input']>;
  interviewer_id?: InputMaybe<Scalars['Int']['input']>;
};

export type UserChapterProgress = {
  __typename?: 'UserChapterProgress';
  is_completed: Scalars['Boolean']['output'];
  lessons_progress: Array<LessonProgress>;
  time_spent: Scalars['Int']['output'];
};

export type UserChapterStatusInputData = {
  chapter_id: Scalars['String']['input'];
  is_completed?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UserChecklistData = {
  __typename?: 'UserChecklistData';
  id: Scalars['String']['output'];
  name?: Maybe<Scalars['String']['output']>;
  user_checklist_data?: Maybe<Array<Maybe<UserAdminChecklistData>>>;
};

export type UserChecklistDataResponse = {
  __typename?: 'UserChecklistDataResponse';
  data?: Maybe<Array<Maybe<UserChecklistData>>>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type UserChecklistHistoryResponse = {
  __typename?: 'UserChecklistHistoryResponse';
  data?: Maybe<ChecklistHistoryData>;
  message: Scalars['String']['output'];
  result: Scalars['Boolean']['output'];
};

export type UserContacts = {
  __typename?: 'UserContacts';
  contact?: Maybe<Contact>;
  contact_id?: Maybe<Scalars['String']['output']>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  user_id?: Maybe<Scalars['String']['output']>;
};

export type UserContactsFilter = {
  moved_to_users_status?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};

export type UserContactsWithCount = {
  __typename?: 'UserContactsWithCount';
  data?: Maybe<Array<Maybe<UserContacts>>>;
  total_count?: Maybe<Scalars['Int']['output']>;
  total_moved_contacts_count?: Maybe<Scalars['Int']['output']>;
};

export type UserContentFilter = {
  expertise?: InputMaybe<Array<Scalars['Int']['input']>>;
};

export type UserCourseAssignmentData = {
  __typename?: 'UserCourseAssignmentData';
  status: AssignmentStatus;
};

export type UserCourseDetails = {
  __typename?: 'UserCourseDetails';
  _count?: Maybe<UserCourseDetailsCountData>;
  course: Course;
  course_completed: Scalars['Boolean']['output'];
  course_completed_count: Scalars['Int']['output'];
  course_user_assignment_map?: Maybe<Array<CourseUserAssignmentMap>>;
  training_completed: Scalars['Boolean']['output'];
  user_chapter_progress: Array<UserChapterProgress>;
};

export type UserCourseDetailsCountData = {
  __typename?: 'UserCourseDetailsCountData';
  course_user_assignment_map: Scalars['Int']['output'];
};

export type UserCourseFilter = {
  designation?: InputMaybe<Array<Scalars['String']['input']>>;
  expertise?: InputMaybe<Array<Scalars['Int']['input']>>;
};

export enum UserCourseProgressStatus {
  Completed = 'COMPLETED',
  InProgress = 'IN_PROGRESS',
  Pending = 'PENDING'
}

export type UserData = {
  __typename?: 'UserData';
  data?: Maybe<User>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type UserDeletedData = {
  __typename?: 'UserDeletedData';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type UserDesignation = {
  __typename?: 'UserDesignation';
  id?: Maybe<Scalars['String']['output']>;
  level?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export enum UserDetailsType {
  Applications = 'APPLICATIONS',
  Individuals = 'INDIVIDUALS',
  Onboarded = 'ONBOARDED',
  Rejected = 'REJECTED',
  ServiceProviders = 'SERVICE_PROVIDERS'
}

export type UserExpMapFrTeamMember = {
  __typename?: 'UserExpMapFrTeamMember';
  expertise: Expertise;
};

export type UserExperience = {
  __typename?: 'UserExperience';
  user_professional_data?: Maybe<ProfessionalData>;
  work_experience?: Maybe<Array<Maybe<WorkExperience>>>;
};

export type UserFilter = {
  designation?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  email?: InputMaybe<Scalars['String']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  expertise?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  fulfillment_type?: InputMaybe<FulfillmentType>;
  gender?: InputMaybe<Gender>;
  id?: InputMaybe<Scalars['Int']['input']>;
  isActive?: InputMaybe<Scalars['Boolean']['input']>;
  location_id?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  onboarding_stage?: InputMaybe<OnboardingStage>;
  phone?: InputMaybe<Scalars['String']['input']>;
  phoneVerified?: InputMaybe<Scalars['Boolean']['input']>;
  phone_verified?: InputMaybe<Scalars['Boolean']['input']>;
  stage?: InputMaybe<Array<InputMaybe<OnboardingStage>>>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  user_type?: InputMaybe<Scalars['String']['input']>;
};

export enum UserFunnelStage {
  ActiveUser = 'ACTIVE_USER',
  LoginPage = 'LOGIN_PAGE',
  OnboardingComplete = 'ONBOARDING_COMPLETE',
  OnboardingStart = 'ONBOARDING_START',
  SignupComplete = 'SIGNUP_COMPLETE',
  SignupStart = 'SIGNUP_START',
  Visitor = 'VISITOR'
}

export type UserInterviewAdminData = {
  __typename?: 'UserInterviewAdminData';
  data?: Maybe<Scalars['JSON']['output']>;
  google_event_id?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  interviewer?: Maybe<Interviewer>;
  name?: Maybe<Scalars['String']['output']>;
  type?: Maybe<CalendarEventType>;
  user?: Maybe<User>;
};

export type UserInterviewCount = {
  __typename?: 'UserInterviewCount';
  data?: Maybe<Array<Maybe<UserInterviewDetails>>>;
  totalCount?: Maybe<Scalars['Int']['output']>;
};

export type UserInterviewData = {
  __typename?: 'UserInterviewData';
  data?: Maybe<Scalars['JSON']['output']>;
  google_event_id?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  interviewer?: Maybe<Interviewer>;
  location?: Maybe<Location>;
  name?: Maybe<Scalars['String']['output']>;
  type?: Maybe<CalendarEventType>;
  user?: Maybe<User>;
};

export type UserInterviewDetails = {
  __typename?: 'UserInterviewDetails';
  email?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  interview?: Maybe<Array<Maybe<InterviewWitInterviewData>>>;
  name?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
};

export type UserInterviewDetailsForAdminResults = {
  __typename?: 'UserInterviewDetailsForAdminResults';
  created_at: Scalars['DateTime']['output'];
  end_time: Scalars['DateTime']['output'];
  feedback?: Maybe<InterviewFeedback>;
  google_meet_link?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  interview_type: InterviewType;
  interviewer: Interviewer;
  note?: Maybe<Scalars['String']['output']>;
  schedule_for?: Maybe<InterviewScheduleFor>;
  scheduled_by?: Maybe<InterviewScheduledBy>;
  start_time: Scalars['DateTime']['output'];
  status: InterviewState;
  updated_at: Scalars['DateTime']['output'];
};

export type UserInterviewResult = {
  __typename?: 'UserInterviewResult';
  attempts?: Maybe<Scalars['Int']['output']>;
  end_time?: Maybe<Scalars['DateTime']['output']>;
  feedback?: Maybe<FeedbackResultsFrUser>;
  google_meet_link?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  interview_type?: Maybe<Scalars['String']['output']>;
  interviewer?: Maybe<Interviewer>;
  is_active?: Maybe<Scalars['Boolean']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  start_time?: Maybe<Scalars['DateTime']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type UserLessonStatusInputData = {
  is_completed?: InputMaybe<Scalars['Boolean']['input']>;
  lesson_id: Scalars['String']['input'];
};

export type UserMascotAlertData = {
  __typename?: 'UserMascotAlertData';
  cta_link?: Maybe<Scalars['String']['output']>;
  cta_required: Scalars['Boolean']['output'];
  cta_text?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  is_read: Scalars['Boolean']['output'];
  message: Scalars['String']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  priority?: Maybe<MascotNotificationPriority>;
  title?: Maybe<Scalars['String']['output']>;
  type: MascotSectionType;
};

export type UserMetaInput = {
  reference?: InputMaybe<Scalars['String']['input']>;
};

export type UserName = {
  __typename?: 'UserName';
  name?: Maybe<Scalars['String']['output']>;
};

export type UserOnboardingData = {
  __typename?: 'UserOnboardingData';
  aadhar_address?: Maybe<Scalars['String']['output']>;
  aadhar_number?: Maybe<Scalars['String']['output']>;
  assessed_by?: Maybe<Scalars['String']['output']>;
  assessment_score?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  current_address?: Maybe<Scalars['Int']['output']>;
  date_of_joining?: Maybe<Scalars['DateTime']['output']>;
  designation?: Maybe<Scalars['String']['output']>;
  employee_id?: Maybe<Scalars['Int']['output']>;
  esic_number?: Maybe<Scalars['String']['output']>;
  family_details?: Maybe<Scalars['String']['output']>;
  fathers_name?: Maybe<Scalars['String']['output']>;
  form_no_11?: Maybe<Scalars['String']['output']>;
  hiring_criteria?: Maybe<HiringCriteria>;
  hiring_manager_id?: Maybe<Scalars['Int']['output']>;
  martial_status?: Maybe<MartialStatus>;
  meta?: Maybe<Scalars['JSON']['output']>;
  mothers_name?: Maybe<Scalars['String']['output']>;
  pancard_number?: Maybe<Scalars['String']['output']>;
  poc?: Maybe<Scalars['String']['output']>;
  remark?: Maybe<Scalars['String']['output']>;
  salary_offered?: Maybe<Scalars['String']['output']>;
  salary_vp?: Maybe<Scalars['String']['output']>;
  source?: Maybe<Scalars['String']['output']>;
  user_created_in_tms?: Maybe<Scalars['Boolean']['output']>;
  user_id?: Maybe<Scalars['Int']['output']>;
};

export type UserOnboardingDataInput = {
  aadhar_address?: InputMaybe<Scalars['String']['input']>;
  aadhar_number?: InputMaybe<Scalars['String']['input']>;
  assessed_by?: InputMaybe<Scalars['String']['input']>;
  assessment_score?: InputMaybe<Scalars['String']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  current_address?: InputMaybe<Scalars['Int']['input']>;
  date_of_joining?: InputMaybe<Scalars['DateTime']['input']>;
  designation?: InputMaybe<Scalars['String']['input']>;
  employee_id?: InputMaybe<Scalars['Int']['input']>;
  esic_number?: InputMaybe<Scalars['String']['input']>;
  family_details?: InputMaybe<Scalars['String']['input']>;
  fathers_name?: InputMaybe<Scalars['String']['input']>;
  form_no_11?: InputMaybe<Scalars['String']['input']>;
  hiring_criteria?: InputMaybe<HiringCriteria>;
  hiring_manager_id?: InputMaybe<Scalars['Int']['input']>;
  martial_status?: InputMaybe<MartialStatus>;
  mothers_name?: InputMaybe<Scalars['String']['input']>;
  pancard_number?: InputMaybe<Scalars['String']['input']>;
  remark?: InputMaybe<Scalars['String']['input']>;
  salary_offered?: InputMaybe<Scalars['String']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  user_created_in_tms?: InputMaybe<Scalars['Boolean']['input']>;
  user_id?: InputMaybe<Scalars['Int']['input']>;
};

export type UserOnboardingDataResult = {
  __typename?: 'UserOnboardingDataResult';
  bank_details?: Maybe<UserBankDetails>;
  designation?: Maybe<UserDesignation>;
  doc_verification_state?: Maybe<DocVerificationStatus>;
  email?: Maybe<Scalars['String']['output']>;
  employee_id?: Maybe<Scalars['Int']['output']>;
  gender?: Maybe<Gender>;
  id?: Maybe<Scalars['String']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  location?: Maybe<Location>;
  location_id?: Maybe<Scalars['Int']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onboarding_stage?: Maybe<OnboardingStage>;
  organization?: Maybe<Organization>;
  organization_id?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  phoneVerified?: Maybe<Scalars['Boolean']['output']>;
  phone_verified?: Maybe<Scalars['Boolean']['output']>;
  photoUrl?: Maybe<Scalars['String']['output']>;
  transfer_to_tms_status?: Maybe<Scalars['Boolean']['output']>;
  user_onboarding_data?: Maybe<Array<Maybe<UserOnboardingData>>>;
};

export type UserOnboardingDataResultWithCount = {
  __typename?: 'UserOnboardingDataResultWithCount';
  data?: Maybe<Array<Maybe<UserOnboardingDataResult>>>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type UserOnboardingDataUpdateInput = {
  date_of_joining?: InputMaybe<Scalars['DateTime']['input']>;
  fathers_name?: InputMaybe<Scalars['String']['input']>;
  martial_status?: InputMaybe<MartialStatus>;
  mothers_name?: InputMaybe<Scalars['String']['input']>;
  salary_offered?: InputMaybe<Scalars['String']['input']>;
  salary_vp?: InputMaybe<Scalars['String']['input']>;
};

export type UserPolicyData = {
  all_accepted?: InputMaybe<Scalars['Boolean']['input']>;
  id?: InputMaybe<Scalars['Int']['input']>;
  last_accepted_date?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  onboarded_date?: InputMaybe<Scalars['String']['input']>;
  policies_accepted?: InputMaybe<Scalars['Int']['input']>;
  total_policies?: InputMaybe<Scalars['Int']['input']>;
  user_code?: InputMaybe<Scalars['String']['input']>;
};

export type UserPolicyStatus = {
  __typename?: 'UserPolicyStatus';
  accepted: Scalars['Boolean']['output'];
  accepted_on?: Maybe<Scalars['DateTime']['output']>;
  user_id: Scalars['Int']['output'];
  user_name?: Maybe<Scalars['String']['output']>;
};

export type UserPolicyTrackingData = {
  __typename?: 'UserPolicyTrackingData';
  all_accepted: Scalars['Boolean']['output'];
  id: Scalars['Int']['output'];
  last_accepted_date?: Maybe<Scalars['DateTime']['output']>;
  name: Scalars['String']['output'];
  onboarded_date: Scalars['DateTime']['output'];
  policies_accepted: Scalars['Int']['output'];
  total_policies: Scalars['Int']['output'];
  user_onboarding_data?: Maybe<Array<Maybe<UserOnboardingData>>>;
};

export type UserPreferredLanguageInput = {
  preferred_language?: InputMaybe<Preferred_Language>;
};

export type UserProfessionalData = {
  __typename?: 'UserProfessionalData';
  year_of_experience?: Maybe<Scalars['Int']['output']>;
};

export type UserProfessionalExperienceArgs = {
  created_at?: InputMaybe<Scalars['DateTime']['input']>;
  current_work_location_id?: InputMaybe<Scalars['Int']['input']>;
  experienced: Scalars['Boolean']['input'];
  highest_education: Scalars['String']['input'];
  location?: InputMaybe<InputLocation>;
  updated_at?: InputMaybe<Scalars['DateTime']['input']>;
  year_of_experience?: InputMaybe<Scalars['Int']['input']>;
};

export type UserProfessionalExperienceResult = {
  __typename?: 'UserProfessionalExperienceResult';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  current_work_location_id?: Maybe<Scalars['Int']['output']>;
  experienced?: Maybe<Scalars['Boolean']['output']>;
  highest_education?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  year_of_experience?: Maybe<Scalars['Int']['output']>;
};

export type UserProfileCompletionResult = {
  __typename?: 'UserProfileCompletionResult';
  data?: Maybe<Scalars['Int']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type UserResultAssignments = {
  __typename?: 'UserResultAssignments';
  config_details?: Maybe<AssignmentConfigData>;
  questions: Array<Maybe<UserResultQuestions>>;
};

export type UserResultQuestions = {
  __typename?: 'UserResultQuestions';
  question: Question;
  users_answer: Option;
};

export type UserResultResponse = {
  __typename?: 'UserResultResponse';
  completed_assignments: Scalars['Int']['output'];
  id: Scalars['Int']['output'];
  last_assignment_date?: Maybe<Scalars['DateTime']['output']>;
  name: Scalars['String']['output'];
  on_going: Scalars['Int']['output'];
  pending: Scalars['Int']['output'];
  qualified_for_training: TrainingApprovalStatus;
  total_assignments: Scalars['Int']['output'];
  total_count?: Maybe<Scalars['Int']['output']>;
};

export enum UserRole {
  NotSpecified = 'NOT_SPECIFIED',
  ServiceProvider = 'SERVICE_PROVIDER',
  Technician = 'TECHNICIAN'
}

export type UserStatus = {
  __typename?: 'UserStatus';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  description: Scalars['String']['output'];
  id: Scalars['String']['output'];
  meta?: Maybe<Scalars['JSON']['output']>;
  status: Scalars['String']['output'];
  title: Scalars['String']['output'];
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  user_id?: Maybe<Scalars['Int']['output']>;
};

export type UserTimelineData = {
  __typename?: 'UserTimelineData';
  admin?: Maybe<AdminName>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  doc_verification_state?: Maybe<DocVerificationStatus>;
  initiated_by?: Maybe<UserType>;
  onboarding_stage?: Maybe<OnboardingStage>;
  transfer_to_tms_status?: Maybe<Scalars['Boolean']['output']>;
  user?: Maybe<UserName>;
};

export type UserTrainingStatusData = {
  __typename?: 'UserTrainingStatusData';
  userTrainingStatus: UserCourseProgressStatus;
};

export enum UserType {
  Admin = 'ADMIN',
  Provider = 'PROVIDER',
  User = 'USER',
  Worker = 'WORKER'
}

export enum UserTypeDocument {
  ServiceProvider = 'SERVICE_PROVIDER',
  Technician = 'TECHNICIAN'
}

export type UserVideoProgressResultData = {
  __typename?: 'UserVideoProgressResultData';
  is_completed: Scalars['Boolean']['output'];
  progress: Scalars['Int']['output'];
};

export type UserWithContentDetails = {
  __typename?: 'UserWithContentDetails';
  content_user_map?: Maybe<Array<Maybe<ContentUserMap>>>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  is_assigned?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  user_expertise_map?: Maybe<Array<Maybe<UsersExpertiseMap>>>;
};

export type UserWithContentDetailsAndCount = {
  __typename?: 'UserWithContentDetailsAndCount';
  user_count?: Maybe<Scalars['Int']['output']>;
  user_details?: Maybe<Array<Maybe<UserWithContentDetails>>>;
};

export type UserWithCourseDetails = {
  __typename?: 'UserWithCourseDetails';
  course_user_map?: Maybe<Array<Maybe<CourseUserMap>>>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  user_expertise_map?: Maybe<Array<Maybe<UsersExpertiseMap>>>;
};

export type UserWorkExperise = {
  __typename?: 'UserWorkExperise';
  year_of_experience: Scalars['Int']['output'];
};

export type User_Policy_Tracking = {
  __typename?: 'User_Policy_Tracking';
  accepted?: Maybe<Scalars['Boolean']['output']>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
};

export type Users = {
  __typename?: 'Users';
  created_at?: Maybe<Scalars['DateTime']['output']>;
  designation?: Maybe<Designation>;
  email?: Maybe<Scalars['String']['output']>;
  fulfillment_type?: Maybe<FulfillmentType>;
  gender?: Maybe<Gender>;
  id?: Maybe<Scalars['Int']['output']>;
  interview_feedback?: Maybe<Array<Maybe<InterviewFeedback>>>;
  interview_status?: Maybe<Scalars['String']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  location?: Maybe<Location>;
  location_id?: Maybe<Scalars['Int']['output']>;
  meta?: Maybe<Scalars['JSON']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onboarding_stage?: Maybe<OnboardingStage>;
  organization?: Maybe<Organization>;
  organization_id?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  phoneVerified?: Maybe<Scalars['Boolean']['output']>;
  phone_verified?: Maybe<Scalars['Boolean']['output']>;
  photoUrl?: Maybe<Scalars['String']['output']>;
  roles?: Maybe<Array<Maybe<Role>>>;
  user_expertise_map?: Maybe<Array<Maybe<UsersExpertise>>>;
  user_onboarding_data?: Maybe<Array<Maybe<UserOnboardingData>>>;
};

export type UsersCountByType = {
  __typename?: 'UsersCountByType';
  applicants?: Maybe<Scalars['String']['output']>;
  interview?: Maybe<Scalars['String']['output']>;
  onboard?: Maybe<Scalars['String']['output']>;
  rejected?: Maybe<Scalars['String']['output']>;
  training?: Maybe<Scalars['String']['output']>;
};

export type UsersExpertise = {
  __typename?: 'UsersExpertise';
  expertise?: Maybe<ExpertiseWithIdAndName>;
};

export type UsersExpertiseMap = {
  __typename?: 'UsersExpertiseMap';
  expertise?: Maybe<ExpertiseWithIdNameIcon>;
};

export type UsersPhoneNumberResult = {
  __typename?: 'UsersPhoneNumberResult';
  phone: Scalars['String']['output'];
};

export type UsersTrainingPerformanceFilter = {
  course_completion_percentage?: InputMaybe<MinMaxPercentage>;
  course_grades_percentage?: InputMaybe<MinMaxPercentage>;
  last_active_date?: InputMaybe<DateRange>;
  progress_status?: InputMaybe<UserCourseProgressStatus>;
};

export type UsersTrainingPerformanceResult = {
  __typename?: 'UsersTrainingPerformanceResult';
  chapters_completed_count: Scalars['Int']['output'];
  chapters_count: Scalars['Int']['output'];
  course_completion_percentage: Scalars['Float']['output'];
  course_completion_progress: UserCourseProgressStatus;
  course_count: Scalars['Int']['output'];
  course_test_completion_percentage: Scalars['Float']['output'];
  id: Scalars['Int']['output'];
  last_active_date: Scalars['DateTime']['output'];
  name: Scalars['String']['output'];
  total_test_count: Scalars['Int']['output'];
  total_test_given: Scalars['Int']['output'];
  total_time_spent_in_learning: Scalars['Float']['output'];
  user_expertise_map?: Maybe<Array<Maybe<UsersExpertiseMap>>>;
  work_experience: Scalars['Int']['output'];
};

export type UsersWithCount = {
  __typename?: 'UsersWithCount';
  totalCount?: Maybe<Scalars['Int']['output']>;
  users?: Maybe<Array<Maybe<Users>>>;
};

export type UsersWithLocation = {
  __typename?: 'UsersWithLocation';
  applicants: Scalars['String']['output'];
  city: Scalars['String']['output'];
  interview: Scalars['String']['output'];
  moved_to_jobs: Scalars['String']['output'];
  onboard: Scalars['String']['output'];
  training_and_test: Scalars['String']['output'];
};

export type UsersWithLocationAndCount = {
  __typename?: 'UsersWithLocationAndCount';
  data?: Maybe<Array<Maybe<UsersWithLocation>>>;
  total_count?: Maybe<Scalars['Int']['output']>;
};

export type UsersWithLocationFilter = {
  city?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  expertise?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};

export type VerificationFilter = {
  id?: InputMaybe<Scalars['Int']['input']>;
  type_id?: InputMaybe<Scalars['Int']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
  verification_message?: InputMaybe<Scalars['String']['input']>;
  verified?: InputMaybe<Scalars['Boolean']['input']>;
};

export type VerifiedOtpData = {
  __typename?: 'VerifiedOTPData';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type VerifyAdminData = {
  __typename?: 'VerifyAdminData';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type VerifyPhoneResult = {
  __typename?: 'VerifyPhoneResult';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type VideoContent = {
  __typename?: 'VideoContent';
  duration: Scalars['Int']['output'];
  id: Scalars['String']['output'];
  video_url: Scalars['String']['output'];
};

export type WorkExpInput = {
  expertise?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  organization?: InputMaybe<Scalars['String']['input']>;
  year_of_experience?: InputMaybe<Scalars['Int']['input']>;
};

export type WorkExpLocation = {
  __typename?: 'WorkExpLocation';
  city?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  landmark?: Maybe<Scalars['String']['output']>;
  pincode?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  work_address?: Maybe<Scalars['String']['output']>;
};

export type WorkExpResult = {
  __typename?: 'WorkExpResult';
  data?: Maybe<Array<Maybe<WorkExperience>>>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['Boolean']['output']>;
};

export type WorkExperience = {
  __typename?: 'WorkExperience';
  expertise?: Maybe<Array<Maybe<Expertise>>>;
  id?: Maybe<Scalars['Int']['output']>;
  organization?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['Int']['output']>;
  year_of_experience?: Maybe<Scalars['Int']['output']>;
};

export type WorkExperienceInput = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  organization?: InputMaybe<Scalars['String']['input']>;
  self_employed?: InputMaybe<Scalars['Boolean']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  workexp_city?: InputMaybe<Scalars['String']['input']>;
  workexp_location?: InputMaybe<Scalars['String']['input']>;
  workexp_pincode?: InputMaybe<Scalars['String']['input']>;
  workexp_state?: InputMaybe<Scalars['String']['input']>;
};

export type CheckIfUserExistsInputData = {
  email?: InputMaybe<Scalars['String']['input']>;
  exclude_email?: InputMaybe<Scalars['String']['input']>;
  exclude_phone?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type CreateFeedbackTemplateResult = {
  __typename?: 'createFeedbackTemplateResult';
  count?: Maybe<Scalars['Int']['output']>;
};

export type GetAllAssignmentsOfUser = {
  assign_to?: InputMaybe<AssignmentUserType>;
};

export type GetAllBucketsInput = {
  assign_to?: InputMaybe<AssignmentUserType>;
};

export type GetAllQuestionsFilter = {
  assign_to?: InputMaybe<AssignmentUserType>;
  bucket_id?: InputMaybe<Scalars['String']['input']>;
  created_at?: InputMaybe<Scalars['DateTime']['input']>;
  difficulty?: InputMaybe<QuestionDifficulty>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  search_term?: InputMaybe<Scalars['String']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};

export type GetBulkQuestionWithAiInput = {
  difficulty?: InputMaybe<QuestionDifficulty>;
  expertise: Scalars['String']['input'];
  language: Scalars['String']['input'];
  no_of_questions: Scalars['String']['input'];
};

export type GetBulkQuestionWithAiResult = {
  __typename?: 'getBulkQuestionWithAIResult';
  correct_option: Scalars['String']['output'];
  difficulty?: Maybe<QuestionDifficulty>;
  option_1: Scalars['String']['output'];
  option_2: Scalars['String']['output'];
  option_3: Scalars['String']['output'];
  option_4: Scalars['String']['output'];
  question_text: Scalars['String']['output'];
};

export type GetCoreResultsDataFilter = {
  assign_to?: InputMaybe<AssignmentUserType>;
};

export type GetUserAssignmentResponse = {
  __typename?: 'getUserAssignmentResponse';
  assignment_duration_seconds: Scalars['Int']['output'];
  id: Scalars['String']['output'];
  images?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  option: Array<Maybe<KeyLabel>>;
  question_audio: Scalars['String']['output'];
  question_text: Scalars['String']['output'];
  user_answer?: Maybe<Scalars['String']['output']>;
  user_answer_id?: Maybe<Scalars['String']['output']>;
};

export type GetUserAssignmentResultsFrAdminFilter = {
  assign_to?: InputMaybe<AssignmentUserType>;
  training_approval_status?: InputMaybe<TrainingApprovalStatus>;
};

export type Paginations = {
  search?: InputMaybe<Scalars['String']['input']>;
};

export enum Preferred_Language {
  English = 'ENGLISH',
  Hindi = 'HINDI'
}

export type SearchUserFilter = {
  user_type?: InputMaybe<AssignmentUserType>;
};

export type SubmitAnswerForSingleQuestionInput = {
  assignment_id: Scalars['String']['input'];
  question_id: Scalars['String']['input'];
  user_answer_id: Scalars['String']['input'];
};

export type UpdateDocumentInput = {
  verification_message?: InputMaybe<Scalars['String']['input']>;
  verification_status?: InputMaybe<Scalars['String']['input']>;
  verified?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateProviderDetailsAdminInput = {
  category?: InputMaybe<Array<Scalars['Int']['input']>>;
  company_name?: InputMaybe<Scalars['String']['input']>;
  company_type?: InputMaybe<CompanyType>;
  email?: InputMaybe<Scalars['String']['input']>;
  is_banned?: InputMaybe<Scalars['Boolean']['input']>;
  is_delete?: InputMaybe<Scalars['Boolean']['input']>;
  is_rejected?: InputMaybe<Scalars['Boolean']['input']>;
  meta?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  org_id: Scalars['Int']['input'];
  owner_name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  poc?: InputMaybe<Scalars['String']['input']>;
  primary_city?: InputMaybe<Scalars['String']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  service_type?: InputMaybe<Array<Scalars['String']['input']>>;
  source?: InputMaybe<Scalars['String']['input']>;
  user_onboarded?: InputMaybe<Scalars['Boolean']['input']>;
  work_experience?: InputMaybe<Scalars['Int']['input']>;
};

export type UpdateUserWatchProgressInputData = {
  chapter_id?: InputMaybe<Scalars['String']['input']>;
  lesson_id?: InputMaybe<Scalars['String']['input']>;
  user_course_id: Scalars['String']['input'];
};

export type UserChapterProgressTrack = {
  __typename?: 'userChapterProgressTrack';
  id?: Maybe<Scalars['String']['output']>;
  is_completed: Scalars['Boolean']['output'];
  is_current: Scalars['Boolean']['output'];
  is_unlocked: Scalars['Boolean']['output'];
  time_spent?: Maybe<Scalars['Int']['output']>;
};

export type UserLessonProgressTrack = {
  __typename?: 'userLessonProgressTrack';
  id: Scalars['String']['output'];
  is_completed: Scalars['Boolean']['output'];
  is_current: Scalars['Boolean']['output'];
  is_unlocked: Scalars['Boolean']['output'];
  time_spent?: Maybe<Scalars['Int']['output']>;
};

export type Userdata = {
  id?: InputMaybe<Scalars['Int']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type VerifyOtp = {
  otp?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type Video_Content = {
  __typename?: 'video_content';
  duration: Scalars['String']['output'];
  video_url: Scalars['String']['output'];
};

export type TrackAnalyticsMutationVariables = Exact<{
  input: TrackAnalyticsInput;
}>;


export type TrackAnalyticsMutation = { __typename?: 'Mutation', trackAnalytics: { __typename?: 'AnalyticsResponse', success: boolean, message: string, data?: any | null } };

export type GetDailyAnalyticsQueryVariables = Exact<{
  date?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GetDailyAnalyticsQuery = { __typename?: 'Query', getDailyAnalytics?: { __typename?: 'DailyAnalytics', id: string, date: any, total_visitors: number, unique_visitors: number, total_signups: number, total_logins: number, average_session_time: number, bounce_rate: number, platform_breakdown: any, language_breakdown: any, funnel_metrics: any, created_at: any, updated_at: any } | null };

export type GetUserActivityQueryVariables = Exact<{
  userId: Scalars['Int']['input'];
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GetUserActivityQuery = { __typename?: 'Query', getUserActivity: Array<{ __typename?: 'UserActivity', id: string, user_id?: number | null, session_id: string, activity_type: string, platform_type: AppPlatformType, language: string, funnel_stage?: UserFunnelStage | null, start_time: any, end_time?: any | null, time_spent?: number | null, url_path?: string | null, user_agent?: string | null, took_action: boolean, meta?: any | null, created_at: any, updated_at: any }> };

export type GetAllAssignmentsOfUserQueryVariables = Exact<{
  data?: InputMaybe<GetAllAssignmentsOfUser>;
}>;


export type GetAllAssignmentsOfUserQuery = { __typename?: 'Query', getAllAssignmentsOfUser?: Array<{ __typename?: 'AssignmentConfig', config_assignment_type: AssignmentType, creator_id: number, questions_assignment_type?: AssignmentType | null, draft_mode: boolean, is_active: boolean, name: string, id: string, no_of_questions: number, general_instructions_md: string, config_assignment_rules?: Array<{ __typename?: 'AutoAssignmentGenRules', name: string, rules: any, created_at: any, id: string, updated_at: any }> | null, questions_assignment_rules?: Array<{ __typename?: 'AutoAssignmentGenRules', created_at: any, id: string, name: string, rules: any, updated_at: any }> | null, assignment?: Array<{ __typename?: 'AssignmentData', status: AssignmentStatus, finished_at?: any | null } | null> | null } | null> | null };

export type GetSingleAssignmentConfigQueryVariables = Exact<{
  getSingleAssignmentConfigId: Scalars['String']['input'];
}>;


export type GetSingleAssignmentConfigQuery = { __typename?: 'Query', getSingleAssignmentConfig?: { __typename?: 'AssignmentConfigData', config_assignment_type: AssignmentType, draft_mode: boolean, general_instructions_md: string, id: string, is_active: boolean, name: string, no_of_questions: number, questions_assignment_type?: AssignmentType | null, updated_at: any, config_assignment_rules?: { __typename?: 'AutoAssignmentGenRules', name: string, id: string, rules: any } | null, questions_assignment_rules?: { __typename?: 'AutoAssignmentGenRules', id: string, name: string, rules: any } | null } | null };

export type GenerateAssignmentForUserMutationVariables = Exact<{
  assignmentConfigId: Scalars['String']['input'];
}>;


export type GenerateAssignmentForUserMutation = { __typename?: 'Mutation', generateAssignmentForUser: { __typename?: 'ResponseResultWithData', data: string, message: string, result: boolean } };

export type GetUserAssignmentQueryVariables = Exact<{
  assignmentId: Scalars['String']['input'];
  assignmentConfigId: Scalars['String']['input'];
}>;


export type GetUserAssignmentQuery = { __typename?: 'Query', getUserAssignment: Array<{ __typename?: 'getUserAssignmentResponse', id: string, images?: Array<string | null> | null, question_text: string, question_audio: string, assignment_duration_seconds: number, user_answer_id?: string | null, user_answer?: string | null, option: Array<{ __typename?: 'KeyLabel', label: string, value: string, option_audio: string } | null> } | null> };

export type SubmitAnswerForSingleQuestionMutationVariables = Exact<{
  data?: InputMaybe<SubmitAnswerForSingleQuestionInput>;
}>;


export type SubmitAnswerForSingleQuestionMutation = { __typename?: 'Mutation', submitAnswerForSingleQuestion: { __typename?: 'ResponseResult', message: string, result: boolean } };

export type CompleteUserAssignmentMutationVariables = Exact<{
  assignmentId: Scalars['String']['input'];
}>;


export type CompleteUserAssignmentMutation = { __typename?: 'Mutation', completeUserAssignment: { __typename?: 'CompleteUserAssignmentResult', message: string, result: boolean, data: AssignmentResultStatus } };

export type GetAssignmentRemainingTimeQueryVariables = Exact<{
  assignmentId: Scalars['String']['input'];
}>;


export type GetAssignmentRemainingTimeQuery = { __typename?: 'Query', getAssignmentRemainingTime: { __typename?: 'ResponseResultWithData', data: string, message: string, result: boolean } };

export type GetSingleManualAssignmentOfUserQueryVariables = Exact<{
  assignmentConfigId: Scalars['String']['input'];
}>;


export type GetSingleManualAssignmentOfUserQuery = { __typename?: 'Query', getSingleManualAssignmentOfUser?: { __typename?: 'ManualAssignment', assignment?: { __typename?: 'AssignmentConfig', assignment?: Array<{ __typename?: 'AssignmentData', status: AssignmentStatus } | null> | null } | null } | null };

export type UserBannersQueryVariables = Exact<{ [key: string]: never; }>;


export type UserBannersQuery = { __typename?: 'Query', banners: Array<{ __typename?: 'Banner', expiry_time?: any | null, id?: string | null, banner_config?: { __typename?: 'BannerConfigFrBanner', description: string, hero_image: string, title: string, id: string, cta_link?: string | null, cta_required?: boolean | null, is_published?: boolean | null, is_active?: boolean | null, image?: string | null, cta_text?: string | null } | null }> };

export type UpdateUserChecklistDataMutationVariables = Exact<{
  data: Array<InputMaybe<UpdateUserChecklistDataInput>> | InputMaybe<UpdateUserChecklistDataInput>;
}>;


export type UpdateUserChecklistDataMutation = { __typename?: 'Mutation', updateUserChecklistData: { __typename?: 'SingleUserChecklistDataResponse', result: boolean, message: string } };

export type GetAllChecklistsQueryVariables = Exact<{
  userType?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetAllChecklistsQuery = { __typename?: 'Query', getAllChecklists: { __typename?: 'ChecklistResponse', result: boolean, message: string, data?: Array<{ __typename?: 'Checklist', id?: string | null, name?: string | null, is_active?: boolean | null, created_at?: any | null, updated_at?: any | null } | null> | null } };

export type GetUserChecklistDataQueryVariables = Exact<{
  userId: Scalars['Int']['input'];
  userType?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetUserChecklistDataQuery = { __typename?: 'Query', getUserChecklistData: { __typename?: 'UserChecklistDataResponse', result: boolean, message: string, data?: Array<{ __typename?: 'UserChecklistData', id: string, name?: string | null, user_checklist_data?: Array<{ __typename?: 'UserAdminChecklistData', id?: string | null, hiring_completed?: boolean | null } | null> | null } | null> | null } };

export type GetFeedbackFormStatusQueryVariables = Exact<{
  userId: Scalars['Int']['input'];
}>;


export type GetFeedbackFormStatusQuery = { __typename?: 'Query', getFeedbackFormStatus: { __typename?: 'FeedbackFormStatusResponse', result: boolean, message: string, submitted: boolean, onboarded: boolean, user_id?: number | null, language?: any | null } };

export type DocumentsQueryVariables = Exact<{ [key: string]: never; }>;


export type DocumentsQuery = { __typename?: 'Query', documents?: Array<{ __typename?: 'Document', url?: string | null, id?: number | null, verification_status?: string | null, doc_number?: string | null, verification_message?: string | null, verified?: boolean | null, type?: { __typename?: 'DocumentType', id?: number | null, required?: boolean | null, name?: string | null } | null } | null> | null };

export type DocumentTypesQueryVariables = Exact<{
  userType?: InputMaybe<UserTypeDocument>;
  isTeamMember?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type DocumentTypesQuery = { __typename?: 'Query', documentTypes?: Array<{ __typename?: 'DocumentType', name?: string | null, id?: number | null, required?: boolean | null, status?: string | null, instructions?: any | null, is_doc_number_required?: boolean | null, document_number_config?: { __typename?: 'DocumentNumberConfig', id?: string | null, validation_type?: DocNumberValidationType | null, max_length?: number | null, min_length?: number | null, document_type_id?: number | null } | null }> | null };

export type AddDocumentMutationVariables = Exact<{
  data?: InputMaybe<DocumentData>;
}>;


export type AddDocumentMutation = { __typename?: 'Mutation', addDocument?: { __typename?: 'Document', url?: string | null, id?: number | null } | null };

export type DeleteDocumentMutationVariables = Exact<{
  deleteDocumentId: Scalars['Int']['input'];
}>;


export type DeleteDocumentMutation = { __typename?: 'Mutation', deleteDocument?: { __typename?: 'DeleteDocumentType', message?: string | null, result?: boolean | null } | null };

export type ExpertiseQueryVariables = Exact<{ [key: string]: never; }>;


export type ExpertiseQuery = { __typename?: 'Query', expertise?: { __typename?: 'ExpertiseResult', message?: string | null, result?: boolean | null, data?: Array<{ __typename?: 'Expertise', id?: number | null, name?: string | null, icon?: string | null } | null> | null } | null };

export type AssignExpertiseMutationVariables = Exact<{
  expertise?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
}>;


export type AssignExpertiseMutation = { __typename?: 'Mutation', assignExpertise?: { __typename?: 'GeneralResult', message?: string | null, result?: boolean | null } | null };

export type DeleteExpertiseMutationVariables = Exact<{
  expertiseId?: InputMaybe<Scalars['Int']['input']>;
}>;


export type DeleteExpertiseMutation = { __typename?: 'Mutation', deleteExpertise?: { __typename?: 'GeneralResult', message?: string | null, result?: boolean | null } | null };

export type GetInterviewsFrUserQueryVariables = Exact<{ [key: string]: never; }>;


export type GetInterviewsFrUserQuery = { __typename?: 'Query', getInterviewsFrUser?: Array<{ __typename?: 'UserInterviewResult', id?: number | null, title?: string | null, is_active?: boolean | null, start_time?: any | null, end_time?: any | null, interview_type?: string | null, google_meet_link?: string | null, note?: string | null, attempts?: number | null, status?: string | null, location?: string | null, interviewer?: { __typename?: 'Interviewer', id?: number | null, email?: string | null, name?: string | null, location?: { __typename?: 'Location', work_address?: string | null } | null } | null, feedback?: { __typename?: 'FeedbackResultsFrUser', feedback_form_id?: string | null, id?: number | null, interviewer_feedback_state?: FeedBackState | null, interview_state?: InterviewState | null, interviewee_feedback_result?: any | null, interviewee_feedback_state?: FeedBackState | null, template?: { __typename?: 'Template', title: string, interviewee_feedback_enabled?: boolean | null } | null } | null }> | null };

export type CreateLeadMutationVariables = Exact<{
  data?: InputMaybe<CreateLeadInput>;
}>;


export type CreateLeadMutation = { __typename?: 'Mutation', createLead?: { __typename?: 'LeadGenerationResult', message?: string | null, result?: boolean | null } | null };

export type CreateContactsMutationVariables = Exact<{
  contacts?: InputMaybe<Scalars['String']['input']>;
}>;


export type CreateContactsMutation = { __typename?: 'Mutation', createContacts: { __typename?: 'ResponseResult', result: boolean, message: string } };

export type GetContactsOfTheUserFrAdminQueryVariables = Exact<{
  filter?: InputMaybe<UserContactsFilter>;
  userId?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetContactsOfTheUserFrAdminQuery = { __typename?: 'Query', getContactsOfTheUserFrAdmin?: { __typename?: 'UserContactsWithCount', total_count?: number | null, total_moved_contacts_count?: number | null, data?: Array<{ __typename?: 'UserContacts', contact_id?: string | null, created_at?: any | null, is_active?: boolean | null, updated_at?: any | null, user_id?: string | null, contact?: { __typename?: 'Contact', id?: string | null, name: string, phone: string, meta?: any | null, timeline?: any | null, is_active?: boolean | null, is_deleted?: boolean | null, moved_to_users?: boolean | null, created_at?: any | null, updated_at?: any | null, phone_contact_id?: string | null } | null } | null> | null } | null };

export type LocationSearchQueryVariables = Exact<{
  term?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  filter?: InputMaybe<LocationFilter>;
}>;


export type LocationSearchQuery = { __typename?: 'Query', textSearch?: Array<{ __typename?: 'Location', pincode?: string | null, state?: string | null, id?: number | null, city?: string | null } | null> | null };

export type GetUserMascotAlertQueryVariables = Exact<{
  type: MascotSectionType;
}>;


export type GetUserMascotAlertQuery = { __typename?: 'Query', getUserMascotAlert?: { __typename?: 'UserMascotAlertData', id: string, type: MascotSectionType, title?: string | null, message: string, is_read: boolean, cta_required: boolean, cta_link?: string | null, priority?: MascotNotificationPriority | null, meta?: any | null, cta_text?: string | null } | null };

export type UpdateUserMascotAlertMutationVariables = Exact<{
  updateUserMascotAlertId: Scalars['String']['input'];
}>;


export type UpdateUserMascotAlertMutation = { __typename?: 'Mutation', updateUserMascotAlert: { __typename?: 'ResponseResult', message: string, result: boolean } };

export type MascotNotificationSubscriptionVariables = Exact<{
  userId: Scalars['Int']['input'];
}>;


export type MascotNotificationSubscription = { __typename?: 'Subscription', mascotNotifications?: { __typename?: 'UserMascotAlertData', id: string, type: MascotSectionType, title?: string | null, message: string, is_read: boolean, cta_required: boolean, cta_link?: string | null, priority?: MascotNotificationPriority | null, meta?: any | null } | null };

export type TransferredToTmsNotificationSubscriptionVariables = Exact<{
  userId: Scalars['Int']['input'];
}>;


export type TransferredToTmsNotificationSubscription = { __typename?: 'Subscription', transferredToTMS: { __typename?: 'Success', result: boolean } };

export type BellNotificationSubscriptionVariables = Exact<{
  userId: Scalars['Int']['input'];
}>;


export type BellNotificationSubscription = { __typename?: 'Subscription', bellNotification: number };

export type GetUserNotificationsQueryVariables = Exact<{
  filter?: InputMaybe<NotificationFilterInput>;
}>;


export type GetUserNotificationsQuery = { __typename?: 'Query', getUserNotifications: Array<{ __typename?: 'Notification', id: string, title: string, message: string, is_read: boolean, redirect_to: string, priority?: NotificationPriority | null, meta: any, created_at: any }> };

export type UpdateNotificationMutationVariables = Exact<{
  notificationId: Scalars['String']['input'];
  data?: InputMaybe<NotificationUpdateInput>;
}>;


export type UpdateNotificationMutation = { __typename?: 'Mutation', updateNotification: { __typename?: 'ResponseResult', message: string, result: boolean } };

export type GetBellNotificationCountQueryVariables = Exact<{ [key: string]: never; }>;


export type GetBellNotificationCountQuery = { __typename?: 'Query', getBellNotificationCount: number };

export type UpdatePolicyAcceptanceMutationVariables = Exact<{
  data: PolicyAcceptInput;
}>;


export type UpdatePolicyAcceptanceMutation = { __typename?: 'Mutation', updatePolicyAcceptance: { __typename?: 'PolicyUpdateResponse', result: boolean, message: string, data?: { __typename?: 'Policy', id: string, policy_name: string, policy_name_hindi?: string | null, url?: string | null, is_active: boolean } | null } };

export type GetPoliciesWithUserStatusQueryVariables = Exact<{
  userId: Scalars['Int']['input'];
}>;


export type GetPoliciesWithUserStatusQuery = { __typename?: 'Query', getPoliciesWithUserStatus: { __typename?: 'PolicyResponse', result: boolean, message: string, data?: Array<{ __typename?: 'Policy', id: string, policy_name: string, policy_name_hindi?: string | null, url?: string | null, is_active: boolean, created_at: any, updated_at: any, user_policy_tracking?: Array<{ __typename?: 'User_Policy_Tracking', created_at?: any | null, accepted?: boolean | null } | null> | null } | null> | null } };

export type UpdateProviderMutationVariables = Exact<{
  data?: InputMaybe<UpdateProviderInput>;
}>;


export type UpdateProviderMutation = { __typename?: 'Mutation', updateProvider: { __typename?: 'ResponseResult', result: boolean, message: string } };

export type CreateProviderOrgMutationVariables = Exact<{
  data: CreateProviderOrgInput;
}>;


export type CreateProviderOrgMutation = { __typename?: 'Mutation', createProviderOrg: { __typename?: 'ResponseResult', result: boolean, message: string } };

export type GetServiceTypesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetServiceTypesQuery = { __typename?: 'Query', getServiceTypes: Array<{ __typename?: 'ServiceType', id: string, name: string, is_active?: boolean | null } | null> };

export type GetProviderQueryVariables = Exact<{ [key: string]: never; }>;


export type GetProviderQuery = { __typename?: 'Query', getProvider?: { __typename?: 'ProviderDetails', company_type?: string | null, id?: number | null, meta?: any | null, name?: string | null, org_expertise_map?: Array<{ __typename?: 'OrgExpertiseMap', team_member_count?: number | null, expertise?: { __typename?: 'Expertise', id?: number | null, name?: string | null } | null } | null> | null, org_owner?: { __typename?: 'OrgOwner', email?: string | null, name: string, id: number, onboarding_stage?: OnboardingStage | null, phone: string, photoUrl?: string | null, meta?: any | null, user_professional_data?: Array<{ __typename?: 'UserProfessionalExperienceResult', year_of_experience?: number | null } | null> | null } | null, organization_service_type_map?: Array<{ __typename?: 'OrganizationServiceTypeMap', service_type?: { __typename?: 'ServiceType', id: string, name: string, is_active?: boolean | null } | null } | null> | null, user?: { __typename?: 'User', email?: string | null, name?: string | null, id?: number | null, onboarding_stage?: OnboardingStage | null, phone?: string | null, photoUrl?: string | null, meta?: any | null, user_professional_data?: Array<{ __typename?: 'UserProfessionalExperienceResult', year_of_experience?: number | null } | null> | null, expertise?: Array<{ __typename?: 'Expertise', id?: number | null, name?: string | null, organization_id?: number | null } | null> | null } | null } | null };

export type GetServiceProviderOnboardingAnalyticsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetServiceProviderOnboardingAnalyticsQuery = { __typename?: 'Query', getServiceProviderOnboardingAnalytics: { __typename?: 'ServiceProviderOnboardingAnalytics', profileCompletion?: { __typename?: 'ProfileCompletion', percentage?: number | null, status?: string | null } | null, documentUpload?: { __typename?: 'DocumentUploadCount', status?: string | null, total?: number | null, uploaded_count?: number | null } | null, testCompletion?: { __typename?: 'TestCompletion', total?: number | null, completed_count?: number | null, status?: string | null } | null, organization?: { __typename?: 'Organization', id?: number | null, name?: string | null, org_owner_id?: number | null } | null } };

export type UpdateTeamMemberMutationVariables = Exact<{
  data?: InputMaybe<UpdateTeamMemberInput>;
}>;


export type UpdateTeamMemberMutation = { __typename?: 'Mutation', updateTeamMember: { __typename?: 'ResponseResult', result: boolean, message: string } };

export type SendRefferalInviteMutationVariables = Exact<{
  data: SendRefferalInviteInput;
}>;


export type SendRefferalInviteMutation = { __typename?: 'Mutation', sendRefferalInvite: { __typename?: 'UnregisteredReferral', id: string, name: string, phone_number: string, meta?: any | null, created_at: any, updated_at: any, referrer?: { __typename?: 'ReferrerData', id?: number | null, name?: string | null } | null } };

export type GetMyUnregisteredReferralsQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetMyUnregisteredReferralsQuery = { __typename?: 'Query', getMyUnregisteredReferrals: { __typename?: 'UnregisteredReferralsResponse', total_count: number, data: Array<{ __typename?: 'UnregisteredReferral', id: string, name: string, phone_number: string, meta?: any | null, created_at: any, updated_at: any }> } };

export type GenerateReferralCodeFrUserQueryVariables = Exact<{ [key: string]: never; }>;


export type GenerateReferralCodeFrUserQuery = { __typename?: 'Query', generateReferralCodeFrUser?: string | null };

export type GetMyReferralsQueryVariables = Exact<{
  search?: InputMaybe<Scalars['String']['input']>;
  pagination?: InputMaybe<Pagination>;
}>;


export type GetMyReferralsQuery = { __typename?: 'Query', getMyReferrals: { __typename?: 'AllUserReferralsResponse', total_count: number, data: Array<{ __typename?: 'ReferredUsersData', created_at?: any | null, status?: string | null, referrer_code?: string | null, referred_name?: string | null, referred_id?: number | null, referred_phone?: string | null, url?: string | null } | null> } };

export type CheckUnregisteredReferralQueryVariables = Exact<{
  phone_number: Scalars['String']['input'];
}>;


export type CheckUnregisteredReferralQuery = { __typename?: 'Query', checkUnregisteredReferral: { __typename?: 'CheckUnregisteredReferralResponse', is_referred: boolean, referrers: Array<{ __typename?: 'ReferrerInfo', id?: number | null, name?: string | null, referral_code?: string | null }> } };

export type CreateTeamMemberInvitationMutationVariables = Exact<{
  data: TeamMemberInvitationInput;
}>;


export type CreateTeamMemberInvitationMutation = { __typename?: 'Mutation', createTeamMemberInvitation: { __typename?: 'ResponseResult', message: string, result: boolean } };

export type GetTeamMemberInvitationsQueryVariables = Exact<{
  filter?: InputMaybe<TeamMemberInvitationFilter>;
}>;


export type GetTeamMemberInvitationsQuery = { __typename?: 'Query', getTeamMemberInvitations?: Array<{ __typename?: 'TeamMemberInvitation', status: TeamMemberInvitationStatus, contact: { __typename?: 'Contact', id?: string | null, name: string, phone: string, meta?: any | null, image_url?: string | null } }> | null };

export type GetTeamMemberContactsQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<TeamMemberInvitationFilter>;
}>;


export type GetTeamMemberContactsQuery = { __typename?: 'Query', getContactsWithInvitationStatus?: Array<{ __typename?: 'Contact', id?: string | null, name: string, phone: string, image_url?: string | null, invitations?: Array<{ __typename?: 'TeamMemberInvitation', status: TeamMemberInvitationStatus, contact: { __typename?: 'Contact', name: string, phone: string } }> | null }> | null };

export type GetUserCoursesDataQueryVariables = Exact<{
  filter?: InputMaybe<GetUserCoursesDataFilter>;
}>;


export type GetUserCoursesDataQuery = { __typename?: 'Query', getUserCoursesData: Array<{ __typename?: 'CourseUserMap', id: string, progress_status: UserCourseProgressStatus, completed_percentage: string, course_completed: boolean, training_completed: boolean, course: { __typename?: 'Course', id?: string | null, name: string, description: string, duration: number, objective: string, image: string, chapter: Array<{ __typename?: 'Chapter', id: string, created_at?: any | null, title: string, order?: number | null, duration?: number | null, lesson: Array<{ __typename?: 'LessonInputFrCourseChapter', title: string, id?: string | null, objective: string, content_id: string, duration: number }> }>, _count?: { __typename?: 'CourseUserMapCount', chapter?: number | null } | null, course_assessment_map?: Array<{ __typename?: 'CourseAssignmentMap', assignment_config?: { __typename?: 'AssignmentConfigWithName', name: string, id?: string | null } | null }> | null }, course_user_assignment_map?: Array<{ __typename?: 'CourseUserAssignmentMap', assignment?: { __typename?: 'UserCourseAssignmentData', status: AssignmentStatus } | null }> | null }> };

export type GetUserCourseDetailsQueryVariables = Exact<{
  courseUserId: Scalars['String']['input'];
}>;


export type GetUserCourseDetailsQuery = { __typename?: 'Query', getUserCourseDetails: { __typename?: 'UserCourseDetails', course_completed_count: number, course_completed: boolean, training_completed: boolean, course: { __typename?: 'Course', id?: string | null, name: string, description: string, duration: number, objective: string, image: string, type: CourseType, method: CourseMethod, meta?: any | null, published: boolean, published_on?: any | null, internal_course_id?: string | null, status: CourseStatus, rating?: number | null, business_vertical: BusinessVertical, is_active?: boolean | null, chapter: Array<{ __typename?: 'Chapter', id: string, created_at?: any | null, title: string, order?: number | null, duration?: number | null, lesson: Array<{ __typename?: 'LessonInputFrCourseChapter', title: string, id?: string | null, objective: string, content_id: string, duration: number, content?: { __typename?: 'ContentFrLesson', title: string, video_content: { __typename?: 'VideoContent', duration: number, id: string, video_url: string } } | null, user_lesson_progress_track: Array<{ __typename?: 'userLessonProgressTrack', id: string, is_completed: boolean, is_unlocked: boolean, is_current: boolean, time_spent?: number | null }> }>, user_chapter_progress_track: Array<{ __typename?: 'userChapterProgressTrack', id?: string | null, is_completed: boolean, is_current: boolean, is_unlocked: boolean, time_spent?: number | null }> }>, _count?: { __typename?: 'CourseUserMapCount', chapter?: number | null, user_chapter_progress_track?: number | null, course_assessment_map?: number | null } | null, course_assessment_map?: Array<{ __typename?: 'CourseAssignmentMap', assignment_config?: { __typename?: 'AssignmentConfigWithName', name: string, id?: string | null } | null }> | null, course_designation_map?: Array<{ __typename?: 'CourseDesignation', designation: { __typename?: 'Designation', id?: string | null, name?: string | null } }> | null }, course_user_assignment_map?: Array<{ __typename?: 'CourseUserAssignmentMap', assignment?: { __typename?: 'UserCourseAssignmentData', status: AssignmentStatus } | null, assignment_config?: { __typename?: 'AssignmentConfigWithName', id?: string | null, name: string } | null }> | null, _count?: { __typename?: 'UserCourseDetailsCountData', course_user_assignment_map: number } | null } };

export type UpdateVideoWatchTimeMutationVariables = Exact<{
  contentId: Scalars['String']['input'];
  videoProgressInSeconds: Scalars['Int']['input'];
  isCompleted?: InputMaybe<Scalars['Boolean']['input']>;
  userCourseId: Scalars['String']['input'];
}>;


export type UpdateVideoWatchTimeMutation = { __typename?: 'Mutation', updateVideoWatchTime: { __typename?: 'UserVideoProgressResultData', is_completed: boolean, progress: number } };

export type GetTrainingVideoWatchTimeQueryVariables = Exact<{
  contentId: Scalars['String']['input'];
}>;


export type GetTrainingVideoWatchTimeQuery = { __typename?: 'Query', getVideoWatchTime: { __typename?: 'UserVideoProgressResultData', is_completed: boolean, progress: number } };

export type UpdateUserWatchProgressMutationVariables = Exact<{
  data?: InputMaybe<UpdateUserWatchProgressInputData>;
}>;


export type UpdateUserWatchProgressMutation = { __typename?: 'Mutation', updateUserWatchProgress: { __typename?: 'ResponseResult', message: string, result: boolean } };

export type UserTrainingStatusSubscriptionVariables = Exact<{
  userId: Scalars['Int']['input'];
}>;


export type UserTrainingStatusSubscription = { __typename?: 'Subscription', userTrainingStatus: { __typename?: 'UserTrainingStatusData', userTrainingStatus: UserCourseProgressStatus } };

export type UserDetailsQueryVariables = Exact<{ [key: string]: never; }>;


export type UserDetailsQuery = { __typename?: 'Query', userDetail?: { __typename?: 'UserData', data?: { __typename?: 'User', onboarded?: boolean | null, id?: number | null, location_id?: number | null, doc_verification_state?: DocVerificationStatus | null, meta?: any | null, name?: string | null, user_state?: string | null, remark?: string | null, gender?: Gender | null, phone?: string | null, photoUrl?: string | null, onboarding_stage?: OnboardingStage | null, email?: string | null, phoneVerified?: boolean | null, transfer_to_tms_done: boolean, user_training_approval_status?: TrainingApprovalStatus | null, expertise?: Array<{ __typename?: 'Expertise', name?: string | null, icon?: string | null, id?: number | null } | null> | null, organization?: { __typename?: 'Organization', name?: string | null, org_owner_id?: number | null } | null, location?: { __typename?: 'Location', id?: number | null, state?: string | null, pincode?: string | null, landmark?: string | null, work_address?: string | null, city?: string | null } | null } | null } | null };

export type UpdateUserMutationVariables = Exact<{
  data?: InputMaybe<UpdateUserInput>;
}>;


export type UpdateUserMutation = { __typename?: 'Mutation', UpdateUser?: { __typename?: 'UpdateUserData', message?: string | null, result?: boolean | null, data?: { __typename?: 'User', name?: string | null } | null } | null };

export type SendOtpMutationVariables = Exact<{
  phone?: InputMaybe<Scalars['String']['input']>;
}>;


export type SendOtpMutation = { __typename?: 'Mutation', sendOtp?: { __typename?: 'SendOtpResult', message?: string | null, result?: boolean | null, type?: SendOtpResultType | null } | null };

export type RegisterUserMutationVariables = Exact<{
  data: RegisterInput;
}>;


export type RegisterUserMutation = { __typename?: 'Mutation', registerUser: { __typename?: 'RegisterData', result: boolean, message: string, token: string, user_type?: string | null, registration_type?: SendOtpType | null, meta?: any | null, is_invited?: boolean | null } };

export type VerifyPhoneNumberMutationVariables = Exact<{
  otp?: InputMaybe<Scalars['String']['input']>;
}>;


export type VerifyPhoneNumberMutation = { __typename?: 'Mutation', verifyPhoneNumber?: { __typename?: 'VerifyPhoneResult', result?: boolean | null, message?: string | null } | null };

export type CreateServiceProviderAccountMutationVariables = Exact<{
  data?: InputMaybe<CreateOrganizationData>;
}>;


export type CreateServiceProviderAccountMutation = { __typename?: 'Mutation', signUpOrganization?: { __typename?: 'AuthResult', result?: boolean | null, token?: string | null, message?: string | null } | null };

export type SignInOrganizationMutationVariables = Exact<{
  phoneNumber: Scalars['String']['input'];
  password: Scalars['String']['input'];
  otp: Scalars['String']['input'];
}>;


export type SignInOrganizationMutation = { __typename?: 'Mutation', signInOrganization?: { __typename?: 'AuthResult', message?: string | null, result?: boolean | null, token?: string | null } | null };

export type SignUpOrganizationMutationVariables = Exact<{
  data?: InputMaybe<UpdateOrganizationInput>;
  updateUserData2?: InputMaybe<UpdateUserInput>;
}>;


export type SignUpOrganizationMutation = { __typename?: 'Mutation', updateOrganization?: { __typename?: 'UpdateOrganizationResult', message?: string | null, result?: boolean | null } | null, UpdateUser?: { __typename?: 'UpdateUserData', data?: { __typename?: 'User', location_id?: number | null } | null } | null };

export type GenerateReferralCodeQueryVariables = Exact<{ [key: string]: never; }>;


export type GenerateReferralCodeQuery = { __typename?: 'Query', generateReferralCodeFrUser?: string | null };

export type UpdateUserMetaMutationVariables = Exact<{
  data?: InputMaybe<UserMetaInput>;
}>;


export type UpdateUserMetaMutation = { __typename?: 'Mutation', updateUserMeta?: boolean | null };

export type GetUserReferralDataQueryVariables = Exact<{ [key: string]: never; }>;


export type GetUserReferralDataQuery = { __typename?: 'Query', getUserReferralData?: { __typename?: 'ReferredUsersData', available_points?: number | null, referral_code?: string | null, updated_at?: any | null, created_at?: any | null, referred_to?: { __typename?: 'ReferrerData', name?: string | null, id?: number | null, user_referred_to?: { __typename?: 'ReferralCode', referral_code?: string | null } | null } | null, referred_by?: { __typename?: 'ReferrerData', name?: string | null, id?: number | null, user_referred_to?: { __typename?: 'ReferralCode', referral_code?: string | null } | null } | null } | null };

export type GetAllFeedbacksQueryVariables = Exact<{
  filter?: InputMaybe<FeedbackFormFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
  pagination?: InputMaybe<Pagination>;
}>;


export type GetAllFeedbacksQuery = { __typename?: 'Query', getAllFeedbacks?: Array<{ __typename?: 'InterviewFeedback', interview_state?: string | null, feedback_state?: FeedBackState | null, user?: { __typename?: 'User', name?: string | null, phone?: string | null, id?: number | null } | null, feedback_result?: Array<{ __typename?: 'FeedbackResult', id?: number | null, score?: number | null, comments?: string | null } | null> | null } | null> | null };

export type UserProfileCompetionPercentageQueryVariables = Exact<{ [key: string]: never; }>;


export type UserProfileCompetionPercentageQuery = { __typename?: 'Query', getUserProfileCompletionPercentage?: { __typename?: 'UserProfileCompletionResult', data?: number | null, message?: string | null, result?: boolean | null } | null };

export type ReferralCodeValidateMutationVariables = Exact<{
  data?: InputMaybe<ReferralInput>;
}>;


export type ReferralCodeValidateMutation = { __typename?: 'Mutation', referralCodeValidate?: { __typename?: 'ReferralCodeValidationResult', validated?: boolean | null, message?: string | null } | null };

export type UpdateUserPreferredLanguageMutationVariables = Exact<{
  data?: InputMaybe<UserPreferredLanguageInput>;
}>;


export type UpdateUserPreferredLanguageMutation = { __typename?: 'Mutation', updateUserPreferredLanguage?: boolean | null };

export type GetLatestReferralConfigurationQueryVariables = Exact<{
  filter: OnboardingStage;
}>;


export type GetLatestReferralConfigurationQuery = { __typename?: 'Query', getLatestReferralConfiguration?: { __typename?: 'ReferralConfig', existing_user_points?: number | null, id?: number | null, new_user_points?: number | null, referral_enabled?: boolean | null, user_point_redeem_stage?: OnboardingStage | null } | null };

export type GetAllReferredUsersOfUserQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAllReferredUsersOfUserQuery = { __typename?: 'Query', getAllReferredUsersOfUser: Array<{ __typename?: 'ReferredUser', id?: number | null, referral_code?: string | null, available_points?: number | null, user_id?: number | null, user_referral_ledger_id?: number | null, referred_by_id?: number | null, created_at?: any | null, referred_to?: { __typename?: 'ReferredTo', id?: number | null, name?: string | null, organization_id?: number | null, isActive?: boolean | null, created_at?: any | null, updated_at?: any | null, onboarding_stage?: OnboardingStage | null, source?: string | null, interview?: Array<{ __typename?: 'InterviewData', status?: string | null } | null> | null } | null } | null> };

export type GetReferralConfigurationForUserQueryVariables = Exact<{ [key: string]: never; }>;


export type GetReferralConfigurationForUserQuery = { __typename?: 'Query', getReferralConfigurationForUser?: { __typename?: 'ReferralConfig', id?: number | null, existing_user_points?: number | null, new_user_points?: number | null, user_point_redeem_stage?: OnboardingStage | null, referral_enabled?: boolean | null, terms_and_condition?: any | null } | null };

export type UserDesignationQueryVariables = Exact<{ [key: string]: never; }>;


export type UserDesignationQuery = { __typename?: 'Query', userDesignation?: { __typename?: 'User', designation?: { __typename?: 'Designation', id?: string | null, is_active?: boolean | null, level?: number | null, name?: string | null, updated_at?: any | null, is_deleted?: boolean | null, designation_expertise_map?: Array<{ __typename?: 'DesignationExpertise', expertise?: { __typename?: 'Expertise', id?: number | null, name?: string | null } | null } | null> | null } | null } | null };

export type UpdateUserTypeMutationVariables = Exact<{
  data: UpdateUserTypeInput;
}>;


export type UpdateUserTypeMutation = { __typename?: 'Mutation', updateUserType: { __typename?: 'UpdateUserTypeResult', message: string, result: boolean } };

export type CreateWorkExpMutationVariables = Exact<{
  data?: InputMaybe<Array<InputMaybe<WorkExpInput>> | InputMaybe<WorkExpInput>>;
}>;


export type CreateWorkExpMutation = { __typename?: 'Mutation', createWorkExp?: { __typename?: 'WorkExpResult', message?: string | null, result?: boolean | null } | null };

export type CreateProfessionalWorkExperienceMutationVariables = Exact<{
  data?: InputMaybe<UserProfessionalExperienceArgs>;
}>;


export type CreateProfessionalWorkExperienceMutation = { __typename?: 'Mutation', createProfessionalExperience?: boolean | null };

export type GetUserProfessionalExperienceQueryVariables = Exact<{ [key: string]: never; }>;


export type GetUserProfessionalExperienceQuery = { __typename?: 'Query', getUserProfessionalExperience?: { __typename?: 'UserProfessionalExperienceResult', highest_education?: string | null, experienced?: boolean | null, current_work_location_id?: number | null, created_at?: any | null, updated_at?: any | null, year_of_experience?: number | null } | null };


export const TrackAnalyticsDocument = gql`
    mutation TrackAnalytics($input: TrackAnalyticsInput!) {
  trackAnalytics(input: $input) {
    success
    message
    data
  }
}
    `;
export type TrackAnalyticsMutationFn = Apollo.MutationFunction<TrackAnalyticsMutation, TrackAnalyticsMutationVariables>;

/**
 * __useTrackAnalyticsMutation__
 *
 * To run a mutation, you first call `useTrackAnalyticsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useTrackAnalyticsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [trackAnalyticsMutation, { data, loading, error }] = useTrackAnalyticsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useTrackAnalyticsMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<TrackAnalyticsMutation, TrackAnalyticsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<TrackAnalyticsMutation, TrackAnalyticsMutationVariables>(TrackAnalyticsDocument, options);
      }
export type TrackAnalyticsMutationHookResult = ReturnType<typeof useTrackAnalyticsMutation>;
export type TrackAnalyticsMutationResult = Apollo.MutationResult<TrackAnalyticsMutation>;
export type TrackAnalyticsMutationOptions = Apollo.BaseMutationOptions<TrackAnalyticsMutation, TrackAnalyticsMutationVariables>;
export const GetDailyAnalyticsDocument = gql`
    query GetDailyAnalytics($date: DateTime) {
  getDailyAnalytics(date: $date) {
    id
    date
    total_visitors
    unique_visitors
    total_signups
    total_logins
    average_session_time
    bounce_rate
    platform_breakdown
    language_breakdown
    funnel_metrics
    created_at
    updated_at
  }
}
    `;

/**
 * __useGetDailyAnalyticsQuery__
 *
 * To run a query within a React component, call `useGetDailyAnalyticsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetDailyAnalyticsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetDailyAnalyticsQuery({
 *   variables: {
 *      date: // value for 'date'
 *   },
 * });
 */
export function useGetDailyAnalyticsQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetDailyAnalyticsQuery, GetDailyAnalyticsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetDailyAnalyticsQuery, GetDailyAnalyticsQueryVariables>(GetDailyAnalyticsDocument, options);
      }
export function useGetDailyAnalyticsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetDailyAnalyticsQuery, GetDailyAnalyticsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetDailyAnalyticsQuery, GetDailyAnalyticsQueryVariables>(GetDailyAnalyticsDocument, options);
        }
export function useGetDailyAnalyticsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetDailyAnalyticsQuery, GetDailyAnalyticsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetDailyAnalyticsQuery, GetDailyAnalyticsQueryVariables>(GetDailyAnalyticsDocument, options);
        }
export type GetDailyAnalyticsQueryHookResult = ReturnType<typeof useGetDailyAnalyticsQuery>;
export type GetDailyAnalyticsLazyQueryHookResult = ReturnType<typeof useGetDailyAnalyticsLazyQuery>;
export type GetDailyAnalyticsSuspenseQueryHookResult = ReturnType<typeof useGetDailyAnalyticsSuspenseQuery>;
export type GetDailyAnalyticsQueryResult = Apollo.QueryResult<GetDailyAnalyticsQuery, GetDailyAnalyticsQueryVariables>;
export const GetUserActivityDocument = gql`
    query GetUserActivity($userId: Int!, $startDate: DateTime, $endDate: DateTime) {
  getUserActivity(userId: $userId, startDate: $startDate, endDate: $endDate) {
    id
    user_id
    session_id
    activity_type
    platform_type
    language
    funnel_stage
    start_time
    end_time
    time_spent
    url_path
    user_agent
    took_action
    meta
    created_at
    updated_at
  }
}
    `;

/**
 * __useGetUserActivityQuery__
 *
 * To run a query within a React component, call `useGetUserActivityQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserActivityQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserActivityQuery({
 *   variables: {
 *      userId: // value for 'userId'
 *      startDate: // value for 'startDate'
 *      endDate: // value for 'endDate'
 *   },
 * });
 */
export function useGetUserActivityQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetUserActivityQuery, GetUserActivityQueryVariables> & ({ variables: GetUserActivityQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUserActivityQuery, GetUserActivityQueryVariables>(GetUserActivityDocument, options);
      }
export function useGetUserActivityLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUserActivityQuery, GetUserActivityQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUserActivityQuery, GetUserActivityQueryVariables>(GetUserActivityDocument, options);
        }
export function useGetUserActivitySuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUserActivityQuery, GetUserActivityQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUserActivityQuery, GetUserActivityQueryVariables>(GetUserActivityDocument, options);
        }
export type GetUserActivityQueryHookResult = ReturnType<typeof useGetUserActivityQuery>;
export type GetUserActivityLazyQueryHookResult = ReturnType<typeof useGetUserActivityLazyQuery>;
export type GetUserActivitySuspenseQueryHookResult = ReturnType<typeof useGetUserActivitySuspenseQuery>;
export type GetUserActivityQueryResult = Apollo.QueryResult<GetUserActivityQuery, GetUserActivityQueryVariables>;
export const GetAllAssignmentsOfUserDocument = gql`
    query GetAllAssignmentsOfUser($data: getAllAssignmentsOfUser) {
  getAllAssignmentsOfUser(data: $data) {
    config_assignment_rules {
      name
      rules
      created_at
      id
      updated_at
    }
    config_assignment_type
    creator_id
    questions_assignment_rules {
      created_at
      id
      name
      rules
      updated_at
    }
    assignment {
      status
      finished_at
    }
    questions_assignment_type
    draft_mode
    is_active
    name
    id
    no_of_questions
    general_instructions_md
  }
}
    `;

/**
 * __useGetAllAssignmentsOfUserQuery__
 *
 * To run a query within a React component, call `useGetAllAssignmentsOfUserQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAllAssignmentsOfUserQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAllAssignmentsOfUserQuery({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useGetAllAssignmentsOfUserQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetAllAssignmentsOfUserQuery, GetAllAssignmentsOfUserQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetAllAssignmentsOfUserQuery, GetAllAssignmentsOfUserQueryVariables>(GetAllAssignmentsOfUserDocument, options);
      }
export function useGetAllAssignmentsOfUserLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetAllAssignmentsOfUserQuery, GetAllAssignmentsOfUserQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetAllAssignmentsOfUserQuery, GetAllAssignmentsOfUserQueryVariables>(GetAllAssignmentsOfUserDocument, options);
        }
export function useGetAllAssignmentsOfUserSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetAllAssignmentsOfUserQuery, GetAllAssignmentsOfUserQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetAllAssignmentsOfUserQuery, GetAllAssignmentsOfUserQueryVariables>(GetAllAssignmentsOfUserDocument, options);
        }
export type GetAllAssignmentsOfUserQueryHookResult = ReturnType<typeof useGetAllAssignmentsOfUserQuery>;
export type GetAllAssignmentsOfUserLazyQueryHookResult = ReturnType<typeof useGetAllAssignmentsOfUserLazyQuery>;
export type GetAllAssignmentsOfUserSuspenseQueryHookResult = ReturnType<typeof useGetAllAssignmentsOfUserSuspenseQuery>;
export type GetAllAssignmentsOfUserQueryResult = Apollo.QueryResult<GetAllAssignmentsOfUserQuery, GetAllAssignmentsOfUserQueryVariables>;
export const GetSingleAssignmentConfigDocument = gql`
    query GetSingleAssignmentConfig($getSingleAssignmentConfigId: String!) {
  getSingleAssignmentConfig(id: $getSingleAssignmentConfigId) {
    config_assignment_rules {
      name
      id
      rules
    }
    config_assignment_type
    draft_mode
    general_instructions_md
    id
    is_active
    name
    no_of_questions
    questions_assignment_rules {
      id
      name
      rules
    }
    questions_assignment_type
    updated_at
  }
}
    `;

/**
 * __useGetSingleAssignmentConfigQuery__
 *
 * To run a query within a React component, call `useGetSingleAssignmentConfigQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSingleAssignmentConfigQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSingleAssignmentConfigQuery({
 *   variables: {
 *      getSingleAssignmentConfigId: // value for 'getSingleAssignmentConfigId'
 *   },
 * });
 */
export function useGetSingleAssignmentConfigQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetSingleAssignmentConfigQuery, GetSingleAssignmentConfigQueryVariables> & ({ variables: GetSingleAssignmentConfigQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetSingleAssignmentConfigQuery, GetSingleAssignmentConfigQueryVariables>(GetSingleAssignmentConfigDocument, options);
      }
export function useGetSingleAssignmentConfigLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetSingleAssignmentConfigQuery, GetSingleAssignmentConfigQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetSingleAssignmentConfigQuery, GetSingleAssignmentConfigQueryVariables>(GetSingleAssignmentConfigDocument, options);
        }
export function useGetSingleAssignmentConfigSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetSingleAssignmentConfigQuery, GetSingleAssignmentConfigQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetSingleAssignmentConfigQuery, GetSingleAssignmentConfigQueryVariables>(GetSingleAssignmentConfigDocument, options);
        }
export type GetSingleAssignmentConfigQueryHookResult = ReturnType<typeof useGetSingleAssignmentConfigQuery>;
export type GetSingleAssignmentConfigLazyQueryHookResult = ReturnType<typeof useGetSingleAssignmentConfigLazyQuery>;
export type GetSingleAssignmentConfigSuspenseQueryHookResult = ReturnType<typeof useGetSingleAssignmentConfigSuspenseQuery>;
export type GetSingleAssignmentConfigQueryResult = Apollo.QueryResult<GetSingleAssignmentConfigQuery, GetSingleAssignmentConfigQueryVariables>;
export const GenerateAssignmentForUserDocument = gql`
    mutation GenerateAssignmentForUser($assignmentConfigId: String!) {
  generateAssignmentForUser(assignment_config_id: $assignmentConfigId) {
    data
    message
    result
  }
}
    `;
export type GenerateAssignmentForUserMutationFn = Apollo.MutationFunction<GenerateAssignmentForUserMutation, GenerateAssignmentForUserMutationVariables>;

/**
 * __useGenerateAssignmentForUserMutation__
 *
 * To run a mutation, you first call `useGenerateAssignmentForUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useGenerateAssignmentForUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [generateAssignmentForUserMutation, { data, loading, error }] = useGenerateAssignmentForUserMutation({
 *   variables: {
 *      assignmentConfigId: // value for 'assignmentConfigId'
 *   },
 * });
 */
export function useGenerateAssignmentForUserMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<GenerateAssignmentForUserMutation, GenerateAssignmentForUserMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<GenerateAssignmentForUserMutation, GenerateAssignmentForUserMutationVariables>(GenerateAssignmentForUserDocument, options);
      }
export type GenerateAssignmentForUserMutationHookResult = ReturnType<typeof useGenerateAssignmentForUserMutation>;
export type GenerateAssignmentForUserMutationResult = Apollo.MutationResult<GenerateAssignmentForUserMutation>;
export type GenerateAssignmentForUserMutationOptions = Apollo.BaseMutationOptions<GenerateAssignmentForUserMutation, GenerateAssignmentForUserMutationVariables>;
export const GetUserAssignmentDocument = gql`
    query GetUserAssignment($assignmentId: String!, $assignmentConfigId: String!) {
  getUserAssignment(
    assignment_id: $assignmentId
    assignment_config_id: $assignmentConfigId
  ) {
    id
    images
    question_text
    question_audio
    assignment_duration_seconds
    user_answer_id
    user_answer
    option {
      label
      value
      option_audio
    }
  }
}
    `;

/**
 * __useGetUserAssignmentQuery__
 *
 * To run a query within a React component, call `useGetUserAssignmentQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserAssignmentQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserAssignmentQuery({
 *   variables: {
 *      assignmentId: // value for 'assignmentId'
 *      assignmentConfigId: // value for 'assignmentConfigId'
 *   },
 * });
 */
export function useGetUserAssignmentQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetUserAssignmentQuery, GetUserAssignmentQueryVariables> & ({ variables: GetUserAssignmentQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUserAssignmentQuery, GetUserAssignmentQueryVariables>(GetUserAssignmentDocument, options);
      }
export function useGetUserAssignmentLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUserAssignmentQuery, GetUserAssignmentQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUserAssignmentQuery, GetUserAssignmentQueryVariables>(GetUserAssignmentDocument, options);
        }
export function useGetUserAssignmentSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUserAssignmentQuery, GetUserAssignmentQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUserAssignmentQuery, GetUserAssignmentQueryVariables>(GetUserAssignmentDocument, options);
        }
export type GetUserAssignmentQueryHookResult = ReturnType<typeof useGetUserAssignmentQuery>;
export type GetUserAssignmentLazyQueryHookResult = ReturnType<typeof useGetUserAssignmentLazyQuery>;
export type GetUserAssignmentSuspenseQueryHookResult = ReturnType<typeof useGetUserAssignmentSuspenseQuery>;
export type GetUserAssignmentQueryResult = Apollo.QueryResult<GetUserAssignmentQuery, GetUserAssignmentQueryVariables>;
export const SubmitAnswerForSingleQuestionDocument = gql`
    mutation SubmitAnswerForSingleQuestion($data: submitAnswerForSingleQuestionInput) {
  submitAnswerForSingleQuestion(data: $data) {
    message
    result
  }
}
    `;
export type SubmitAnswerForSingleQuestionMutationFn = Apollo.MutationFunction<SubmitAnswerForSingleQuestionMutation, SubmitAnswerForSingleQuestionMutationVariables>;

/**
 * __useSubmitAnswerForSingleQuestionMutation__
 *
 * To run a mutation, you first call `useSubmitAnswerForSingleQuestionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSubmitAnswerForSingleQuestionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [submitAnswerForSingleQuestionMutation, { data, loading, error }] = useSubmitAnswerForSingleQuestionMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useSubmitAnswerForSingleQuestionMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<SubmitAnswerForSingleQuestionMutation, SubmitAnswerForSingleQuestionMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<SubmitAnswerForSingleQuestionMutation, SubmitAnswerForSingleQuestionMutationVariables>(SubmitAnswerForSingleQuestionDocument, options);
      }
export type SubmitAnswerForSingleQuestionMutationHookResult = ReturnType<typeof useSubmitAnswerForSingleQuestionMutation>;
export type SubmitAnswerForSingleQuestionMutationResult = Apollo.MutationResult<SubmitAnswerForSingleQuestionMutation>;
export type SubmitAnswerForSingleQuestionMutationOptions = Apollo.BaseMutationOptions<SubmitAnswerForSingleQuestionMutation, SubmitAnswerForSingleQuestionMutationVariables>;
export const CompleteUserAssignmentDocument = gql`
    mutation completeUserAssignment($assignmentId: String!) {
  completeUserAssignment(assignment_id: $assignmentId) {
    message
    result
    data
  }
}
    `;
export type CompleteUserAssignmentMutationFn = Apollo.MutationFunction<CompleteUserAssignmentMutation, CompleteUserAssignmentMutationVariables>;

/**
 * __useCompleteUserAssignmentMutation__
 *
 * To run a mutation, you first call `useCompleteUserAssignmentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCompleteUserAssignmentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [completeUserAssignmentMutation, { data, loading, error }] = useCompleteUserAssignmentMutation({
 *   variables: {
 *      assignmentId: // value for 'assignmentId'
 *   },
 * });
 */
export function useCompleteUserAssignmentMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<CompleteUserAssignmentMutation, CompleteUserAssignmentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<CompleteUserAssignmentMutation, CompleteUserAssignmentMutationVariables>(CompleteUserAssignmentDocument, options);
      }
export type CompleteUserAssignmentMutationHookResult = ReturnType<typeof useCompleteUserAssignmentMutation>;
export type CompleteUserAssignmentMutationResult = Apollo.MutationResult<CompleteUserAssignmentMutation>;
export type CompleteUserAssignmentMutationOptions = Apollo.BaseMutationOptions<CompleteUserAssignmentMutation, CompleteUserAssignmentMutationVariables>;
export const GetAssignmentRemainingTimeDocument = gql`
    query GetAssignmentRemainingTime($assignmentId: String!) {
  getAssignmentRemainingTime(assignment_id: $assignmentId) {
    data
    message
    result
  }
}
    `;

/**
 * __useGetAssignmentRemainingTimeQuery__
 *
 * To run a query within a React component, call `useGetAssignmentRemainingTimeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAssignmentRemainingTimeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAssignmentRemainingTimeQuery({
 *   variables: {
 *      assignmentId: // value for 'assignmentId'
 *   },
 * });
 */
export function useGetAssignmentRemainingTimeQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetAssignmentRemainingTimeQuery, GetAssignmentRemainingTimeQueryVariables> & ({ variables: GetAssignmentRemainingTimeQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetAssignmentRemainingTimeQuery, GetAssignmentRemainingTimeQueryVariables>(GetAssignmentRemainingTimeDocument, options);
      }
export function useGetAssignmentRemainingTimeLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetAssignmentRemainingTimeQuery, GetAssignmentRemainingTimeQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetAssignmentRemainingTimeQuery, GetAssignmentRemainingTimeQueryVariables>(GetAssignmentRemainingTimeDocument, options);
        }
export function useGetAssignmentRemainingTimeSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetAssignmentRemainingTimeQuery, GetAssignmentRemainingTimeQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetAssignmentRemainingTimeQuery, GetAssignmentRemainingTimeQueryVariables>(GetAssignmentRemainingTimeDocument, options);
        }
export type GetAssignmentRemainingTimeQueryHookResult = ReturnType<typeof useGetAssignmentRemainingTimeQuery>;
export type GetAssignmentRemainingTimeLazyQueryHookResult = ReturnType<typeof useGetAssignmentRemainingTimeLazyQuery>;
export type GetAssignmentRemainingTimeSuspenseQueryHookResult = ReturnType<typeof useGetAssignmentRemainingTimeSuspenseQuery>;
export type GetAssignmentRemainingTimeQueryResult = Apollo.QueryResult<GetAssignmentRemainingTimeQuery, GetAssignmentRemainingTimeQueryVariables>;
export const GetSingleManualAssignmentOfUserDocument = gql`
    query GetSingleManualAssignmentOfUser($assignmentConfigId: String!) {
  getSingleManualAssignmentOfUser(assignment_config_id: $assignmentConfigId) {
    assignment {
      assignment {
        status
      }
    }
  }
}
    `;

/**
 * __useGetSingleManualAssignmentOfUserQuery__
 *
 * To run a query within a React component, call `useGetSingleManualAssignmentOfUserQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSingleManualAssignmentOfUserQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSingleManualAssignmentOfUserQuery({
 *   variables: {
 *      assignmentConfigId: // value for 'assignmentConfigId'
 *   },
 * });
 */
export function useGetSingleManualAssignmentOfUserQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetSingleManualAssignmentOfUserQuery, GetSingleManualAssignmentOfUserQueryVariables> & ({ variables: GetSingleManualAssignmentOfUserQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetSingleManualAssignmentOfUserQuery, GetSingleManualAssignmentOfUserQueryVariables>(GetSingleManualAssignmentOfUserDocument, options);
      }
export function useGetSingleManualAssignmentOfUserLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetSingleManualAssignmentOfUserQuery, GetSingleManualAssignmentOfUserQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetSingleManualAssignmentOfUserQuery, GetSingleManualAssignmentOfUserQueryVariables>(GetSingleManualAssignmentOfUserDocument, options);
        }
export function useGetSingleManualAssignmentOfUserSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetSingleManualAssignmentOfUserQuery, GetSingleManualAssignmentOfUserQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetSingleManualAssignmentOfUserQuery, GetSingleManualAssignmentOfUserQueryVariables>(GetSingleManualAssignmentOfUserDocument, options);
        }
export type GetSingleManualAssignmentOfUserQueryHookResult = ReturnType<typeof useGetSingleManualAssignmentOfUserQuery>;
export type GetSingleManualAssignmentOfUserLazyQueryHookResult = ReturnType<typeof useGetSingleManualAssignmentOfUserLazyQuery>;
export type GetSingleManualAssignmentOfUserSuspenseQueryHookResult = ReturnType<typeof useGetSingleManualAssignmentOfUserSuspenseQuery>;
export type GetSingleManualAssignmentOfUserQueryResult = Apollo.QueryResult<GetSingleManualAssignmentOfUserQuery, GetSingleManualAssignmentOfUserQueryVariables>;
export const UserBannersDocument = gql`
    query UserBanners {
  banners {
    banner_config {
      description
      hero_image
      title
      id
      cta_link
      cta_required
      is_published
      is_active
      image
      cta_text
    }
    expiry_time
    id
  }
}
    `;

/**
 * __useUserBannersQuery__
 *
 * To run a query within a React component, call `useUserBannersQuery` and pass it any options that fit your needs.
 * When your component renders, `useUserBannersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUserBannersQuery({
 *   variables: {
 *   },
 * });
 */
export function useUserBannersQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<UserBannersQuery, UserBannersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<UserBannersQuery, UserBannersQueryVariables>(UserBannersDocument, options);
      }
export function useUserBannersLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<UserBannersQuery, UserBannersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<UserBannersQuery, UserBannersQueryVariables>(UserBannersDocument, options);
        }
export function useUserBannersSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<UserBannersQuery, UserBannersQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<UserBannersQuery, UserBannersQueryVariables>(UserBannersDocument, options);
        }
export type UserBannersQueryHookResult = ReturnType<typeof useUserBannersQuery>;
export type UserBannersLazyQueryHookResult = ReturnType<typeof useUserBannersLazyQuery>;
export type UserBannersSuspenseQueryHookResult = ReturnType<typeof useUserBannersSuspenseQuery>;
export type UserBannersQueryResult = Apollo.QueryResult<UserBannersQuery, UserBannersQueryVariables>;
export const UpdateUserChecklistDataDocument = gql`
    mutation UpdateUserChecklistData($data: [UpdateUserChecklistDataInput]!) {
  updateUserChecklistData(data: $data) {
    result
    message
  }
}
    `;
export type UpdateUserChecklistDataMutationFn = Apollo.MutationFunction<UpdateUserChecklistDataMutation, UpdateUserChecklistDataMutationVariables>;

/**
 * __useUpdateUserChecklistDataMutation__
 *
 * To run a mutation, you first call `useUpdateUserChecklistDataMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserChecklistDataMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserChecklistDataMutation, { data, loading, error }] = useUpdateUserChecklistDataMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateUserChecklistDataMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateUserChecklistDataMutation, UpdateUserChecklistDataMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateUserChecklistDataMutation, UpdateUserChecklistDataMutationVariables>(UpdateUserChecklistDataDocument, options);
      }
export type UpdateUserChecklistDataMutationHookResult = ReturnType<typeof useUpdateUserChecklistDataMutation>;
export type UpdateUserChecklistDataMutationResult = Apollo.MutationResult<UpdateUserChecklistDataMutation>;
export type UpdateUserChecklistDataMutationOptions = Apollo.BaseMutationOptions<UpdateUserChecklistDataMutation, UpdateUserChecklistDataMutationVariables>;
export const GetAllChecklistsDocument = gql`
    query GetAllChecklists($userType: String) {
  getAllChecklists(user_type: $userType) {
    result
    message
    data {
      id
      name
      is_active
      created_at
      updated_at
    }
  }
}
    `;

/**
 * __useGetAllChecklistsQuery__
 *
 * To run a query within a React component, call `useGetAllChecklistsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAllChecklistsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAllChecklistsQuery({
 *   variables: {
 *      userType: // value for 'userType'
 *   },
 * });
 */
export function useGetAllChecklistsQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetAllChecklistsQuery, GetAllChecklistsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetAllChecklistsQuery, GetAllChecklistsQueryVariables>(GetAllChecklistsDocument, options);
      }
export function useGetAllChecklistsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetAllChecklistsQuery, GetAllChecklistsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetAllChecklistsQuery, GetAllChecklistsQueryVariables>(GetAllChecklistsDocument, options);
        }
export function useGetAllChecklistsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetAllChecklistsQuery, GetAllChecklistsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetAllChecklistsQuery, GetAllChecklistsQueryVariables>(GetAllChecklistsDocument, options);
        }
export type GetAllChecklistsQueryHookResult = ReturnType<typeof useGetAllChecklistsQuery>;
export type GetAllChecklistsLazyQueryHookResult = ReturnType<typeof useGetAllChecklistsLazyQuery>;
export type GetAllChecklistsSuspenseQueryHookResult = ReturnType<typeof useGetAllChecklistsSuspenseQuery>;
export type GetAllChecklistsQueryResult = Apollo.QueryResult<GetAllChecklistsQuery, GetAllChecklistsQueryVariables>;
export const GetUserChecklistDataDocument = gql`
    query GetUserChecklistData($userId: Int!, $userType: String) {
  getUserChecklistData(user_id: $userId, user_type: $userType) {
    result
    message
    data {
      id
      name
      user_checklist_data {
        id
        hiring_completed
      }
    }
  }
}
    `;

/**
 * __useGetUserChecklistDataQuery__
 *
 * To run a query within a React component, call `useGetUserChecklistDataQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserChecklistDataQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserChecklistDataQuery({
 *   variables: {
 *      userId: // value for 'userId'
 *      userType: // value for 'userType'
 *   },
 * });
 */
export function useGetUserChecklistDataQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetUserChecklistDataQuery, GetUserChecklistDataQueryVariables> & ({ variables: GetUserChecklistDataQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUserChecklistDataQuery, GetUserChecklistDataQueryVariables>(GetUserChecklistDataDocument, options);
      }
export function useGetUserChecklistDataLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUserChecklistDataQuery, GetUserChecklistDataQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUserChecklistDataQuery, GetUserChecklistDataQueryVariables>(GetUserChecklistDataDocument, options);
        }
export function useGetUserChecklistDataSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUserChecklistDataQuery, GetUserChecklistDataQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUserChecklistDataQuery, GetUserChecklistDataQueryVariables>(GetUserChecklistDataDocument, options);
        }
export type GetUserChecklistDataQueryHookResult = ReturnType<typeof useGetUserChecklistDataQuery>;
export type GetUserChecklistDataLazyQueryHookResult = ReturnType<typeof useGetUserChecklistDataLazyQuery>;
export type GetUserChecklistDataSuspenseQueryHookResult = ReturnType<typeof useGetUserChecklistDataSuspenseQuery>;
export type GetUserChecklistDataQueryResult = Apollo.QueryResult<GetUserChecklistDataQuery, GetUserChecklistDataQueryVariables>;
export const GetFeedbackFormStatusDocument = gql`
    query GetFeedbackFormStatus($userId: Int!) {
  getFeedbackFormStatus(user_id: $userId) {
    result
    message
    submitted
    onboarded
    user_id
    language
  }
}
    `;

/**
 * __useGetFeedbackFormStatusQuery__
 *
 * To run a query within a React component, call `useGetFeedbackFormStatusQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetFeedbackFormStatusQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetFeedbackFormStatusQuery({
 *   variables: {
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useGetFeedbackFormStatusQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetFeedbackFormStatusQuery, GetFeedbackFormStatusQueryVariables> & ({ variables: GetFeedbackFormStatusQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetFeedbackFormStatusQuery, GetFeedbackFormStatusQueryVariables>(GetFeedbackFormStatusDocument, options);
      }
export function useGetFeedbackFormStatusLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetFeedbackFormStatusQuery, GetFeedbackFormStatusQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetFeedbackFormStatusQuery, GetFeedbackFormStatusQueryVariables>(GetFeedbackFormStatusDocument, options);
        }
export function useGetFeedbackFormStatusSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetFeedbackFormStatusQuery, GetFeedbackFormStatusQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetFeedbackFormStatusQuery, GetFeedbackFormStatusQueryVariables>(GetFeedbackFormStatusDocument, options);
        }
export type GetFeedbackFormStatusQueryHookResult = ReturnType<typeof useGetFeedbackFormStatusQuery>;
export type GetFeedbackFormStatusLazyQueryHookResult = ReturnType<typeof useGetFeedbackFormStatusLazyQuery>;
export type GetFeedbackFormStatusSuspenseQueryHookResult = ReturnType<typeof useGetFeedbackFormStatusSuspenseQuery>;
export type GetFeedbackFormStatusQueryResult = Apollo.QueryResult<GetFeedbackFormStatusQuery, GetFeedbackFormStatusQueryVariables>;
export const DocumentsDocument = gql`
    query Documents {
  documents {
    url
    id
    verification_status
    doc_number
    type {
      id
      required
      name
    }
    verification_message
    verification_status
    verified
  }
}
    `;

/**
 * __useDocumentsQuery__
 *
 * To run a query within a React component, call `useDocumentsQuery` and pass it any options that fit your needs.
 * When your component renders, `useDocumentsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDocumentsQuery({
 *   variables: {
 *   },
 * });
 */
export function useDocumentsQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<DocumentsQuery, DocumentsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<DocumentsQuery, DocumentsQueryVariables>(DocumentsDocument, options);
      }
export function useDocumentsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<DocumentsQuery, DocumentsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<DocumentsQuery, DocumentsQueryVariables>(DocumentsDocument, options);
        }
export function useDocumentsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<DocumentsQuery, DocumentsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<DocumentsQuery, DocumentsQueryVariables>(DocumentsDocument, options);
        }
export type DocumentsQueryHookResult = ReturnType<typeof useDocumentsQuery>;
export type DocumentsLazyQueryHookResult = ReturnType<typeof useDocumentsLazyQuery>;
export type DocumentsSuspenseQueryHookResult = ReturnType<typeof useDocumentsSuspenseQuery>;
export type DocumentsQueryResult = Apollo.QueryResult<DocumentsQuery, DocumentsQueryVariables>;
export const DocumentTypesDocument = gql`
    query DocumentTypes($userType: UserTypeDocument, $isTeamMember: Boolean) {
  documentTypes(user_type: $userType, is_team_member: $isTeamMember) {
    name
    id
    required
    status
    instructions
    document_number_config {
      id
      validation_type
      max_length
      min_length
      document_type_id
    }
    is_doc_number_required
  }
}
    `;

/**
 * __useDocumentTypesQuery__
 *
 * To run a query within a React component, call `useDocumentTypesQuery` and pass it any options that fit your needs.
 * When your component renders, `useDocumentTypesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDocumentTypesQuery({
 *   variables: {
 *      userType: // value for 'userType'
 *      isTeamMember: // value for 'isTeamMember'
 *   },
 * });
 */
export function useDocumentTypesQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<DocumentTypesQuery, DocumentTypesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<DocumentTypesQuery, DocumentTypesQueryVariables>(DocumentTypesDocument, options);
      }
export function useDocumentTypesLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<DocumentTypesQuery, DocumentTypesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<DocumentTypesQuery, DocumentTypesQueryVariables>(DocumentTypesDocument, options);
        }
export function useDocumentTypesSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<DocumentTypesQuery, DocumentTypesQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<DocumentTypesQuery, DocumentTypesQueryVariables>(DocumentTypesDocument, options);
        }
export type DocumentTypesQueryHookResult = ReturnType<typeof useDocumentTypesQuery>;
export type DocumentTypesLazyQueryHookResult = ReturnType<typeof useDocumentTypesLazyQuery>;
export type DocumentTypesSuspenseQueryHookResult = ReturnType<typeof useDocumentTypesSuspenseQuery>;
export type DocumentTypesQueryResult = Apollo.QueryResult<DocumentTypesQuery, DocumentTypesQueryVariables>;
export const AddDocumentDocument = gql`
    mutation AddDocument($data: DocumentData) {
  addDocument(data: $data) {
    url
    id
  }
}
    `;
export type AddDocumentMutationFn = Apollo.MutationFunction<AddDocumentMutation, AddDocumentMutationVariables>;

/**
 * __useAddDocumentMutation__
 *
 * To run a mutation, you first call `useAddDocumentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddDocumentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addDocumentMutation, { data, loading, error }] = useAddDocumentMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useAddDocumentMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<AddDocumentMutation, AddDocumentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<AddDocumentMutation, AddDocumentMutationVariables>(AddDocumentDocument, options);
      }
export type AddDocumentMutationHookResult = ReturnType<typeof useAddDocumentMutation>;
export type AddDocumentMutationResult = Apollo.MutationResult<AddDocumentMutation>;
export type AddDocumentMutationOptions = Apollo.BaseMutationOptions<AddDocumentMutation, AddDocumentMutationVariables>;
export const DeleteDocumentDocument = gql`
    mutation DeleteDocument($deleteDocumentId: Int!) {
  deleteDocument(id: $deleteDocumentId) {
    message
    result
  }
}
    `;
export type DeleteDocumentMutationFn = Apollo.MutationFunction<DeleteDocumentMutation, DeleteDocumentMutationVariables>;

/**
 * __useDeleteDocumentMutation__
 *
 * To run a mutation, you first call `useDeleteDocumentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteDocumentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteDocumentMutation, { data, loading, error }] = useDeleteDocumentMutation({
 *   variables: {
 *      deleteDocumentId: // value for 'deleteDocumentId'
 *   },
 * });
 */
export function useDeleteDocumentMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<DeleteDocumentMutation, DeleteDocumentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<DeleteDocumentMutation, DeleteDocumentMutationVariables>(DeleteDocumentDocument, options);
      }
export type DeleteDocumentMutationHookResult = ReturnType<typeof useDeleteDocumentMutation>;
export type DeleteDocumentMutationResult = Apollo.MutationResult<DeleteDocumentMutation>;
export type DeleteDocumentMutationOptions = Apollo.BaseMutationOptions<DeleteDocumentMutation, DeleteDocumentMutationVariables>;
export const ExpertiseDocument = gql`
    query Expertise {
  expertise {
    data {
      id
      name
      icon
    }
    message
    result
  }
}
    `;

/**
 * __useExpertiseQuery__
 *
 * To run a query within a React component, call `useExpertiseQuery` and pass it any options that fit your needs.
 * When your component renders, `useExpertiseQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useExpertiseQuery({
 *   variables: {
 *   },
 * });
 */
export function useExpertiseQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<ExpertiseQuery, ExpertiseQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<ExpertiseQuery, ExpertiseQueryVariables>(ExpertiseDocument, options);
      }
export function useExpertiseLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<ExpertiseQuery, ExpertiseQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<ExpertiseQuery, ExpertiseQueryVariables>(ExpertiseDocument, options);
        }
export function useExpertiseSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<ExpertiseQuery, ExpertiseQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<ExpertiseQuery, ExpertiseQueryVariables>(ExpertiseDocument, options);
        }
export type ExpertiseQueryHookResult = ReturnType<typeof useExpertiseQuery>;
export type ExpertiseLazyQueryHookResult = ReturnType<typeof useExpertiseLazyQuery>;
export type ExpertiseSuspenseQueryHookResult = ReturnType<typeof useExpertiseSuspenseQuery>;
export type ExpertiseQueryResult = Apollo.QueryResult<ExpertiseQuery, ExpertiseQueryVariables>;
export const AssignExpertiseDocument = gql`
    mutation assignExpertise($expertise: [Int]) {
  assignExpertise(expertise: $expertise) {
    message
    result
  }
}
    `;
export type AssignExpertiseMutationFn = Apollo.MutationFunction<AssignExpertiseMutation, AssignExpertiseMutationVariables>;

/**
 * __useAssignExpertiseMutation__
 *
 * To run a mutation, you first call `useAssignExpertiseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAssignExpertiseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [assignExpertiseMutation, { data, loading, error }] = useAssignExpertiseMutation({
 *   variables: {
 *      expertise: // value for 'expertise'
 *   },
 * });
 */
export function useAssignExpertiseMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<AssignExpertiseMutation, AssignExpertiseMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<AssignExpertiseMutation, AssignExpertiseMutationVariables>(AssignExpertiseDocument, options);
      }
export type AssignExpertiseMutationHookResult = ReturnType<typeof useAssignExpertiseMutation>;
export type AssignExpertiseMutationResult = Apollo.MutationResult<AssignExpertiseMutation>;
export type AssignExpertiseMutationOptions = Apollo.BaseMutationOptions<AssignExpertiseMutation, AssignExpertiseMutationVariables>;
export const DeleteExpertiseDocument = gql`
    mutation DeleteExpertise($expertiseId: Int) {
  deleteExpertise(expertise_id: $expertiseId) {
    message
    result
  }
}
    `;
export type DeleteExpertiseMutationFn = Apollo.MutationFunction<DeleteExpertiseMutation, DeleteExpertiseMutationVariables>;

/**
 * __useDeleteExpertiseMutation__
 *
 * To run a mutation, you first call `useDeleteExpertiseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteExpertiseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteExpertiseMutation, { data, loading, error }] = useDeleteExpertiseMutation({
 *   variables: {
 *      expertiseId: // value for 'expertiseId'
 *   },
 * });
 */
export function useDeleteExpertiseMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<DeleteExpertiseMutation, DeleteExpertiseMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<DeleteExpertiseMutation, DeleteExpertiseMutationVariables>(DeleteExpertiseDocument, options);
      }
export type DeleteExpertiseMutationHookResult = ReturnType<typeof useDeleteExpertiseMutation>;
export type DeleteExpertiseMutationResult = Apollo.MutationResult<DeleteExpertiseMutation>;
export type DeleteExpertiseMutationOptions = Apollo.BaseMutationOptions<DeleteExpertiseMutation, DeleteExpertiseMutationVariables>;
export const GetInterviewsFrUserDocument = gql`
    query GetInterviewsFrUser {
  getInterviewsFrUser {
    id
    title
    is_active
    start_time
    end_time
    interview_type
    google_meet_link
    note
    attempts
    status
    location
    interviewer {
      id
      email
      name
      location {
        work_address
      }
    }
    feedback {
      feedback_form_id
      id
      interviewer_feedback_state
      interview_state
      interviewee_feedback_result
      interviewee_feedback_state
      template {
        title
        interviewee_feedback_enabled
      }
    }
  }
}
    `;

/**
 * __useGetInterviewsFrUserQuery__
 *
 * To run a query within a React component, call `useGetInterviewsFrUserQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInterviewsFrUserQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInterviewsFrUserQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetInterviewsFrUserQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetInterviewsFrUserQuery, GetInterviewsFrUserQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetInterviewsFrUserQuery, GetInterviewsFrUserQueryVariables>(GetInterviewsFrUserDocument, options);
      }
export function useGetInterviewsFrUserLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetInterviewsFrUserQuery, GetInterviewsFrUserQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetInterviewsFrUserQuery, GetInterviewsFrUserQueryVariables>(GetInterviewsFrUserDocument, options);
        }
export function useGetInterviewsFrUserSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetInterviewsFrUserQuery, GetInterviewsFrUserQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetInterviewsFrUserQuery, GetInterviewsFrUserQueryVariables>(GetInterviewsFrUserDocument, options);
        }
export type GetInterviewsFrUserQueryHookResult = ReturnType<typeof useGetInterviewsFrUserQuery>;
export type GetInterviewsFrUserLazyQueryHookResult = ReturnType<typeof useGetInterviewsFrUserLazyQuery>;
export type GetInterviewsFrUserSuspenseQueryHookResult = ReturnType<typeof useGetInterviewsFrUserSuspenseQuery>;
export type GetInterviewsFrUserQueryResult = Apollo.QueryResult<GetInterviewsFrUserQuery, GetInterviewsFrUserQueryVariables>;
export const CreateLeadDocument = gql`
    mutation CreateLead($data: CreateLeadInput) {
  createLead(data: $data) {
    message
    result
  }
}
    `;
export type CreateLeadMutationFn = Apollo.MutationFunction<CreateLeadMutation, CreateLeadMutationVariables>;

/**
 * __useCreateLeadMutation__
 *
 * To run a mutation, you first call `useCreateLeadMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateLeadMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createLeadMutation, { data, loading, error }] = useCreateLeadMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useCreateLeadMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<CreateLeadMutation, CreateLeadMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<CreateLeadMutation, CreateLeadMutationVariables>(CreateLeadDocument, options);
      }
export type CreateLeadMutationHookResult = ReturnType<typeof useCreateLeadMutation>;
export type CreateLeadMutationResult = Apollo.MutationResult<CreateLeadMutation>;
export type CreateLeadMutationOptions = Apollo.BaseMutationOptions<CreateLeadMutation, CreateLeadMutationVariables>;
export const CreateContactsDocument = gql`
    mutation CreateContacts($contacts: String) {
  createContacts(contacts: $contacts) {
    result
    message
  }
}
    `;
export type CreateContactsMutationFn = Apollo.MutationFunction<CreateContactsMutation, CreateContactsMutationVariables>;

/**
 * __useCreateContactsMutation__
 *
 * To run a mutation, you first call `useCreateContactsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateContactsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createContactsMutation, { data, loading, error }] = useCreateContactsMutation({
 *   variables: {
 *      contacts: // value for 'contacts'
 *   },
 * });
 */
export function useCreateContactsMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<CreateContactsMutation, CreateContactsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<CreateContactsMutation, CreateContactsMutationVariables>(CreateContactsDocument, options);
      }
export type CreateContactsMutationHookResult = ReturnType<typeof useCreateContactsMutation>;
export type CreateContactsMutationResult = Apollo.MutationResult<CreateContactsMutation>;
export type CreateContactsMutationOptions = Apollo.BaseMutationOptions<CreateContactsMutation, CreateContactsMutationVariables>;
export const GetContactsOfTheUserFrAdminDocument = gql`
    query GetContactsOfTheUserFrAdmin($filter: UserContactsFilter, $userId: Int) {
  getContactsOfTheUserFrAdmin(filter: $filter, user_id: $userId) {
    data {
      contact {
        id
        name
        phone
        meta
        timeline
        is_active
        is_deleted
        moved_to_users
        created_at
        updated_at
        phone_contact_id
      }
      contact_id
      created_at
      is_active
      updated_at
      user_id
    }
    total_count
    total_moved_contacts_count
  }
}
    `;

/**
 * __useGetContactsOfTheUserFrAdminQuery__
 *
 * To run a query within a React component, call `useGetContactsOfTheUserFrAdminQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetContactsOfTheUserFrAdminQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetContactsOfTheUserFrAdminQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useGetContactsOfTheUserFrAdminQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetContactsOfTheUserFrAdminQuery, GetContactsOfTheUserFrAdminQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetContactsOfTheUserFrAdminQuery, GetContactsOfTheUserFrAdminQueryVariables>(GetContactsOfTheUserFrAdminDocument, options);
      }
export function useGetContactsOfTheUserFrAdminLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetContactsOfTheUserFrAdminQuery, GetContactsOfTheUserFrAdminQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetContactsOfTheUserFrAdminQuery, GetContactsOfTheUserFrAdminQueryVariables>(GetContactsOfTheUserFrAdminDocument, options);
        }
export function useGetContactsOfTheUserFrAdminSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetContactsOfTheUserFrAdminQuery, GetContactsOfTheUserFrAdminQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetContactsOfTheUserFrAdminQuery, GetContactsOfTheUserFrAdminQueryVariables>(GetContactsOfTheUserFrAdminDocument, options);
        }
export type GetContactsOfTheUserFrAdminQueryHookResult = ReturnType<typeof useGetContactsOfTheUserFrAdminQuery>;
export type GetContactsOfTheUserFrAdminLazyQueryHookResult = ReturnType<typeof useGetContactsOfTheUserFrAdminLazyQuery>;
export type GetContactsOfTheUserFrAdminSuspenseQueryHookResult = ReturnType<typeof useGetContactsOfTheUserFrAdminSuspenseQuery>;
export type GetContactsOfTheUserFrAdminQueryResult = Apollo.QueryResult<GetContactsOfTheUserFrAdminQuery, GetContactsOfTheUserFrAdminQueryVariables>;
export const LocationSearchDocument = gql`
    query LocationSearch($term: String, $limit: Int, $filter: LocationFilter) {
  textSearch(term: $term, limit: $limit, filter: $filter) {
    pincode
    state
    id
    city
  }
}
    `;

/**
 * __useLocationSearchQuery__
 *
 * To run a query within a React component, call `useLocationSearchQuery` and pass it any options that fit your needs.
 * When your component renders, `useLocationSearchQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useLocationSearchQuery({
 *   variables: {
 *      term: // value for 'term'
 *      limit: // value for 'limit'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useLocationSearchQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<LocationSearchQuery, LocationSearchQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<LocationSearchQuery, LocationSearchQueryVariables>(LocationSearchDocument, options);
      }
export function useLocationSearchLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<LocationSearchQuery, LocationSearchQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<LocationSearchQuery, LocationSearchQueryVariables>(LocationSearchDocument, options);
        }
export function useLocationSearchSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<LocationSearchQuery, LocationSearchQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<LocationSearchQuery, LocationSearchQueryVariables>(LocationSearchDocument, options);
        }
export type LocationSearchQueryHookResult = ReturnType<typeof useLocationSearchQuery>;
export type LocationSearchLazyQueryHookResult = ReturnType<typeof useLocationSearchLazyQuery>;
export type LocationSearchSuspenseQueryHookResult = ReturnType<typeof useLocationSearchSuspenseQuery>;
export type LocationSearchQueryResult = Apollo.QueryResult<LocationSearchQuery, LocationSearchQueryVariables>;
export const GetUserMascotAlertDocument = gql`
    query GetUserMascotAlert($type: MascotSectionType!) {
  getUserMascotAlert(type: $type) {
    id
    type
    title
    message
    is_read
    cta_required
    cta_link
    priority
    meta
    cta_text
  }
}
    `;

/**
 * __useGetUserMascotAlertQuery__
 *
 * To run a query within a React component, call `useGetUserMascotAlertQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserMascotAlertQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserMascotAlertQuery({
 *   variables: {
 *      type: // value for 'type'
 *   },
 * });
 */
export function useGetUserMascotAlertQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetUserMascotAlertQuery, GetUserMascotAlertQueryVariables> & ({ variables: GetUserMascotAlertQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUserMascotAlertQuery, GetUserMascotAlertQueryVariables>(GetUserMascotAlertDocument, options);
      }
export function useGetUserMascotAlertLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUserMascotAlertQuery, GetUserMascotAlertQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUserMascotAlertQuery, GetUserMascotAlertQueryVariables>(GetUserMascotAlertDocument, options);
        }
export function useGetUserMascotAlertSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUserMascotAlertQuery, GetUserMascotAlertQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUserMascotAlertQuery, GetUserMascotAlertQueryVariables>(GetUserMascotAlertDocument, options);
        }
export type GetUserMascotAlertQueryHookResult = ReturnType<typeof useGetUserMascotAlertQuery>;
export type GetUserMascotAlertLazyQueryHookResult = ReturnType<typeof useGetUserMascotAlertLazyQuery>;
export type GetUserMascotAlertSuspenseQueryHookResult = ReturnType<typeof useGetUserMascotAlertSuspenseQuery>;
export type GetUserMascotAlertQueryResult = Apollo.QueryResult<GetUserMascotAlertQuery, GetUserMascotAlertQueryVariables>;
export const UpdateUserMascotAlertDocument = gql`
    mutation UpdateUserMascotAlert($updateUserMascotAlertId: String!) {
  updateUserMascotAlert(id: $updateUserMascotAlertId) {
    message
    result
  }
}
    `;
export type UpdateUserMascotAlertMutationFn = Apollo.MutationFunction<UpdateUserMascotAlertMutation, UpdateUserMascotAlertMutationVariables>;

/**
 * __useUpdateUserMascotAlertMutation__
 *
 * To run a mutation, you first call `useUpdateUserMascotAlertMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserMascotAlertMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserMascotAlertMutation, { data, loading, error }] = useUpdateUserMascotAlertMutation({
 *   variables: {
 *      updateUserMascotAlertId: // value for 'updateUserMascotAlertId'
 *   },
 * });
 */
export function useUpdateUserMascotAlertMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateUserMascotAlertMutation, UpdateUserMascotAlertMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateUserMascotAlertMutation, UpdateUserMascotAlertMutationVariables>(UpdateUserMascotAlertDocument, options);
      }
export type UpdateUserMascotAlertMutationHookResult = ReturnType<typeof useUpdateUserMascotAlertMutation>;
export type UpdateUserMascotAlertMutationResult = Apollo.MutationResult<UpdateUserMascotAlertMutation>;
export type UpdateUserMascotAlertMutationOptions = Apollo.BaseMutationOptions<UpdateUserMascotAlertMutation, UpdateUserMascotAlertMutationVariables>;
export const MascotNotificationDocument = gql`
    subscription MascotNotification($userId: Int!) {
  mascotNotifications(user_id: $userId) {
    id
    type
    title
    message
    is_read
    cta_required
    cta_link
    priority
    meta
  }
}
    `;

/**
 * __useMascotNotificationSubscription__
 *
 * To run a query within a React component, call `useMascotNotificationSubscription` and pass it any options that fit your needs.
 * When your component renders, `useMascotNotificationSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMascotNotificationSubscription({
 *   variables: {
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useMascotNotificationSubscription(baseOptions: ApolloReactHooks.SubscriptionHookOptions<MascotNotificationSubscription, MascotNotificationSubscriptionVariables> & ({ variables: MascotNotificationSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useSubscription<MascotNotificationSubscription, MascotNotificationSubscriptionVariables>(MascotNotificationDocument, options);
      }
export type MascotNotificationSubscriptionHookResult = ReturnType<typeof useMascotNotificationSubscription>;
export type MascotNotificationSubscriptionResult = Apollo.SubscriptionResult<MascotNotificationSubscription>;
export const TransferredToTmsNotificationDocument = gql`
    subscription TransferredToTmsNotification($userId: Int!) {
  transferredToTMS(user_id: $userId) {
    result
  }
}
    `;

/**
 * __useTransferredToTmsNotificationSubscription__
 *
 * To run a query within a React component, call `useTransferredToTmsNotificationSubscription` and pass it any options that fit your needs.
 * When your component renders, `useTransferredToTmsNotificationSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useTransferredToTmsNotificationSubscription({
 *   variables: {
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useTransferredToTmsNotificationSubscription(baseOptions: ApolloReactHooks.SubscriptionHookOptions<TransferredToTmsNotificationSubscription, TransferredToTmsNotificationSubscriptionVariables> & ({ variables: TransferredToTmsNotificationSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useSubscription<TransferredToTmsNotificationSubscription, TransferredToTmsNotificationSubscriptionVariables>(TransferredToTmsNotificationDocument, options);
      }
export type TransferredToTmsNotificationSubscriptionHookResult = ReturnType<typeof useTransferredToTmsNotificationSubscription>;
export type TransferredToTmsNotificationSubscriptionResult = Apollo.SubscriptionResult<TransferredToTmsNotificationSubscription>;
export const BellNotificationDocument = gql`
    subscription BellNotification($userId: Int!) {
  bellNotification(user_id: $userId)
}
    `;

/**
 * __useBellNotificationSubscription__
 *
 * To run a query within a React component, call `useBellNotificationSubscription` and pass it any options that fit your needs.
 * When your component renders, `useBellNotificationSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useBellNotificationSubscription({
 *   variables: {
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useBellNotificationSubscription(baseOptions: ApolloReactHooks.SubscriptionHookOptions<BellNotificationSubscription, BellNotificationSubscriptionVariables> & ({ variables: BellNotificationSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useSubscription<BellNotificationSubscription, BellNotificationSubscriptionVariables>(BellNotificationDocument, options);
      }
export type BellNotificationSubscriptionHookResult = ReturnType<typeof useBellNotificationSubscription>;
export type BellNotificationSubscriptionResult = Apollo.SubscriptionResult<BellNotificationSubscription>;
export const GetUserNotificationsDocument = gql`
    query GetUserNotifications($filter: NotificationFilterInput) {
  getUserNotifications(filter: $filter) {
    id
    title
    message
    is_read
    redirect_to
    priority
    meta
    created_at
  }
}
    `;

/**
 * __useGetUserNotificationsQuery__
 *
 * To run a query within a React component, call `useGetUserNotificationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserNotificationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserNotificationsQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetUserNotificationsQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetUserNotificationsQuery, GetUserNotificationsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUserNotificationsQuery, GetUserNotificationsQueryVariables>(GetUserNotificationsDocument, options);
      }
export function useGetUserNotificationsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUserNotificationsQuery, GetUserNotificationsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUserNotificationsQuery, GetUserNotificationsQueryVariables>(GetUserNotificationsDocument, options);
        }
export function useGetUserNotificationsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUserNotificationsQuery, GetUserNotificationsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUserNotificationsQuery, GetUserNotificationsQueryVariables>(GetUserNotificationsDocument, options);
        }
export type GetUserNotificationsQueryHookResult = ReturnType<typeof useGetUserNotificationsQuery>;
export type GetUserNotificationsLazyQueryHookResult = ReturnType<typeof useGetUserNotificationsLazyQuery>;
export type GetUserNotificationsSuspenseQueryHookResult = ReturnType<typeof useGetUserNotificationsSuspenseQuery>;
export type GetUserNotificationsQueryResult = Apollo.QueryResult<GetUserNotificationsQuery, GetUserNotificationsQueryVariables>;
export const UpdateNotificationDocument = gql`
    mutation UpdateNotification($notificationId: String!, $data: NotificationUpdateInput) {
  updateNotification(notification_id: $notificationId, data: $data) {
    message
    result
  }
}
    `;
export type UpdateNotificationMutationFn = Apollo.MutationFunction<UpdateNotificationMutation, UpdateNotificationMutationVariables>;

/**
 * __useUpdateNotificationMutation__
 *
 * To run a mutation, you first call `useUpdateNotificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateNotificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateNotificationMutation, { data, loading, error }] = useUpdateNotificationMutation({
 *   variables: {
 *      notificationId: // value for 'notificationId'
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateNotificationMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateNotificationMutation, UpdateNotificationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateNotificationMutation, UpdateNotificationMutationVariables>(UpdateNotificationDocument, options);
      }
export type UpdateNotificationMutationHookResult = ReturnType<typeof useUpdateNotificationMutation>;
export type UpdateNotificationMutationResult = Apollo.MutationResult<UpdateNotificationMutation>;
export type UpdateNotificationMutationOptions = Apollo.BaseMutationOptions<UpdateNotificationMutation, UpdateNotificationMutationVariables>;
export const GetBellNotificationCountDocument = gql`
    query GetBellNotificationCount {
  getBellNotificationCount
}
    `;

/**
 * __useGetBellNotificationCountQuery__
 *
 * To run a query within a React component, call `useGetBellNotificationCountQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetBellNotificationCountQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetBellNotificationCountQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetBellNotificationCountQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetBellNotificationCountQuery, GetBellNotificationCountQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetBellNotificationCountQuery, GetBellNotificationCountQueryVariables>(GetBellNotificationCountDocument, options);
      }
export function useGetBellNotificationCountLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetBellNotificationCountQuery, GetBellNotificationCountQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetBellNotificationCountQuery, GetBellNotificationCountQueryVariables>(GetBellNotificationCountDocument, options);
        }
export function useGetBellNotificationCountSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetBellNotificationCountQuery, GetBellNotificationCountQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetBellNotificationCountQuery, GetBellNotificationCountQueryVariables>(GetBellNotificationCountDocument, options);
        }
export type GetBellNotificationCountQueryHookResult = ReturnType<typeof useGetBellNotificationCountQuery>;
export type GetBellNotificationCountLazyQueryHookResult = ReturnType<typeof useGetBellNotificationCountLazyQuery>;
export type GetBellNotificationCountSuspenseQueryHookResult = ReturnType<typeof useGetBellNotificationCountSuspenseQuery>;
export type GetBellNotificationCountQueryResult = Apollo.QueryResult<GetBellNotificationCountQuery, GetBellNotificationCountQueryVariables>;
export const UpdatePolicyAcceptanceDocument = gql`
    mutation UpdatePolicyAcceptance($data: PolicyAcceptInput!) {
  updatePolicyAcceptance(data: $data) {
    result
    message
    data {
      id
      policy_name
      policy_name_hindi
      url
      is_active
    }
  }
}
    `;
export type UpdatePolicyAcceptanceMutationFn = Apollo.MutationFunction<UpdatePolicyAcceptanceMutation, UpdatePolicyAcceptanceMutationVariables>;

/**
 * __useUpdatePolicyAcceptanceMutation__
 *
 * To run a mutation, you first call `useUpdatePolicyAcceptanceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePolicyAcceptanceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePolicyAcceptanceMutation, { data, loading, error }] = useUpdatePolicyAcceptanceMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdatePolicyAcceptanceMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdatePolicyAcceptanceMutation, UpdatePolicyAcceptanceMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdatePolicyAcceptanceMutation, UpdatePolicyAcceptanceMutationVariables>(UpdatePolicyAcceptanceDocument, options);
      }
export type UpdatePolicyAcceptanceMutationHookResult = ReturnType<typeof useUpdatePolicyAcceptanceMutation>;
export type UpdatePolicyAcceptanceMutationResult = Apollo.MutationResult<UpdatePolicyAcceptanceMutation>;
export type UpdatePolicyAcceptanceMutationOptions = Apollo.BaseMutationOptions<UpdatePolicyAcceptanceMutation, UpdatePolicyAcceptanceMutationVariables>;
export const GetPoliciesWithUserStatusDocument = gql`
    query GetPoliciesWithUserStatus($userId: Int!) {
  getPoliciesWithUserStatus(user_id: $userId) {
    result
    message
    data {
      id
      policy_name
      policy_name_hindi
      url
      is_active
      created_at
      updated_at
      user_policy_tracking {
        created_at
        accepted
      }
    }
  }
}
    `;

/**
 * __useGetPoliciesWithUserStatusQuery__
 *
 * To run a query within a React component, call `useGetPoliciesWithUserStatusQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPoliciesWithUserStatusQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPoliciesWithUserStatusQuery({
 *   variables: {
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useGetPoliciesWithUserStatusQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetPoliciesWithUserStatusQuery, GetPoliciesWithUserStatusQueryVariables> & ({ variables: GetPoliciesWithUserStatusQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetPoliciesWithUserStatusQuery, GetPoliciesWithUserStatusQueryVariables>(GetPoliciesWithUserStatusDocument, options);
      }
export function useGetPoliciesWithUserStatusLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetPoliciesWithUserStatusQuery, GetPoliciesWithUserStatusQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetPoliciesWithUserStatusQuery, GetPoliciesWithUserStatusQueryVariables>(GetPoliciesWithUserStatusDocument, options);
        }
export function useGetPoliciesWithUserStatusSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetPoliciesWithUserStatusQuery, GetPoliciesWithUserStatusQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetPoliciesWithUserStatusQuery, GetPoliciesWithUserStatusQueryVariables>(GetPoliciesWithUserStatusDocument, options);
        }
export type GetPoliciesWithUserStatusQueryHookResult = ReturnType<typeof useGetPoliciesWithUserStatusQuery>;
export type GetPoliciesWithUserStatusLazyQueryHookResult = ReturnType<typeof useGetPoliciesWithUserStatusLazyQuery>;
export type GetPoliciesWithUserStatusSuspenseQueryHookResult = ReturnType<typeof useGetPoliciesWithUserStatusSuspenseQuery>;
export type GetPoliciesWithUserStatusQueryResult = Apollo.QueryResult<GetPoliciesWithUserStatusQuery, GetPoliciesWithUserStatusQueryVariables>;
export const UpdateProviderDocument = gql`
    mutation UpdateProvider($data: UpdateProviderInput) {
  updateProvider(data: $data) {
    result
    message
  }
}
    `;
export type UpdateProviderMutationFn = Apollo.MutationFunction<UpdateProviderMutation, UpdateProviderMutationVariables>;

/**
 * __useUpdateProviderMutation__
 *
 * To run a mutation, you first call `useUpdateProviderMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateProviderMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateProviderMutation, { data, loading, error }] = useUpdateProviderMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateProviderMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateProviderMutation, UpdateProviderMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateProviderMutation, UpdateProviderMutationVariables>(UpdateProviderDocument, options);
      }
export type UpdateProviderMutationHookResult = ReturnType<typeof useUpdateProviderMutation>;
export type UpdateProviderMutationResult = Apollo.MutationResult<UpdateProviderMutation>;
export type UpdateProviderMutationOptions = Apollo.BaseMutationOptions<UpdateProviderMutation, UpdateProviderMutationVariables>;
export const CreateProviderOrgDocument = gql`
    mutation CreateProviderOrg($data: CreateProviderOrgInput!) {
  createProviderOrg(data: $data) {
    result
    message
  }
}
    `;
export type CreateProviderOrgMutationFn = Apollo.MutationFunction<CreateProviderOrgMutation, CreateProviderOrgMutationVariables>;

/**
 * __useCreateProviderOrgMutation__
 *
 * To run a mutation, you first call `useCreateProviderOrgMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateProviderOrgMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createProviderOrgMutation, { data, loading, error }] = useCreateProviderOrgMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useCreateProviderOrgMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<CreateProviderOrgMutation, CreateProviderOrgMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<CreateProviderOrgMutation, CreateProviderOrgMutationVariables>(CreateProviderOrgDocument, options);
      }
export type CreateProviderOrgMutationHookResult = ReturnType<typeof useCreateProviderOrgMutation>;
export type CreateProviderOrgMutationResult = Apollo.MutationResult<CreateProviderOrgMutation>;
export type CreateProviderOrgMutationOptions = Apollo.BaseMutationOptions<CreateProviderOrgMutation, CreateProviderOrgMutationVariables>;
export const GetServiceTypesDocument = gql`
    query GetServiceTypes {
  getServiceTypes {
    id
    name
    is_active
  }
}
    `;

/**
 * __useGetServiceTypesQuery__
 *
 * To run a query within a React component, call `useGetServiceTypesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetServiceTypesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetServiceTypesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetServiceTypesQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetServiceTypesQuery, GetServiceTypesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetServiceTypesQuery, GetServiceTypesQueryVariables>(GetServiceTypesDocument, options);
      }
export function useGetServiceTypesLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetServiceTypesQuery, GetServiceTypesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetServiceTypesQuery, GetServiceTypesQueryVariables>(GetServiceTypesDocument, options);
        }
export function useGetServiceTypesSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetServiceTypesQuery, GetServiceTypesQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetServiceTypesQuery, GetServiceTypesQueryVariables>(GetServiceTypesDocument, options);
        }
export type GetServiceTypesQueryHookResult = ReturnType<typeof useGetServiceTypesQuery>;
export type GetServiceTypesLazyQueryHookResult = ReturnType<typeof useGetServiceTypesLazyQuery>;
export type GetServiceTypesSuspenseQueryHookResult = ReturnType<typeof useGetServiceTypesSuspenseQuery>;
export type GetServiceTypesQueryResult = Apollo.QueryResult<GetServiceTypesQuery, GetServiceTypesQueryVariables>;
export const GetProviderDocument = gql`
    query GetProvider {
  getProvider {
    company_type
    id
    meta
    name
    org_expertise_map {
      expertise {
        id
        name
      }
      team_member_count
    }
    org_owner {
      email
      name
      id
      onboarding_stage
      phone
      photoUrl
      meta
      user_professional_data {
        year_of_experience
      }
    }
    organization_service_type_map {
      service_type {
        id
        name
        is_active
      }
    }
    user {
      email
      name
      id
      onboarding_stage
      phone
      photoUrl
      meta
      user_professional_data {
        year_of_experience
      }
      expertise {
        id
        name
        organization_id
      }
    }
  }
}
    `;

/**
 * __useGetProviderQuery__
 *
 * To run a query within a React component, call `useGetProviderQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetProviderQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetProviderQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetProviderQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetProviderQuery, GetProviderQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetProviderQuery, GetProviderQueryVariables>(GetProviderDocument, options);
      }
export function useGetProviderLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetProviderQuery, GetProviderQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetProviderQuery, GetProviderQueryVariables>(GetProviderDocument, options);
        }
export function useGetProviderSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetProviderQuery, GetProviderQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetProviderQuery, GetProviderQueryVariables>(GetProviderDocument, options);
        }
export type GetProviderQueryHookResult = ReturnType<typeof useGetProviderQuery>;
export type GetProviderLazyQueryHookResult = ReturnType<typeof useGetProviderLazyQuery>;
export type GetProviderSuspenseQueryHookResult = ReturnType<typeof useGetProviderSuspenseQuery>;
export type GetProviderQueryResult = Apollo.QueryResult<GetProviderQuery, GetProviderQueryVariables>;
export const GetServiceProviderOnboardingAnalyticsDocument = gql`
    query GetServiceProviderOnboardingAnalytics {
  getServiceProviderOnboardingAnalytics {
    profileCompletion {
      percentage
      status
    }
    documentUpload {
      status
      total
      uploaded_count
    }
    testCompletion {
      total
      completed_count
      status
    }
    organization {
      id
      name
      org_owner_id
    }
  }
}
    `;

/**
 * __useGetServiceProviderOnboardingAnalyticsQuery__
 *
 * To run a query within a React component, call `useGetServiceProviderOnboardingAnalyticsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetServiceProviderOnboardingAnalyticsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetServiceProviderOnboardingAnalyticsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetServiceProviderOnboardingAnalyticsQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetServiceProviderOnboardingAnalyticsQuery, GetServiceProviderOnboardingAnalyticsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetServiceProviderOnboardingAnalyticsQuery, GetServiceProviderOnboardingAnalyticsQueryVariables>(GetServiceProviderOnboardingAnalyticsDocument, options);
      }
export function useGetServiceProviderOnboardingAnalyticsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetServiceProviderOnboardingAnalyticsQuery, GetServiceProviderOnboardingAnalyticsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetServiceProviderOnboardingAnalyticsQuery, GetServiceProviderOnboardingAnalyticsQueryVariables>(GetServiceProviderOnboardingAnalyticsDocument, options);
        }
export function useGetServiceProviderOnboardingAnalyticsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetServiceProviderOnboardingAnalyticsQuery, GetServiceProviderOnboardingAnalyticsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetServiceProviderOnboardingAnalyticsQuery, GetServiceProviderOnboardingAnalyticsQueryVariables>(GetServiceProviderOnboardingAnalyticsDocument, options);
        }
export type GetServiceProviderOnboardingAnalyticsQueryHookResult = ReturnType<typeof useGetServiceProviderOnboardingAnalyticsQuery>;
export type GetServiceProviderOnboardingAnalyticsLazyQueryHookResult = ReturnType<typeof useGetServiceProviderOnboardingAnalyticsLazyQuery>;
export type GetServiceProviderOnboardingAnalyticsSuspenseQueryHookResult = ReturnType<typeof useGetServiceProviderOnboardingAnalyticsSuspenseQuery>;
export type GetServiceProviderOnboardingAnalyticsQueryResult = Apollo.QueryResult<GetServiceProviderOnboardingAnalyticsQuery, GetServiceProviderOnboardingAnalyticsQueryVariables>;
export const UpdateTeamMemberDocument = gql`
    mutation UpdateTeamMember($data: UpdateTeamMemberInput) {
  updateTeamMember(data: $data) {
    result
    message
  }
}
    `;
export type UpdateTeamMemberMutationFn = Apollo.MutationFunction<UpdateTeamMemberMutation, UpdateTeamMemberMutationVariables>;

/**
 * __useUpdateTeamMemberMutation__
 *
 * To run a mutation, you first call `useUpdateTeamMemberMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateTeamMemberMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateTeamMemberMutation, { data, loading, error }] = useUpdateTeamMemberMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateTeamMemberMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateTeamMemberMutation, UpdateTeamMemberMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateTeamMemberMutation, UpdateTeamMemberMutationVariables>(UpdateTeamMemberDocument, options);
      }
export type UpdateTeamMemberMutationHookResult = ReturnType<typeof useUpdateTeamMemberMutation>;
export type UpdateTeamMemberMutationResult = Apollo.MutationResult<UpdateTeamMemberMutation>;
export type UpdateTeamMemberMutationOptions = Apollo.BaseMutationOptions<UpdateTeamMemberMutation, UpdateTeamMemberMutationVariables>;
export const SendRefferalInviteDocument = gql`
    mutation SendRefferalInvite($data: SendRefferalInviteInput!) {
  sendRefferalInvite(data: $data) {
    id
    name
    phone_number
    meta
    referrer {
      id
      name
    }
    created_at
    updated_at
  }
}
    `;
export type SendRefferalInviteMutationFn = Apollo.MutationFunction<SendRefferalInviteMutation, SendRefferalInviteMutationVariables>;

/**
 * __useSendRefferalInviteMutation__
 *
 * To run a mutation, you first call `useSendRefferalInviteMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSendRefferalInviteMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sendRefferalInviteMutation, { data, loading, error }] = useSendRefferalInviteMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useSendRefferalInviteMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<SendRefferalInviteMutation, SendRefferalInviteMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<SendRefferalInviteMutation, SendRefferalInviteMutationVariables>(SendRefferalInviteDocument, options);
      }
export type SendRefferalInviteMutationHookResult = ReturnType<typeof useSendRefferalInviteMutation>;
export type SendRefferalInviteMutationResult = Apollo.MutationResult<SendRefferalInviteMutation>;
export type SendRefferalInviteMutationOptions = Apollo.BaseMutationOptions<SendRefferalInviteMutation, SendRefferalInviteMutationVariables>;
export const GetMyUnregisteredReferralsDocument = gql`
    query GetMyUnregisteredReferrals($pagination: Pagination, $search: String) {
  getMyUnregisteredReferrals(pagination: $pagination, search: $search) {
    data {
      id
      name
      phone_number
      meta
      created_at
      updated_at
    }
    total_count
  }
}
    `;

/**
 * __useGetMyUnregisteredReferralsQuery__
 *
 * To run a query within a React component, call `useGetMyUnregisteredReferralsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMyUnregisteredReferralsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMyUnregisteredReferralsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      search: // value for 'search'
 *   },
 * });
 */
export function useGetMyUnregisteredReferralsQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetMyUnregisteredReferralsQuery, GetMyUnregisteredReferralsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetMyUnregisteredReferralsQuery, GetMyUnregisteredReferralsQueryVariables>(GetMyUnregisteredReferralsDocument, options);
      }
export function useGetMyUnregisteredReferralsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetMyUnregisteredReferralsQuery, GetMyUnregisteredReferralsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetMyUnregisteredReferralsQuery, GetMyUnregisteredReferralsQueryVariables>(GetMyUnregisteredReferralsDocument, options);
        }
export function useGetMyUnregisteredReferralsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetMyUnregisteredReferralsQuery, GetMyUnregisteredReferralsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetMyUnregisteredReferralsQuery, GetMyUnregisteredReferralsQueryVariables>(GetMyUnregisteredReferralsDocument, options);
        }
export type GetMyUnregisteredReferralsQueryHookResult = ReturnType<typeof useGetMyUnregisteredReferralsQuery>;
export type GetMyUnregisteredReferralsLazyQueryHookResult = ReturnType<typeof useGetMyUnregisteredReferralsLazyQuery>;
export type GetMyUnregisteredReferralsSuspenseQueryHookResult = ReturnType<typeof useGetMyUnregisteredReferralsSuspenseQuery>;
export type GetMyUnregisteredReferralsQueryResult = Apollo.QueryResult<GetMyUnregisteredReferralsQuery, GetMyUnregisteredReferralsQueryVariables>;
export const GenerateReferralCodeFrUserDocument = gql`
    query GenerateReferralCodeFrUser {
  generateReferralCodeFrUser
}
    `;

/**
 * __useGenerateReferralCodeFrUserQuery__
 *
 * To run a query within a React component, call `useGenerateReferralCodeFrUserQuery` and pass it any options that fit your needs.
 * When your component renders, `useGenerateReferralCodeFrUserQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGenerateReferralCodeFrUserQuery({
 *   variables: {
 *   },
 * });
 */
export function useGenerateReferralCodeFrUserQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GenerateReferralCodeFrUserQuery, GenerateReferralCodeFrUserQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GenerateReferralCodeFrUserQuery, GenerateReferralCodeFrUserQueryVariables>(GenerateReferralCodeFrUserDocument, options);
      }
export function useGenerateReferralCodeFrUserLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GenerateReferralCodeFrUserQuery, GenerateReferralCodeFrUserQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GenerateReferralCodeFrUserQuery, GenerateReferralCodeFrUserQueryVariables>(GenerateReferralCodeFrUserDocument, options);
        }
export function useGenerateReferralCodeFrUserSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GenerateReferralCodeFrUserQuery, GenerateReferralCodeFrUserQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GenerateReferralCodeFrUserQuery, GenerateReferralCodeFrUserQueryVariables>(GenerateReferralCodeFrUserDocument, options);
        }
export type GenerateReferralCodeFrUserQueryHookResult = ReturnType<typeof useGenerateReferralCodeFrUserQuery>;
export type GenerateReferralCodeFrUserLazyQueryHookResult = ReturnType<typeof useGenerateReferralCodeFrUserLazyQuery>;
export type GenerateReferralCodeFrUserSuspenseQueryHookResult = ReturnType<typeof useGenerateReferralCodeFrUserSuspenseQuery>;
export type GenerateReferralCodeFrUserQueryResult = Apollo.QueryResult<GenerateReferralCodeFrUserQuery, GenerateReferralCodeFrUserQueryVariables>;
export const GetMyReferralsDocument = gql`
    query GetMyReferrals($search: String, $pagination: Pagination) {
  getMyReferrals(search: $search, pagination: $pagination) {
    data {
      created_at
      status
      referrer_code
      referred_name
      referred_id
      referred_phone
      url
    }
    total_count
  }
}
    `;

/**
 * __useGetMyReferralsQuery__
 *
 * To run a query within a React component, call `useGetMyReferralsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMyReferralsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMyReferralsQuery({
 *   variables: {
 *      search: // value for 'search'
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useGetMyReferralsQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetMyReferralsQuery, GetMyReferralsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetMyReferralsQuery, GetMyReferralsQueryVariables>(GetMyReferralsDocument, options);
      }
export function useGetMyReferralsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetMyReferralsQuery, GetMyReferralsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetMyReferralsQuery, GetMyReferralsQueryVariables>(GetMyReferralsDocument, options);
        }
export function useGetMyReferralsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetMyReferralsQuery, GetMyReferralsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetMyReferralsQuery, GetMyReferralsQueryVariables>(GetMyReferralsDocument, options);
        }
export type GetMyReferralsQueryHookResult = ReturnType<typeof useGetMyReferralsQuery>;
export type GetMyReferralsLazyQueryHookResult = ReturnType<typeof useGetMyReferralsLazyQuery>;
export type GetMyReferralsSuspenseQueryHookResult = ReturnType<typeof useGetMyReferralsSuspenseQuery>;
export type GetMyReferralsQueryResult = Apollo.QueryResult<GetMyReferralsQuery, GetMyReferralsQueryVariables>;
export const CheckUnregisteredReferralDocument = gql`
    query CheckUnregisteredReferral($phone_number: String!) {
  checkUnregisteredReferral(phone_number: $phone_number) {
    is_referred
    referrers {
      id
      name
      referral_code
    }
  }
}
    `;

/**
 * __useCheckUnregisteredReferralQuery__
 *
 * To run a query within a React component, call `useCheckUnregisteredReferralQuery` and pass it any options that fit your needs.
 * When your component renders, `useCheckUnregisteredReferralQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCheckUnregisteredReferralQuery({
 *   variables: {
 *      phone_number: // value for 'phone_number'
 *   },
 * });
 */
export function useCheckUnregisteredReferralQuery(baseOptions: ApolloReactHooks.QueryHookOptions<CheckUnregisteredReferralQuery, CheckUnregisteredReferralQueryVariables> & ({ variables: CheckUnregisteredReferralQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<CheckUnregisteredReferralQuery, CheckUnregisteredReferralQueryVariables>(CheckUnregisteredReferralDocument, options);
      }
export function useCheckUnregisteredReferralLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<CheckUnregisteredReferralQuery, CheckUnregisteredReferralQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<CheckUnregisteredReferralQuery, CheckUnregisteredReferralQueryVariables>(CheckUnregisteredReferralDocument, options);
        }
export function useCheckUnregisteredReferralSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<CheckUnregisteredReferralQuery, CheckUnregisteredReferralQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<CheckUnregisteredReferralQuery, CheckUnregisteredReferralQueryVariables>(CheckUnregisteredReferralDocument, options);
        }
export type CheckUnregisteredReferralQueryHookResult = ReturnType<typeof useCheckUnregisteredReferralQuery>;
export type CheckUnregisteredReferralLazyQueryHookResult = ReturnType<typeof useCheckUnregisteredReferralLazyQuery>;
export type CheckUnregisteredReferralSuspenseQueryHookResult = ReturnType<typeof useCheckUnregisteredReferralSuspenseQuery>;
export type CheckUnregisteredReferralQueryResult = Apollo.QueryResult<CheckUnregisteredReferralQuery, CheckUnregisteredReferralQueryVariables>;
export const CreateTeamMemberInvitationDocument = gql`
    mutation CreateTeamMemberInvitation($data: TeamMemberInvitationInput!) {
  createTeamMemberInvitation(data: $data) {
    message
    result
  }
}
    `;
export type CreateTeamMemberInvitationMutationFn = Apollo.MutationFunction<CreateTeamMemberInvitationMutation, CreateTeamMemberInvitationMutationVariables>;

/**
 * __useCreateTeamMemberInvitationMutation__
 *
 * To run a mutation, you first call `useCreateTeamMemberInvitationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateTeamMemberInvitationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createTeamMemberInvitationMutation, { data, loading, error }] = useCreateTeamMemberInvitationMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useCreateTeamMemberInvitationMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<CreateTeamMemberInvitationMutation, CreateTeamMemberInvitationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<CreateTeamMemberInvitationMutation, CreateTeamMemberInvitationMutationVariables>(CreateTeamMemberInvitationDocument, options);
      }
export type CreateTeamMemberInvitationMutationHookResult = ReturnType<typeof useCreateTeamMemberInvitationMutation>;
export type CreateTeamMemberInvitationMutationResult = Apollo.MutationResult<CreateTeamMemberInvitationMutation>;
export type CreateTeamMemberInvitationMutationOptions = Apollo.BaseMutationOptions<CreateTeamMemberInvitationMutation, CreateTeamMemberInvitationMutationVariables>;
export const GetTeamMemberInvitationsDocument = gql`
    query GetTeamMemberInvitations($filter: TeamMemberInvitationFilter) {
  getTeamMemberInvitations(filter: $filter) {
    contact {
      id
      name
      phone
      meta
      image_url
    }
    status
  }
}
    `;

/**
 * __useGetTeamMemberInvitationsQuery__
 *
 * To run a query within a React component, call `useGetTeamMemberInvitationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTeamMemberInvitationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTeamMemberInvitationsQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetTeamMemberInvitationsQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetTeamMemberInvitationsQuery, GetTeamMemberInvitationsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetTeamMemberInvitationsQuery, GetTeamMemberInvitationsQueryVariables>(GetTeamMemberInvitationsDocument, options);
      }
export function useGetTeamMemberInvitationsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetTeamMemberInvitationsQuery, GetTeamMemberInvitationsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetTeamMemberInvitationsQuery, GetTeamMemberInvitationsQueryVariables>(GetTeamMemberInvitationsDocument, options);
        }
export function useGetTeamMemberInvitationsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetTeamMemberInvitationsQuery, GetTeamMemberInvitationsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetTeamMemberInvitationsQuery, GetTeamMemberInvitationsQueryVariables>(GetTeamMemberInvitationsDocument, options);
        }
export type GetTeamMemberInvitationsQueryHookResult = ReturnType<typeof useGetTeamMemberInvitationsQuery>;
export type GetTeamMemberInvitationsLazyQueryHookResult = ReturnType<typeof useGetTeamMemberInvitationsLazyQuery>;
export type GetTeamMemberInvitationsSuspenseQueryHookResult = ReturnType<typeof useGetTeamMemberInvitationsSuspenseQuery>;
export type GetTeamMemberInvitationsQueryResult = Apollo.QueryResult<GetTeamMemberInvitationsQuery, GetTeamMemberInvitationsQueryVariables>;
export const GetTeamMemberContactsDocument = gql`
    query GetTeamMemberContacts($limit: Int, $page: Int, $search: String, $filter: TeamMemberInvitationFilter) {
  getContactsWithInvitationStatus(
    limit: $limit
    page: $page
    search: $search
    filter: $filter
  ) {
    id
    name
    phone
    image_url
    invitations {
      status
      contact {
        name
        phone
      }
    }
  }
}
    `;

/**
 * __useGetTeamMemberContactsQuery__
 *
 * To run a query within a React component, call `useGetTeamMemberContactsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTeamMemberContactsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTeamMemberContactsQuery({
 *   variables: {
 *      limit: // value for 'limit'
 *      page: // value for 'page'
 *      search: // value for 'search'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetTeamMemberContactsQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetTeamMemberContactsQuery, GetTeamMemberContactsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetTeamMemberContactsQuery, GetTeamMemberContactsQueryVariables>(GetTeamMemberContactsDocument, options);
      }
export function useGetTeamMemberContactsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetTeamMemberContactsQuery, GetTeamMemberContactsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetTeamMemberContactsQuery, GetTeamMemberContactsQueryVariables>(GetTeamMemberContactsDocument, options);
        }
export function useGetTeamMemberContactsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetTeamMemberContactsQuery, GetTeamMemberContactsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetTeamMemberContactsQuery, GetTeamMemberContactsQueryVariables>(GetTeamMemberContactsDocument, options);
        }
export type GetTeamMemberContactsQueryHookResult = ReturnType<typeof useGetTeamMemberContactsQuery>;
export type GetTeamMemberContactsLazyQueryHookResult = ReturnType<typeof useGetTeamMemberContactsLazyQuery>;
export type GetTeamMemberContactsSuspenseQueryHookResult = ReturnType<typeof useGetTeamMemberContactsSuspenseQuery>;
export type GetTeamMemberContactsQueryResult = Apollo.QueryResult<GetTeamMemberContactsQuery, GetTeamMemberContactsQueryVariables>;
export const GetUserCoursesDataDocument = gql`
    query GetUserCoursesData($filter: GetUserCoursesDataFilter) {
  getUserCoursesData(filter: $filter) {
    id
    progress_status
    course {
      id
      name
      description
      duration
      objective
      image
      chapter {
        lesson {
          title
          id
          objective
          content_id
          duration
        }
        id
        created_at
        title
        order
        duration
      }
      _count {
        chapter
      }
      course_assessment_map {
        assignment_config {
          name
          id
        }
      }
    }
    completed_percentage
    course_user_assignment_map {
      assignment {
        status
      }
    }
    course_completed
    training_completed
  }
}
    `;

/**
 * __useGetUserCoursesDataQuery__
 *
 * To run a query within a React component, call `useGetUserCoursesDataQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserCoursesDataQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserCoursesDataQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetUserCoursesDataQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetUserCoursesDataQuery, GetUserCoursesDataQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUserCoursesDataQuery, GetUserCoursesDataQueryVariables>(GetUserCoursesDataDocument, options);
      }
export function useGetUserCoursesDataLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUserCoursesDataQuery, GetUserCoursesDataQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUserCoursesDataQuery, GetUserCoursesDataQueryVariables>(GetUserCoursesDataDocument, options);
        }
export function useGetUserCoursesDataSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUserCoursesDataQuery, GetUserCoursesDataQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUserCoursesDataQuery, GetUserCoursesDataQueryVariables>(GetUserCoursesDataDocument, options);
        }
export type GetUserCoursesDataQueryHookResult = ReturnType<typeof useGetUserCoursesDataQuery>;
export type GetUserCoursesDataLazyQueryHookResult = ReturnType<typeof useGetUserCoursesDataLazyQuery>;
export type GetUserCoursesDataSuspenseQueryHookResult = ReturnType<typeof useGetUserCoursesDataSuspenseQuery>;
export type GetUserCoursesDataQueryResult = Apollo.QueryResult<GetUserCoursesDataQuery, GetUserCoursesDataQueryVariables>;
export const GetUserCourseDetailsDocument = gql`
    query GetUserCourseDetails($courseUserId: String!) {
  getUserCourseDetails(course_user_id: $courseUserId) {
    course {
      id
      name
      description
      duration
      objective
      image
      type
      method
      meta
      published
      published_on
      internal_course_id
      status
      rating
      business_vertical
      is_active
      chapter {
        lesson {
          title
          id
          objective
          content_id
          content {
            title
            video_content {
              duration
              id
              video_url
            }
          }
          duration
          user_lesson_progress_track {
            id
            is_completed
            is_unlocked
            is_current
            time_spent
          }
        }
        id
        created_at
        title
        order
        duration
        user_chapter_progress_track {
          id
          is_completed
          is_current
          is_unlocked
          time_spent
        }
      }
      _count {
        chapter
        user_chapter_progress_track
        course_assessment_map
      }
      course_assessment_map {
        assignment_config {
          name
          id
        }
      }
      course_designation_map {
        designation {
          id
          name
        }
      }
    }
    course_completed_count
    course_user_assignment_map {
      assignment {
        status
      }
      assignment_config {
        id
        name
      }
    }
    course_completed
    training_completed
    _count {
      course_user_assignment_map
    }
  }
}
    `;

/**
 * __useGetUserCourseDetailsQuery__
 *
 * To run a query within a React component, call `useGetUserCourseDetailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserCourseDetailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserCourseDetailsQuery({
 *   variables: {
 *      courseUserId: // value for 'courseUserId'
 *   },
 * });
 */
export function useGetUserCourseDetailsQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetUserCourseDetailsQuery, GetUserCourseDetailsQueryVariables> & ({ variables: GetUserCourseDetailsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUserCourseDetailsQuery, GetUserCourseDetailsQueryVariables>(GetUserCourseDetailsDocument, options);
      }
export function useGetUserCourseDetailsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUserCourseDetailsQuery, GetUserCourseDetailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUserCourseDetailsQuery, GetUserCourseDetailsQueryVariables>(GetUserCourseDetailsDocument, options);
        }
export function useGetUserCourseDetailsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUserCourseDetailsQuery, GetUserCourseDetailsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUserCourseDetailsQuery, GetUserCourseDetailsQueryVariables>(GetUserCourseDetailsDocument, options);
        }
export type GetUserCourseDetailsQueryHookResult = ReturnType<typeof useGetUserCourseDetailsQuery>;
export type GetUserCourseDetailsLazyQueryHookResult = ReturnType<typeof useGetUserCourseDetailsLazyQuery>;
export type GetUserCourseDetailsSuspenseQueryHookResult = ReturnType<typeof useGetUserCourseDetailsSuspenseQuery>;
export type GetUserCourseDetailsQueryResult = Apollo.QueryResult<GetUserCourseDetailsQuery, GetUserCourseDetailsQueryVariables>;
export const UpdateVideoWatchTimeDocument = gql`
    mutation UpdateVideoWatchTime($contentId: String!, $videoProgressInSeconds: Int!, $isCompleted: Boolean, $userCourseId: String!) {
  updateVideoWatchTime(
    content_id: $contentId
    video_progress_in_seconds: $videoProgressInSeconds
    is_completed: $isCompleted
    user_course_id: $userCourseId
  ) {
    is_completed
    progress
  }
}
    `;
export type UpdateVideoWatchTimeMutationFn = Apollo.MutationFunction<UpdateVideoWatchTimeMutation, UpdateVideoWatchTimeMutationVariables>;

/**
 * __useUpdateVideoWatchTimeMutation__
 *
 * To run a mutation, you first call `useUpdateVideoWatchTimeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateVideoWatchTimeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateVideoWatchTimeMutation, { data, loading, error }] = useUpdateVideoWatchTimeMutation({
 *   variables: {
 *      contentId: // value for 'contentId'
 *      videoProgressInSeconds: // value for 'videoProgressInSeconds'
 *      isCompleted: // value for 'isCompleted'
 *      userCourseId: // value for 'userCourseId'
 *   },
 * });
 */
export function useUpdateVideoWatchTimeMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateVideoWatchTimeMutation, UpdateVideoWatchTimeMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateVideoWatchTimeMutation, UpdateVideoWatchTimeMutationVariables>(UpdateVideoWatchTimeDocument, options);
      }
export type UpdateVideoWatchTimeMutationHookResult = ReturnType<typeof useUpdateVideoWatchTimeMutation>;
export type UpdateVideoWatchTimeMutationResult = Apollo.MutationResult<UpdateVideoWatchTimeMutation>;
export type UpdateVideoWatchTimeMutationOptions = Apollo.BaseMutationOptions<UpdateVideoWatchTimeMutation, UpdateVideoWatchTimeMutationVariables>;
export const GetTrainingVideoWatchTimeDocument = gql`
    query GetTrainingVideoWatchTime($contentId: String!) {
  getVideoWatchTime(content_id: $contentId) {
    is_completed
    progress
  }
}
    `;

/**
 * __useGetTrainingVideoWatchTimeQuery__
 *
 * To run a query within a React component, call `useGetTrainingVideoWatchTimeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTrainingVideoWatchTimeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTrainingVideoWatchTimeQuery({
 *   variables: {
 *      contentId: // value for 'contentId'
 *   },
 * });
 */
export function useGetTrainingVideoWatchTimeQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetTrainingVideoWatchTimeQuery, GetTrainingVideoWatchTimeQueryVariables> & ({ variables: GetTrainingVideoWatchTimeQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetTrainingVideoWatchTimeQuery, GetTrainingVideoWatchTimeQueryVariables>(GetTrainingVideoWatchTimeDocument, options);
      }
export function useGetTrainingVideoWatchTimeLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetTrainingVideoWatchTimeQuery, GetTrainingVideoWatchTimeQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetTrainingVideoWatchTimeQuery, GetTrainingVideoWatchTimeQueryVariables>(GetTrainingVideoWatchTimeDocument, options);
        }
export function useGetTrainingVideoWatchTimeSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetTrainingVideoWatchTimeQuery, GetTrainingVideoWatchTimeQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetTrainingVideoWatchTimeQuery, GetTrainingVideoWatchTimeQueryVariables>(GetTrainingVideoWatchTimeDocument, options);
        }
export type GetTrainingVideoWatchTimeQueryHookResult = ReturnType<typeof useGetTrainingVideoWatchTimeQuery>;
export type GetTrainingVideoWatchTimeLazyQueryHookResult = ReturnType<typeof useGetTrainingVideoWatchTimeLazyQuery>;
export type GetTrainingVideoWatchTimeSuspenseQueryHookResult = ReturnType<typeof useGetTrainingVideoWatchTimeSuspenseQuery>;
export type GetTrainingVideoWatchTimeQueryResult = Apollo.QueryResult<GetTrainingVideoWatchTimeQuery, GetTrainingVideoWatchTimeQueryVariables>;
export const UpdateUserWatchProgressDocument = gql`
    mutation UpdateUserWatchProgress($data: updateUserWatchProgressInputData) {
  updateUserWatchProgress(data: $data) {
    message
    result
  }
}
    `;
export type UpdateUserWatchProgressMutationFn = Apollo.MutationFunction<UpdateUserWatchProgressMutation, UpdateUserWatchProgressMutationVariables>;

/**
 * __useUpdateUserWatchProgressMutation__
 *
 * To run a mutation, you first call `useUpdateUserWatchProgressMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserWatchProgressMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserWatchProgressMutation, { data, loading, error }] = useUpdateUserWatchProgressMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateUserWatchProgressMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateUserWatchProgressMutation, UpdateUserWatchProgressMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateUserWatchProgressMutation, UpdateUserWatchProgressMutationVariables>(UpdateUserWatchProgressDocument, options);
      }
export type UpdateUserWatchProgressMutationHookResult = ReturnType<typeof useUpdateUserWatchProgressMutation>;
export type UpdateUserWatchProgressMutationResult = Apollo.MutationResult<UpdateUserWatchProgressMutation>;
export type UpdateUserWatchProgressMutationOptions = Apollo.BaseMutationOptions<UpdateUserWatchProgressMutation, UpdateUserWatchProgressMutationVariables>;
export const UserTrainingStatusDocument = gql`
    subscription UserTrainingStatus($userId: Int!) {
  userTrainingStatus(user_id: $userId) {
    userTrainingStatus
  }
}
    `;

/**
 * __useUserTrainingStatusSubscription__
 *
 * To run a query within a React component, call `useUserTrainingStatusSubscription` and pass it any options that fit your needs.
 * When your component renders, `useUserTrainingStatusSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUserTrainingStatusSubscription({
 *   variables: {
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useUserTrainingStatusSubscription(baseOptions: ApolloReactHooks.SubscriptionHookOptions<UserTrainingStatusSubscription, UserTrainingStatusSubscriptionVariables> & ({ variables: UserTrainingStatusSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useSubscription<UserTrainingStatusSubscription, UserTrainingStatusSubscriptionVariables>(UserTrainingStatusDocument, options);
      }
export type UserTrainingStatusSubscriptionHookResult = ReturnType<typeof useUserTrainingStatusSubscription>;
export type UserTrainingStatusSubscriptionResult = Apollo.SubscriptionResult<UserTrainingStatusSubscription>;
export const UserDetailsDocument = gql`
    query UserDetails {
  userDetail {
    data {
      onboarded
      expertise {
        name
        icon
        id
      }
      id
      location_id
      doc_verification_state
      meta
      name
      user_state
      remark
      gender
      phone
      photoUrl
      onboarding_stage
      email
      phoneVerified
      transfer_to_tms_done
      organization {
        name
        org_owner_id
      }
      location {
        id
        state
        pincode
        landmark
        work_address
        city
      }
      user_training_approval_status
    }
  }
}
    `;

/**
 * __useUserDetailsQuery__
 *
 * To run a query within a React component, call `useUserDetailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useUserDetailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUserDetailsQuery({
 *   variables: {
 *   },
 * });
 */
export function useUserDetailsQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<UserDetailsQuery, UserDetailsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<UserDetailsQuery, UserDetailsQueryVariables>(UserDetailsDocument, options);
      }
export function useUserDetailsLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<UserDetailsQuery, UserDetailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<UserDetailsQuery, UserDetailsQueryVariables>(UserDetailsDocument, options);
        }
export function useUserDetailsSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<UserDetailsQuery, UserDetailsQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<UserDetailsQuery, UserDetailsQueryVariables>(UserDetailsDocument, options);
        }
export type UserDetailsQueryHookResult = ReturnType<typeof useUserDetailsQuery>;
export type UserDetailsLazyQueryHookResult = ReturnType<typeof useUserDetailsLazyQuery>;
export type UserDetailsSuspenseQueryHookResult = ReturnType<typeof useUserDetailsSuspenseQuery>;
export type UserDetailsQueryResult = Apollo.QueryResult<UserDetailsQuery, UserDetailsQueryVariables>;
export const UpdateUserDocument = gql`
    mutation UpdateUser($data: UpdateUserInput) {
  UpdateUser(data: $data) {
    data {
      name
    }
    message
    result
  }
}
    `;
export type UpdateUserMutationFn = Apollo.MutationFunction<UpdateUserMutation, UpdateUserMutationVariables>;

/**
 * __useUpdateUserMutation__
 *
 * To run a mutation, you first call `useUpdateUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserMutation, { data, loading, error }] = useUpdateUserMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateUserMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateUserMutation, UpdateUserMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateUserMutation, UpdateUserMutationVariables>(UpdateUserDocument, options);
      }
export type UpdateUserMutationHookResult = ReturnType<typeof useUpdateUserMutation>;
export type UpdateUserMutationResult = Apollo.MutationResult<UpdateUserMutation>;
export type UpdateUserMutationOptions = Apollo.BaseMutationOptions<UpdateUserMutation, UpdateUserMutationVariables>;
export const SendOtpDocument = gql`
    mutation SendOtp($phone: String) {
  sendOtp(phone: $phone) {
    message
    result
    type
  }
}
    `;
export type SendOtpMutationFn = Apollo.MutationFunction<SendOtpMutation, SendOtpMutationVariables>;

/**
 * __useSendOtpMutation__
 *
 * To run a mutation, you first call `useSendOtpMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSendOtpMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sendOtpMutation, { data, loading, error }] = useSendOtpMutation({
 *   variables: {
 *      phone: // value for 'phone'
 *   },
 * });
 */
export function useSendOtpMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<SendOtpMutation, SendOtpMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<SendOtpMutation, SendOtpMutationVariables>(SendOtpDocument, options);
      }
export type SendOtpMutationHookResult = ReturnType<typeof useSendOtpMutation>;
export type SendOtpMutationResult = Apollo.MutationResult<SendOtpMutation>;
export type SendOtpMutationOptions = Apollo.BaseMutationOptions<SendOtpMutation, SendOtpMutationVariables>;
export const RegisterUserDocument = gql`
    mutation RegisterUser($data: RegisterInput!) {
  registerUser(data: $data) {
    result
    message
    token
    user_type
    registration_type
    meta
    is_invited
  }
}
    `;
export type RegisterUserMutationFn = Apollo.MutationFunction<RegisterUserMutation, RegisterUserMutationVariables>;

/**
 * __useRegisterUserMutation__
 *
 * To run a mutation, you first call `useRegisterUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRegisterUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [registerUserMutation, { data, loading, error }] = useRegisterUserMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useRegisterUserMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<RegisterUserMutation, RegisterUserMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<RegisterUserMutation, RegisterUserMutationVariables>(RegisterUserDocument, options);
      }
export type RegisterUserMutationHookResult = ReturnType<typeof useRegisterUserMutation>;
export type RegisterUserMutationResult = Apollo.MutationResult<RegisterUserMutation>;
export type RegisterUserMutationOptions = Apollo.BaseMutationOptions<RegisterUserMutation, RegisterUserMutationVariables>;
export const VerifyPhoneNumberDocument = gql`
    mutation VerifyPhoneNumber($otp: String) {
  verifyPhoneNumber(otp: $otp) {
    result
    message
  }
}
    `;
export type VerifyPhoneNumberMutationFn = Apollo.MutationFunction<VerifyPhoneNumberMutation, VerifyPhoneNumberMutationVariables>;

/**
 * __useVerifyPhoneNumberMutation__
 *
 * To run a mutation, you first call `useVerifyPhoneNumberMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useVerifyPhoneNumberMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [verifyPhoneNumberMutation, { data, loading, error }] = useVerifyPhoneNumberMutation({
 *   variables: {
 *      otp: // value for 'otp'
 *   },
 * });
 */
export function useVerifyPhoneNumberMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<VerifyPhoneNumberMutation, VerifyPhoneNumberMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<VerifyPhoneNumberMutation, VerifyPhoneNumberMutationVariables>(VerifyPhoneNumberDocument, options);
      }
export type VerifyPhoneNumberMutationHookResult = ReturnType<typeof useVerifyPhoneNumberMutation>;
export type VerifyPhoneNumberMutationResult = Apollo.MutationResult<VerifyPhoneNumberMutation>;
export type VerifyPhoneNumberMutationOptions = Apollo.BaseMutationOptions<VerifyPhoneNumberMutation, VerifyPhoneNumberMutationVariables>;
export const CreateServiceProviderAccountDocument = gql`
    mutation CreateServiceProviderAccount($data: CreateOrganizationData) {
  signUpOrganization(data: $data) {
    result
    token
    message
  }
}
    `;
export type CreateServiceProviderAccountMutationFn = Apollo.MutationFunction<CreateServiceProviderAccountMutation, CreateServiceProviderAccountMutationVariables>;

/**
 * __useCreateServiceProviderAccountMutation__
 *
 * To run a mutation, you first call `useCreateServiceProviderAccountMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateServiceProviderAccountMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createServiceProviderAccountMutation, { data, loading, error }] = useCreateServiceProviderAccountMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useCreateServiceProviderAccountMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<CreateServiceProviderAccountMutation, CreateServiceProviderAccountMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<CreateServiceProviderAccountMutation, CreateServiceProviderAccountMutationVariables>(CreateServiceProviderAccountDocument, options);
      }
export type CreateServiceProviderAccountMutationHookResult = ReturnType<typeof useCreateServiceProviderAccountMutation>;
export type CreateServiceProviderAccountMutationResult = Apollo.MutationResult<CreateServiceProviderAccountMutation>;
export type CreateServiceProviderAccountMutationOptions = Apollo.BaseMutationOptions<CreateServiceProviderAccountMutation, CreateServiceProviderAccountMutationVariables>;
export const SignInOrganizationDocument = gql`
    mutation SignInOrganization($phoneNumber: String!, $password: String!, $otp: String!) {
  signInOrganization(phoneNumber: $phoneNumber, password: $password, otp: $otp) {
    message
    result
    token
  }
}
    `;
export type SignInOrganizationMutationFn = Apollo.MutationFunction<SignInOrganizationMutation, SignInOrganizationMutationVariables>;

/**
 * __useSignInOrganizationMutation__
 *
 * To run a mutation, you first call `useSignInOrganizationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSignInOrganizationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [signInOrganizationMutation, { data, loading, error }] = useSignInOrganizationMutation({
 *   variables: {
 *      phoneNumber: // value for 'phoneNumber'
 *      password: // value for 'password'
 *      otp: // value for 'otp'
 *   },
 * });
 */
export function useSignInOrganizationMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<SignInOrganizationMutation, SignInOrganizationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<SignInOrganizationMutation, SignInOrganizationMutationVariables>(SignInOrganizationDocument, options);
      }
export type SignInOrganizationMutationHookResult = ReturnType<typeof useSignInOrganizationMutation>;
export type SignInOrganizationMutationResult = Apollo.MutationResult<SignInOrganizationMutation>;
export type SignInOrganizationMutationOptions = Apollo.BaseMutationOptions<SignInOrganizationMutation, SignInOrganizationMutationVariables>;
export const SignUpOrganizationDocument = gql`
    mutation SignUpOrganization($data: UpdateOrganizationInput, $updateUserData2: UpdateUserInput) {
  updateOrganization(data: $data) {
    message
    result
  }
  UpdateUser(data: $updateUserData2) {
    data {
      location_id
    }
  }
}
    `;
export type SignUpOrganizationMutationFn = Apollo.MutationFunction<SignUpOrganizationMutation, SignUpOrganizationMutationVariables>;

/**
 * __useSignUpOrganizationMutation__
 *
 * To run a mutation, you first call `useSignUpOrganizationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSignUpOrganizationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [signUpOrganizationMutation, { data, loading, error }] = useSignUpOrganizationMutation({
 *   variables: {
 *      data: // value for 'data'
 *      updateUserData2: // value for 'updateUserData2'
 *   },
 * });
 */
export function useSignUpOrganizationMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<SignUpOrganizationMutation, SignUpOrganizationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<SignUpOrganizationMutation, SignUpOrganizationMutationVariables>(SignUpOrganizationDocument, options);
      }
export type SignUpOrganizationMutationHookResult = ReturnType<typeof useSignUpOrganizationMutation>;
export type SignUpOrganizationMutationResult = Apollo.MutationResult<SignUpOrganizationMutation>;
export type SignUpOrganizationMutationOptions = Apollo.BaseMutationOptions<SignUpOrganizationMutation, SignUpOrganizationMutationVariables>;
export const GenerateReferralCodeDocument = gql`
    query GenerateReferralCode {
  generateReferralCodeFrUser
}
    `;

/**
 * __useGenerateReferralCodeQuery__
 *
 * To run a query within a React component, call `useGenerateReferralCodeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGenerateReferralCodeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGenerateReferralCodeQuery({
 *   variables: {
 *   },
 * });
 */
export function useGenerateReferralCodeQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GenerateReferralCodeQuery, GenerateReferralCodeQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GenerateReferralCodeQuery, GenerateReferralCodeQueryVariables>(GenerateReferralCodeDocument, options);
      }
export function useGenerateReferralCodeLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GenerateReferralCodeQuery, GenerateReferralCodeQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GenerateReferralCodeQuery, GenerateReferralCodeQueryVariables>(GenerateReferralCodeDocument, options);
        }
export function useGenerateReferralCodeSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GenerateReferralCodeQuery, GenerateReferralCodeQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GenerateReferralCodeQuery, GenerateReferralCodeQueryVariables>(GenerateReferralCodeDocument, options);
        }
export type GenerateReferralCodeQueryHookResult = ReturnType<typeof useGenerateReferralCodeQuery>;
export type GenerateReferralCodeLazyQueryHookResult = ReturnType<typeof useGenerateReferralCodeLazyQuery>;
export type GenerateReferralCodeSuspenseQueryHookResult = ReturnType<typeof useGenerateReferralCodeSuspenseQuery>;
export type GenerateReferralCodeQueryResult = Apollo.QueryResult<GenerateReferralCodeQuery, GenerateReferralCodeQueryVariables>;
export const UpdateUserMetaDocument = gql`
    mutation UpdateUserMeta($data: UserMetaInput) {
  updateUserMeta(data: $data)
}
    `;
export type UpdateUserMetaMutationFn = Apollo.MutationFunction<UpdateUserMetaMutation, UpdateUserMetaMutationVariables>;

/**
 * __useUpdateUserMetaMutation__
 *
 * To run a mutation, you first call `useUpdateUserMetaMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserMetaMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserMetaMutation, { data, loading, error }] = useUpdateUserMetaMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateUserMetaMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateUserMetaMutation, UpdateUserMetaMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateUserMetaMutation, UpdateUserMetaMutationVariables>(UpdateUserMetaDocument, options);
      }
export type UpdateUserMetaMutationHookResult = ReturnType<typeof useUpdateUserMetaMutation>;
export type UpdateUserMetaMutationResult = Apollo.MutationResult<UpdateUserMetaMutation>;
export type UpdateUserMetaMutationOptions = Apollo.BaseMutationOptions<UpdateUserMetaMutation, UpdateUserMetaMutationVariables>;
export const GetUserReferralDataDocument = gql`
    query GetUserReferralData {
  getUserReferralData {
    available_points
    referral_code
    referred_to {
      name
      id
      user_referred_to {
        referral_code
      }
    }
    referred_by {
      name
      id
      user_referred_to {
        referral_code
      }
    }
    updated_at
    created_at
  }
}
    `;

/**
 * __useGetUserReferralDataQuery__
 *
 * To run a query within a React component, call `useGetUserReferralDataQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserReferralDataQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserReferralDataQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetUserReferralDataQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetUserReferralDataQuery, GetUserReferralDataQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUserReferralDataQuery, GetUserReferralDataQueryVariables>(GetUserReferralDataDocument, options);
      }
export function useGetUserReferralDataLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUserReferralDataQuery, GetUserReferralDataQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUserReferralDataQuery, GetUserReferralDataQueryVariables>(GetUserReferralDataDocument, options);
        }
export function useGetUserReferralDataSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUserReferralDataQuery, GetUserReferralDataQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUserReferralDataQuery, GetUserReferralDataQueryVariables>(GetUserReferralDataDocument, options);
        }
export type GetUserReferralDataQueryHookResult = ReturnType<typeof useGetUserReferralDataQuery>;
export type GetUserReferralDataLazyQueryHookResult = ReturnType<typeof useGetUserReferralDataLazyQuery>;
export type GetUserReferralDataSuspenseQueryHookResult = ReturnType<typeof useGetUserReferralDataSuspenseQuery>;
export type GetUserReferralDataQueryResult = Apollo.QueryResult<GetUserReferralDataQuery, GetUserReferralDataQueryVariables>;
export const GetAllFeedbacksDocument = gql`
    query GetAllFeedbacks($filter: FeedbackFormFilter, $search: String, $pagination: Pagination) {
  getAllFeedbacks(filter: $filter, search: $search, pagination: $pagination) {
    interview_state
    user {
      name
      phone
      id
    }
    feedback_result {
      id
      score
      comments
    }
    feedback_state
  }
}
    `;

/**
 * __useGetAllFeedbacksQuery__
 *
 * To run a query within a React component, call `useGetAllFeedbacksQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAllFeedbacksQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAllFeedbacksQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *      search: // value for 'search'
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useGetAllFeedbacksQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetAllFeedbacksQuery, GetAllFeedbacksQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetAllFeedbacksQuery, GetAllFeedbacksQueryVariables>(GetAllFeedbacksDocument, options);
      }
export function useGetAllFeedbacksLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetAllFeedbacksQuery, GetAllFeedbacksQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetAllFeedbacksQuery, GetAllFeedbacksQueryVariables>(GetAllFeedbacksDocument, options);
        }
export function useGetAllFeedbacksSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetAllFeedbacksQuery, GetAllFeedbacksQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetAllFeedbacksQuery, GetAllFeedbacksQueryVariables>(GetAllFeedbacksDocument, options);
        }
export type GetAllFeedbacksQueryHookResult = ReturnType<typeof useGetAllFeedbacksQuery>;
export type GetAllFeedbacksLazyQueryHookResult = ReturnType<typeof useGetAllFeedbacksLazyQuery>;
export type GetAllFeedbacksSuspenseQueryHookResult = ReturnType<typeof useGetAllFeedbacksSuspenseQuery>;
export type GetAllFeedbacksQueryResult = Apollo.QueryResult<GetAllFeedbacksQuery, GetAllFeedbacksQueryVariables>;
export const UserProfileCompetionPercentageDocument = gql`
    query UserProfileCompetionPercentage {
  getUserProfileCompletionPercentage {
    data
    message
    result
  }
}
    `;

/**
 * __useUserProfileCompetionPercentageQuery__
 *
 * To run a query within a React component, call `useUserProfileCompetionPercentageQuery` and pass it any options that fit your needs.
 * When your component renders, `useUserProfileCompetionPercentageQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUserProfileCompetionPercentageQuery({
 *   variables: {
 *   },
 * });
 */
export function useUserProfileCompetionPercentageQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<UserProfileCompetionPercentageQuery, UserProfileCompetionPercentageQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<UserProfileCompetionPercentageQuery, UserProfileCompetionPercentageQueryVariables>(UserProfileCompetionPercentageDocument, options);
      }
export function useUserProfileCompetionPercentageLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<UserProfileCompetionPercentageQuery, UserProfileCompetionPercentageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<UserProfileCompetionPercentageQuery, UserProfileCompetionPercentageQueryVariables>(UserProfileCompetionPercentageDocument, options);
        }
export function useUserProfileCompetionPercentageSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<UserProfileCompetionPercentageQuery, UserProfileCompetionPercentageQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<UserProfileCompetionPercentageQuery, UserProfileCompetionPercentageQueryVariables>(UserProfileCompetionPercentageDocument, options);
        }
export type UserProfileCompetionPercentageQueryHookResult = ReturnType<typeof useUserProfileCompetionPercentageQuery>;
export type UserProfileCompetionPercentageLazyQueryHookResult = ReturnType<typeof useUserProfileCompetionPercentageLazyQuery>;
export type UserProfileCompetionPercentageSuspenseQueryHookResult = ReturnType<typeof useUserProfileCompetionPercentageSuspenseQuery>;
export type UserProfileCompetionPercentageQueryResult = Apollo.QueryResult<UserProfileCompetionPercentageQuery, UserProfileCompetionPercentageQueryVariables>;
export const ReferralCodeValidateDocument = gql`
    mutation referralCodeValidate($data: ReferralInput) {
  referralCodeValidate(data: $data) {
    validated
    message
  }
}
    `;
export type ReferralCodeValidateMutationFn = Apollo.MutationFunction<ReferralCodeValidateMutation, ReferralCodeValidateMutationVariables>;

/**
 * __useReferralCodeValidateMutation__
 *
 * To run a mutation, you first call `useReferralCodeValidateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useReferralCodeValidateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [referralCodeValidateMutation, { data, loading, error }] = useReferralCodeValidateMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useReferralCodeValidateMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<ReferralCodeValidateMutation, ReferralCodeValidateMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<ReferralCodeValidateMutation, ReferralCodeValidateMutationVariables>(ReferralCodeValidateDocument, options);
      }
export type ReferralCodeValidateMutationHookResult = ReturnType<typeof useReferralCodeValidateMutation>;
export type ReferralCodeValidateMutationResult = Apollo.MutationResult<ReferralCodeValidateMutation>;
export type ReferralCodeValidateMutationOptions = Apollo.BaseMutationOptions<ReferralCodeValidateMutation, ReferralCodeValidateMutationVariables>;
export const UpdateUserPreferredLanguageDocument = gql`
    mutation UpdateUserPreferredLanguage($data: UserPreferredLanguageInput) {
  updateUserPreferredLanguage(data: $data)
}
    `;
export type UpdateUserPreferredLanguageMutationFn = Apollo.MutationFunction<UpdateUserPreferredLanguageMutation, UpdateUserPreferredLanguageMutationVariables>;

/**
 * __useUpdateUserPreferredLanguageMutation__
 *
 * To run a mutation, you first call `useUpdateUserPreferredLanguageMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserPreferredLanguageMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserPreferredLanguageMutation, { data, loading, error }] = useUpdateUserPreferredLanguageMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateUserPreferredLanguageMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateUserPreferredLanguageMutation, UpdateUserPreferredLanguageMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateUserPreferredLanguageMutation, UpdateUserPreferredLanguageMutationVariables>(UpdateUserPreferredLanguageDocument, options);
      }
export type UpdateUserPreferredLanguageMutationHookResult = ReturnType<typeof useUpdateUserPreferredLanguageMutation>;
export type UpdateUserPreferredLanguageMutationResult = Apollo.MutationResult<UpdateUserPreferredLanguageMutation>;
export type UpdateUserPreferredLanguageMutationOptions = Apollo.BaseMutationOptions<UpdateUserPreferredLanguageMutation, UpdateUserPreferredLanguageMutationVariables>;
export const GetLatestReferralConfigurationDocument = gql`
    query getLatestReferralConfiguration($filter: OnboardingStage!) {
  getLatestReferralConfiguration(filter: $filter) {
    existing_user_points
    id
    new_user_points
    referral_enabled
    user_point_redeem_stage
  }
}
    `;

/**
 * __useGetLatestReferralConfigurationQuery__
 *
 * To run a query within a React component, call `useGetLatestReferralConfigurationQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLatestReferralConfigurationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLatestReferralConfigurationQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetLatestReferralConfigurationQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetLatestReferralConfigurationQuery, GetLatestReferralConfigurationQueryVariables> & ({ variables: GetLatestReferralConfigurationQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetLatestReferralConfigurationQuery, GetLatestReferralConfigurationQueryVariables>(GetLatestReferralConfigurationDocument, options);
      }
export function useGetLatestReferralConfigurationLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetLatestReferralConfigurationQuery, GetLatestReferralConfigurationQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetLatestReferralConfigurationQuery, GetLatestReferralConfigurationQueryVariables>(GetLatestReferralConfigurationDocument, options);
        }
export function useGetLatestReferralConfigurationSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetLatestReferralConfigurationQuery, GetLatestReferralConfigurationQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetLatestReferralConfigurationQuery, GetLatestReferralConfigurationQueryVariables>(GetLatestReferralConfigurationDocument, options);
        }
export type GetLatestReferralConfigurationQueryHookResult = ReturnType<typeof useGetLatestReferralConfigurationQuery>;
export type GetLatestReferralConfigurationLazyQueryHookResult = ReturnType<typeof useGetLatestReferralConfigurationLazyQuery>;
export type GetLatestReferralConfigurationSuspenseQueryHookResult = ReturnType<typeof useGetLatestReferralConfigurationSuspenseQuery>;
export type GetLatestReferralConfigurationQueryResult = Apollo.QueryResult<GetLatestReferralConfigurationQuery, GetLatestReferralConfigurationQueryVariables>;
export const GetAllReferredUsersOfUserDocument = gql`
    query GetAllReferredUsersOfUser {
  getAllReferredUsersOfUser {
    id
    referral_code
    available_points
    user_id
    user_referral_ledger_id
    referred_by_id
    created_at
    referred_to {
      id
      name
      organization_id
      isActive
      created_at
      updated_at
      onboarding_stage
      source
      interview {
        status
      }
    }
  }
}
    `;

/**
 * __useGetAllReferredUsersOfUserQuery__
 *
 * To run a query within a React component, call `useGetAllReferredUsersOfUserQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAllReferredUsersOfUserQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAllReferredUsersOfUserQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetAllReferredUsersOfUserQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetAllReferredUsersOfUserQuery, GetAllReferredUsersOfUserQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetAllReferredUsersOfUserQuery, GetAllReferredUsersOfUserQueryVariables>(GetAllReferredUsersOfUserDocument, options);
      }
export function useGetAllReferredUsersOfUserLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetAllReferredUsersOfUserQuery, GetAllReferredUsersOfUserQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetAllReferredUsersOfUserQuery, GetAllReferredUsersOfUserQueryVariables>(GetAllReferredUsersOfUserDocument, options);
        }
export function useGetAllReferredUsersOfUserSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetAllReferredUsersOfUserQuery, GetAllReferredUsersOfUserQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetAllReferredUsersOfUserQuery, GetAllReferredUsersOfUserQueryVariables>(GetAllReferredUsersOfUserDocument, options);
        }
export type GetAllReferredUsersOfUserQueryHookResult = ReturnType<typeof useGetAllReferredUsersOfUserQuery>;
export type GetAllReferredUsersOfUserLazyQueryHookResult = ReturnType<typeof useGetAllReferredUsersOfUserLazyQuery>;
export type GetAllReferredUsersOfUserSuspenseQueryHookResult = ReturnType<typeof useGetAllReferredUsersOfUserSuspenseQuery>;
export type GetAllReferredUsersOfUserQueryResult = Apollo.QueryResult<GetAllReferredUsersOfUserQuery, GetAllReferredUsersOfUserQueryVariables>;
export const GetReferralConfigurationForUserDocument = gql`
    query GetReferralConfigurationForUser {
  getReferralConfigurationForUser {
    id
    existing_user_points
    new_user_points
    user_point_redeem_stage
    referral_enabled
    terms_and_condition
  }
}
    `;

/**
 * __useGetReferralConfigurationForUserQuery__
 *
 * To run a query within a React component, call `useGetReferralConfigurationForUserQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetReferralConfigurationForUserQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetReferralConfigurationForUserQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetReferralConfigurationForUserQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetReferralConfigurationForUserQuery, GetReferralConfigurationForUserQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetReferralConfigurationForUserQuery, GetReferralConfigurationForUserQueryVariables>(GetReferralConfigurationForUserDocument, options);
      }
export function useGetReferralConfigurationForUserLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetReferralConfigurationForUserQuery, GetReferralConfigurationForUserQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetReferralConfigurationForUserQuery, GetReferralConfigurationForUserQueryVariables>(GetReferralConfigurationForUserDocument, options);
        }
export function useGetReferralConfigurationForUserSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetReferralConfigurationForUserQuery, GetReferralConfigurationForUserQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetReferralConfigurationForUserQuery, GetReferralConfigurationForUserQueryVariables>(GetReferralConfigurationForUserDocument, options);
        }
export type GetReferralConfigurationForUserQueryHookResult = ReturnType<typeof useGetReferralConfigurationForUserQuery>;
export type GetReferralConfigurationForUserLazyQueryHookResult = ReturnType<typeof useGetReferralConfigurationForUserLazyQuery>;
export type GetReferralConfigurationForUserSuspenseQueryHookResult = ReturnType<typeof useGetReferralConfigurationForUserSuspenseQuery>;
export type GetReferralConfigurationForUserQueryResult = Apollo.QueryResult<GetReferralConfigurationForUserQuery, GetReferralConfigurationForUserQueryVariables>;
export const UserDesignationDocument = gql`
    query UserDesignation {
  userDesignation {
    designation {
      designation_expertise_map {
        expertise {
          id
          name
        }
      }
      id
      is_active
      level
      name
      updated_at
      is_deleted
    }
  }
}
    `;

/**
 * __useUserDesignationQuery__
 *
 * To run a query within a React component, call `useUserDesignationQuery` and pass it any options that fit your needs.
 * When your component renders, `useUserDesignationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUserDesignationQuery({
 *   variables: {
 *   },
 * });
 */
export function useUserDesignationQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<UserDesignationQuery, UserDesignationQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<UserDesignationQuery, UserDesignationQueryVariables>(UserDesignationDocument, options);
      }
export function useUserDesignationLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<UserDesignationQuery, UserDesignationQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<UserDesignationQuery, UserDesignationQueryVariables>(UserDesignationDocument, options);
        }
export function useUserDesignationSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<UserDesignationQuery, UserDesignationQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<UserDesignationQuery, UserDesignationQueryVariables>(UserDesignationDocument, options);
        }
export type UserDesignationQueryHookResult = ReturnType<typeof useUserDesignationQuery>;
export type UserDesignationLazyQueryHookResult = ReturnType<typeof useUserDesignationLazyQuery>;
export type UserDesignationSuspenseQueryHookResult = ReturnType<typeof useUserDesignationSuspenseQuery>;
export type UserDesignationQueryResult = Apollo.QueryResult<UserDesignationQuery, UserDesignationQueryVariables>;
export const UpdateUserTypeDocument = gql`
    mutation UpdateUserType($data: UpdateUserTypeInput!) {
  updateUserType(data: $data) {
    message
    result
  }
}
    `;
export type UpdateUserTypeMutationFn = Apollo.MutationFunction<UpdateUserTypeMutation, UpdateUserTypeMutationVariables>;

/**
 * __useUpdateUserTypeMutation__
 *
 * To run a mutation, you first call `useUpdateUserTypeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserTypeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserTypeMutation, { data, loading, error }] = useUpdateUserTypeMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateUserTypeMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<UpdateUserTypeMutation, UpdateUserTypeMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<UpdateUserTypeMutation, UpdateUserTypeMutationVariables>(UpdateUserTypeDocument, options);
      }
export type UpdateUserTypeMutationHookResult = ReturnType<typeof useUpdateUserTypeMutation>;
export type UpdateUserTypeMutationResult = Apollo.MutationResult<UpdateUserTypeMutation>;
export type UpdateUserTypeMutationOptions = Apollo.BaseMutationOptions<UpdateUserTypeMutation, UpdateUserTypeMutationVariables>;
export const CreateWorkExpDocument = gql`
    mutation CreateWorkExp($data: [WorkExpInput]) {
  createWorkExp(data: $data) {
    message
    result
  }
}
    `;
export type CreateWorkExpMutationFn = Apollo.MutationFunction<CreateWorkExpMutation, CreateWorkExpMutationVariables>;

/**
 * __useCreateWorkExpMutation__
 *
 * To run a mutation, you first call `useCreateWorkExpMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateWorkExpMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createWorkExpMutation, { data, loading, error }] = useCreateWorkExpMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useCreateWorkExpMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<CreateWorkExpMutation, CreateWorkExpMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<CreateWorkExpMutation, CreateWorkExpMutationVariables>(CreateWorkExpDocument, options);
      }
export type CreateWorkExpMutationHookResult = ReturnType<typeof useCreateWorkExpMutation>;
export type CreateWorkExpMutationResult = Apollo.MutationResult<CreateWorkExpMutation>;
export type CreateWorkExpMutationOptions = Apollo.BaseMutationOptions<CreateWorkExpMutation, CreateWorkExpMutationVariables>;
export const CreateProfessionalWorkExperienceDocument = gql`
    mutation CreateProfessionalWorkExperience($data: UserProfessionalExperienceArgs) {
  createProfessionalExperience(data: $data)
}
    `;
export type CreateProfessionalWorkExperienceMutationFn = Apollo.MutationFunction<CreateProfessionalWorkExperienceMutation, CreateProfessionalWorkExperienceMutationVariables>;

/**
 * __useCreateProfessionalWorkExperienceMutation__
 *
 * To run a mutation, you first call `useCreateProfessionalWorkExperienceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateProfessionalWorkExperienceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createProfessionalWorkExperienceMutation, { data, loading, error }] = useCreateProfessionalWorkExperienceMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useCreateProfessionalWorkExperienceMutation(baseOptions?: ApolloReactHooks.MutationHookOptions<CreateProfessionalWorkExperienceMutation, CreateProfessionalWorkExperienceMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useMutation<CreateProfessionalWorkExperienceMutation, CreateProfessionalWorkExperienceMutationVariables>(CreateProfessionalWorkExperienceDocument, options);
      }
export type CreateProfessionalWorkExperienceMutationHookResult = ReturnType<typeof useCreateProfessionalWorkExperienceMutation>;
export type CreateProfessionalWorkExperienceMutationResult = Apollo.MutationResult<CreateProfessionalWorkExperienceMutation>;
export type CreateProfessionalWorkExperienceMutationOptions = Apollo.BaseMutationOptions<CreateProfessionalWorkExperienceMutation, CreateProfessionalWorkExperienceMutationVariables>;
export const GetUserProfessionalExperienceDocument = gql`
    query getUserProfessionalExperience {
  getUserProfessionalExperience {
    highest_education
    experienced
    current_work_location_id
    created_at
    updated_at
    year_of_experience
  }
}
    `;

/**
 * __useGetUserProfessionalExperienceQuery__
 *
 * To run a query within a React component, call `useGetUserProfessionalExperienceQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserProfessionalExperienceQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserProfessionalExperienceQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetUserProfessionalExperienceQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetUserProfessionalExperienceQuery, GetUserProfessionalExperienceQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUserProfessionalExperienceQuery, GetUserProfessionalExperienceQueryVariables>(GetUserProfessionalExperienceDocument, options);
      }
export function useGetUserProfessionalExperienceLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUserProfessionalExperienceQuery, GetUserProfessionalExperienceQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUserProfessionalExperienceQuery, GetUserProfessionalExperienceQueryVariables>(GetUserProfessionalExperienceDocument, options);
        }
export function useGetUserProfessionalExperienceSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUserProfessionalExperienceQuery, GetUserProfessionalExperienceQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUserProfessionalExperienceQuery, GetUserProfessionalExperienceQueryVariables>(GetUserProfessionalExperienceDocument, options);
        }
export type GetUserProfessionalExperienceQueryHookResult = ReturnType<typeof useGetUserProfessionalExperienceQuery>;
export type GetUserProfessionalExperienceLazyQueryHookResult = ReturnType<typeof useGetUserProfessionalExperienceLazyQuery>;
export type GetUserProfessionalExperienceSuspenseQueryHookResult = ReturnType<typeof useGetUserProfessionalExperienceSuspenseQuery>;
export type GetUserProfessionalExperienceQueryResult = Apollo.QueryResult<GetUserProfessionalExperienceQuery, GetUserProfessionalExperienceQueryVariables>;