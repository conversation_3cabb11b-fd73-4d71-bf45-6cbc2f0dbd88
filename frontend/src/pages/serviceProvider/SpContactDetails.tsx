import { App } from "@capacitor/app";
import { Capacitor } from "@capacitor/core";
import { ArrowRightIcon } from "@chakra-ui/icons";
import { Box, Button, FormControl, FormLabel, Input, useToast, VStack } from "@chakra-ui/react";
import { motion } from "framer-motion";
import i18next from "i18next";
import isEmpty from "is-empty";
import React, { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { IoChevronBack } from "react-icons/io5";
import { useNavigate } from "react-router-dom";
import {
  Preferred_Language,
  useUpdateProviderMutation,
  useUpdateUserPreferredLanguageMutation,
  useUserDetailsQuery
} from "../../__generated__";
import DesignerLanguageWrapper from "../../components/DesignerLanguageWrapper";
import { useAnalytics } from "../../hooks/useAnalytics";
import { createNavigationHandlers } from "../../utils/route.utils";
import Storage from "../../utils/Storage";

const SpContactDetails: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const toast = useToast();
  const { navigateToPrevious, getNextRoute } = createNavigationHandlers({ navigate });
  const { trackUserAction } = useAnalytics();
  const {
    handleSubmit,
    control,
    formState: { errors },
    watch,
    setValue
  } = useForm();

  //GraphQl Query
  const { data: userData } = useUserDetailsQuery({
    fetchPolicy: "no-cache"
  });

  const userDetails = userData?.userDetail?.data;

  //GraphQl Mutation
  const [updateProvider, { loading: updateProviderLoading }] = useUpdateProviderMutation();
  const [updateUserPreferredLanguage] = useUpdateUserPreferredLanguageMutation();

  // Set initial values when userDetails are loaded
  useEffect(() => {
    if (userDetails) {
      setValue("name", userDetails.name || "");
      setValue("email", userDetails.email || "");
    }
  }, [userDetails, setValue]);

  // Watch the entire form values
  const mandatoryFormValues = watch();

  // Check if all required fields are filled
  const isNextButtonDisabled = !mandatoryFormValues.name;

  //handleFormSubmit
  const updateProviderDetails = (e: any) => {
    const { name, email } = e;
    const { navigateToNext } = createNavigationHandlers({ navigate });

    // Create userDetails object without email first
    const userDetails = {
      name: name,
      meta: {
        is_user_journey_completed: false,
        last_location: getNextRoute()?.path
      }
    };

    // Only add email if it's provided and valid
    if (email && email.trim()) {
      Object.assign(userDetails, { email: email.trim() });
    }

    updateProvider({
      variables: {
        data: {
          userDetails
        }
      },
      onCompleted: async () => {
        // Track user activity
        await trackUserAction("CONTACT_DETAILS_UPDATE", {
          name,
          email: email?.trim(),
          is_user_journey_completed: false,
          last_location: getNextRoute()?.path
        });

        navigateToNext();
      },
      onError(error) {
        console.log("error", error);
        toast({
          position: "top",
          title: error.message,
          duration: 5000,
          isClosable: true,
          status: "error"
        });
      }
    });
  };

  const changePreferredLanguage = async (preferred_language: Preferred_Language) => {
    const isLoggedIn = !isEmpty(localStorage.getItem("onboarding_token"));
    if (isLoggedIn) {
      try {
        await updateUserPreferredLanguage({
          variables: {
            data: {
              preferred_language: preferred_language
            }
          }
        });
      } catch (error) {
        console.error(error);
      }
    }
  };
  const handleLanguageChange = async () => {
    i18next.changeLanguage("en");
    await Storage.set("preferred_language", Preferred_Language.English);
    await changePreferredLanguage(Preferred_Language.English);
  };
  //disable Capacitor back button
  useEffect(() => {
    //set App Language to en
    handleLanguageChange();
    if (Capacitor.getPlatform() === "android") {
      App.addListener("backButton", async () => {
        navigateToPrevious();
      });
    }

    // Cleanup listener when component unmounts
    return () => {
      if (Capacitor.getPlatform() === "android") {
        App.removeAllListeners();
      }
    };
  }, []);

  return (
    <DesignerLanguageWrapper customCss={"pt-12"} showLanguageChanger={false}>
      <Box
        px={6}
        style={{
          userSelect: "none"
        }}
      >
        <Box className="flex justify-tart items-center" mb={10}>
          <Box>
            {/* <Button variant="ghost" fontSize={30} p={0} ml={-2}>
              <MdKeyboardArrowLeft />
            </Button> */}
          </Box>
          <Box fontSize={20} fontWeight={"bold"} letterSpacing={1.2}>
            {t("let_us_know")}
          </Box>
        </Box>
        <Box fontSize={14} fontWeight={"semibold"} mb={5} letterSpacing={1.2}>
          {t("point_of_contact_details")}
        </Box>
        <form onSubmit={handleSubmit(updateProviderDetails)} noValidate>
          <VStack className="mt-2 grid grid-cols-2 gap-5">
            <FormControl isInvalid={!!errors.name} isRequired>
              <FormLabel fontSize={14} className="text-coolGray-600" fontWeight={300}>
                {t("name")}
              </FormLabel>
              <Controller
                name="name"
                control={control}
                defaultValue={userDetails?.name || ""}
                rules={{
                  required: true,
                  pattern: {
                    value: /^[^\s][a-zA-Z\s\u0900-\u097F]*$/u,
                    message: t("error_invalid_name")
                  }
                }}
                render={({ field, fieldState }) => (
                  <>
                    <Input type="text" {...field} placeholder={t("type_your_name")} />
                    {fieldState?.error && (
                      <p className="text-red-500 text-xs mt-[4px]">{fieldState?.error.message}</p>
                    )}
                  </>
                )}
              />
            </FormControl>

            <FormControl className="my-5" isInvalid={!!errors.email}>
              <FormLabel fontSize={14} className="text-coolGray-600" fontWeight={300}>
                {t("email")} ({t("optional")})
              </FormLabel>
              <Controller
                name="email"
                control={control}
                defaultValue={userDetails?.email || ""}
                rules={{
                  validate: (value) => {
                    if (!value || value.trim() === "") return true;
                    return (
                      /^[^\s][a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/.test(value) ||
                      t("error_email")
                    );
                  }
                }}
                render={({ field, fieldState }) => (
                  <>
                    <Input type="email" {...field} placeholder={t("type_your_email")} />
                    {fieldState?.error && (
                      <p className="text-red-500 text-xs mt-[4px]">{fieldState?.error.message}</p>
                    )}
                  </>
                )}
              />
            </FormControl>
            <FormControl isRequired>
              <FormLabel className="text-coolGray-600" fontWeight={300} fontSize={14}>
                {t("phone_number")}
              </FormLabel>
              <Input
                disabled
                fontSize={14}
                value={userDetails?.phone || ""}
                color="black"
                fontWeight="semibold"
              />
            </FormControl>
          </VStack>
          <Box className="fixed md:relative md:pt-10 flex justify-center md:justify-end bottom-0 left-0 right-0 bg-gray-50/10 bg-white border-t border-gray-100 p-3 z-30">
            <Box className="grid grid-cols-2 gap-4 w-full">
              <motion.div whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline"
                  onClick={() => {
                    navigateToPrevious();
                  }}
                  leftIcon={<IoChevronBack size={20} />}
                  size="md"
                  className="w-full"
                  colorScheme="blue"
                >
                  {t("back")}
                </Button>
              </motion.div>
              <motion.div whileTap={{ scale: 0.95 }}>
                <Button
                  type="submit"
                  loadingText={t("saving")}
                  size="md"
                  className="w-full"
                  colorScheme="blue"
                  isDisabled={isNextButtonDisabled}
                  isLoading={updateProviderLoading}
                  rightIcon={<ArrowRightIcon />}
                >
                  {t("next")}
                </Button>
              </motion.div>
            </Box>
          </Box>
        </form>
      </Box>
    </DesignerLanguageWrapper>
  );
};

export default SpContactDetails;
