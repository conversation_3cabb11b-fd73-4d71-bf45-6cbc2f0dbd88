import { Modal, Tabs, message } from "antd";
import { useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  Expertise,
  GetManualAssignmentsDocument,
  useAssignAssignmentToUsersMutation
} from "../../__generated__";
import AssignManualAssignment from "./ManualAssignmentToUserTab";
import ManuallyAssignedUsersTable from "./ManuallyAssignedUsersListTab";

export interface SelectedUserType {
  id: number;
  name: string;
  expertise: Expertise[];
  location: {
    city: string;
  };
  phone: string;
}
const AssignConfigUserModal: React.FC = () => {
  //Hooks
  const [selectedUsers, setSelectedUsers] = useState<Array<SelectedUserType>>([]);
  const [activeTabKey, setActiveTabKey] = useState("assign_users");
  const [searchParams, setSearchParams] = useSearchParams("");
  const [assignUsersListSearch, setAssignUsersListSearch] = useState(
    searchParams.get("search") || ""
  );
  const [assignedUsersTableSearch, setAssignedUsersTableSearch] = useState("");

  const { assignment_config_id } = useParams();
  const nav = useNavigate();

  //GraphQl Mutation
  const [assignAssignmentsToUsers, { loading: assigning }] = useAssignAssignmentToUsersMutation();

  const onAssign = () => {
    assignAssignmentsToUsers({
      variables: {
        assignment_config_id: assignment_config_id || "",
        user_ids: selectedUsers?.map((user) => user?.id)
      },
      onCompleted(data) {
        if (data.assignAssignmentToUsers.result) {
          message.success("Successfully assigned");
        } else {
          message.error("Something went wrong");
        }
        setSelectedUsers([]);
        setActiveTabKey("assigned_users_list");
        localStorage.removeItem("selected_users");
      },
      onError() {
        message.error("Something went wrong");
      },
      refetchQueries: [GetManualAssignmentsDocument]
    });
  };

  const onTabClick = (key: string) => {
    setActiveTabKey(key), setSearchParams({});
    //on tab change don't want the ant d default value issue
    if (assignUsersListSearch) {
      setAssignUsersListSearch("");
    }
    if (assignedUsersTableSearch) {
      setAssignedUsersTableSearch("");
    }
  };

  return (
    <Modal
      closeIcon={false}
      onCancel={() => {
        localStorage.removeItem("selected_users"), nav("/dashboard/assignment/config");
      }}
      onOk={onAssign}
      cancelText={"Cancel"}
      okText={"Assign"}
      okButtonProps={{ disabled: selectedUsers.length < 1, loading: assigning }}
      width={"80%"}
      open
      footer={activeTabKey == "assigned_users_list" ? null : undefined}
    >
      <Tabs
        activeKey={activeTabKey}
        onTabClick={(key) => onTabClick(key)}
        items={[
          {
            label: "Assign Users",
            key: "assign_users",
            children: (
              <AssignManualAssignment
                selectedUsers={selectedUsers}
                setSelectedUsers={setSelectedUsers}
                searchTerm={assignUsersListSearch}
                setSearchTerm={setAssignUsersListSearch}
              />
            )
          },
          {
            label: "List of Assigned Users",
            key: "assigned_users_list",
            children: (
              <ManuallyAssignedUsersTable
                searchTerm={assignedUsersTableSearch}
                setSearchTerm={setAssignedUsersTableSearch}
              />
            )
          }
        ]}
      />
    </Modal>
  );
};

export default AssignConfigUserModal;
