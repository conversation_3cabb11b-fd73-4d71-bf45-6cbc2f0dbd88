import {
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Switch,
  message
} from "antd";
import { useForm, useWatch } from "antd/es/form/Form";
import FormItem from "antd/es/form/FormItem";
import React, { useEffect, useState } from "react";
import ReactQuill from "react-quill";

import isEmpty from "is-empty";
import "react-quill/dist/quill.snow.css";
import { useNavigate, useParams } from "react-router-dom";
import {
  AssignmentType,
  AssignmentUserType,
  FilterType,
  GetAllAssignmentConfigDocument,
  useCreateAssignmentConfigMutation,
  useGetAllBucketsQuery,
  useGetAllRulesModuleQuery,
  useGetSingleConfigDetailsLazyQuery,
  useUpdateAssignmentConfigMutation
} from "../../__generated__";
import { arraysHaveSameElements } from "../../utils/utils";
import ErrorComponent from "../error/Error";
import Loading from "../loading/Loading";

const SpUpdateAssessmentConfiguration: React.FC = () => {
  //Hooks
  const [isAssignmentPublished, setIsAssignmentPublished] = useState(false);

  //router stuff
  const nav = useNavigate();
  const { assignment_config_id } = useParams();

  //form
  const [form] = useForm();

  //form watchers
  const user_assignment_type = useWatch<AssignmentType>("config_assignment_type", form);
  const questions_assignment_type = useWatch<AssignmentType>("questions_assignment_type", form);
  const on_basis_of = useWatch("on_basis_of", form);
  const is_test_time_bound = useWatch("is_time_bounded", form);

  //network calls
  //prettier-ignore
  const { data: bucketModulesRes, error: bucketError, loading: bucketLoading } = useGetAllBucketsQuery({
    variables : {
      data : {
        assign_to : AssignmentUserType.ServiceProvider
      }
    }
  });
  const {
    data: rulesRes,
    error: rulesError,
    loading: rulesLoading
  } = useGetAllRulesModuleQuery({
    variables: {
      filter: {
        type: FilterType.Config,
        assign_to: AssignmentUserType.ServiceProvider
      }
    }
  });

  //mutations
  const [createAssignmentConfig, { loading: createLoading }] = useCreateAssignmentConfigMutation();
  const [getSingleConfigDetails, { data: configData, refetch }] =
    useGetSingleConfigDetailsLazyQuery();
  const [updateAssignmentConfig, { loading: updateLoading }] = useUpdateAssignmentConfigMutation();

  //handlers
  const saveOrUpdateAssignmentConfig = (values: any) => {
    if (!assignment_config_id) {
      values.no_of_questions = parseInt(values.no_of_questions);

      if (values.is_time_bounded) {
        values.hard_question_time = parseInt(values.hard_question_time);
        values.medium_question_time = parseInt(values.medium_question_time);
        values.easy_question_time = parseInt(values.easy_question_time);
      }

      createAssignmentConfig({
        variables: {
          data: {
            name: values.name,
            no_of_questions: parseInt(values.no_of_questions),
            time_bounded: values.is_time_bounded,
            ...(values.is_time_bounded
              ? {
                  medium_question_time: values.medium_question_time,
                  easy_question_time: values.easy_question_time,
                  hard_question_time: values.hard_question_time
                }
              : {}),
            config_assignment_type: values.config_assignment_type,
            questions_assignment_type: values.questions_assignment_type,
            general_instructions_md: values.general_instructions_md || "",
            on_basis_of: values.on_basis_of,
            difficulty: values.difficulty,
            buckets: values.buckets,
            config_auto_assignment_gen_rules_id: values.config_auto_assignment_gen_rules_id,
            assigned_to_only_team_members: values.assigned_to_only_team_members
          }
        },
        onCompleted: (ans) => {
          message.success("Assignment config created successfully");
          if (values.questions_assignment_type === AssignmentType.Manual) {
            nav(`/dashboard/sp/assignment/config/add_questions/${ans.createAssignmentConfig.id}`, {
              replace: true
            });
          } else {
            nav(`/dashboard/sp/assignment/config`, {
              replace: true
            });
          }
        },
        onError: (err) => {
          console.error(err);
          message.error(
            err?.message || "There was some issue during updating the assignment config"
          );
        },
        refetchQueries: [GetAllAssignmentConfigDocument]
      });
    } else {
      const updated_values: {
        name?: string;
        no_of_questions?: number;
        is_time_bounded?: boolean;
        hard_question_time?: number;
        medium_question_time?: number;
        easy_question_time?: number;
        config_assignment_type?: AssignmentType;
        questions_assignment_type?: AssignmentType;
        general_instructions_md?: string;
        on_basis_of?: string[];
        rules?: any;
        assigned_to_only_team_members?: boolean;
      } = {};
      const assignment_config = configData?.getSingleAssignmentConfig;

      if (!configData) {
        throw new Error("No config data found");
      }

      //deleting the changes and putting it in the updated values
      if (values.name !== assignment_config?.name) {
        updated_values["name"] = values.name;
      }
      if (values.no_of_questions !== assignment_config?.no_of_questions) {
        updated_values["no_of_questions"] = parseInt(values.no_of_questions);
      }
      if (values.is_time_bounded !== assignment_config?.time_bounded) {
        updated_values["is_time_bounded"] = values.is_time_bounded;
      }
      if (values.is_time_bounded && values.hard_question_time !== assignment_config?.hard_time) {
        updated_values["hard_question_time"] = values.hard_question_time;
      }
      if (
        values.is_time_bounded &&
        values.medium_question_time !== assignment_config?.medium_time
      ) {
        updated_values["medium_question_time"] = values.medium_question_time;
      }
      if (values.is_time_bounded && values.easy_question_time !== assignment_config?.easy_time) {
        updated_values["easy_question_time"] = values.easy_question_time;
      }
      if (values.config_assignment_type !== assignment_config?.config_assignment_type) {
        updated_values["config_assignment_type"] = values.config_assignment_type;
      }
      if (
        values.questions_assignment_type !== assignment_config?.questions_assignment_type ||
        values.questions_assignment_type === assignment_config?.questions_assignment_type
      ) {
        updated_values["questions_assignment_type"] = values.questions_assignment_type;
      }
      if (values.general_instructions_md !== assignment_config?.general_instructions_md) {
        updated_values["general_instructions_md"] = values.general_instructions_md;
      }
      if (
        values.assigned_to_only_team_members !== assignment_config?.assigned_to_only_team_members
      ) {
        updated_values["assigned_to_only_team_members"] = values.assigned_to_only_team_members;
      }
      if (!arraysHaveSameElements(values.on_basis_of || [], assignment_config?.on_basis_of || [])) {
        updated_values["on_basis_of"] = values.on_basis_of;
      }

      const rules = [
        {
          field_name: "difficulty",
          operator: "in",
          value: {
            value: values.difficulty
          }
        },
        {
          field_name: "buckets",
          operator: "in",
          value: {
            value: values.buckets
          }
        }
      ];

      const assignment_rules = assignment_config?.questions_assignment_rules?.rules;

      if (
        !assignment_rules ||
        rules[0].value.value !== assignment_rules[0].value.value ||
        rules[1].value.value !== assignment_rules[1].value.value
      ) {
        updated_values["rules"] = rules.filter((r) => {
          if (!isEmpty(values.difficulty) && r.field_name === "difficulty") {
            return r;
          } else if (!isEmpty(values.buckets) && r.field_name === "buckets") {
            return r;
          }
        });
      }

      updateAssignmentConfig({
        variables: {
          id: assignment_config_id || "invalid_id",
          data: updated_values
        },
        onCompleted: () => {
          message.success("Assignment config updated successfully");
          refetch();
          nav(-1);
        },
        onError: (err) => {
          console.error(err);
          message.error(
            err?.message || "There was some issue during updating the assignment config"
          );
        },
        refetchQueries: [GetAllAssignmentConfigDocument]
      });
    }
  };

  useEffect(() => {
    (async () => {
      if (assignment_config_id) {
        try {
          await getSingleConfigDetails({
            variables: {
              id: assignment_config_id
            },
            onError: (err) => {
              console.error(err);
              message.error("There was some issue while fetching the config details");
            },
            onCompleted: (config) => {
              const details = config.getSingleAssignmentConfig;

              if (!details) {
                message.error("There was some issue while fetching the config details");
                return;
              }

              form.setFieldsValue({
                name: details.name,
                no_of_questions: details.no_of_questions,
                is_time_bounded: details.time_bounded,
                medium_question_time: details.medium_time,
                easy_question_time: details.easy_time,
                hard_question_time: details.hard_time,
                config_assignment_type: details.config_assignment_type,
                questions_assignment_type: details.questions_assignment_type,
                general_instructions_md: details.general_instructions_md,
                on_basis_of: details.on_basis_of,
                config_auto_assignment_gen_rules_id: details.config_assignment_rules?.id,
                assigned_to_only_team_members: details.assigned_to_only_team_members,
                difficulty: details.questions_assignment_rules?.rules
                  ?.flatMap((rule: { field_name: string; value: { value: any } }) =>
                    rule.field_name === "difficulty" && rule.value.value ? rule.value.value : []
                  )
                  .filter((value: any) => value !== null),
                buckets: details.questions_assignment_rules?.rules
                  ?.flatMap((rule: { field_name: string; value: { value: string[] } }) =>
                    rule.field_name === "buckets" && rule.value.value ? rule.value.value : []
                  )
                  .filter((value: any) => value !== null)
              });
              setIsAssignmentPublished(details.draft_mode ? false : true);
            }
          });
        } catch (error) {
          console.error(error);
        }
      }
    })();
  }, [assignment_config_id]);

  if (rulesError || bucketError) {
    return <ErrorComponent error={rulesLoading || bucketError} />;
  }

  if (rulesLoading || bucketLoading) {
    return <Loading tip="Loading ..." />;
  }

  return (
    <Modal
      closeIcon={false}
      centered
      onCancel={() => {
        nav("/dashboard/sp/assignment/config");
      }}
      footer={
        isAssignmentPublished ? null : (
          <>
            <Button
              onClick={() => {
                nav(-1);
              }}
            >
              Cancel
            </Button>
            <Button
              loading={createLoading || updateLoading}
              type="primary"
              onClick={() => {
                form.submit();
              }}
            >
              {assignment_config_id ? "Update" : "Create"}
            </Button>
          </>
        )
      }
      width={1200}
      title={<div className="text-bold text-2xl">Basic Info</div>}
      open
    >
      <Form
        onFinish={saveOrUpdateAssignmentConfig}
        disabled={isAssignmentPublished}
        form={form}
        layout="vertical"
      >
        <FormItem
          rules={[
            {
              required: true,
              message: "Name is required"
            }
          ]}
          label={"Name"}
          name={"name"}
        >
          <Input />
        </FormItem>
        <FormItem
          name={"no_of_questions"}
          label={"Number of questions"}
          rules={[
            {
              required: true,
              message: "Number of questions is required"
            }
          ]}
        >
          <InputNumber className="w-full" value={0} min={1} max={100} />
        </FormItem>
        <FormItem
          required
          name={"is_time_bounded"}
          label={"Do you want to keep the assignment time bounded ?"}
          rules={[{ required: true, message: "assignment time bound is required" }]}
        >
          <Radio.Group>
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </FormItem>
        {is_test_time_bound && (
          <Row gutter={12}>
            <Col span={8}>
              <FormItem
                name={"hard_question_time"}
                label={"Hard question time (seconds)"}
                rules={[{ required: true, message: "Hard question time is required" }]}
              >
                <InputNumber min={60} className="w-full" />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem
                name={"medium_question_time"}
                label={"Medium question time (seconds)"}
                rules={[{ required: true, message: "Medium question time is required" }]}
              >
                <InputNumber min={60} className="w-full" />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem
                name={"easy_question_time"}
                label={"Easy question time (seconds)"}
                rules={[{ required: true, message: "Easy question time is required" }]}
              >
                <InputNumber min={60} className="w-full" />
              </FormItem>
            </Col>
          </Row>
        )}

        <FormItem label={"General Instructions"} name={"general_instructions_md"}>
          <ReactQuill
            className={isAssignmentPublished ? `bg-gray-300 pointer-events-none` : ""}
            onChange={(value) => {
              form.setFieldValue("general_instructions_md", value);
            }}
            value={form.getFieldValue("general_instructions_md")}
            theme="snow"
          />
        </FormItem>
        <FormItem
          rules={[
            {
              required: true,
              message: "User assignment type is required"
            }
          ]}
          label={"User assignment type"}
          name={"config_assignment_type"}
        >
          <Select
            options={[
              {
                value: AssignmentType.Automatic,
                label: "Automatic"
              },
              {
                value: AssignmentType.Manual,
                label: "Manual"
              }
            ]}
          />
        </FormItem>
        {user_assignment_type === AssignmentType.Automatic && (
          <FormItem
            name={"config_auto_assignment_gen_rules_id"}
            rules={[
              {
                required: true,
                message: "Rule module is required"
              }
            ]}
            required
            label={"Select rule module"}
          >
            <Select
              options={rulesRes?.getAllRulesModule?.data?.map((res) => ({
                value: res?.id,
                label: res?.name
              }))}
            />
          </FormItem>
        )}
        <FormItem
          rules={[
            {
              required: true,
              message: "Question assignment type is required"
            }
          ]}
          name={"questions_assignment_type"}
          label={"Question assignment type"}
        >
          <Select
            options={[
              {
                value: AssignmentType.Automatic,
                label: "Automatic"
              },
              {
                value: AssignmentType.Manual,
                label: "Manual"
              }
            ]}
          />
        </FormItem>
        {questions_assignment_type === AssignmentType.Automatic && (
          <FormItem
            rules={[
              {
                required: true,
                message: "On the basis of is required"
              }
            ]}
            required
            name={"on_basis_of"}
            label={"Auto assign questions on the basis of"}
          >
            <Checkbox.Group
              options={[
                {
                  value: "bucket",
                  label: "Bucket"
                },
                {
                  value: "difficulty",
                  label: "Difficulty"
                }
              ]}
            />
          </FormItem>
        )}
        {on_basis_of && on_basis_of.length > 0 && (
          <Card title={"Auto question assignment configuration"}>
            {on_basis_of && on_basis_of.includes("bucket") && (
              <FormItem
                name={"buckets"}
                rules={[
                  {
                    required: true,
                    message: "Bucket is type is required"
                  }
                ]}
                required
                label={"Select buckets"}
              >
                <Select
                  mode="multiple"
                  options={bucketModulesRes?.getAllBuckets?.data?.map((bucket) => ({
                    value: bucket?.id,
                    label: bucket?.name
                  }))}
                />
              </FormItem>
            )}
            {on_basis_of && on_basis_of.includes("difficulty") && (
              <FormItem
                name={"difficulty"}
                rules={[
                  {
                    required: true,
                    message: "Difficulty is required"
                  }
                ]}
                label={"Difficulty"}
              >
                <Select
                  mode="multiple"
                  options={[
                    {
                      value: "HARD",
                      label: "Hard"
                    },
                    {
                      value: "MEDIUM",
                      label: "Medium"
                    },
                    {
                      value: "EASY",
                      label: "Easy"
                    }
                  ]}
                />
              </FormItem>
            )}
          </Card>
        )}
        <FormItem name={"assigned_to_only_team_members"} label={"Assigned to only team members"}>
          <Switch />
        </FormItem>
      </Form>
    </Modal>
  );
};

export default SpUpdateAssessmentConfiguration;
