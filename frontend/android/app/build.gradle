apply plugin: 'com.android.application'

android {
    namespace "co.in.wify.wifytech"
    compileSdkVersion 34
  
    
    defaultConfig {
        applicationId "co.in.wify.wifytech"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion 34
        versionCode 18
        versionName "18"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        aaptOptions {
            // Files and dirs to omit from the packaged assets dir, modified to accommodate modern web apps.
            // Default: https://android.googlesource.com/platform/frameworks/base/+/282e181b58cf72b6ca770dc7ca5f91f135444502/tools/aapt/AaptAssets.cpp#61
            ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    flavorDimensions "version"
    productFlavors {
        original {
            dimension "version"
            applicationId "co.in.wify.wifytech"
            versionNameSuffix ""
            manifestPlaceholders = [
                appName: "Wify Partner App",
                appIcon: "@mipmap/ic_launcher",
                appRoundIcon: "@mipmap/ic_launcher_round",
                deepLinkHost: "work.wify.co.in"
            ]
        }
        clone {
            dimension "version"
            applicationId "co.in.wify.wifytech.clone"
            versionNameSuffix ".clone"
            manifestPlaceholders = [
                appName: "Wify Partner Clone",
                appIcon: "@mipmap/ic_launcher",
                appRoundIcon: "@mipmap/ic_launcher_round",
                deepLinkHost: "work-clone.wify.co.in"
            ]
        }
    }
}

repositories {
    flatDir{
        dirs '../capacitor-cordova-android-plugins/src/main/libs', 'libs'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "androidx.coordinatorlayout:coordinatorlayout:$androidxCoordinatorLayoutVersion"
    implementation "androidx.core:core-splashscreen:$coreSplashScreenVersion"
    implementation project(':capacitor-android')
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation project(':capacitor-cordova-android-plugins')

    implementation platform('com.google.firebase:firebase-bom:32.2.0')

    implementation 'com.google.firebase:firebase-messaging:23.1.2'
//    implementation 'com.google.android.play:core:1.10.3'
    implementation 'id.zelory:compressor:3.0.1'
    implementation 'androidx.core:core-ktx:1.3.2'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.6.0"

    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.4.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.1'
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    implementation 'androidx.annotation:annotation:1.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.google.android.gms:play-services-location:20.0.0'
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    implementation 'com.google.android.material:material:1.6.1'
    implementation 'com.google.firebase:firebase-crashlytics:18.2.11'

    implementation files('../tms-uat.aar')
}

apply from: 'capacitor.build.gradle'

