import { Contacts } from "@capacitor-community/contacts";
import { Capacitor } from "@capacitor/core";
import { Box, Button, Divider, Flex, Grid, Input, Text, useClipboard } from "@chakra-ui/react";
import { ArrowRight } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { PiShareNetworkBold } from "react-icons/pi";
import { useNavigate } from "react-router-dom";
import { useCreateContactsMutation, useGenerateReferralCodeQuery } from "../../__generated__";
import { getContactPermission, getContactPermissionStatus } from "../../services/CapacitorInit";
import { chunkArray } from "../../utils/common";
import Loading from "../Loading";
import ManualReferralDrawer from "./ManualReferralDrawer";

interface ReferAndEarnProps {
  userId: number | undefined;
}

const ReferAndEarn: React.FC<ReferAndEarnProps> = ({ userId }) => {
  const { setValue } = useClipboard("");
  const navigate = useNavigate();
  // const { data: userDetails } = useUserDetailsQuery();
  // const user_data = userDetails?.userDetail?.data;
  const { data: referral } = useGenerateReferralCodeQuery();
  const [buttonLoading, setButtonLoading] = useState(false);
  // const { data } = useGetLatestReferralConfigurationQuery({
  //   variables: {
  //     filter: OnboardingStage.SignUp
  //   }
  // });
  // const [isServiceProviderOwner, setIsServiceProviderOwner] = useState<boolean>(false);
  const { t } = useTranslation();
  const [openManualReferralDrawer, setOpenManualReferralDrawer] = useState(false);
  const [createContacts] = useCreateContactsMutation();
  // useEffect(() => {
  //   const is_service_provider_owner = user_data?.id === user_data?.organization?.org_owner_id;
  //   setIsServiceProviderOwner(is_service_provider_owner);
  // }, [user_data]);

  const syncContact = async () => {
    try {
      console.log("Syncing contacts...");

      if (Capacitor.getPlatform() !== "android") return null;
      setButtonLoading(true);

      // Request permission to access contacts
      await getContactPermission();
      setButtonLoading(false);

      // Retrieve contacts from the device
      const contacts = await Contacts.getContacts({
        projection: {
          phones: true,
          emails: true,
          name: true
        }
      });

      // Batch size for sending contacts
      const batchSize = 500;
      const contactChunks = chunkArray(contacts.contacts, batchSize);

      for (const chunk of contactChunks) {
        await createContacts({
          variables: {
            contacts: JSON.stringify(chunk)
          }
        });
      }

      console.log("All contacts synced successfully.");
    } catch (error) {
      setButtonLoading(false);
      console.error("Error syncing contacts:", error);
    }
  };

  const handleShare = async () => {
    try {
      if (Capacitor.getPlatform() === "android") {
        const contactPermission = await getContactPermissionStatus();
        if (!contactPermission || contactPermission === "denied") {
          await getContactPermission();
          setOpenManualReferralDrawer(true);
        } else {
          if (contactPermission === "new_granted") {
            await syncContact();
          }
          navigate(`/select-contact/${userId}`);
        }
      } else {
        setOpenManualReferralDrawer(true);
        // navigate(`/select-contact/${userId}`);
      }
    } catch (error) {
      console.log(`Error Sharing `, error);
    }
  };

  if (buttonLoading) return <Loading />;

  return (
    <Box backgroundColor={"#fff"} my={"2"} rounded={"lg"}>
      <Box
        display={"flex"}
        justifyContent={"space-between"}
        alignItems={"center"}
        position={"relative"}
      >
        <Text fontWeight={"semibold"} fontSize={"xl"} p={3}>
          {t("profile_refer")}
        </Text>
      </Box>
      <Divider />
      <Box p={2} position={"relative"}>
        <Box className="w-4/5">
          <Text fontSize={"sm"} className="md:text-start">
            {t("profile_refer_friends")}
          </Text>
          <Flex mb={2}>
            <Input
              value={referral?.generateReferralCodeFrUser || ""}
              onChange={(e) => {
                setValue(e.target.value);
              }}
              size={"md"}
              textAlign={"center"}
              mr={2}
            />
          </Flex>
        </Box>
        <Box
          className="-left-20 -top-14"
          position={"absolute"}
          backgroundImage={"url(/confetti.svg)"}
          width={"full"}
          height={50}
          backgroundPosition={"center"}
          backgroundSize={"contain"}
          backgroundRepeat={"no-repeat"}
        />
        <Box
          className="right-0 -top-20"
          position={"absolute"}
          backgroundImage={"url(/giftbox.png?v=123)"}
          width={150}
          height={150}
          backgroundPosition={"center"}
          backgroundSize={"contain"}
          backgroundRepeat={"no-repeat"}
        />
      </Box>
      <Divider />
      <Grid gap={2} padding={2} templateColumns={"repeat(1 , 1fr)"}>
        <Button size={"sm"} onClick={handleShare} bgColor={"#ff9f45"} color={"white"}>
          <Box display={"flex"} justifyContent={"center"} alignItems={"center"} gap={2}>
            <PiShareNetworkBold className="text-xl" /> {t("profile_refer_friend_btn")}
          </Box>
        </Button>
        {/* //todo verify condition of only transferred indivduals can see my referrals and sp 
         owner cant see my referrals */}
        <Button size={"sm"} onClick={() => navigate("/rewards")}>
          <Box
            display={"flex"}
            justifyContent={"center"}
            alignItems={"center"}
            gap={1}
            margin={"auto"}
          >
            {t("profile_my_referrals")} <ArrowRight size={20} className="text-lg" />
          </Box>
        </Button>
      </Grid>
      <ManualReferralDrawer
        isOpen={openManualReferralDrawer}
        referralCode={referral?.generateReferralCodeFrUser || ""}
        onClose={() => setOpenManualReferralDrawer(false)}
      />
    </Box>
  );
};

export default ReferAndEarn;
