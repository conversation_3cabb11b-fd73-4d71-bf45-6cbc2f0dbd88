import { Capacitor } from "@capacitor/core";
import html2canvas from "html2canvas";
import Storage from "./Storage";

export const getGreeting = () => {
  const today = new Date();
  const curHr = today.getHours();

  if (curHr < 12) {
    return "Good morning";
  } else if (curHr < 18) {
    return "Good afternoon";
  } else {
    return "Good evening";
  }
};

export const debounce = (func: any, wait: number) => {
  let timeout: any;
  return function executedFunction(...args: any) {
    const later = () => {
      timeout = null;
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

//This function is of google analytics to track the page load events
// export const googleAnalyticsPageLoadType = (hittype: string, page: string) => {
//   ReactGA.send({
//     hitType: hittype,
//     page: page
//   });
// };

export function getRemainingTime(
  /**
   * Calculates the remaining time in seconds until the end of an assignment.
   *
   * @param {string} assignmentStartTimeString - The start time of the assignment as a string.
   * @param {number} assignmentDurationSeconds - The duration of the assignment in seconds.
   * @return {number} The remaining time in seconds until the end of the assignment.
   */
  assignmentStartTimeString: string,
  assignmentDurationSeconds: number
): number {
  const assignmentStartTime = new Date(assignmentStartTimeString);
  const currentTime = new Date();
  const assignmentEndTime = new Date(
    assignmentStartTime.getTime() + assignmentDurationSeconds * 1000
  );

  if (currentTime < assignmentEndTime) {
    const remainingTimeInSeconds = Math.floor(
      (assignmentEndTime.getTime() - currentTime.getTime()) / 1000
    );
    return remainingTimeInSeconds;
  } else {
    return 0;
  }
}

export function formatTime(seconds: number): string {
  if (seconds < 0) {
    throw new Error("Seconds cannot be negative");
  }

  const totalMinutes = Math.floor(seconds / 60);
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}min`;
  }
}
export const createElementToImage = async (element: HTMLDivElement): Promise<string> => {
  const canvas = await html2canvas(element, {
    logging: false // Disable logging for cleaner output
  });

  return canvas.toDataURL("image/png");
};

interface GeoLocationData {
  formattedAddress: string;
  city: string;
  state: string;
  landmark: string;
  pincode: string;
  area: string;
}

export const getGeoLocationData = async (lat: number, lng: number): Promise<GeoLocationData> => {
  const apiKey = import.meta.env.VITE_GOOGLE_MAP_API_KEY;

  const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`;

  try {
    //fetch use instead
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Error fetching geolocation data: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.status === "OK") {
      const result = data.results[0];
      const addressComponents = result.address_components;

      const formattedAddress = result.formatted_address;
      let city = "";
      let state = "";
      let landmark = "";
      let pincode = "";
      let area = "";

      addressComponents.forEach((component: any) => {
        if (component.types.includes("sublocality")) {
          area = component.long_name;
        }
        if (component.types.includes("locality")) {
          city = component.long_name;
        }
        if (component.types.includes("administrative_area_level_1")) {
          state = component.long_name;
        }
        if (component.types.includes("point_of_interest")) {
          landmark = component.long_name;
        }
        if (component.types.includes("postal_code")) {
          pincode = component.long_name;
        }
      });

      return {
        formattedAddress,
        city,
        state,
        landmark,
        pincode,
        area
      };
    } else {
      throw new Error("Unable to retrieve geolocation data");
    }
  } catch (error) {
    console.error("Error fetching geolocation data:", error);
    throw error;
  }
};

//this will check if the user is on android , if not show playstore link else home page //TODO:test it
export const checkIfAndroid = () => {
  // Check if the platform is Linux and user agent contains Android
  const isLinux = /Linux/i.test(navigator.platform);
  const isAndroidUserAgent = /Android/i.test(navigator.userAgent);

  return isLinux && isAndroidUserAgent;
};

export const checkIfDesktop = () => {
  return !/Mobi|Android/i.test(navigator.userAgent);
};

// Function to remove country code if it starts with +91
export const formatPhoneNumber = (number: string) => {
  // Remove '+91' if present
  if (number.startsWith("+91")) {
    return number.slice(3);
  }
  // Remove '91' if present and length is more than 10
  if (number.startsWith("91") && number.length > 10) {
    return number.slice(2);
  }
  // Return as-is if neither condition applies
  return number;
};

export const extractOtpFromSms = (sms: string) => {
  const otpRegex = /\b\d{4,6}\b/; // Adjust the regex according to the length of the OTP (e.g., 4-6 digits)
  const match = sms.match(otpRegex);
  return match ? match[0] : "";
};

// Helper function to chunk contacts array
export function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

export const toTitleCase = (text: string) => {
  return text
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export const redirectUserIfFeedbackExist = async (userId: number, navigate: any) => {
  const redirectToFeedback = await Storage.get("feedback_url_redirect");
  const isAndroid = Capacitor.getPlatform() === "android";

  if (
    redirectToFeedback &&
    userId &&
    +redirectToFeedback === userId &&
    location.pathname !== `/technician-feedback/${userId}` &&
    isAndroid
  ) {
    navigate(`/technician-feedback/${userId}`);
    await Storage.remove("feedback_url_redirect");
    return true;
  }
  return false;
};

export const showPolicyReminder = async () => {
  const currentDate = new Date();
  const lastShownDateString = await Storage.get("policyReminderLastShownDate");
  const lastShownDate = lastShownDateString ? JSON.parse(lastShownDateString) : null;
  const gracePeriodInMs = 15 * 60 * 1000;

  // Set the last shown date initially if not set
  if (!lastShownDate) {
    await Storage.set("policyReminderLastShownDate", JSON.stringify(currentDate));
  }

  // Check if it's a new day compared to when the reminder was last shown
  const isNewDate = currentDate.getDate() !== new Date(lastShownDate)?.getDate();
  const isWithinGracePeriod =
    currentDate.getTime() <= new Date(lastShownDate)?.getTime() + gracePeriodInMs;

  // Show popup if it's a new day
  if (isNewDate || (isNewDate && isWithinGracePeriod)) {
    await Storage.set("policyReminderLastShownDate", JSON.stringify(currentDate));
    return true;
  }
  return false;
};
