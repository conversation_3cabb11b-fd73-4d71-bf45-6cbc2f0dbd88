import { Checkbox, Form, Input, Modal, Switch, notification } from "antd";
import { useForm } from "antd/es/form/Form";
import { useNavigate } from "react-router-dom";
import { DocumentTypesDocument, useCreateDocumentTypeFrAdminMutation } from "../../__generated__";
import { DocumentStatus } from "./UpdateDocumentType";

interface FormValues {
  name: string;
  required: boolean;
  status: DocumentStatus;
  english: string;
  hindi: string;
}

const CreateDocumentType: React.FC = () => {
  const { TextArea } = Input;

  //navigation
  const navigate = useNavigate();

  //Hooks
  const [form] = useForm();

  //Graphql Mutation
  const [createDocumentType, { loading }] = useCreateDocumentTypeFrAdminMutation();

  // Handle Form Submit
  const createNewDocumentType = async (values: FormValues) => {
    try {
      await createDocumentType({
        variables: {
          data: {
            name: values?.name,
            required: values?.required == undefined || values?.required == false ? false : true,
            status: values?.status ? DocumentStatus.Active : DocumentStatus.Inactive,
            instructions: JSON.stringify({ eng: values?.english, hi: values?.hindi })
          }
        },
        onCompleted() {
          form.resetFields();
          notification.success({
            message: "Document type added successfully"
          });
          navigate(-1);
        },
        refetchQueries: [DocumentTypesDocument]
      });
    } catch (err) {
      notification.error({
        message: "Error Creating Document Type"
      });
    }
  };

  return (
    <>
      <Modal
        open
        destroyOnClose
        onOk={form.submit}
        onCancel={() => navigate(-1)}
        okText={"Save"}
        title={"Add a New Document Type"}
        cancelButtonProps={{ style: { display: "none" } }}
        centered
        confirmLoading={loading}
      >
        <Form form={form} onFinish={createNewDocumentType} layout="vertical">
          <Form.Item
            name={"name"}
            required
            label={"Document Name"}
            rules={[{ required: true, message: "This field is required" }]}
          >
            <Input placeholder="Aadhar card" />
          </Form.Item>
          <div className="mb-2 ">Document Description</div>
          <Form.Item
            name={"english"}
            required
            label={"English"}
            rules={[{ required: true, message: "Document name is required" }]}
          >
            <TextArea rows={2} placeholder="Start typing in English" name="eng" />
          </Form.Item>
          <Form.Item
            name={"hindi"}
            required
            label={"Hindi"}
            rules={[{ required: true, message: "Document name is required" }]}
          >
            <TextArea rows={2} placeholder="हिंदी में लिखना शुरू करें" name="hi" />
          </Form.Item>
          <Form.Item
            name={"required"}
            label={"Is this Document Required ?"}
            valuePropName="checked"
          >
            <Checkbox>Required</Checkbox>
          </Form.Item>
          <Form.Item name={"status"} label={"Status"}>
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
export default CreateDocumentType;
