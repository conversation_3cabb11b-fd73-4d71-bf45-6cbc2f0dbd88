import { ChevronRight, MessageSquare } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useUserDetailsQuery } from "../../__generated__";

export function TechnicianFeedbackCard() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { data: userDetails } = useUserDetailsQuery();
  const userId = userDetails?.userDetail?.data?.id;

  const handleFeedbackCardClick = () => {
    if (userId) {
      navigate(`/technician-feedback/${userId}`);
    }
  };

  return (
    <div
      onClick={handleFeedbackCardClick}
      className="bg-white/45 mb-5 rounded-xl p-4 shadow-sm hover:shadow-md transition-all relative overflow-hidden group cursor-pointer"
    >
      <div className="absolute inset-0 bg-[radial-gradient(#4444ff11_1px,transparent_1px)] [background-size:16px_16px] opacity-40"></div>

      <div className="flex items-center justify-between relative">
        <div className="flex items-center gap-3">
          <div
            className="w-20 h-12 rounded-xl flex items-center justify-center shadow-lg transition-all 
              bg-gradient-to-br from-purple-500 to-indigo-600 shadow-purple-500/20
              group-hover:scale-105"
          >
            <MessageSquare className="h-6 w-6 text-white" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold text-gray-800">{t("feedback_header")}</h3>
            </div>
            <p className="text-sm text-gray-600 mt-0.5">{t("profile_feedback")}</p>
          </div>
        </div>
        <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-purple-500 group-hover:translate-x-1 transition-all" />
      </div>
    </div>
  );
}
