/*
  Warnings:

  - You are about to drop the column `order` on the `admin_module` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "admin_module" DROP COLUMN "order",
ADD COLUMN     "visible_in_sp" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "visible_in_users" BOOLEAN NOT NULL DEFAULT true,
ALTER COLUMN "parent_module_id" DROP NOT NULL;

-- AlterTable
ALTER TABLE "admin_protected_feature" ADD COLUMN     "icon_name" TEXT NOT NULL DEFAULT 'RiLayoutGridLine',
ADD COLUMN     "is_module" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';
