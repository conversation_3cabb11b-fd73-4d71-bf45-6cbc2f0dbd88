import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Divider,
  Dropdown,
  Form,
  Layout,
  message,
  notification,
  Row,
  Spin,
  Typography
} from "antd";
import FormBuilder from "antd-form-builder";
import { useForm } from "antd/es/form/Form";
import React, { useEffect, useState } from "react";
import { RiArrowDownSLine } from "react-icons/ri";
import { useMediaQuery } from "react-responsive";
import { useNavigate, useParams } from "react-router-dom";
import {
  useGetUserChecklistDataQuery,
  useUpdateTechnicianFeedbackDataMutation
} from "../../__generated__";
import { createAntdFormMeta } from "../../utils/formbuilder.helper";
import {
  technicianFeedbackForm,
  technicianFeedbackFormPureHindi
} from "../../utils/technicianFeedbackUtils";
import TechnicianFeedbackSubmitted from "./SubmittedForm";
const { Content } = Layout;

// //map for checklist id from form and checklist name from db checklist

interface FieldType {
  key: string;
  label?: string;
  [key: string]: any;
}
const TechnicianFeedback: React.FC = () => {
  const [meta, setMeta] = useState<FieldType[]>([]);
  const [form] = useForm();
  const navigate = useNavigate();

  const params = useParams();
  const userId = Number(params.user_id);
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  const [isAlreadySubmitted, setIsAlreadySubmitted] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  // Get language from query string
  const searchParams = new URLSearchParams(window.location.search);
  const language = searchParams.get("language") === "hindi" ? "hindi" : "en";

  const languages = [
    { code: "en", name: "English" },
    { code: "hindi", name: "हिंदी" }
  ];

  const handleLanguageChange = (value: string) => {
    const newSearchParams = new URLSearchParams(window.location.search);
    if (value === "en") {
      newSearchParams.delete("language");
    } else {
      newSearchParams.set("language", value);
    }
    navigate(`${window.location.pathname}?${newSearchParams.toString()}`);
  };

  // Query to check if user has already submitted feedback
  const { loading: checklistLoading } = useGetUserChecklistDataQuery({
    variables: { userId },
    onCompleted: (data) => {
      // Check if any technician feedback is already completed
      const hasCompletedFeedback = data?.getUserChecklistData?.data?.some((checklist) =>
        checklist?.user_checklist_data?.some((item) => item?.technician_completed)
      );

      if (hasCompletedFeedback) {
        setIsSubmitted(true);
        setIsAlreadySubmitted(true);
      }
      setIsLoading(false);
    },
    onError: () => {
      setIsLoading(false);
    },
    fetchPolicy: "no-cache"
  });

  // Initialize the mutation hook
  const [updateTechnicianFeedbackData, { loading: updateLoading }] =
    useUpdateTechnicianFeedbackDataMutation({
      onCompleted: (data) => {
        if (data?.updateTechnicianFeedbackData?.result) {
          message.success(
            language === "en"
              ? "Feedback submitted successfully!"
              : "प्रतिक्रिया सफलतापूर्वक भेजी गई!"
          );
          setIsSubmitted(true);
        } else {
          message.error(data?.updateTechnicianFeedbackData?.message || "Failed to submit feedback");
        }
      },
      onError: (error) => {
        notification.error({
          message: error.message
        });
      }
    });

  useEffect(() => {
    try {
      const formMeta = JSON.stringify(
        language === "hindi" ? technicianFeedbackFormPureHindi : technicianFeedbackForm
      );
      const meta = createAntdFormMeta(formMeta) as FieldType[];
      setMeta(meta);
    } catch (error) {
      console.error("Error parsing technician feedback template:", error);
    }
  }, [form, language]);

  const mapMetaToBooleanWithLabels = (meta: Record<string, string>) => {
    const result: Record<string, { value: boolean | number; label: string }> = {};
    for (const field of technicianFeedbackForm.originalFields) {
      const selectedId = meta[field.id];
      result[field.id] = {
        value: field.id === "onboarding-rating" ? Number(selectedId) : !!selectedId,
        label: field.id === "onboarding-rating" ? "Rating" : field.field
      };
    }
    return result;
  };
  const handleSubmit = (values: any) => {
    const parsedMeta = mapMetaToBooleanWithLabels(values);
    const formData = {
      user_id: Number(params.user_id),
      language: language === "hindi" ? "hi" : "en",
      meta: parsedMeta
    };
    updateTechnicianFeedbackData({
      variables: {
        data: formData
      }
    });
  };

  if (isLoading || checklistLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spin size="large" tip="Loading..." />
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <TechnicianFeedbackSubmitted isAlreadySubmitted={isAlreadySubmitted} language={language} />
    );
  }
  return (
    <Layout className="min-h-screen w-screen bg-gray-50" style={{ userSelect: "none" }}>
      <Content className="py-6 px-4 md:px-6 lg:px-8 overflow-y-auto h-full">
        <Row justify="center">
          <Col xs={24} sm={22} md={20} lg={18} xl={16}>
            <Card
              className="shadow-lg"
              style={{
                borderRadius: "12px",
                boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)"
              }}
              title={
                <div
                  style={{
                    textAlign: "center",
                    padding: "20px 0",
                    borderBottom: "1px solid #f0f0f0",
                    width: "100%"
                  }}
                >
                  <div className="flex justify-end mb-4">
                    <Dropdown
                      menu={{
                        items: languages.map((lang) => ({
                          key: lang.code,
                          label: lang.name,
                          onClick: () => handleLanguageChange(lang.code),
                          style:
                            language === lang.code
                              ? {
                                  backgroundColor: "#faf5ff",
                                  width: "120px",
                                  borderRadius: "4px",
                                  margin: "0"
                                }
                              : { width: "120px", margin: "0" }
                        }))
                      }}
                      dropdownRender={(menu) => (
                        <div className="w-[120px] overflow-hidden rounded-md shadow-lg bg-white border">
                          <div className="flex flex-col">{menu}</div>
                        </div>
                      )}
                      placement="bottomRight"
                      trigger={["click"]}
                    >
                      <div className="flex items-center cursor-pointer hover:opacity-80 transition-opacity">
                        <img src="/icons/language_change_icon.svg" width={25} alt="Language" />
                        <RiArrowDownSLine color="black" />
                      </div>
                    </Dropdown>
                  </div>
                  <div className="flex justify-center items-center mb-4">
                    <div
                      style={{
                        margin: 0,
                        padding: 0,
                        fontSize: isMobile ? "24px" : "32px",
                        display: "flex",
                        alignItems: "center",
                        fontWeight: 600,
                        maxWidth: "100%",
                        flexWrap: "wrap",
                        justifyContent: "center",
                        gap: "8px"
                      }}
                    >
                      <div className="flex flex-wrap items-center justify-center ">
                        <span className="whitespace-nowrap">
                          {language === "en" ? "Welcome to " : ""}
                        </span>
                        <img
                          src="/images/logo.png"
                          alt="WIFY Logo"
                          style={{
                            height: isMobile ? "26px" : "40px",
                            margin: "0 4px"
                          }}
                        />
                        <span className="">🎉</span>
                      </div>
                    </div>
                  </div>
                  <Typography.Text
                    className="text-center block text-gray-600"
                    style={{
                      fontSize: isMobile ? "16px" : "18px",
                      lineHeight: 1.5,
                      maxWidth: "100%",
                      wordBreak: "normal",
                      overflowWrap: "break-word"
                    }}
                  >
                    {language === "en"
                      ? "We're glad to have you on board."
                      : "WIFY से जुड़ने के लिए आपका धन्यवाद।"}
                  </Typography.Text>
                </div>
              }
            >
              <div className="px-2 md:px-4">
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleSubmit}
                  requiredMark={true}
                  validateMessages={{
                    required: language === "en" ? "This field is required" : "यह फील्ड आवश्यक है"
                  }}
                >
                  <div className="text-center mb-8 bg-purple-50 p-6 rounded-xl border border-purple-100">
                    <Typography.Text
                      className="text-lg md:text-xl font-medium text-gray-700"
                      style={{
                        lineHeight: 1.5,
                        maxWidth: "100%",
                        wordBreak: "normal",
                        overflowWrap: "break-word",
                        display: "inline-block"
                      }}
                    >
                      {language === "en"
                        ? "Kindly complete the Onboarding Completion and Experience Feedback form below."
                        : "कृपया फीडबैक फॉर्म भरें।"}
                    </Typography.Text>
                  </div>
                  <div className="py-4 px-2 md:px-6 max-w-full overflow-x-hidden">
                    <FormBuilder form={form} meta={meta} />
                  </div>

                  <Divider style={{ marginTop: 40, marginBottom: 32 }} />

                  <div className="flex justify-center">
                    <Form.Item className="text-center mb-0">
                      <Button
                        loading={updateLoading}
                        type="primary"
                        htmlType="submit"
                        size="large"
                        className="hover:shadow-lg transition-all duration-300"
                        style={{
                          minWidth: isMobile ? "140px" : "180px",
                          height: "48px",
                          fontSize: "16px",
                          fontWeight: 500,
                          borderRadius: "8px",
                          boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                        }}
                      >
                        {language === "en" ? "Submit Feedback" : "सबमिट करें"}
                      </Button>
                    </Form.Item>
                  </div>
                </Form>
              </div>
            </Card>
          </Col>
        </Row>
      </Content>
    </Layout>
  );
};

export default TechnicianFeedback;
