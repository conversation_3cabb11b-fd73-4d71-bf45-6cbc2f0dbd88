// create a 404 page
import { But<PERSON>, Result } from "antd";
import React from "react";
import { Link, useNavigate } from "react-router-dom";

const NotAuthorized: React.FC = () => {
  const navigate = useNavigate();
  return (
    <Result
      status="403"
      title="403"
      subTitle="Oops! You don't have permission to view this page. Please contact your administrator."
      extra={
        <Link to="/">
          <Button
            onClick={() => {
              const userTypeChanged = localStorage.getItem("userTypeChanged");
              if (userTypeChanged) {
                const currentBasePath = localStorage.getItem("base_path") || "";
                if (currentBasePath === "sp") {
                  localStorage.setItem("base_path", "");
                } else {
                  localStorage.setItem("base_path", "sp");
                }
              }
              navigate(-1);
            }}
            type="primary"
          >
            {" "}
            Back to Previous Page{" "}
          </Button>
        </Link>
      }
    />
  );
};

export default NotAuthorized;
