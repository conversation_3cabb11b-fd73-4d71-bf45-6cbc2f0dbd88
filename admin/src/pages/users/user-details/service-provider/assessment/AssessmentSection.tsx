import { GoArrowRight } from "react-icons/go";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>board<PERSON>heck } from "react-icons/lu";

import { Empty, Tooltip } from "antd";
import dayjs from "dayjs";
import { useNavigate, useParams } from "react-router-dom";
import {
  useGetAllFeedbacksQuery,
  useGetAllUserAssignmentsQuery
} from "../../../../../__generated__";
import ErrorComponent from "../../../../../components/error/Error";
import Loading from "../../../../../components/loading/Loading";
import { calculatePercentage } from "../../../../../utils/utils";
import RatingDisplay from "../../common/RatingDisplay";
import SkillProgress from "../../common/SkillProgress";

export interface InterviewRating {
  id: number;
  rating: number;
  date: string;
  interviewer: string;
  comments?: string;
}

export interface AssessmentStats {
  averageRating: number;
  totalInterviews: number;
  latestRating?: InterviewRating;
}

export const calculateStats = (ratings: InterviewRating[]): AssessmentStats => {
  if (!ratings.length) {
    return {
      averageRating: 0,
      totalInterviews: 0
    };
  }

  const average = ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length;

  return {
    averageRating: Number(average.toFixed(1)),
    totalInterviews: ratings.length,
    latestRating: ratings[ratings.length - 1]
  };
};
export interface TestResult {
  id: number;
  skillName: string;
  testName: string;
  percentage: number;
  completedAt: string;
}

export const calculateOverallScore = (tests: TestResult[]): number => {
  if (!tests.length) return 0;
  const total = tests.reduce((sum, test) => sum + test.percentage, 0);
  return Number((total / tests.length).toFixed(1));
};

interface AssessmentSectionProps {
  interviewRatings: InterviewRating[];
  testResults: TestResult[];
  averageRating: number;
  totalInterviews: number;
  setActiveKey: any;
  org_id?: number;
}

const AssessmentSection = ({ setActiveKey, org_id }: AssessmentSectionProps) => {
  const { user_id } = useParams();
  const navigate = useNavigate();
  const { data, error, loading } = useGetAllFeedbacksQuery({
    variables: {
      org_id: org_id || -1
    }
  });
  const {
    data: userAssignments,
    loading: userAssignmentsLoading,
    error: userAssignmentsError
  } = useGetAllUserAssignmentsQuery({
    variables: {
      userId: Number(user_id) || -1
    }
  });
  if (error || userAssignmentsError) {
    return <ErrorComponent error={error || userAssignmentsError} />;
  }

  if (loading || userAssignmentsLoading) {
    return <Loading tip="Loading ..." />;
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div
        style={{
          borderBottom: "1px solid #e5e7eb",
          borderBottomWidth: "1px",
          borderBottomColor: "#e5e7eb",
          borderBottomStyle: "solid"
        }}
        className="px-6 py-5 border-b border-gray-200"
      >
        <div className="flex items-center">
          <LuClipboardCheck className="h-6 w-6  text-indigo-600 mr-2" />
          <div className="  text-xl font-semibold text-gray-900">Assessment</div>
        </div>
      </div>

      <div className="p-6">
        <div className="flex flex-col gap-6">
          <div>
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-x-1">
                <div className="text-lg font-medium text-gray-900">
                  Part 1: Interview Assessment
                </div>
              </div>
              <div
                className=" flex gap-x-2 items-center justify-end text-colorPrimary cursor-pointer"
                onClick={() => {
                  setActiveKey("interview");
                  navigate(`/dashboard/sp/details/${user_id}/interview`);
                }}
              >
                <span>View All Interviews</span>
                <GoArrowRight />
              </div>
            </div>
            <div className="overflow-x-auto pb-4 ">
              <div className="inline-flex gap-4 min-w-full">
                {data?.getAllFeedbacks?.length ? (
                  data?.getAllFeedbacks.slice(0, 3).map((feedback) => (
                    <div
                      key={feedback?.id}
                      className="bg-gray-50 rounded-lg p-4 min-w-[300px] max-w-[300px] h-[184px]  max-h-[184px]  cursor-default"
                    >
                      <div className="flex justify-between items-start mb-3">
                        <div className="font-medium text-gray-900 capitalize  max-w-[60%] overflow-hidden whitespace-nowrap text-ellipsis">
                          {feedback?.interviewer?.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {dayjs(feedback?.meta?.interviewer_feedback_submitted_at).format(
                            "YYYY-MM-DD"
                          )}
                        </div>
                      </div>

                      <div className="mb-3">
                        <RatingDisplay
                          rating={feedback?.interviewer_feedback_result?.["overall-rating"]}
                          size="lg"
                        />
                      </div>
                      <Tooltip
                        title={(feedback?.interview?.length && feedback?.interview[0]?.note) || ""}
                        placement="bottomLeft"
                      >
                        <p className="text-sm text-gray-600 max-h-[80px] min-w-[268px] overflow-hidden text-ellipsis ">
                          {(feedback?.interview?.length && feedback?.interview[0]?.note) || ""}
                        </p>
                      </Tooltip>
                    </div>
                  ))
                ) : (
                  <div className="bg-gray-50 rounded-lg p-4 w-full">
                    <Empty description="No Assessment Available" />
                  </div>
                )}
              </div>
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-4">
              <div className="text-lg font-medium text-gray-900">Part 2: Test Assessment</div>

              <div
                className="flex gap-x-2 items-center justify-end text-colorPrimary cursor-pointer"
                onClick={() => {
                  setActiveKey("assignment");
                  navigate(`/dashboard/sp/details/${user_id}/assignment`);
                }}
              >
                <span>View All Tests</span>
                <GoArrowRight />
              </div>
            </div>
            <div className="overflow-x-auto pb-4">
              <div className="inline-flex gap-4 min-w-full">
                {userAssignments?.getUserAllAssignmentResult?.length ? (
                  userAssignments?.getUserAllAssignmentResult?.slice(0, 3).map((assignment) => (
                    <div
                      key={assignment?.id}
                      className="bg-gray-50 rounded-lg p-4  min-w-[300px] max-w-[300px] cursor-default"
                    >
                      <div className="flex items-center justify-between mb-5">
                        <div className="flex items-center gap-2 max-w-[60%] ">
                          <Tooltip title="System Generated">
                            <div className="flex items-center text-xs text-gray-500">
                              <LuBot className="h-4 w-4 mr-1" />
                            </div>
                          </Tooltip>
                          <Tooltip
                            title={assignment?.assignment_configration?.name}
                            placement="bottom"
                          >
                            <div className="font-medium text-gray-900 capitalize  overflow-hidden whitespace-nowrap text-ellipsis">
                              {assignment?.assignment_configration?.name}
                            </div>
                          </Tooltip>
                        </div>
                        <div className="text-sm text-gray-500 whitespace-nowrap">
                          {dayjs(assignment?.finished_at).format("YYYY-MM-DD")}
                        </div>
                      </div>
                      <div className="mb-3">
                        <SkillProgress
                          percentage={
                            assignment?.result
                              ? calculatePercentage(
                                  assignment?.result?.correct_answer,
                                  assignment?.result?.total_questions
                                )
                              : null
                          }
                          className="mb-3"
                        />
                        <div className="flex gap-x-1 text-sm font-medium text-gray-900">
                          <div className="flex text-gray-500">Test Result : </div>
                          <div>
                            {assignment?.result
                              ? calculatePercentage(
                                  assignment?.result?.correct_answer,
                                  assignment?.result?.total_questions
                                )
                              : null}
                            %
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="bg-gray-50 rounded-lg p-4 w-full">
                    <Empty description="No Assessment Available" />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default AssessmentSection;
