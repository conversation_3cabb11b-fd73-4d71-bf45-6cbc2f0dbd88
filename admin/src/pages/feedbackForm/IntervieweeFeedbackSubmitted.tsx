import { CheckCircleFilled } from "@ant-design/icons";
import { <PERSON>, <PERSON>, Divider, Layout, Row, Typography } from "antd";

const { Header, Content } = Layout;
const { Title, Text } = Typography;

const IntervieweeFeedbackSubmitted = () => {
  return (
    <Layout className="min-h-screen bg-gray-50" style={{ userSelect: "none" }}>
      {/* Header with WIFY logo */}
      <Header className="bg-white p-0 h-auto shadow-sm sticky top-0 z-10">
        <div className="py-2 md:px-6 flex items-center justify-start">
          <img src="/images/logo.png" alt="WIFY Logo" className="w-24 md:w-28" />
        </div>
        <Divider className="m-0" />
      </Header>

      {/* Centered Content */}
      <Content className="pt-8 px-2 pb-12 overflow-y-auto h-full">
        <Row justify="center">
          <Col xs={24} sm={22} md={20} lg={18} xl={16}>
            <Card className="shadow-md text-center" style={{ borderRadius: "8px" }}>
              <div className="py-8 px-4">
                <CheckCircleFilled
                  style={{
                    fontSize: 80,
                    color: "#52c41a",
                    marginBottom: "24px"
                  }}
                />

                <Title level={2} style={{ marginBottom: "16px" }}>
                  Feedback Submitted Successfully!
                </Title>

                <Text style={{ fontSize: "16px", display: "block", marginBottom: "32px" }}>
                  Thank you for providing your valuable feedback. Your input has been recorded.
                </Text>
              </div>
            </Card>
          </Col>
        </Row>
      </Content>
    </Layout>
  );
};

export default IntervieweeFeedbackSubmitted;
