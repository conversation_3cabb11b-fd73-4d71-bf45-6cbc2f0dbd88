mutation CreateReferralConfig($data: ReferralConfigInput) {
  createReferralConfigAdmin(data: $data) {
    existing_user_points
    new_user_points
    user_point_redeem_stage
    referral_enabled
    terms_and_condition
  }
}
query GetReferralConfiguration($configurationId: Int) {
  getReferralConfiguration(configuration_id: $configurationId) {
    existing_user_points
    new_user_points
    referral_enabled
    user_point_redeem_stage
    id
    terms_and_condition
  }
}

query GetAllReferredUsersOfUser($userId: Int) {
  getAllReferredUsersOfUser(user_id: $userId) {
    id
    referral_code
    available_points
    user_id
    user_referral_ledger_id
    referred_by_id
    created_at
    referred_to {
      id
      name
      organization_id
      isActive
      created_at
      updated_at
      onboarding_stage
      source
      interview {
        status
      }
    }
  }
}

query GetExportableReferredData(
  $filter: ExportableReferredData
  $search: String
  $pagination: Pagination
) {
  getExportableReferredData(filter: $filter, search: $search, pagination: $pagination) {
    data {
      available_points
      created_at
      referral_code
      referred_by {
        created_at
        id
        name
        onboarding_stage
        updated_at
        designation {
          id
          name
          level
        }
        user_referred_to {
          referral_code
        }
      }
      referred_count
      referred_to {
        created_at
        id
        name
        onboarding_stage
        updated_at
        user_referred_to {
          referral_code
        }
        designation {
          name
          id
        }
      }
      referrer_code
      updated_at
    }
    total_count
  }
}

query GetUnregisteredReferrals($search: String, $pagination: Pagination) {
  getUnregisteredReferrals(search: $search, pagination: $pagination) {
    data {
      id
      name
      phone_number
      referrer {
        id
        name
        phone
        user_referred_to {
          referral_code
        }
      }
      created_at
      updated_at
      meta
    }
    total_count
  }
}

mutation updateReferralConfig($configurationId: Int!, $data: UpdateReferralConfigInput) {
  updateReferralConfigurations(configuration_id: $configurationId, data: $data)
}

query ExportUnregisteredReferrals($search: String, $startDate: DateTime, $endDate: DateTime) {
  exportUnregisteredReferrals(search: $search, start_date: $startDate, end_date: $endDate) {
    data {
      id
      name
      phone_number
      referrer {
        id
        name
        phone
        user_referred_to {
          referral_code
        }
      }
      created_at
      updated_at
    }
    total_count
  }
}
