-- CreateEnum
CREATE TYPE "AppPlatformType" AS ENUM ('ANDROID', 'WEB');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "AppInstallationStatus" AS ENUM ('INSTALLED', 'UNINSTALLED');

-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';

-- CreateTable
CREATE TABLE "app_installation" (
    "id" TEXT NOT NULL,
    "user_id" INTEGER NOT NULL,
    "platform_type" "AppPlatformType" NOT NULL,
    "installation_date" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "uninstall_date" TIMESTAMP(3),
    "status" "AppInstallationStatus" NOT NULL DEFAULT 'INSTALLED',
    "meta" JSONB DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "app_installation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "app_installation_user_id_platform_type_idx" ON "app_installation"("user_id", "platform_type");

-- CreateIndex
CREATE INDEX "app_installation_installation_date_idx" ON "app_installation"("installation_date");

-- CreateIndex
CREATE INDEX "app_installation_uninstall_date_idx" ON "app_installation"("uninstall_date");

-- CreateIndex
CREATE INDEX "app_installation_status_idx" ON "app_installation"("status");

-- CreateIndex
CREATE INDEX "app_installation_user_id_platform_type_status_idx" ON "app_installation"("user_id", "platform_type", "status");

-- CreateIndex
CREATE UNIQUE INDEX "app_installation_user_id_platform_type_status_key" ON "app_installation"("user_id", "platform_type", "status");

-- AddForeignKey
ALTER TABLE "app_installation" ADD CONSTRAINT "app_installation_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
