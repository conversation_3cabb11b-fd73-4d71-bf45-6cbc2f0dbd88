import { AppInstallationStatus } from '@prisma/client'
import dayjs from 'dayjs'
import DataProvider from '../data'
import {
    AppPlatformType,
    TrackAnalyticsInput,
} from '../graphql/__generated__/types'
import { getLocationFromIp } from '../utils/location'
import { Context } from '../utils/types'

class AnalyticsService {
    private prisma = DataProvider.getDbInstance()

    public async trackAppActivity(data: TrackAnalyticsInput, ctx: Context) {
        const {
            ipAddress,
            activityType,
            platformType,
            funnelStage,
            userAgent,
            meta,
            deviceId,
            referrer,
            deviceInfo,
            installationStatus,
        } = data

        const location = ipAddress ? getLocationFromIp(ipAddress) : null

        //check if data already exists
        if (!deviceId) {
            throw new Error('Device ID is required for app analytics')
        }
        // const existingData = await this.prisma.app_installation.findUnique({
        //     where: {
        //         device_id: deviceId,
        //     },
        // })

        if (activityType === 'APP_INSTALL') {
            const inputData = {
                platform_type:
                    (platformType as AppPlatformType) || AppPlatformType.Web,
                installation_date: new Date().toISOString(),
                status: installationStatus || AppInstallationStatus.INSTALLED,
                city: location?.city || undefined,
                state: location?.state || undefined,
                country: location?.country || undefined,
                ip_address: ipAddress || undefined,
                user_agent: userAgent || undefined,
                device_id: deviceId,
                referrer: referrer || undefined,
                device_info: {
                    ...(deviceInfo || {}),
                    lastUpdated: new Date().toISOString(),
                },
                last_active_at: new Date().toISOString(),
                is_dropped: true,
                meta: {
                    ...(meta || {}),
                    funnel_stage: funnelStage || undefined,
                },
            }

            const appInstallation = await this.prisma.app_installation.upsert({
                where: {
                    device_id: deviceId,
                },
                update: {
                    ...inputData,
                    installation_date: undefined, //do not update installation date
                },
                create: inputData,
            })

            //Update device activity daily table
            const checkIfDeviceActivityExists =
                await this.prisma.device_activity_daily.findFirst({
                    where: {
                        device_id: deviceId,
                        date: {
                            gte: dayjs().startOf('day').toDate(),
                            lt: dayjs().endOf('day').toDate(),
                        },
                    },
                })
            if (checkIfDeviceActivityExists) {
                return checkIfDeviceActivityExists
            } else {
                return await this.prisma.device_activity_daily.create({
                    data: {
                        device_id: deviceId,
                        date: new Date().toISOString(),
                        platform_type: platformType as AppPlatformType,
                        app_installation_id: appInstallation.id,
                    },
                })
            }
        }
        if (activityType === 'SIGN_UP') {
            const user_id = ctx.user?.id
            await this.prisma.app_installation.update({
                where: {
                    device_id: deviceId,
                },
                data: {
                    is_dropped: false,
                    user_id: user_id,
                    last_active_at: new Date().toISOString(),
                },
            })
            //TODO: When app open event is added remove this and use there
            await this.prisma.app_installation_user_map.create({
                data: {
                    device_id: deviceId,
                    user_id: user_id,
                },
            })
            return await this.prisma.device_activity_daily.create({
                data: {
                    device_id: deviceId,
                    date: new Date().toISOString(),
                    platform_type: 'ANDROID',
                    app_installation: {
                        connect: {
                            device_id: deviceId,
                        },
                    },
                    user: {
                        connect: {
                            id: user_id,
                        },
                    },
                },
            })
        }
        if (activityType === 'LOGIN') {
            const user_id = ctx.user?.id
            const appInstallation = await this.prisma.app_installation.update({
                where: {
                    device_id: deviceId,
                },
                data: {
                    is_dropped: false,
                    user_id: user_id,
                    last_active_at: new Date().toISOString(),
                },
            })
            //TODO: When app open event is added remove this and use there
            return await this.prisma.device_activity_daily.create({
                data: {
                    device_id: deviceId,
                    date: new Date().toISOString(),
                    platform_type: 'ANDROID',
                    app_installation: {
                        connect: {
                            id: appInstallation.id,
                        },
                    },
                },
            })
        }
        return null
    }
}

export default AnalyticsService
