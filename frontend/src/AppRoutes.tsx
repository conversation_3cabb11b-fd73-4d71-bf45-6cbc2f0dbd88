import { Capacitor } from "@capacitor/core";
import { Image } from "@chakra-ui/react";
import "@fontsource/montserrat";
import { AnimatePresence } from "framer-motion";
import LogRocket from "logrocket";
import mixpanel from "mixpanel-browser";
import React, { useEffect, useState } from "react";
import "react-circular-progressbar/dist/styles.css";
import { useLocation, useNavigate, useRoutes } from "react-router-dom";
import { useUserDetailsQuery } from "./__generated__";
import initFCM from "./hooks/fcm";
import UrlListner from "./hooks/urlListner";
import { useAnalytics } from "./hooks/useAnalytics";
import "./lib/multiLanguage";
import InitialRoutingProvider from "./providers/InitialRoutingProvider";
import OneAppIntegration from "./providers/OneAppIntegration";
import { routes } from "./routes";
import { getSimCardsPermission } from "./services/CapacitorInit";
import setupKeepAwakeForAndroid from "./services/KeepAwakeForAndroid";
import { checkAuthTokenFromLocalStorage, getUserMetaLocalStorage } from "./utils/auth";
import { debounce, redirectUserIfFeedbackExist } from "./utils/common";
import { getRouteSection } from "./utils/routeUtils";

if (import.meta.env.VITE_NODE_ENV === "production") {
  mixpanel.init(import.meta.env.VITE_MIXPANEL_TOKEN);
  // ReactGA.initialize(import.meta.env.VITE_GOOGLE_ANALYTICS_TRACKING_ID);
  LogRocket.init(import.meta.env.VITE_LOG_ROCKET_API);
} else {
  mixpanel.track = () => {};
  mixpanel.identify = () => {};
}

const AppRoutes: React.FC = () => {
  initFCM();
  const navigate = useNavigate();
  const location = useLocation();
  const current_route = useRoutes(routes);
  const { trackAppOpen } = useAnalytics();

  const [isLoading, setIsLoading] = useState(true);
  const { data: userData, loading: userDataLoading } = useUserDetailsQuery();

  //This function is for tracking events for both google analytics and logrocket
  const trackUserEventsOnPageLoad = () => {
    const token: string = localStorage.getItem("onboarding_token") || "";
    if (!token) return;
    const decodedToken = atob(token?.split(".")[1]);
    const parsedToken = JSON.parse(decodedToken);
    // googleAnalyticsPageLoadType("pageview", location.pathname);
    if (parsedToken?.name && parsedToken?.phone) {
      if (import.meta.env.PROD) {
        LogRocket.identify(parsedToken.name, {
          name: parsedToken?.name,
          phone: parsedToken?.phone
        });
      }
    }
  };

  React.useEffect(() => {
    debounce(trackUserEventsOnPageLoad, 1000)();
  }, [location.pathname]);

  const navigateToLastRoute = async () => {
    try {
      const current_route = getRouteSection(location.pathname);

      const isAuthenticated = !(await checkAuthTokenFromLocalStorage());
      const transferred_to_tms = await getUserMetaLocalStorage("transferred_to_tms");

      // If user is authenticated and we have user data, check for last_location
      if (isAuthenticated && userData?.userDetail?.data) {
        //check if the user is transferred to tms
        if (transferred_to_tms && current_route === "auth") {
          navigate("/home");
          return;
        }
        const lastLocation = userData.userDetail.data.meta?.last_location;

        if (
          lastLocation &&
          current_route === "auth" &&
          !userData?.userDetail?.data?.transfer_to_tms_done
        ) {
          navigate(lastLocation);
          return;
        }
      }
    } catch (error) {
      console.error("Navigation Error: ", error);
    } finally {
      setIsLoading(false); // Hide loader after navigation
    }
  };
  useEffect(() => {
    // Initialize Capacitor plugins
    const setupCapacitor = async () => {
      if (Capacitor.getPlatform() !== "android") return;
      try {
        await getSimCardsPermission();
        const cleanup = await setupKeepAwakeForAndroid();

        // Clean up when the component is unmounted
        return () => {
          cleanup();
        };
      } catch (error) {
        console.error("Capacitor Error ", error);
      }
    };
    trackAppOpen();

    setupCapacitor();
  }, []);
  useEffect(() => {
    // Only run navigation logic when userData is loaded or when we're on auth pages
    if (!userDataLoading || location.pathname.includes("/auth")) {
      navigateToLastRoute();
    }
  }, [userData, userDataLoading, location.pathname]);

  if (!current_route) {
    return <div>no route matched</div>;
  }

  // Show loading screen when initial loading or when waiting for API response
  if (isLoading || (userDataLoading && !location.pathname.includes("/auth"))) {
    return (
      <div className="w-full h-screen flex justify-center items-center">
        <Image src="/images/loading.gif" alt="Loading" className="w-[50px] h-[50px]" />
      </div>
    );
  }

  if (userData?.userDetail?.data?.id) {
    redirectUserIfFeedbackExist(userData?.userDetail?.data?.id, navigate);
  }
  return (
    <OneAppIntegration>
      <InitialRoutingProvider>
        <UrlListner />
        <AnimatePresence mode="wait">
          {React.cloneElement(current_route, { key: location.pathname })}
        </AnimatePresence>
      </InitialRoutingProvider>
    </OneAppIntegration>
  );
};

export default AppRoutes;
