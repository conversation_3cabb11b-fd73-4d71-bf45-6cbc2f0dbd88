/*
  Warnings:

  - You are about to drop the column `meta` on the `app_installation` table. All the data in the column will be lost.
  - You are about to drop the column `uninstall_date` on the `app_installation` table. All the data in the column will be lost.
  - Made the column `installation_date` on table `app_installation` required. This step will fail if there are existing NULL values in that column.

*/
-- <PERSON><PERSON>Enum
CREATE TYPE "UserFunnelStage" AS ENUM ('VISITOR', 'LOGIN_PAGE', 'SIGNUP_START', 'SIGNUP_COMPLETE', 'ONBOARDING_START', 'ONBOARDING_COMPLETE', 'ACTIVE_USER');

-- DropIndex
DROP INDEX "app_installation_uninstall_date_idx";

-- DropIndex
DROP INDEX "app_installation_user_id_platform_type_status_idx";

-- DropIndex
DROP INDEX "app_installation_user_id_platform_type_status_key";

-- AlterTable
ALTER TABLE "app_installation" DROP COLUMN "meta",
DROP COLUMN "uninstall_date",
ADD COLUMN     "city" TEXT,
ADD COLUMN     "country" TEXT,
ADD COLUMN     "device_id" TEXT,
ADD COLUMN     "device_info" JSONB DEFAULT '{}',
ADD COLUMN     "ip_address" TEXT,
ADD COLUMN     "referrer" TEXT,
ADD COLUMN     "state" TEXT,
ADD COLUMN     "uninstallation_date" TIMESTAMP(3),
ADD COLUMN     "user_agent" TEXT,
ALTER COLUMN "user_id" DROP NOT NULL,
ALTER COLUMN "installation_date" SET NOT NULL;

-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';

-- CreateTable
CREATE TABLE "user_session_screen_recording" (
    "id" TEXT NOT NULL,
    "user_id" INTEGER,
    "file_path" TEXT NOT NULL,
    "start_time" TIMESTAMP(3) NOT NULL,
    "end_time" TIMESTAMP(3),
    "meta" JSONB DEFAULT '{}',
    "duration" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_session_screen_recording_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "daily_analytics" (
    "id" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "total_visitors" INTEGER NOT NULL,
    "unique_visitors" INTEGER NOT NULL,
    "total_signups" INTEGER NOT NULL,
    "total_logins" INTEGER NOT NULL,
    "average_session_time" DOUBLE PRECISION NOT NULL,
    "bounce_rate" DOUBLE PRECISION NOT NULL,
    "platform_breakdown" JSONB NOT NULL,
    "language_breakdown" JSONB NOT NULL,
    "funnel_metrics" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "daily_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_activity" (
    "id" TEXT NOT NULL,
    "user_id" INTEGER,
    "session_id" TEXT NOT NULL,
    "ip_address" TEXT,
    "activity_type" TEXT NOT NULL,
    "platform_type" "AppPlatformType" NOT NULL,
    "language" "preferred_language" NOT NULL,
    "funnel_stage" "UserFunnelStage",
    "start_time" TIMESTAMP(3) NOT NULL,
    "end_time" TIMESTAMP(3),
    "time_spent" INTEGER,
    "url_path" TEXT,
    "user_agent" TEXT,
    "took_action" BOOLEAN NOT NULL DEFAULT false,
    "meta" JSONB DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "app_installation_id" TEXT,

    CONSTRAINT "user_activity_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "user_session_screen_recording_user_id_idx" ON "user_session_screen_recording"("user_id");

-- CreateIndex
CREATE INDEX "daily_analytics_date_idx" ON "daily_analytics"("date");

-- CreateIndex
CREATE UNIQUE INDEX "daily_analytics_date_key" ON "daily_analytics"("date");

-- CreateIndex
CREATE UNIQUE INDEX "user_activity_session_id_key" ON "user_activity"("session_id");

-- CreateIndex
CREATE INDEX "user_activity_user_id_idx" ON "user_activity"("user_id");

-- CreateIndex
CREATE INDEX "user_activity_session_id_idx" ON "user_activity"("session_id");

-- CreateIndex
CREATE INDEX "user_activity_activity_type_idx" ON "user_activity"("activity_type");

-- CreateIndex
CREATE INDEX "user_activity_platform_type_idx" ON "user_activity"("platform_type");

-- CreateIndex
CREATE INDEX "user_activity_app_installation_id_idx" ON "user_activity"("app_installation_id");

-- AddForeignKey
ALTER TABLE "user_session_screen_recording" ADD CONSTRAINT "user_session_screen_recording_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_activity" ADD CONSTRAINT "user_activity_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_activity" ADD CONSTRAINT "user_activity_app_installation_id_fkey" FOREIGN KEY ("app_installation_id") REFERENCES "app_installation"("id") ON DELETE SET NULL ON UPDATE CASCADE;
