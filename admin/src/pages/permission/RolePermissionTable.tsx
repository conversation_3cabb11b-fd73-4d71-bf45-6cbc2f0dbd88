import { InfoCircleOutlined } from "@ant-design/icons";
import { Checkbox, Form, Input, notification, Segmented, Table, Tooltip } from "antd";
import { useEffect, useState } from "react";
import { CgChevronDown, CgChevronUp } from "react-icons/cg";
import { decodeToken } from "react-jwt";

export interface PermissionSet {
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
}
export interface FeaturePermissionData {
  id?: number;
  admin_role_id?: number;
  feature_id: number;
  permission_for_sp: PermissionSet;
  permission_for_user: PermissionSet;
  [key: string]: any;
}

const CustomCheckboxGroup = ({ checkboxArray, setRoleData, roleData }: any) => {
  return (
    <div className="flex ml-2 min-w-[100px] justify-center items-center  ">
      {checkboxArray.map(({ title, customKey, tooltip, disabled }: any) => (
        <Tooltip title={tooltip}>
          <Checkbox
            key={customKey}
            checked={roleData?.[customKey]}
            disabled={disabled}
            onChange={(e) => {
              const newState = {
                ...roleData,
                [customKey]: e.target.checked
              };
              setRoleData(newState);
            }}
          >
            <span className="flex min-w-[90px] ">{title} </span>
          </Checkbox>
        </Tooltip>
      ))}
    </div>
  );
};
const InputAndCheckBoxGroup = ({
  inputName,
  label,
  rules,
  checkboxArray,
  roleData,
  setRoleData,
  placeholder
}: any) => {
  return (
    <div className="flex w-full items-center justify-between gap-x-2 ">
      <Form.Item name={inputName} label={label} className="flex-1" rules={rules}>
        <Input className="w-full " placeholder={placeholder} />
      </Form.Item>

      <CustomCheckboxGroup
        checkboxArray={checkboxArray}
        roleData={roleData}
        setRoleData={setRoleData}
      />
    </div>
  );
};

const RolePermissionTable = ({
  featurePermissionArray,
  setFeaturePermissionArray,
  roleData,
  setRoleData,
  addpermission,
  handleSave
}: any) => {
  const permissionsData = localStorage.getItem("permissionsToken");
  if (!permissionsData) return <></>;
  const { adminPermissions }: any = decodeToken(permissionsData);
  const [minimizedModulesSp, setMinimizedModulesSp] = useState<number[]>(
    adminPermissions
      ?.filter((feature: any) => feature.is_module)
      .map((feature: any) => feature.module_id) || []
  );
  const [minimizedModulesUser, setMinimizedModulesUser] = useState<number[]>(
    adminPermissions
      ?.filter((feature: any) => feature.is_module)
      .map((feature: any) => feature.module_id) || []
  );

  const [segmentedValue, setSegmentedValue] = useState("Individual");

  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    const selectAll = adminPermissions.every((adminPerm: any) => {
      const feature = featurePermissionArray?.find(
        (fp: any) => fp.feature_id === adminPerm.feature_id
      );
      if (!feature) return false;
      const targetPermissions =
        segmentedValue === "Individual" ? feature.permission_for_user : feature.permission_for_sp;
      return (
        targetPermissions.create &&
        targetPermissions.read &&
        targetPermissions.update &&
        targetPermissions.delete
      );
    });
    setSelectAll(selectAll);
  }, [featurePermissionArray, segmentedValue]);

  const handleToggleMinimize = (moduleId: number) => {
    if (segmentedValue === "Individual") {
      setMinimizedModulesUser((prevState: number[]) => {
        if (prevState.includes(moduleId)) {
          return prevState.filter((id: number) => id !== moduleId);
        }

        return [...prevState, moduleId];
      });
    } else {
      setMinimizedModulesSp((prevState: number[]) => {
        if (prevState.includes(moduleId)) {
          return prevState.filter((id: number) => id !== moduleId);
        }

        return [...prevState, moduleId];
      });
    }
  };
  useEffect(() => console.log(featurePermissionArray, "changes"), [featurePermissionArray]);
  /**
   * 1. When create/update/delete is selected, read is automatically selected
   * 2. When a module's read permission is unselected, all permissions for that module are cleared
   * 3. Permissions are tracked separately for service providers and individual users
   *
   */
  const handleCheckboxChange = (
    action: "create" | "read" | "update" | "delete",
    e: any,
    record: {
      key: number;
      is_module: boolean;
      feature_id: any;
      is_duplicate?: boolean;
      module_id: number;
    }
  ) => {
    const checked = e.target.checked;

    // Special action handling: when create/update/delete is selected, read should automatically be selected
    const isSpecialAction = ["create", "update", "delete"].includes(action);

    setFeaturePermissionArray((prev: any) => {
      // Special case: If a module's read permission is being turned off
      // All permissions for that module and its features should be cleared
      if (action === "read" && !checked && record?.is_module) {
        // If the module is not already minimized, minimize it for better UX
        if (
          segmentedValue === "Individual"
            ? !minimizedModulesUser?.includes(record?.module_id)
            : !minimizedModulesSp?.includes(record?.module_id)
        ) {
          handleToggleMinimize(record?.module_id);
        }

        // Process all features to remove permissions for those belonging to this module
        return prev.map((fp: any) => {
          const feature = adminPermissions.find((f: any) => f.feature_id === fp.feature_id);

          // Clear all permissions for features that belong to this module
          // Only affects the active permission type (SP or Individual) based on segmentedValue
          if (feature?.module_id === record?.module_id) {
            return {
              ...fp,
              permission_for_sp:
                segmentedValue !== "Individual"
                  ? { read: false, create: false, update: false, delete: false } // Clear SP permissions
                  : fp.permission_for_sp, // Keep SP permissions unchanged
              permission_for_user:
                segmentedValue === "Individual"
                  ? { read: false, create: false, update: false, delete: false } // Clear user permissions
                  : fp.permission_for_user // Keep user permissions unchanged
            };
          }
          return fp;
        });
      }

      const index = prev.findIndex((fp: any) => fp.feature_id === record.feature_id);

      if (index !== -1) {
        const updatedFeature = {
          ...prev[index],

          permission_for_user:
            segmentedValue === "Individual"
              ? {
                  ...prev[index].permission_for_user,
                  [action]: checked,
                  ...(isSpecialAction && checked ? { read: true } : {})
                }
              : prev[index].permission_for_user,

          permission_for_sp:
            segmentedValue !== "Individual"
              ? {
                  ...prev[index].permission_for_sp,
                  [action]: checked,
                  ...(isSpecialAction && checked ? { read: true } : {})
                }
              : prev[index].permission_for_sp
        };

        return [...prev.slice(0, index), updatedFeature, ...prev.slice(index + 1)];
      } else {
        const newFeature = {
          feature_id: record?.feature_id,

          permission_for_sp:
            segmentedValue !== "Individual"
              ? {
                  create: false,
                  read: false,
                  update: false,
                  delete: false,
                  [action]: checked,
                  ...(isSpecialAction && checked ? { read: true } : {})
                }
              : { create: false, read: false, update: false, delete: false },

          permission_for_user:
            segmentedValue === "Individual"
              ? {
                  create: false,
                  read: false,
                  update: false,
                  delete: false,
                  [action]: checked,
                  ...(isSpecialAction && checked ? { read: true } : {})
                }
              : { create: false, read: false, update: false, delete: false }
        };

        return [...prev, newFeature];
      }
    });
  };

  const handleSelectAll = (e: any) => {
    const checked = e.target.checked;
    const isIndividual = segmentedValue === "Individual";

    setFeaturePermissionArray((prev: FeaturePermissionData[]) => {
      const existingPermissions = new Map(prev.map((fp) => [fp.feature_id, fp]));

      return adminPermissions.map((adminPerm: any) => {
        const existing = existingPermissions.get(adminPerm?.feature_id) ?? null;
        const defaultPermissions = {
          create: false,
          read: false,
          update: false,
          delete: false
        };

        const preservedSp = existing?.permission_for_sp || defaultPermissions;
        const preservedUser = existing?.permission_for_user || defaultPermissions;

        return {
          feature_id: adminPerm.feature_id,
          permission_for_user: isIndividual
            ? { create: checked, read: checked, update: checked, delete: checked }
            : preservedUser,
          permission_for_sp: !isIndividual
            ? { create: checked, read: checked, update: checked, delete: checked }
            : preservedSp
        };
      });
    });
  };
  const isModuleSelected = (record: any) => {
    const feature = featurePermissionArray.find(
      (feat: any) => feat.feature_id === record.feature_id
    );

    const hasReadPermission =
      segmentedValue === "Individual"
        ? feature?.permission_for_user.read
        : feature?.permission_for_sp.read;
    return hasReadPermission;
  };

  const columns = [
    {
      title: "",
      dataIndex: "chevron",
      key: "chevron",
      render: (_: any, record: any) =>
        record.is_module && record?.is_duplicate ? (
          adminPermissions.some(
            (module: any) => module.module_id === record.module_id && module !== record
          ) ? (
            <div
              className="flex items-center cursor-pointer"
              onClick={() => {
                if (isModuleSelected(record)) {
                  handleToggleMinimize(record.module_id);
                } else {
                  notification.error({
                    message: "Please give read access to the module for viewing it's features ."
                  });
                }
              }}
            >
              {segmentedValue === "Individual" ? (
                minimizedModulesUser.includes(record.module_id) ? (
                  <CgChevronDown
                    className={`${
                      !isModuleSelected(record) ? "text-gray-200" : "text-violet-400"
                    } mr-2`}
                    size="20px"
                  />
                ) : (
                  <CgChevronUp className="mr-2 text-violet-400" size="20px" />
                )
              ) : minimizedModulesSp.includes(record.module_id) ? (
                <CgChevronDown
                  className={`${
                    !isModuleSelected(record) ? "text-gray-200" : "text-violet-400"
                  } mr-2`}
                  size="20px"
                />
              ) : (
                <CgChevronUp className="mr-2 text-violet-400" size="20px" />
              )}
            </div>
          ) : (
            <CgChevronDown className="mr-2 text-gray-200" size="20px" />
          )
        ) : (
          <></>
        )
    },
    {
      title: "Module Name",
      dataIndex: "module_name",
      key: "name",
      render: (text: string, record: any) => (
        <Tooltip title={record.is_module && record?.is_duplicate ? text : record.sub_module_name}>
          <div className=" min-w-[120px] max-w-[120px] cursor-default">
            <div
              className={` overflow-hidden whitespace-nowrap text-ellipsis  capitalize ${
                record.is_module && record?.is_duplicate ? "font-bold" : ""
              } `}
            >
              {record.is_module && record?.is_duplicate ? text : record.sub_module_name}
            </div>
          </div>
        </Tooltip>
      )
    },
    ...(["create", "read", "update", "delete"] as const).map((action) => ({
      title: action.charAt(0).toUpperCase() + action.slice(1), // Capitalize for display
      dataIndex: action,
      key: action,
      className: "",
      render: (
        _: any,
        record: {
          key: number;
          is_module: boolean;
          feature_id: any;
          module_id: number;
          [key: string]: any;
        }
      ) => {
        const currentPermission =
          segmentedValue === "Individual"
            ? featurePermissionArray?.find((f: any) => f.feature_id === record.feature_id)
                ?.permission_for_user[action]
            : featurePermissionArray?.find((f: any) => f.feature_id === record.feature_id)
                ?.permission_for_sp[action];

        return (
          <Checkbox
            checked={currentPermission}
            onChange={(e) => handleCheckboxChange(action, e, record)}
          />
        );
      }
    }))
  ];
  const checkboxArray = [
    {
      role: roleData.can_reject,
      title: "Can Reject",
      customKey: "can_reject",
      tooltip: "Can Reject a user"
    },
    { role: roleData.can_ban, title: "Can Ban", customKey: "can_ban", tooltip: "Can Ban a user" },
    {
      role: roleData.can_accept,
      title: "Can Accept",
      customKey: "can_accept",
      tooltip: "Can Accept a banned/rejected user"
    },
    {
      role: roleData.has_admin_privileges,
      title: "Admin Rights",
      customKey: "has_admin_privileges",
      tooltip: "Has access to admin privileges"
    },
    {
      role: roleData.can_download,
      title: "Can Download",
      customKey: "can_download",
      tooltip: "Can download data "
    }
  ];

  const transformed = adminPermissions
    .sort((a: any, b: any) => a.module_id - b.module_id)
    .flatMap((item: any) => {
      if (item.is_module) {
        return [
          { ...item, is_duplicate: true }, // original module row
          { ...item } // extra row with a marker
        ];
      }

      return [item]; // leave non-modules as is
    });

  return (
    <Form layout="vertical" form={addpermission} className=" overflow-y-clip" onFinish={handleSave}>
      <div className="flex border-1 ">
        <InputAndCheckBoxGroup
          inputName="role_name"
          placeholder="Enter role name"
          label="Role Name"
          roleData={roleData}
          setRoleData={setRoleData}
          rules={[
            { required: true, message: "Please Enter a Role Name" },
            {
              pattern: /^[A-Za-z ]+$/,
              message: "Enter Valid Admin Name"
            },
            {
              validator: (_: any, value: any) => {
                // Check if the value contains only whitespace
                if (value && value.startsWith(" ")) {
                  return Promise.reject("Whitespace is not allowed at start");
                }
                return Promise.resolve();
              }
            }
          ]}
          checkboxArray={checkboxArray.slice(0, 3)}
        />
        <Tooltip
          title={
            "Checkbox permissions will apply only when respective page read permissions are alloted to the Role "
          }
        >
          <InfoCircleOutlined className="text-colorPrimary absolute right-6" />
        </Tooltip>
      </div>

      <InputAndCheckBoxGroup
        inputName="description"
        placeholder="Enter description for role"
        label="Description"
        roleData={roleData}
        rules={[]}
        setRoleData={setRoleData}
        checkboxArray={checkboxArray.slice(3)}
      />

      <div className="flex flex-col h-[380px] w-full">
        <div className="flex justify-between mb-2 items-center">
          <Segmented
            options={[
              {
                label: <div style={{ padding: 1, minWidth: "80px" }}>Individual</div>,
                value: "Individual"
              },
              {
                label: <div style={{ padding: 1, minWidth: "80px" }}>Service Provider</div>,
                value: "SP"
              }
            ]}
            size="large"
            value={segmentedValue}
            className="w-fit mt-2"
            onChange={(value) => setSegmentedValue(value)}
          />
          <div className="flex">
            <Checkbox
              checked={selectAll}
              onChange={(e) => {
                setSelectAll(e.target.checked);
                handleSelectAll(e);
              }}
            >
              Select All
            </Checkbox>
          </div>
        </div>

        <Table
          dataSource={transformed.filter((feature: any) => {
            const isMinimized = !(segmentedValue === "Individual"
              ? minimizedModulesUser.includes(feature?.module_id) && !feature.is_duplicate
              : minimizedModulesSp.includes(feature?.module_id) && !feature.is_duplicate);

            const belongsToBasePath =
              feature.sub_module_url_sp !== null && feature.sub_module_url_user !== null
                ? true
                : segmentedValue === "Individual"
                ? feature.sub_module_url_sp === null
                : feature.sub_module_url_user === null;

            return isMinimized && belongsToBasePath;
          })}
          columns={columns}
          scroll={{ y: 250 }}
          pagination={false}
          className="mb-4 w-full  overflow-y-scroll  custom-permission-table"
          rowClassName={(record) =>
            record.is_module &&
            record.is_duplicate &&
            adminPermissions.some(
              (module: any) => module.module_id === record.module_id && module !== record
            )
              ? " p-2 bg-[#faf9ff]	 bor font-[400]   w-full font-bold"
              : " pl-4 font-[200] w-full"
          }
        />
      </div>
    </Form>
  );
};

export default RolePermissionTable;
