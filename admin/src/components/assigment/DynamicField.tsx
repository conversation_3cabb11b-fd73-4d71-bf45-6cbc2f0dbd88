import { Select } from "antd";
import FormItem from "antd/es/form/FormItem";
import React from "react";
import { AllowedOperators, ConfigRule } from "./CreateRulesModal";

interface DynamicFieldProps {
  current_config?: ConfigRule;
  operator?: AllowedOperators;
  value?: string | number | string[] | number[];
  onChange?: (val: string | number | string[] | number[]) => void;
}

const DynamicField: React.FC<DynamicFieldProps> = ({
  current_config,
  operator,
  onChange,
  value
}) => {
  let ele: React.ReactNode = <></>;

  if (!current_config || !operator) {
    return <div className="w-full px-3 py-1">Select an operation</div>;
  }

  if (current_config.options) {
    ele = (
      <Select
        onChange={onChange}
        value={value}
        options={current_config.options}
        allowClear={true}
        {...(operator === "in"
          ? {
              mode: "multiple"
            }
          : null)}
      />
    );
  }

  return <FormItem>{ele}</FormItem>;
};

export default DynamicField;
