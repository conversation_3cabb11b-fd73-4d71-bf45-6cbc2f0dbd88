import { Button, Input, notification, Tooltip } from "antd";
import dayjs from "dayjs";
import { CheckSquare, Copy, Edit2 } from "lucide-react";
import React, { useEffect, useState } from "react";
import { TbSquareX } from "react-icons/tb";
import { useParams } from "react-router-dom";
import {
  GetUserChecklistDataDocument,
  GetUserChecklistHistoryDocument,
  useGetUserChecklistDataQuery,
  useGetUserChecklistHistoryQuery,
  useUpdateUserChecklistDataMutation,
  useUserDetailsQueryLazyQuery
} from "../../__generated__";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import RatingDisplay from "../users/user-details/common/RatingDisplay";

const ChecklistSection: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const params = useParams();
  const basePath = localStorage.getItem("base_path") || "";

  const copyTechnicianFeedbackLink = () => {
    const feedbackUrl = `${import.meta.env.VITE_FRONTEND_URL}/technician-feedback/${
      params.user_id
    }`;
    navigator.clipboard.writeText(feedbackUrl);
    notification.success({
      message: "Technician feedback link copied to clipboard"
    });
  };

  const {
    data: userChecklistData,
    loading,
    error
  } = useGetUserChecklistDataQuery({
    variables: {
      userId: Number(params.user_id),
      userType: basePath === "sp" ? "SERVICE_PROVIDER" : undefined
    },
    fetchPolicy: "cache-and-network"
  });
  const {
    data: userChecklistHistory,
    loading: historyLoading,
    error: historyError
  } = useGetUserChecklistHistoryQuery({
    variables: {
      userId: Number(params.user_id)
    },
    fetchPolicy: "cache-and-network"
  });
  const [
    fetchUserDetails,
    { data: userDetails, error: userDetailsError, loading: userDetailsLoading, refetch }
  ] = useUserDetailsQueryLazyQuery({
    variables: {
      userId: Number(params.user_id)
    }
  });
  const [updateUserChecklist] = useUpdateUserChecklistDataMutation();
  const [checklistData, setChecklistData] = useState(userChecklistData?.getUserChecklistData?.data);
  const [pendingChanges, setPendingChanges] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    if (userChecklistData?.getUserChecklistData?.data?.length) {
      setChecklistData(userChecklistData?.getUserChecklistData?.data);
    }
  }, [userChecklistData?.getUserChecklistData?.data]);
  useEffect(() => {
    fetchUserDetails({
      variables: {
        userId: Number(params.user_id)
      }
    });
    refetch();
  }, []);
  if (loading || historyLoading || userDetailsLoading) {
    return <div>Loading...</div>;
  }

  if (error || historyError || userDetailsError) {
    return <div>Error: {error?.message}</div>;
  }
  return (
    <ProtectedRoute>
      {({ permissions }) => (
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6 ">
          <div className="flex justify-between items-center mb-4">
            <div className="text-lg font-semibold">Onboarding Checklist</div>
            <div className="flex gap-3">
              <Button
                disabled={!permissions?.update}
                onClick={async () => {
                  if (isEditing && Object.keys(pendingChanges).length > 0) {
                    try {
                      // Convert pendingChanges to array format for batch update
                      const updateData = Object.entries(pendingChanges).map(
                        ([checklist_id, hiring_completed]) => ({
                          user_id: Number(params.user_id),
                          checklist_id,
                          hiring_completed
                        })
                      );

                      await updateUserChecklist({
                        variables: {
                          data: updateData
                        },
                        refetchQueries: [
                          GetUserChecklistDataDocument,
                          GetUserChecklistHistoryDocument
                        ]
                      });

                      notification.success({
                        message: "User Checklist updated successfully"
                      });
                      setPendingChanges({});
                    } catch (error) {
                      notification.error({
                        message: (error as Error).message || "Failed to update checklist"
                      });
                    }
                  }
                  setIsEditing(!isEditing);
                }}
                className={`px-3 py-1.5 ${
                  isEditing
                    ? "border-1 shadow-md  border-solid border-colorPrimary  text-purple-500 "
                    : "bg-colorPrimary text-white "
                }  rounded-lg transition-colors text-sm flex items-center gap-2 `}
              >
                <Edit2 size={16} />
                {isEditing ? "Done" : "Edit"}
              </Button>
            </div>
          </div>
          <table className="w-full">
            <thead className="bg-gray-100">
              <tr>
                <th className="text-left py-2 px-3 text-gray-600 font-medium rounded-l-lg text-sm">
                  Onboarding Steps
                </th>
                <th className="text-left py-2 px-3 text-gray-600 font-medium text-sm w-1/3">
                  <div>Admin Feedback</div>
                </th>
                <th className="text-left py-2 px-3 text-gray-600 font-medium text-sm w-1/3">
                  <div className="flex items-center">
                    <div>Technician Feedback</div>
                    {userDetails?.userDetailsForAdmin?.transfer_to_tms_done && (
                      <Tooltip title="Copy technician feedback link">
                        <div
                          onClick={copyTechnicianFeedbackLink}
                          className="flex items-center text-sm gap-x-[3px] ml-3  text-purple-500  hover:text-purple-600"
                        >
                          <Copy size={12} />
                          <div className="text-xs">Copy Link</div>
                        </div>
                      </Tooltip>
                    )}
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              {checklistData
                ?.filter((item) => item?.name !== "Rating")
                .map((item) => (
                  <tr key={item?.id} className="hover:bg-gray-50">
                    <td className="py-2 px-3 text-gray-700 text-sm">{item?.name}</td>
                    <td className="py-2 px-3">
                      <div className="flex items-center gap-3">
                        {isEditing ? (
                          <input
                            type="checkbox"
                            checked={
                              item?.id &&
                              Object.prototype.hasOwnProperty.call(pendingChanges, item.id)
                                ? pendingChanges[item.id]
                                : item?.user_checklist_data?.[0]?.hiring_completed || false
                            }
                            onChange={(e) => {
                              if (item?.id) {
                                setPendingChanges({
                                  ...pendingChanges,
                                  [item.id]: e.target.checked
                                });
                              }
                            }}
                            disabled={!isEditing}
                            className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                          />
                        ) : item?.user_checklist_data?.[0]?.hiring_completed ? (
                          <CheckSquare className="w-4 h-4 text-green-500 inline" />
                        ) : (
                          <Input
                            type="checkbox"
                            checked={item?.user_checklist_data?.[0]?.hiring_completed || false}
                            disabled
                            className="w-4 h-4 text-purple-600 border-gray-300  rounded focus:ring-purple-500"
                          />
                        )}

                        {(item?.id && Object.prototype.hasOwnProperty.call(pendingChanges, item.id)
                          ? pendingChanges[item.id]
                          : item?.user_checklist_data?.[0]?.hiring_completed) &&
                          item?.user_checklist_data?.[0]?.hiring_completed_at && (
                            <span className="text-xs text-gray-500">
                              {dayjs(item?.user_checklist_data?.[0]?.hiring_completed_at).format(
                                "DD MMM YYYY"
                              )}
                            </span>
                          )}
                      </div>
                    </td>
                    <td className="py-2 px-3">
                      <div className="flex items-center gap-3">
                        {item?.user_checklist_data?.[0]?.technician_completed_at &&
                        item?.user_checklist_data?.[0]?.technician_completed === false ? (
                          <TbSquareX className="w-4 h-4 text-red-500 inline" />
                        ) : item?.user_checklist_data?.[0]?.technician_completed ? (
                          <CheckSquare className="w-4 h-4 text-green-500 inline" />
                        ) : (
                          <Input
                            type="checkbox"
                            checked={item?.user_checklist_data?.[0]?.technician_completed || false}
                            disabled
                            className="w-4 h-4 text-purple-600 border-gray-300  rounded focus:ring-purple-500"
                          />
                        )}

                        {item?.user_checklist_data?.[0] &&
                          item?.user_checklist_data?.[0]?.technician_completed_at && (
                            <span className="text-xs text-gray-500">
                              {dayjs(
                                item?.user_checklist_data?.[0]?.technician_completed_at
                              ).format("DD MMM YYYY")}
                            </span>
                          )}
                      </div>
                    </td>
                  </tr>
                ))}
            </tbody>
            {(userChecklistHistory?.getUserChecklistHistory?.data?.created_at ||
              userChecklistHistory?.getUserChecklistHistory?.data?.updated_at) && (
              <tfoot className="border-t">
                <tr>
                  <td colSpan={3} className="py-3 px-3">
                    <div className="bg-purple-100 w-full h-20 border-solid border rounded-md mb-2 border-purple-500 flex justify-center p-4 flex-col">
                      <div className="flex justify-between items-end">
                        <div className="flex flex-col ">
                          {userChecklistHistory?.getUserChecklistHistory?.data?.updated_at && (
                            <div className="">
                              Last updated :{" "}
                              <span className="font-semibold">
                                {(userChecklistHistory?.getUserChecklistHistory?.data
                                  ?.updated_at as any)
                                  ? dayjs(
                                      userChecklistHistory?.getUserChecklistHistory?.data
                                        ?.updated_at as any
                                    ).format("MMM-DD-YYYY h:mm A")
                                  : "Not Available"}
                              </span>
                              {` , by ${
                                userChecklistHistory?.getUserChecklistHistory?.data?.last_editor
                                  ?.name || "Not available"
                              }`}
                            </div>
                          )}
                          {userChecklistHistory?.getUserChecklistHistory?.data?.created_at && (
                            <div className="mt-2 ">
                              Created on :{" "}
                              <span className="font-semibold">
                                {userChecklistHistory?.getUserChecklistHistory?.data?.created_at
                                  ? dayjs(
                                      userChecklistHistory?.getUserChecklistHistory?.data
                                        ?.created_at
                                    ).format("MMM-DD-YYYY h:mm A")
                                  : "Not Available"}
                              </span>
                              {` , by ${
                                userChecklistHistory?.getUserChecklistHistory?.data?.creator
                                  ?.name || "Not available"
                              }`}
                            </div>
                          )}
                        </div>
                        <div className=" flex gap-x-2 items-end  text-md  cursor-default">
                          <span className="">Onboarding experience rating by technician</span>:{" "}
                          <RatingDisplay
                            rating={
                              checklistData?.find((item) => item?.name === "Rating")
                                ?.user_checklist_data?.[0]?.meta?.value
                            }
                            size="lg"
                          />
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tfoot>
            )}
          </table>
        </div>
      )}
    </ProtectedRoute>
  );
};

export default ChecklistSection;
