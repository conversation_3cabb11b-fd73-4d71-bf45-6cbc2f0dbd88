import React, { useState } from "react";
import SpShowBulkUploadQuestionTemplate from "./SpShowBulkUploadQuestionTemplate";
import SpShowFilledQuestionsFromTemplate from "./SpShowFilledQuestionsFromTemplate";

export interface BulkQuestionAdditionProps {
  question: string;
  option_1: string;
  option_2: string;
  option_3: string;
  option_4: string;
  correct_option: string;
  difficulty: string;
  bucket: string;
  bucket_name?: string;
}

interface SpCreateBulkQuestionProps {
  okConfirmLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

const SpCreateBulkQuestion: React.FC<SpCreateBulkQuestionProps> = ({ okConfirmLoading }) => {
  //Hooks
  const [isTemplateUploaded, setIsTemplateUploaded] = useState(false);

  const [filledQuestions, setFilledQuestions] = useState<Array<BulkQuestionAdditionProps>>([]);

  return (
    <div>
      {isTemplateUploaded ? (
        <SpShowFilledQuestionsFromTemplate
          filledQuestions={filledQuestions}
          okConfirmLoading={okConfirmLoading}
        />
      ) : (
        // Render the ShowBulkUploadQuestionTemplate component and pass the function to set the state
        <SpShowBulkUploadQuestionTemplate
          setIsTemplateUploaded={setIsTemplateUploaded}
          setFilledQuestions={setFilledQuestions}
        />
      )}
    </div>
  );
};

export default SpCreateBulkQuestion;
