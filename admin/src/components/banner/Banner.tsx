import { banner_images, banner_links } from "../../utils/utils";

function Banner({
  bgUrl = banner_images[0].url,
  title,
  sideImgUrl,
  ctaLink,
  ctaRequired
}: {
  bgUrl: string;
  title: string;
  sideImgUrl: string;
  ctaLink?: string;
  ctaRequired?: boolean;
}) {
  return (
    <div className="bg-[url(/images/partner_app.png)] w-[399px] h-[740px] bg-cover bg-center relative">
      <div
        style={{
          backgroundImage: `url(${bgUrl})`
        }}
        className={`w-[260px] shadow-md left-[70px] top-[365px] h-[100px] rounded-lg bg-cover bg-no-repeat absolute`}
      >
        <div className="flex p-2 items-center">
          <div className="flex-grow">
            <div className="text-black font-semibold text-sm">{title}</div>
            {ctaRequired && ctaLink && (
              <button className="text-xs shadow-md border  border-solid inline-block px-2 py-1 text-orange-500 border-orange-600 rounded-lg bg-white">
                {banner_links.map((link) => link.value === ctaLink && link.label)}{" "}
              </button>
            )}
          </div>
          <div className="flex items-center justify-center h-[80px]">
            {sideImgUrl && <img src={sideImgUrl} alt="hero" className="rounded-lg w-[70px]" />}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Banner;
