import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { useNavigate } from "react-router-dom";
import AppRoutes from "./AppRoutes";
import { useUserDetailsQueryQuery } from "./__generated__";

function App() {
  const navigate = useNavigate();
  // do nothing if the user is in public route

  if (!window.location.pathname.startsWith("/feedbacks")) {
    useUserDetailsQueryQuery({
      onError: () => {
        localStorage.removeItem("adminToken");
        navigate("/login", {
          replace: true
        });
      }
    });
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <AppRoutes />;
    </DndProvider>
  );
}

export default App;
