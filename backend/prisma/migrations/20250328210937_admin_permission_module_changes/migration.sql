-- AlterTable
ALTER TABLE "admin" ADD COLUMN     "admin_role_id" INTEGER;

-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';

-- CreateTable
CREATE TABLE "admin_role" (
    "admin_role_id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "is_root_admin" BOOLEAN NOT NULL DEFAULT false,
    "has_admin_privileges" BOOLEAN NOT NULL DEFAULT false,
    "can_reject" BOOLEAN NOT NULL DEFAULT false,
    "can_ban" BOOLEAN NOT NULL DEFAULT false,
    "can_accept" BOOLEAN NOT NULL DEFAULT false,
    "can_publish" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "creator_id" INTEGER NOT NULL,
    "updated_by" INTEGER,

    CONSTRAINT "admin_role_pkey" PRIMARY KEY ("admin_role_id")
);

-- CreateTable
CREATE TABLE "admin_role_permission" (
    "id" SERIAL NOT NULL,
    "admin_role_id" INTEGER NOT NULL,
    "feature_id" INTEGER NOT NULL,
    "can_download" BOOLEAN NOT NULL DEFAULT true,
    "permission_for_user" JSONB,
    "permission_for_sp" JSONB,

    CONSTRAINT "admin_role_permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "admin_protected_feature" (
    "feature_id" SERIAL NOT NULL,
    "parent_module_id" INTEGER,
    "module_id" INTEGER,
    "parent_module_name" TEXT,
    "module_name" TEXT,
    "sub_module_name" TEXT,
    "sub_module_url" TEXT,
    "section_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "admin_protected_feature_pkey" PRIMARY KEY ("feature_id")
);

-- CreateTable
CREATE TABLE "admin_module" (
    "module_name" TEXT NOT NULL,
    "order" SERIAL NOT NULL,
    "parent_module_name" TEXT,
    "parent_module_id" INTEGER NOT NULL,
    "module_id" SERIAL NOT NULL,
    "is_published" BOOLEAN NOT NULL DEFAULT false,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "icon_name" TEXT NOT NULL DEFAULT 'RiLayoutGridLine',
    "published_at" TIMESTAMP(3),
    "published_by" INTEGER,

    CONSTRAINT "admin_module_pkey" PRIMARY KEY ("module_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "admin_role_name_key" ON "admin_role"("name");

-- CreateIndex
CREATE UNIQUE INDEX "admin_module_module_name_key" ON "admin_module"("module_name");

-- AddForeignKey
ALTER TABLE "admin" ADD CONSTRAINT "admin_admin_role_id_fkey" FOREIGN KEY ("admin_role_id") REFERENCES "admin_role"("admin_role_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "admin_role_permission" ADD CONSTRAINT "admin_role_permission_admin_role_id_fkey" FOREIGN KEY ("admin_role_id") REFERENCES "admin_role"("admin_role_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "admin_role_permission" ADD CONSTRAINT "admin_role_permission_feature_id_fkey" FOREIGN KEY ("feature_id") REFERENCES "admin_protected_feature"("feature_id") ON DELETE CASCADE ON UPDATE CASCADE;
