import { db } from './common'

import xlsx from 'xlsx'

function parseBool(val: any): boolean {
    return val === true || val === 'TRUE' || val === 'true' || val === 1
}

async function getRandomUserId(): Promise<number> {
    const [user] = await db.$queryRawUnsafe<{ id: number }[]>(
        `SELECT id FROM "user" ORDER BY RANDOM() LIMIT 1`
    )
    if (!user) throw new Error('No users found in the database')
    return user.id
}

export async function importContactsFromExcel(filePath: string) {
    const workbook = xlsx.readFile(filePath)
    const sheet = workbook.Sheets[workbook.SheetNames[0]]
    const rows = xlsx.utils.sheet_to_json(sheet)

    type ContactRow = {
        id: string
        name: string
        phone: string | number
        meta?: string
        timeline?: string
        is_active: boolean | string | number
        is_deleted: boolean | string | number
        moved_to_users: boolean | string | number
        phone_contact_id: string
        image_url?: string | null
        created_at: string
        updated_at: string
    }

    for (const row of rows as ContactRow[]) {
        try {
            const contact = await db.contact.create({
                data: {
                    id: row.id,
                    name: row.name,
                    phone: String(row.phone),
                    meta: row.meta ? JSON.parse(row.meta) : {},
                    timeline: row.timeline ? JSON.parse(row.timeline) : null,
                    is_active: parseBool(row.is_active),
                    is_deleted: parseBool(row.is_deleted),
                    moved_to_users: parseBool(row.moved_to_users),
                    phone_contact_id: String(row.phone_contact_id),
                    image_url: row.image_url || null,
                    created_at: new Date(row.created_at),
                    updated_at: new Date(row.updated_at),
                },
            })

            const userId = await getRandomUserId()

            await db.user_contact_map.create({
                data: {
                    user_id: userId,
                    contact_id: contact.id,
                },
            })

            console.log(`✅ Imported & mapped contact ${contact.name}`)
        } catch (err) {
            console.error(
                `❌ Failed to import contact ${row.name || row.id}:`,
                err.message
            )
        }
    }
}

// Call it
