import { Button, DatePicker, Form, Modal, notification, Select, Spin } from "antd";
import { useForm } from "antd/es/form/Form";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { isEmpty } from "lodash";
import { useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { AssignmentUserType, QuestionDifficulty, useGetAllBucketsQuery } from "../../__generated__";

dayjs.extend(utc);

interface QuestionFilterProps {
  openModal: boolean;
  onClose: () => void;
  handleSetCurrentPage: (page: number) => void;
}
interface SearchFilter {
  bucket_id: string;
  search_term: string;
  difficulty: QuestionDifficulty;
}
const SpQuestionFilterModal: React.FC<QuestionFilterProps> = ({
  openModal,
  onClose,
  handleSetCurrentPage
}) => {
  //Hooks
  const [search, setSearchParams] = useSearchParams();

  //Form Instance
  const [form] = useForm();

  //GraphQL
  const { data: buckets, loading: bucketLoading } = useGetAllBucketsQuery({
    variables: {
      data: {
        assign_to: AssignmentUserType.ServiceProvider
      }
    }
  });

  //Handle Filter Clear
  const handleFilterClear = () => {
    const searched_text = search.get("search");
    handleSetCurrentPage(1);
    if (!isEmpty(searched_text)) {
      setSearchParams({
        search: searched_text || ""
      });
    } else {
      setSearchParams({});
    }
    form.resetFields();
  };

  const handleSearchParams = (values: any): void => {
    setSearchParams({
      filter: JSON.stringify({
        ...values
      }),
      search: search?.get("search") || ""
    });
  };

  //Handle Form Submit
  const handleSubmit = () => {
    //check if form has values
    if (!form.isFieldsTouched()) {
      notification.error({
        message: "No filter applied"
      });
      return;
    }

    handleSetCurrentPage(1);
    form.submit();
    handleClose();
  };

  //Handle Modal Close
  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    //Handle Form Initial Values
    const filter = search.get("filter") ? JSON.parse(search.get("filter") || "") : "";
    filter &&
      form.setFieldsValue({
        ...filter,
        ...(filter.start_date ? { start_date: dayjs(filter?.start_date) } : {}),
        ...(filter.end_date ? { end_date: dayjs(filter?.end_date) } : {})
      });
  });

  if (bucketLoading) {
    return <Spin />;
  }

  return (
    <Modal open={openModal} onCancel={handleClose} footer={null} centered>
      <div className="text-center text-purple-500 text-xl mb-4"> Add Filter </div>
      <div className="p-2">
        <Form
          preserve
          form={form}
          onFinish={(val: SearchFilter) => handleSearchParams(val)}
          layout="vertical"
        >
          <Form.Item name={"bucket_id"} label={"Bucket"}>
            <Select
              className=""
              options={buckets?.getAllBuckets?.data?.map((bucket) => ({
                value: bucket?.id,
                label: bucket?.name
              }))}
            />
          </Form.Item>
          <Form.Item name={"difficulty"} label={"Difficulty"}>
            <Select
              options={[
                {
                  value: QuestionDifficulty.Hard,
                  label: "Hard"
                },
                {
                  value: QuestionDifficulty.Medium,
                  label: "Medium"
                },
                {
                  value: QuestionDifficulty.Easy,
                  label: "Easy"
                }
              ]}
            />
          </Form.Item>
          <Form.Item label="Select Date">
            <div className="flex justify-start gap-4 ">
              <Form.Item name={"start_date"}>
                <DatePicker
                  placeholder="From"
                  presets={[
                    { label: "Yesterday", value: dayjs().add(-1, "d") },
                    { label: "Last Week", value: dayjs().add(-7, "d") },
                    { label: "Last 2 Weeks", value: dayjs().add(-14, "d") },
                    { label: "Last Month", value: dayjs().add(-1, "month") },
                    { label: "Last Three Month", value: dayjs().add(-3, "month") }
                  ]}
                  onChange={(date) => {
                    // Set the time component to 00:00:00 for the start date
                    const adjustedStartDate = date?.startOf("day");
                    form.setFieldsValue({ start_date: adjustedStartDate });
                  }}
                />
              </Form.Item>
              <span className="items-center mt-1">-</span>
              <Form.Item name={"end_date"}>
                <DatePicker
                  placeholder="To"
                  presets={[
                    { label: "Yesterday", value: dayjs().add(-1, "d") },
                    { label: "Last Week", value: dayjs().add(-7, "d") },
                    { label: "Last 2 Weeks", value: dayjs().add(-14, "d") },
                    { label: "Last Month", value: dayjs().add(-1, "month") },
                    { label: "Last Three Month", value: dayjs().add(-3, "month") }
                  ]}
                  onChange={(date) => {
                    // Set the time component to 23:59:59 for the end date
                    const adjustedEndDate = date?.endOf("day");
                    form.setFieldsValue({ end_date: adjustedEndDate });
                  }}
                />
              </Form.Item>
            </div>
          </Form.Item>
          <div className="text-right">
            <Button className="mr-4" onClick={handleFilterClear}>
              Clear Filter
            </Button>
            <Button type="primary" onClick={handleSubmit}>
              OK
            </Button>
          </div>
        </Form>
      </div>
    </Modal>
  );
};

export default SpQuestionFilterModal;
