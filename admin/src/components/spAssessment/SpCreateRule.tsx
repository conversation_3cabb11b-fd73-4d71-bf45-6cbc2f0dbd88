import {
  <PERSON><PERSON>,
  Card,
  Col,
  Empty,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Table,
  Tag,
  message,
  notification
} from "antd";
import FormItem from "antd/es/form/FormItem";
import { isArray, isEmpty, uniqueId } from "lodash";
import React, { useEffect, useState } from "react";
import { MdDelete } from "react-icons/md";
import { useNavigate } from "react-router-dom";
import {
  AllowedOperator,
  AssignmentUserType,
  GetAllRulesModuleDocument,
  Preferred_Language,
  useCreateAssignmentMutation,
  useExpertiseLazyQuery
} from "../../__generated__";
import { getRandomTagBgColor } from "../../utils/utils";
import DynamicField from "../assigment/DynamicField";

export type AllowedOperators = "equals" | "not_equals" | "less_than" | "more_than" | "in";
export type ValueType = "number" | "text";

export interface ConfigRule {
  field_value: string;
  field_label: string;
  operators: AllowedOperators[];
  value_type: ValueType;
  options?: {
    label: string;
    value: string | number;
  }[];
}

interface Rule {
  field_name: string;
  operator: AllowedOperators;
  value: {
    value: string | string[] | number | number[];
  };
  uuid: string;
}

const SpCreateRulesModal: React.FC = () => {
  //states
  const [rules, setRules] = useState<Array<Rule>>([]);
  const [current_config, setCurrentConfig] = useState<ConfigRule>();
  const [operator, setOperator] = useState<AllowedOperators>();
  const [value, setValue] = useState<string | number | string[] | number[]>();
  const [config_rules, setConfigRules] = useState<ConfigRule[]>([]);
  const [name, setName] = useState("");

  //network calls
  const [fetchExpertise] = useExpertiseLazyQuery();
  // const [fetchUserCountOnWhichRulesApplies, { data: userCount, loading: userCountLoading }] =
  //   useGetTotalUsersThatRuleAppliesToLazyQuery();
  const [createRule, { loading: creatingAssignmentRule }] = useCreateAssignmentMutation();

  //other hooks
  const navigate = useNavigate();

  //on new rule submit
  const onCreateNewRule = () => {
    if (!current_config || !operator || (value !== 0 && !value)) {
      notification.error({
        message: "Please fill all fields to add rules"
      });
    } else {
      const already_check = rules.findIndex(
        (r) =>
          r.field_name === current_config.field_value &&
          r.operator === operator &&
          r.value.value === value
      );

      if (already_check === -1) {
        setRules([
          ...rules,
          {
            field_name: current_config.field_value,
            operator: operator,
            value: {
              value: value
            },
            uuid: uniqueId()
          }
        ]);

        setOperator(undefined);
        setValue(undefined);
      } else {
        notification.error({
          message: "Same rule already added"
        });
      }
    }
  };

  const createRuleModule = () => {
    if (rules.length === 0 || isEmpty(name)) {
      message.info("Please add at least 1 rule and name of the rule group");
      return;
    }

    createRule({
      variables: {
        data: {
          name,
          rules: rules.map((r) => ({
            field_name: r.field_name,
            operator: r.operator as AllowedOperator,
            value: r.value
          })),
          assign_to: AssignmentUserType.ServiceProvider
        }
      },
      onError: (error) => {
        console.error(error);
        message.error(error.message);
      },
      onCompleted(data) {
        if (data.createAssignmentRule.result) {
          message.success("Rule created successfully");
          navigate(-1);
        } else {
          message.error(data.createAssignmentRule.message);
        }
      },
      refetchQueries: [GetAllRulesModuleDocument]
    });
  };

  // effects
  useEffect(() => {
    fetchExpertise({
      onCompleted(data) {
        const expertise_data = data.expertise?.data;
        if (!expertise_data) {
          console.error("Cannot find expertise data");
        } else {
          const config_data: ConfigRule[] = [
            {
              field_value: "expertise",
              field_label: "Expertise",
              operators: ["equals", "in"],
              value_type: "text",
              options: expertise_data.map((d) => ({
                label: d?.name || "not_found",
                value: d?.id || -1
              }))
            },
            {
              field_value: "work_experience",
              field_label: "Work Experience",
              operators: ["less_than", "more_than", "equals", "in"],
              value_type: "number",
              options: new Array(41).fill(0).map((_, i) => ({ value: i, label: i.toString() }))
            },
            {
              field_value: "language",
              field_label: "Language",
              operators: ["equals", "in"],
              value_type: "text",
              options: [
                {
                  label: "English",
                  value: Preferred_Language.English
                },
                {
                  label: "Hindi",
                  value: Preferred_Language.Hindi
                }
              ]
            }
          ];
          setConfigRules(config_data);
        }
      },
      onError(error) {
        console.error(error);
      }
    });
  }, []);

  // useEffect(() => {
  //   fetchUserCountOnWhichRulesApplies({
  //     variables: {
  //       rules: rules.map((rule) => ({
  //         field_name: rule.field_name,
  //         operator: rule.operator as AllowedOperator,
  //         value: rule.value
  //       }))
  //     },
  //     onError(error) {
  //       console.error(error);
  //       message.error("There was some issue during fetching the user count");
  //     }
  //   });
  // }, [rules]);

  useEffect(() => {
    setValue(undefined);
  }, [operator]);

  return (
    <Modal
      closeIcon={false}
      centered
      onOk={createRuleModule}
      onCancel={() => {
        navigate(-1);
      }}
      open
      okButtonProps={{
        loading: creatingAssignmentRule
      }}
      width={800}
      okText={"Create"}
    >
      <div className="text-2xl my-3">
        Rule group
        {/* <Tag color="green" className="text-xl ml-2">
          Applies to {userCountLoading ? "loading ..." : userCount?.getTotalUsersOnWhichRulesApply}{" "}
          users
        </Tag> */}
      </div>
      <Card className="mb-3">
        <div className="pb-2">Rule group name</div>
        <Input
          value={name}
          onChange={(c) => setName(c.target.value)}
          placeholder="Enter name of rule"
        />
      </Card>
      <Card>
        <Form onFinish={onCreateNewRule} layout="vertical">
          <Row gutter={5}>
            <Col span={7}>
              <FormItem name={"field_value"} label="Field name">
                <Select
                  value={current_config?.field_value}
                  onChange={(v) => {
                    //reset the operation and value
                    setOperator(undefined);
                    setValue(undefined);

                    //then set the config
                    const config = config_rules.find((ele) => ele.field_value == v);
                    setCurrentConfig(config);
                  }}
                  options={config_rules.map((c) => ({
                    value: c.field_value,
                    label: c.field_label
                  }))}
                />
              </FormItem>
            </Col>
            <Col span={7}>
              <FormItem label="Operation">
                <Select
                  value={operator}
                  onChange={(v) => setOperator(v)}
                  disabled={!current_config}
                  options={current_config?.operators.map((op) => ({
                    value: op,
                    label: op
                  }))}
                />
              </FormItem>
            </Col>
            <Col span={7}>
              <FormItem label="Value">
                <DynamicField
                  onChange={setValue}
                  value={value}
                  operator={operator}
                  current_config={current_config}
                />
              </FormItem>
            </Col>
            <Col span={3}>
              <FormItem label="Actions">
                <Button onClick={onCreateNewRule} type="primary">
                  Add
                </Button>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Card>
      <Card title={"Added Rules"} className="mt-3" bodyStyle={{ padding: 5 }}>
        {rules.length > 0 ? (
          <Table
            pagination={{
              defaultPageSize: 3
            }}
            dataSource={rules}
            columns={[
              {
                title: "Field name",
                dataIndex: "field_name",
                key: "field_name",
                align: "center",
                render: (val) => config_rules.find((r) => r.field_value === val)?.field_label
              },
              {
                title: "Operation",
                dataIndex: "operator",
                key: "operator",
                align: "center"
              },
              {
                title: "Value",
                dataIndex: "value",
                align: "center",
                render: ({ value }, all) => {
                  if (isArray(value)) {
                    return (
                      <div className="flex flex-wrap gap-2">
                        {value.map((e: string) => (
                          <Tag color={getRandomTagBgColor(e)} key={`t_${e}`}>
                            {
                              config_rules
                                .find((rule) => rule.field_value === all.field_name)
                                ?.options?.find((op) => op.value === e)?.label
                            }
                          </Tag>
                        ))}
                      </div>
                    );
                  } else {
                    return config_rules
                      .find((rule) => rule.field_value === all.field_name)
                      ?.options?.find((op) => op.value === value)?.label;
                  }
                }
              },
              {
                title: "Actions",
                dataIndex: "uuid",
                key: "uuid",
                align: "center",
                render: (uuid) => (
                  <Button
                    onClick={() => {
                      setRules(rules.filter((r) => r.uuid !== uuid));
                    }}
                    icon={<MdDelete />}
                    danger
                  />
                )
              }
            ]}
          />
        ) : (
          <Empty description={"No rules added yet"} />
        )}
      </Card>
    </Modal>
  );
};

export default SpCreateRulesModal;
