import { useCallback, useEffect, useState } from "react";

/**
 * Custom hook to manage the base path for navigation between Individual and Service Provider views
 * @returns {Object} - Object containing basePath state and setBasePath function
 */
export const useBasePath = () => {
  const [basePath, setBasePathState] = useState<string>(localStorage.getItem("base_path") || "");

  // Update basePath in localStorage and dispatch event
  const setBasePath = useCallback((newBasePath: string) => {
    localStorage.setItem("base_path", newBasePath);
    setBasePathState(newBasePath);

    // Dispatch event to notify other components
    window.dispatchEvent(new Event("basePathChanged"));
  }, []);

  // Listen for basePathChanged events from other components
  useEffect(() => {
    const handleBasePathChange = () => {
      const storedBasePath = localStorage.getItem("base_path") || "";
      setBasePathState(storedBasePath);
    };

    window.addEventListener("basePathChanged", handleBasePathChange);

    return () => {
      window.removeEventListener("basePathChanged", handleBasePathChange);
    };
  }, []);

  return { basePath, setBasePath };
};

export default useBasePath;
