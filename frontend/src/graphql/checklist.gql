mutation UpdateUserChecklistData($data: [UpdateUserChecklistDataInput]!) {
  updateUserChecklistData(data: $data) {
    result
    message
  }
}

query GetAllChecklists($userType: String) {
  getAllChecklists(user_type: $userType) {
    result
    message
    data {
      id
      name
      is_active
      created_at
      updated_at
    }
  }
}
query GetUserChecklistData($userId: Int!, $userType: String) {
  getUserChecklistData(user_id: $userId, user_type: $userType) {
    result
    message
    data {
      id
      name
      user_checklist_data {
        id
        hiring_completed
      }
    }
  }
}

query GetFeedbackFormStatus($userId: Int!) {
  getFeedbackFormStatus(user_id: $userId) {
    result
    message
    submitted
    onboarded
    user_id
    language
  }
}
