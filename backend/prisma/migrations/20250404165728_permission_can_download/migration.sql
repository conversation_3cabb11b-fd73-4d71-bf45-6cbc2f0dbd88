/*
  Warnings:

  - You are about to drop the column `sub_module_url` on the `admin_protected_feature` table. All the data in the column will be lost.
  - You are about to drop the column `can_publish` on the `admin_role` table. All the data in the column will be lost.
  - You are about to drop the column `can_download` on the `admin_role_permission` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "admin_protected_feature" DROP COLUMN "sub_module_url",
ADD COLUMN     "sub_module_url_sp" TEXT,
ADD COLUMN     "sub_module_url_user" TEXT;

-- AlterTable
ALTER TABLE "admin_role" DROP COLUMN "can_publish",
ADD COLUMN     "can_download" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "admin_role_permission" DROP COLUMN "can_download";

-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';



--SETTING UP ROOT ADMIN ROLE AND <PERSON><PERSON><PERSON><PERSON> FEATURES


INSERT INTO admin_module (module_name, parent_module_name, parent_module_id, is_published, is_active, icon_name, visible_in_users,visible_in_sp)
VALUES 
('dashboard', NULL, NULL, TRUE, TRUE, 'RiDashboardLine', TRUE,TRUE),
('users', 'dashboard', 1, TRUE, TRUE, 'BiUser', TRUE, FALSE),
('service providers', 'dashboard', 1, TRUE, TRUE, 'BiUser', FALSE,TRUE),
('documents', 'dashboard', 1, TRUE, TRUE, 'HiOutlineDocumentDuplicate', TRUE,TRUE),
('interviews', 'dashboard', 1, TRUE, TRUE, 'BsPersonCheck', TRUE,TRUE),
('tests', 'dashboard', 1, TRUE, TRUE, 'BsClipboard2', TRUE,TRUE),
('training', 'dashboard', 1, TRUE, TRUE, 'GrTemplate', TRUE,FALSE),
('rejected users', 'dashboard', 1, TRUE, TRUE, 'UserDeleteOutlined', TRUE,FALSE),
('transfer to tms', 'dashboard', 1, TRUE, TRUE, 'RiSendPlane2Line', TRUE,FALSE),
('referrals', 'dashboard', 1, TRUE, TRUE, 'GoShareAndroid', TRUE,FALSE),
('banners', 'dashboard', 1, TRUE, TRUE, 'MdOutlineInsertPhoto', TRUE,FALSE),
('new releases', 'dashboard', 1, TRUE, TRUE, 'NewReleasesIcon', TRUE,FALSE),
('settings', 'dashboard', 1, TRUE, TRUE, 'GoTools', TRUE,TRUE),
('lead contacts', 'dashboard', 1, TRUE, TRUE, 'FaRegAddressBook', TRUE,FALSE),
('user management', 'dashboard', 1, TRUE, TRUE, 'RiShieldStarLine', FALSE,TRUE),
('admin', 'dashboard', 1, TRUE, TRUE, 'MdOutlineFeedback', TRUE,FALSE);





INSERT INTO admin_protected_feature (
  is_module, parent_module_id, module_id, parent_module_name, module_name, sub_module_name, 
  sub_module_url_user, sub_module_url_sp, icon_name, section_id, created_at, is_active
) VALUES
  (true, NULL, NULL, 'Dashboard', 'Dashboard', 'Dashboard', 
  '/dashboard', '/dashboard/sp', 'RiDashboardLine', NULL, NOW(), true),

  (true, 1, NULL, 'Dashboard', 'Service Providers', 'Service Provider', 
  NULL, '/dashboard/sp/service_providers', 'RiUserStarLine', NULL, NOW(), true),

 
  (true, 1, NULL, 'Dashboard', 'Users', 'Users', 
  '/dashboard/users/', NULL, 
  'RiUserLine', NULL, NOW(), true),
  
   (false, 1, NULL, 'Dashboard', 'Users', 'User Details', 
  '/dashboard/users/:user_id', NULL, 
  'RiUserLine', NULL, NOW(), true),

  (false, 1, NULL, 'Dashboard', 'Service Providers', 'SP Details', 
  NULL, '/dashboard/sp/details/:user_id', 
  'RiUserStarLine', NULL, NOW(), true),

  (true, 1, NULL, 'Dashboard', 'Documents', 'Documents', 
  '/dashboard/documents', '/dashboard/sp/documents', 
  'RiFileListLine', NULL, NOW(), true),
  (false, 1, NULL, 'Dashboard', 'Documents', 'Documents Types', 
  '/dashboard/documents/document_types', '/dashboard/sp/documents/document_types', 
  'RiFileListLine', NULL, NOW(), true),
  (false, 1, NULL, 'Dashboard', 'Documents', 'Document Details', 
  '/dashboard/users/:user_id/document/:document_id', '/dashboard/sp/details/:user_id/documents/:document_id', 
  'RiFileListLine', NULL, NOW(), true),
   (true, 1, NULL, 'Dashboard', 'Interviews', 'Interviews', 
  '/dashboard/interviews', '/dashboard/sp/interviews', 
  'RiUserVoiceLine', NULL, NOW(), true),

    (false, 1, NULL, 'Dashboard', 'Interviews', 'Interviewers', 
  '/dashboard/interviews/interviewers', '/dashboard/sp/interviews/interviewers', 
  'RiUserVoiceLine', NULL, NOW(), true),

    (false, 1, NULL, 'Dashboard', 'Interviews', 'Config Templates', 
  '/dashboard/interviews/feedbacks/templates', '/dashboard/sp/interviews/feedbacks/templates', 
  'RiUserVoiceLine', NULL, NOW(), true),
  
  (false, 1, NULL, 'Dashboard', 'Interviews', 'Config Approved Users', 
  '/dashboard/interviews/approved', '/dashboard/sp/interviews/approved', 
  'RiCalendarCheckLine', NULL, NOW(), true),

  (false, 1, NULL, 'Dashboard', 'Interviews', 'Interview Details', 
  '/dashboard/users/:user_id/interview/:interview_id', 
  '/dashboard/sp/details/:user_id/interview/:interview_id/:org_id', 
  'RiUserSearchLine', NULL, NOW(), true),

  (true, 1, NULL, 'Dashboard', 'Tests', 'Performance', 
  '/dashboard/assignment', '/dashboard/sp/assignment', 
  'RiFilePaperLine', NULL, NOW(), true),
  (false, 1, NULL, 'Dashboard', 'Tests', 'Assignment', 
  '/dashboard/assignment/config', '/dashboard/sp/assignment/config', 
  'RiFilePaperLine', NULL, NOW(), true),
  (false, 1, NULL, 'Dashboard', 'Tests', 'Buckets', 
  '/dashboard/assignment/buckets', '/dashboard/sp/assignment/buckets', 
  'RiFilePaperLine', NULL, NOW(), true),
  (false, 1, NULL, 'Dashboard', 'Tests', 'Rules', 
  '/dashboard/assignment/rules', '/dashboard/sp/assignment/rules', 
  'RiFilePaperLine', NULL, NOW(), true),
  (false, 1, NULL, 'Dashboard', 'Tests', 'Questions', 
  '/dashboard/assignment/questions', '/dashboard/sp/assignment/questions', 
  'RiFilePaperLine', NULL, NOW(), true),

  (false, 1, NULL, 'Dashboard', 'Tests', 'Assessment Details', 
  '/dashboard/users/:user_id/assignment/:assignment_id', '/dashboard/sp/details/:user_id/assignment/:assignment_id', 
  'RiFilePaperLine', NULL, NOW(), true),

  (false, 1, NULL, 'Dashboard', 'Tests', 'Performance Approval', 
  '/dashboard/assignment/results/:user_id/:assignment_id', 
  '/dashboard/sp/assignment/results/:user_id/:assignment_id', 
  'RiFileTextLine', NULL, NOW(), true),

 (true, 1, NULL, 'Dashboard', 'Training', 'Performance', 
  '/dashboard/training', NULL, 
  'RiDashboard3Line', NULL, NOW(), true),
 (false, 1, NULL, 'Dashboard', 'Training', 'Courses', 
  '/dashboard/training/courses', NULL, 
  'RiDashboard3Line', NULL, NOW(), true),
 (false, 1, NULL, 'Dashboard', 'Training', 'Content Library', 
  '/dashboard/training/content_creation', NULL, 
  'RiDashboard3Line', NULL, NOW(), true),
 (false, 1, NULL, 'Dashboard', 'Training', 'Manage', 
  '/dashboard/training/manage', NULL, 
  'RiDashboard3Line', NULL, NOW(), true),

  (false, 1, NULL, 'Dashboard', 'Training', 'Course Performance', 
  '/dashboard/users/:userid/training', NULL, 
  'RiBarChart2Line', NULL, NOW(), true),

  (false, 1, NULL, 'Dashboard', 'Training', 'Assigned Course Details', 
  '/dashboard/training/courses/assigned/:course_id', 
  '/dashboard/training/courses/assigned/:course_id', 
  'RiListCheckLine', NULL, NOW(), true),

  (false, 1, NULL, 'Dashboard', 'Training', 'View Course', 
  '/dashboard/training/courses/course_view/:internal_course_id', 
  '/dashboard/training/courses/course_view/:internal_course_id', 
  'RiEyeLine', NULL, NOW(), true),

 (true, 1, NULL, 'Dashboard', 'User Management', 'User Management', 
  NULL, '/dashboard/sp/users_manage', 'RiUserSettingsLine', NULL, NOW(), true),
 (false, 1, NULL, 'Dashboard', 'User Management', 'User Details', 
  NULL, '/dashboard/sp/user/details/:user_id', 'RiUserSettingsLine', NULL, NOW(), true),

 
(true, 1, NULL, 'Dashboard', 'Transfer to TMS', 'Transfer to TMS', 
  '/dashboard/transfer_to_tms', NULL, 'RiExchangeLine', NULL, NOW(), true),

  (true, 1, NULL, 'Dashboard', 'Rejected Users', 'Rejected Users', 
  '/dashboard/rejected', NULL,'RiUserUnfollowLine', NULL, NOW(), true),

  (true, 1, NULL, 'Dashboard', 'Referrals', 'Referrals', 
  '/dashboard/referral', NULL, 'RiUserHeartLine', NULL, NOW(), true),
  (true, 1, NULL, 'Dashboard', 'Banners', 'Banners', 
  '/dashboard/banners', NULL, 'RiUserHeartLine', NULL, NOW(), true),

  (false, 1, NULL, 'Dashboard', 'Referrals', 'Configuration', 
  '/dashboard/referral/referral_config', NULL, 'RiSettingsLine', NULL, NOW(), true),

  (true, 1, NULL, 'Dashboard', 'New Releases', 'New Releases', 
  '/dashboard/releases', NULL, 'RiRocketLine', NULL, NOW(), true),

  (true, 1, NULL, 'Dashboard', 'Settings', 'SP Skills', 
  '/dashboard/settings', '/dashboard/sp/settings', 'RiLightbulbLine', NULL, NOW(), true),

  (false, 1, NULL, 'Dashboard', 'Settings', 'Designation', 
  '/dashboard/settings/designation', NULL, 'RiTeamLine', NULL, NOW(), true),

(true, 1, NULL, 'Dashboard', 'Lead Contacts', 'Lead Contacts', 
  '/dashboard/lead_contacts', NULL, 'FaRegAddressBook', NULL, NOW(), true),
  (true, 1, NULL, 'Dashboard', 'Admin', 'Manage Admin', 
  '/dashboard/admin', NULL, 'BiMessageRoundedDetail', NULL, NOW(), true),
  (false, 1, NULL, 'Dashboard', 'Admin', 'Permissions', 
  '/dashboard/admin/permission', NULL, 'GrTemplate', NULL, NOW(), true),
  (false, 1, NULL, 'Dashboard', 'Admin', 'Publish Modules', 
  '/dashboard/admin/publish', NULL, 'MdOutlinePublishedWithChanges', NULL, NOW(), true);

  


-- Update parent_module_id 
UPDATE admin_protected_feature f
SET parent_module_id = m.module_id
FROM admin_module m
WHERE 
  LOWER(f.parent_module_name) = LOWER(m.module_name)
  AND LOWER(f.module_name) <> LOWER(f.parent_module_name);



-- Update module_id
UPDATE admin_protected_feature f
SET module_id = m.module_id
FROM admin_module m
WHERE LOWER(f.module_name) = LOWER(m.module_name);

-- Update icon_name
UPDATE admin_protected_feature f
SET icon_name = m.icon_name
FROM admin_module m
WHERE 
  f.is_module = true
  AND f.module_id = m.module_id;


-- Step 1: Create the Root Admin role
INSERT INTO admin_role (
  name, description, is_root_admin, has_admin_privileges, 
  can_reject, can_ban, can_accept, can_download,
  creator_id, created_at
) VALUES (
  'Root Admin', 'Has full access to all features and settings', 
  true, true, true, true, true, true,
  1, NOW()
) RETURNING admin_role_id;

-- Step 2: Assign all permissions to the Root Admin role
INSERT INTO admin_role_permission (
  admin_role_id, feature_id, permission_for_user, permission_for_sp
) 
SELECT 
  (SELECT admin_role_id FROM admin_role WHERE name = 'Root Admin'), 
  feature_id,  
  '{"create":true, "update":true, "read":true, "delete":true}'::json, 
  '{"create":true, "update":true, "read":true, "delete":true}'::json
FROM admin_protected_feature;


-- --- Set these users as root admin
-- UPDATE admin
-- SET admin_role_id = 1
-- WHERE email IN (
--   '<EMAIL>',
--   '<EMAIL>',
--   '<EMAIL>' -- add more emails as needed
-- );

