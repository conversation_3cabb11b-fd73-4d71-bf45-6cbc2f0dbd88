import { Checkbox, Form, Input, Modal, notification, Switch } from "antd";
import { useForm } from "antd/es/form/Form";
import isEmpty from "is-empty";
import { useNavigate, useParams } from "react-router-dom";
import {
  DocumentTypesDocument,
  useDocumentTypesQuery,
  useUpdateDocumentTypeMutation
} from "../../__generated__";

export enum DocumentStatus {
  Active = "ACTIVE",
  Inactive = "INACTIVE"
}

interface FormValues {
  name: string;
  required: boolean;
  status: DocumentStatus;
  english: string;
  hindi: string;
}

const UpdateDocumentType: React.FC = () => {
  const { TextArea } = Input;

  //navigation
  const navigate = useNavigate();

  //params
  const params = useParams();

  //Hooks
  const [form] = useForm();

  //Graphql Query
  useDocumentTypesQuery({
    variables: {
      typeId: parseInt(params?.document_type_id || "-1")
    },
    onCompleted(data) {
      data.getDocumentTypeDataFrAdmin?.forEach((type) => {
        const instructions = !isEmpty(type?.instructions) ? JSON.parse(type?.instructions) : {};
        form.setFieldsValue({
          name: type?.name,
          required: type?.required ? true : false,
          status: type?.status === DocumentStatus.Active ? true : false,
          english: instructions?.eng,
          hindi: instructions?.hi
        });
      });
    }
  });

  //Graphql Mutation
  const [updateDocType] = useUpdateDocumentTypeMutation();

  const updateDocumentType = async (values: FormValues) => {
    try {
      await updateDocType({
        variables: {
          data: {
            name: values?.name,
            required: values?.required == undefined || values?.required == false ? false : true,
            document_id: parseInt(params?.document_type_id || "-1"),
            status: values?.status ? DocumentStatus.Active : DocumentStatus.Inactive,
            instructions: JSON.stringify({ eng: values?.english, hi: values?.hindi })
          }
        },
        onCompleted() {
          notification.success({
            message: "Document type updated successfully"
          });
          navigate(-1);
        },
        refetchQueries: [DocumentTypesDocument]
      });
    } catch (err) {
      notification.error({
        message: "There was something wrong during updating your type"
      });
    }
  };

  {
    return (
      <>
        <Modal
          title="Edit Document Type"
          open
          onOk={form.submit}
          okText="Update"
          onCancel={() => {
            navigate(-1);
          }}
        >
          <Form form={form} onFinish={updateDocumentType} layout="vertical">
            <Form.Item
              name={"name"}
              required
              label={"Document Name"}
              rules={[{ required: true, message: "Document name is required" }]}
            >
              <Input placeholder="Aadhar card" />
            </Form.Item>
            <div className="mb-2">Document Description</div>
            <Form.Item
              name={"english"}
              required
              label={"English"}
              rules={[{ required: true, message: "Document Description is required" }]}
            >
              <TextArea rows={2} placeholder="Start typing in English" />
            </Form.Item>
            <Form.Item
              name={"hindi"}
              required
              label={"Hindi"}
              rules={[{ required: true, message: "Document Description is required" }]}
            >
              <TextArea rows={2} placeholder="हिंदी में लिखना शुरू करें" />
            </Form.Item>

            <Form.Item
              name={"required"}
              label={"Is this Document Required ?"}
              valuePropName="checked"
            >
              <Checkbox>Required</Checkbox>
            </Form.Item>

            <Form.Item name={"status"} label={"Status"} valuePropName="checked">
              <Switch />
            </Form.Item>
          </Form>
        </Modal>
      </>
    );
  }
};
export default UpdateDocumentType;
