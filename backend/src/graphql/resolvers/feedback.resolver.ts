import { InterviewState, OnboardingStage, Prisma } from '@prisma/client'
import { GraphQLError } from 'graphql'
import { validateAdmin } from '../..//utils/authValidators'
import DataProvider from '../../data'
import { logHistory } from '../../services/history.service'
import { checkGraphQLAuth } from '../../utils/common'
import { EventList, EventType } from '../../utils/eventTypes'
import { Context } from '../../utils/types'
import { awardReferralPoints, createUserTimestamp } from '../../utils/user'
import {
    Interviewee<PERSON><PERSON>backForm,
    InterviewerFeedbackForm,
    InterviewerFeedbackResults,
    MutationResolvers,
    QueryResolvers,
} from '../__generated__/types'

const db = DataProvider.getDbInstance()

const getFeedbackTemplates: QueryResolvers['getFeedbackTemplates'] = async (
    _,
    args,
    ctx: Context
) => {
    validateAdmin(ctx)

    const templates = await db.feedback_template.findMany({
        where: {
            title: { contains: args.search ? args.search : undefined },
        },
        select: {
            id: true,
            title: true,
            status: true,
            created_at: true,
            interview_feedback: {
                select: {
                    id: true,
                    interviewer_feedback_state: true,
                },
            },
        },
        orderBy: {
            created_at: 'desc',
        },
    })
    return templates
}
const createFeedbackTemplate: MutationResolvers['createFeedbackTemplate'] =
    async (_, args, ctx: Context) => {
        try {
            const admin = validateAdmin(ctx)

            const {
                title,
                interviewee_feedback_enabled,
                interviewee_feedback_meta,
                interviewer_feedback_enabled,
                interviewer_feedback_meta,
                status,
                meta,
            } = args.data || {}

            const feedbackTemplateInput: Prisma.feedback_templateCreateInput = {
                title,
                status,
                created_at: new Date(),
                interviewee_feedback_enabled:
                    interviewee_feedback_enabled || undefined,
                interviewee_feedback_meta:
                    interviewee_feedback_meta || undefined,
                interviewer_feedback_enabled,
                interviewer_feedback_meta,
                meta: {
                    updated_at: new Date().toISOString(),
                    created_at: new Date().toISOString(),
                    created_by: admin.id,
                    created_by_name: admin.name,
                    updated_by: admin.id,
                    updated_by_name: admin.name,
                    ...meta,
                },
                creator: {
                    connect: {
                        id: admin.id,
                    },
                },
            }

            await db.feedback_template.create({
                data: feedbackTemplateInput,
            })

            return {
                result: true,
                message: 'Feedback template created successfully',
            }
        } catch (error) {
            throw new GraphQLError(
                `Failed to create feedback template: ${error.message}`
            )
        }
    }
const updateFeedbackTemplate: MutationResolvers['updateFeedbackTemplate'] =
    async (_, args, ctx: Context) => {
        try {
            const admin = validateAdmin(ctx)
            const {
                title,
                id,
                status,
                interviewee_feedback_enabled,
                interviewee_feedback_meta,
                interviewer_feedback_enabled,
                interviewer_feedback_meta,
                meta,
            } = args.data
            const feedbackTemplate = await db.feedback_template.findUnique({
                where: { id: id },
            })
            const feedbackTemplateMeta = feedbackTemplate?.meta as object
            const feedbackTemplateInput: Prisma.feedback_templateUpdateInput = {
                title: title || undefined,
                status: status as boolean,
                interviewee_feedback_enabled:
                    interviewee_feedback_enabled as boolean,
                interviewee_feedback_meta:
                    interviewee_feedback_meta as Prisma.InputJsonValue,
                interviewer_feedback_enabled:
                    interviewer_feedback_enabled as boolean,
                interviewer_feedback_meta:
                    interviewer_feedback_meta as Prisma.InputJsonValue,
                meta: {
                    ...feedbackTemplateMeta,
                    updated_at: new Date().toISOString(),
                    updated_by: admin.id,
                    updated_by_name: admin.name,
                    ...meta,
                },
            }
            await db.feedback_template.update({
                where: { id: id },
                data: feedbackTemplateInput,
            })
            return {
                result: true,
                message: 'Feedback template updated successfully',
            }
        } catch (error) {
            throw new GraphQLError(error)
        }
    }
const createFeedbackSectionResolver = async (
    _: any,
    args: any,
    ctx: Context
) => {
    try {
        const { data } = args
        const { admin } = ctx
        if (admin) {
            const section = await db.feedback_section.create({
                data: {
                    instruction: data.instruction,
                    title: data.title,
                    max_score: data.max_score,
                    template: {
                        connect: {
                            id: data.template_id,
                        },
                    },
                    required: data.required,
                },
            })
            return section
        } else {
            throw new GraphQLError('Authentication  Failed')
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const updateFeedbackSectionResolver = async (
    _: any,
    args: any,
    ctx: Context
) => {
    try {
        const { admin } = ctx
        if (admin) {
            const section = await db.feedback_section.update({
                data: args.data,
                where: {
                    id: args.data.feedback_id,
                },
            })
            return section
        } else {
            throw new GraphQLError('Authentication  Failed')
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getFeedbackSectionsResolver = async (_: any, args: any, ctx: Context) => {
    try {
        const { admin } = ctx
        if (admin) {
            const sections = await db.feedback_section.findMany({
                where: {
                    template: {
                        id: args.id,
                    },
                },
            })
            return sections
        } else {
            throw new GraphQLError('Authentication  Failed')
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const createInterviewFeedbackResolver = async (
    _: any,
    args: any,
    ctx: Context
) => {
    try {
        const { admin } = ctx
        const { data } = args
        if (admin) {
            const interview_feedback = await db.interview_feedback.create({
                data: {
                    summary: data.summary || undefined,
                    interviewer_feedback_state:
                        data.feedback_state || 'PENDING',
                    user: {
                        connect: {
                            id: data.user_id,
                        },
                    },
                    interviewer: {
                        connect: {
                            id: data.interviewer_id,
                        },
                    },
                    template: {
                        connect: {
                            id: data.template_id,
                        },
                    },
                },
            })
            const user = await db.user.findFirst({
                where: {
                    id: data.user_id,
                },
            })
            logHistory({
                entity_id: interview_feedback.id.toString(),
                ...EventList[EventType.INTERVIEW_FEEDBACK_CREATED],
                creator_type: 'ADMIN',
                new_values: {
                    ...interview_feedback,
                },
                user_id: data.user_id,
                org_id: user?.organization_id || -1,
                creator_id: admin.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx.userAgent || '',
                    user_id: user?.id || -1,
                    user_name: user?.name || '',
                    user_org_id: user?.organization_id || -1,
                    creator_id: admin.id,
                    creator_name: admin.name,
                    creator_org_id: admin.active_org_id || -1,
                },
            })
            return interview_feedback
        } else {
            throw new GraphQLError('Authentication Failed')
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getInterviewerFeedbackFormData: QueryResolvers['getInterviewerFeedbackFormData'] =
    async (_, args) => {
        try {
            const template = await db.interview_feedback.findUnique({
                where: {
                    feedback_form_id: args?.template_form_id,
                },
                select: {
                    interviewer_feedback_result: true,
                    interviewer_feedback_state: true,
                    template: {
                        select: {
                            interviewer_feedback_meta: true,
                        },
                    },
                    user: {
                        select: {
                            name: true,
                        },
                    },
                    interviewer: {
                        select: {
                            name: true,
                        },
                    },
                },
            })
            return template as InterviewerFeedbackForm
        } catch (error) {
            throw new GraphQLError('Something went wrong')
        }
    }

const getIntervieweeFeedbackFormData: QueryResolvers['getIntervieweeFeedbackFormData'] =
    async (_, args: { template_form_id?: string }) => {
        try {
            const template = await db.interview_feedback.findUnique({
                where: {
                    feedback_form_id: args?.template_form_id,
                },
                select: {
                    interviewee_feedback_result: true,
                    interviewee_feedback_state: true,
                    template: {
                        select: {
                            interviewee_feedback_meta: true,
                        },
                    },
                    meta: true,
                    user: {
                        select: {
                            name: true,
                        },
                    },
                    interviewer: {
                        select: {
                            name: true,
                        },
                    },
                },
            })
            return template as IntervieweeFeedbackForm
        } catch (error) {
            throw new GraphQLError('Something went wrong')
        }
    }
const updateInterviewerFeedbackForm: MutationResolvers['updateInterviewerFeedbackForm'] =
    async (_, args, ctx: Context) => {
        try {
            const { feedback_form_id, interviewer_feedback_result } =
                args?.data || {}
            const interviewer_feedback = await db.interview_feedback.findUnique(
                {
                    where: {
                        feedback_form_id: feedback_form_id,
                    },
                }
            )

            if (!interviewer_feedback) {
                throw new GraphQLError('Interviewer feedback not found')
            }

            const meta = interviewer_feedback?.meta as object

            const interview_feedback = await db.interview_feedback.update({
                where: {
                    feedback_form_id: feedback_form_id,
                },
                data: {
                    interviewer_feedback_state: 'COMPLETED',
                    interviewer_feedback_result: interviewer_feedback_result,
                    meta: {
                        ...meta,
                        interviewer_feedback_submitted_at:
                            new Date().toISOString(),
                    },
                },
            })
            const user = await db.user.findFirst({
                where: {
                    id: interview_feedback.user_id,
                },
            })
            const interviewerDetails = await db.interviewer.findFirst({
                where: {
                    id: interview_feedback.interviewer_id,
                },
                select: {
                    id: true,
                    name: true,
                },
            })

            logHistory({
                entity_id: interview_feedback.id.toString(),
                ...EventList[EventType.INTERVIEW_FEEDBACK_SUBMITTED],
                creator_type: 'ADMIN',
                new_values: {
                    status: interview_feedback.interviewer_feedback_state,
                    interview_id: interview_feedback.id,
                    feedback_state: 'COMPLETED',
                    feedback_id: interview_feedback.id,
                    interviewer_id: interviewerDetails?.id,
                    interviewer_name: interviewerDetails?.name,
                },
                user_id: user?.id || -1,
                org_id: user?.organization_id || -1,
                creator_id: ctx.admin?.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: user?.id || -1,
                    user_name: user?.name || '',
                    user_org_id: user?.organization_id || -1,
                    creator_id: ctx.admin?.id,
                    creator_name: ctx.admin?.name,
                    creator_org_id: ctx.admin?.active_org_id,
                },
            })
            return {
                result: true,
                message: 'Interviewer feedback updated successfully',
            }
        } catch (error) {
            throw new GraphQLError(error)
        }
    }

const updateIntervieweeFeedbackForm: MutationResolvers['updateIntervieweeFeedbackForm'] =
    async (
        _,
        args: {
            data?: {
                feedback_form_id: string
                interviewee_feedback_result: any
            }
        },
        ctx: Context
    ) => {
        try {
            const { feedback_form_id, interviewee_feedback_result } =
                args?.data || {}
            const interviewee_feedback = await db.interview_feedback.findUnique(
                {
                    where: {
                        feedback_form_id: feedback_form_id,
                    },
                }
            )

            if (!interviewee_feedback) {
                throw new GraphQLError('Interviewee feedback not found')
            }

            const meta = interviewee_feedback?.meta as object

            const interview_feedback = await db.interview_feedback.update({
                where: {
                    feedback_form_id: feedback_form_id,
                },
                data: {
                    interviewee_feedback_state: 'COMPLETED',
                    interviewee_feedback_result: interviewee_feedback_result,
                    meta: {
                        ...meta,
                        interviewee_feedback_submitted_at:
                            new Date().toISOString(),
                    },
                },
            })
            const user = await db.user.findFirst({
                where: {
                    id: interview_feedback.user_id,
                },
            })
            // User details are already available from the user query above

            logHistory({
                entity_id: interview_feedback.id.toString(),
                ...EventList[EventType.INTERVIEW_FEEDBACK_SUBMITTED],
                creator_type: 'USER',
                new_values: {
                    status: interview_feedback.interviewee_feedback_state,
                    interview_id: interview_feedback.id,
                    feedback_state: 'COMPLETED',
                    feedback_id: interview_feedback.id,
                    user_id: user?.id,
                    user_name: user?.name,
                },
                user_id: user?.id || -1,
                org_id: user?.organization_id || -1,
                creator_id: user?.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx?.userAgent || '',
                    user_id: user?.id || -1,
                    user_name: user?.name || '',
                    user_org_id: user?.organization_id || -1,
                    creator_id: user?.id,
                    creator_name: user?.name,
                },
            })
            return {
                result: true,
                message: 'Interviewee feedback updated successfully',
            }
        } catch (error) {
            throw new GraphQLError(error)
        }
    }
const updateInterviewResult = async (_: any, args: any, ctx: Context) => {
    try {
        const { admin } = ctx
        if (admin) {
            const result = await db.interview_feedback.update({
                data: {
                    interview_state: args.status,
                },
                where: {
                    id: args.id,
                },
            })
            const user = await db.user.findFirst({
                where: {
                    id: result.user_id,
                },
            })
            logHistory({
                entity_id: result.id.toString(),
                ...EventList[EventType.INTERVIEW_SINGLE_RESULT_UPDATED],
                creator_type: 'ADMIN',
                new_values: {
                    status: args.status,
                    interview_id: args.id,
                },
                user_id: user?.id || -1,
                org_id: user?.organization_id || -1,
                creator_id: admin.id,
                meta: {
                    ip_addr: ctx?.ipAddr || '',
                    user_agent: ctx.userAgent || '',
                    user_id: user?.id || -1,
                    user_name: user?.name || '',
                    user_org_id: user?.organization_id || -1,
                    creator_id: admin.id,
                    creator_name: admin.name,
                    creator_org_id: admin.active_org_id,
                },
            })
            return {
                data: result,
                message: 'Updated successfully',
            }
        } else {
            throw new GraphQLError('Authentication Failed')
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const rejectUserForTraining = async (
    _: any,
    args: { user_id: number },
    ctx: Context
) => {
    try {
        const admin = validateAdmin(ctx)
        const { user_id } = args

        await db.interview_feedback.updateMany({
            data: {
                interview_state: InterviewState.REJECTED,
            },
            where: {
                user_id: user_id,
            },
        })
        const user = await db.user.findFirst({
            where: {
                id: user_id,
            },
        })
        logHistory({
            entity_id: user?.id.toString() || -1,
            ...EventList[EventType.INTERVIEW_FINAL_RESULT_UPDATED],
            event_title: `${
                EventList[EventType.INTERVIEW_FINAL_RESULT_UPDATED].event_title
            }  ${InterviewState.REJECTED}`,
            creator_type: 'ADMIN',
            new_values: {
                interview_state: InterviewState.REJECTED,
            },
            user_id: user?.id || -1,
            org_id: user?.organization_id || -1,
            creator_id: admin.id,
            meta: {
                ip_addr: ctx?.ipAddr || '',
                user_agent: ctx.userAgent || '',
                user_id: user?.id || -1,
                user_name: user?.name || '',
                user_org_id: user?.organization_id || -1,
                creator_id: admin.id,
                creator_name: admin.name,
                creator_org_id: admin.active_org_id,
            },
        })
        return true
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const approveUserForTraining = async (
    _: any,
    args: { user_id: number },
    ctx: Context
) => {
    try {
        const { admin } = ctx
        if (admin) {
            await db.interview_feedback.updateMany({
                data: {
                    interview_state: InterviewState.COMPLETED,
                },
                where: {
                    user_id: args.user_id,
                },
            })
            const result = await db.user.update({
                data: {
                    onboarding_stage: OnboardingStage.TRAINING_AND_TESTS,
                },
                where: {
                    id: args.user_id,
                },
            })
            logHistory({
                ...EventList[EventType.INTERVIEW_FINAL_RESULT_UPDATED],
                event_title: `${
                    EventList[EventType.INTERVIEW_FINAL_RESULT_UPDATED]
                        .event_title
                } " ${InterviewState.COMPLETED}`,
                new_values: {
                    interview_state: InterviewState.REJECTED,
                },
                entity_id: args.user_id.toString(),
                creator_type: 'ADMIN',
                user_id: result.id,
                org_id: result.organization_id,
                creator_id: admin.id,
                meta: {
                    creator_id: admin.id,
                    creator_name: admin.name,
                    creator_org_id: admin.active_org_id || -1,
                    user_id: args.user_id,
                    user_name: result.name,
                    user_org_id: result.organization_id,
                    user_agent: ctx.userAgent || '',
                    ip_addr: ctx?.ipAddr || '',
                },
            })
            await createUserTimestamp({
                user_id: result.id,
                admin_id: admin.id,
            })
            await awardReferralPoints(result)
            return true
        } else {
            throw new GraphQLError('Authentication Failed')
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const approvedCandidate = async (_: any, args: any, ctx: Context) => {
    try {
        const { admin } = ctx
        if (admin) {
            const result = await db.user.findMany({
                where: {
                    onboarding_stage: 'TRAINING_AND_TESTS',
                    organization: {
                        id: admin.active_org_id || -1,
                    },
                    OR: [
                        {
                            name: {
                                contains: args.data.search
                                    ? args.data.search
                                    : '',
                                mode: 'insensitive',
                            },
                        },
                        {
                            email: {
                                contains: args.data.search
                                    ? args.data.search
                                    : '',
                                mode: 'insensitive',
                            },
                        },
                        {
                            phone: {
                                contains: args.data.search
                                    ? args.data.search
                                    : '',
                            },
                        },
                    ],
                },
                take: args.data.size,
                skip: (args.data.batch - 1) * args.data.size,
            })
            return result
        } else {
            throw new GraphQLError('Authentication Failed')
        }
    } catch (error) {
        throw new GraphQLError(error)
    }
}

const getInterviewerFeedbackResults: QueryResolvers['getInterviewerFeedbackResults'] =
    async (_: any, args, ctx: Context) => {
        try {
            validateAdmin(ctx)

            const { id } = args

            if (!id) {
                throw new GraphQLError('Feedback id is required')
            }

            const result = await db.interview_feedback.findUnique({
                where: {
                    id: id,
                },
                select: {
                    id: true,
                    interviewer_feedback_result: true,
                    interviewer_feedback_state: true,
                    created_at: true,
                    user: {
                        select: {
                            name: true,
                        },
                    },
                    interviewer: {
                        select: {
                            name: true,
                        },
                    },
                    template: {
                        select: {
                            interviewer_feedback_meta: true,
                        },
                    },
                },
            })
            return result as InterviewerFeedbackResults
        } catch (error) {
            throw new GraphQLError(error)
        }
    }
const getAllFeedbacksResolver = async (_: any, args: any, ctx: Context) => {
    try {
        if (ctx.admin) {
            validateAdmin(ctx)
        } else {
            checkGraphQLAuth(ctx)
        }
        const { filter, search, org_id } = args
        const feedbacks = await db.interview_feedback.findMany({
            where: {
                ...filter,
                user: {
                    organization: {
                        id: org_id
                            ? org_id
                            : ctx.admin
                            ? ctx.admin?.active_org_id
                            : ctx.user?.organization_id || -1,
                    },
                    OR: [
                        {
                            name: {
                                contains: search ? search : '',
                                mode: 'insensitive',
                            },
                        },
                        { email: { contains: search ? search : '' } },
                        { phone: { contains: search ? search : '' } },
                    ],
                },
            },
            orderBy: [{ interviewer_feedback_state: 'asc' }, { id: 'desc' }],
            include: {
                interviewer: true,
                user: true,
                template: true,
                feedback_result: true,
                interview: {
                    select: {
                        id: true,
                        created_at: true,
                        note: true,
                    },
                },
            },
        })

        const sortedFeedbacks = feedbacks.sort((feedbackA, feedbackB) => {
            if (
                feedbackA.interviewer_feedback_state === 'COMPLETED' &&
                feedbackB.interviewer_feedback_state !== 'COMPLETED'
            ) {
                return -1
            }
            if (
                feedbackA.interviewer_feedback_state !== 'COMPLETED' &&
                feedbackB.interviewer_feedback_state === 'COMPLETED'
            ) {
                return 1
            }
            return feedbackB.id - feedbackA.id
        })

        return sortedFeedbacks
    } catch (error) {
        throw new GraphQLError(error)
    }
}
const getSingleFeedbackTemplate: QueryResolvers['getSingleFeedbackTemplate'] =
    async (_, args, ctx: Context) => {
        try {
            validateAdmin(ctx)

            const template = await db.feedback_template.findUnique({
                where: {
                    id: args.template_id,
                },
            })
            return template as any
        } catch (error) {
            throw new GraphQLError(error)
        }
    }
const feedbackResolver = {
    Query: {
        getFeedbackTemplates,
        getSingleFeedbackTemplate,
        feedbackSection: getFeedbackSectionsResolver,
        getInterviewerFeedbackFormData,
        getIntervieweeFeedbackFormData,
        getInterviewerFeedbackResults,
        getAllFeedbacks: getAllFeedbacksResolver,
        approvedCandidate,
    },
    Mutation: {
        createFeedbackTemplate,
        updateFeedbackTemplate,
        updateInterviewerFeedbackForm,
        updateIntervieweeFeedbackForm,
        createFeedbackSection: createFeedbackSectionResolver,
        updateFeedbackSection: updateFeedbackSectionResolver,
        createInterviewFeedback: createInterviewFeedbackResolver,
        rejectUserForTraining,
        updateInterviewResult,
        approveUserForTraining,
    },
}
export default feedbackResolver
