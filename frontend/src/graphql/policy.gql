mutation UpdatePolicyAcceptance($data: PolicyAcceptInput!) {
  updatePolicyAcceptance(data: $data) {
    result
    message
    data {
      id
      policy_name
      policy_name_hindi
      url
      is_active
    }
  }
}

query GetPoliciesWithUserStatus($userId: Int!) {
  getPoliciesWithUserStatus(user_id: $userId) {
    result
    message
    data {
      id
      policy_name
      policy_name_hindi
      url
      is_active
      created_at
      updated_at
      user_policy_tracking {
        created_at
        accepted
      }
    }
  }
}
