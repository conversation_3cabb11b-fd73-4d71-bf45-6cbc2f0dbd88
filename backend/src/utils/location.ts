import geoip from 'geoip-lite'

interface LocationInfo {
    city: string | null
    state: string | null
    country: string | null
    region: string | null
    timezone: string | null
    coordinates: {
        latitude: number | null
        longitude: number | null
    } | null
}

/**
 * Get location information from an IP address
 * @param ipAddress - The IP address to look up
 * @returns Location information including city, state, country, and coordinates
 */
export function getLocationFromIp(ipAddress: string): LocationInfo {
    try {
        const geo = geoip.lookup(ipAddress)

        if (!geo) {
            return {
                city: null,
                state: null,
                country: null,
                region: null,
                timezone: null,
                coordinates: null,
            }
        }

        return {
            city: geo.city || null,
            state: geo.region || null,
            country: geo.country || null,
            region: geo.region || null,
            timezone: geo.timezone || null,
            coordinates: {
                latitude: geo.ll?.[0] || null,
                longitude: geo.ll?.[1] || null,
            },
        }
    } catch (error) {
        console.error('Error getting location from IP:', error)
        return {
            city: null,
            state: null,
            country: null,
            region: null,
            timezone: null,
            coordinates: null,
        }
    }
}

/**
 * Get a formatted location string from an IP address
 * @param ipAddress - The IP address to look up
 * @returns A formatted string like "City, State, Country" or null if location cannot be determined
 */
export function getFormattedLocation(ipAddress: string): string | null {
    const location = getLocationFromIp(ipAddress)

    if (!location.city && !location.state && !location.country) {
        return null
    }

    const parts = [location.city, location.state, location.country].filter(
        Boolean
    )

    return parts.length > 0 ? parts.join(', ') : null
}

/**
 * Check if an IP address is from a specific country
 * @param ipAddress - The IP address to check
 * @param countryCode - The two-letter country code to check against (e.g., 'US', 'GB')
 * @returns boolean indicating if the IP is from the specified country
 */
export function isIpFromCountry(
    ipAddress: string,
    countryCode: string
): boolean {
    const location = getLocationFromIp(ipAddress)
    return location.country?.toUpperCase() === countryCode.toUpperCase()
}
