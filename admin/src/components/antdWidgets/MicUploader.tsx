import {
  AudioOutlined,
  DeleteOutlined,
  LoadingOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined
} from "@ant-design/icons";
import { Button, message, notification } from "antd";
import axios from "axios";
import React, { useEffect, useRef, useState } from "react";

interface MicUploaderProps {
  value?: string;
  onChange?: (url: string) => void;
}

const MicUploader: React.FC<MicUploaderProps> = ({ value, onChange }) => {
  const [recording, setRecording] = useState(false);
  const [audioUrl, setAudioUrl] = useState(value || "");
  const [uploadedFileName, setUploadedFileName] = useState("");
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>("");
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const isMobile = /Mobi|Android/i.test(navigator.userAgent);

  useEffect(() => {
    navigator.mediaDevices.enumerateDevices().then((devices) => {
      const audioInputs = devices.filter((device) => device.kind === "audioinput");
      setAudioDevices(audioInputs);
      if (audioInputs.length > 0) {
        setSelectedDeviceId(audioInputs[0].deviceId);
      }
    });
  }, []);

  const startRecording = () => {
    console.log("Start Recording clicked");
    navigator.mediaDevices
      .getUserMedia({ audio: { deviceId: selectedDeviceId } })
      .then((stream) => {
        console.log("Microphone access granted");
        const mediaRecorder = new MediaRecorder(stream);
        mediaRecorderRef.current = mediaRecorder;
        mediaRecorder.start();
        setRecording(true);
        console.log("Recording started");

        mediaRecorder.ondataavailable = (event) => {
          audioChunksRef.current.push(event.data);
        };

        mediaRecorder.onstop = async () => {
          console.log("Recording stopped");
          const audioBlob = new Blob(audioChunksRef.current, { type: "audio/wav" });
          const audioFile = new File([audioBlob], "recording.wav", { type: "audio/wav" });
          const url = await uploadFileToS3(audioFile);
          setAudioUrl(url);
          setUploadedFileName("recording.wav");
          onChange?.(url);
          message.success("Audio recorded and uploaded successfully");
          audioChunksRef.current = [];
        };
      })
      .catch((error) => {
        message.error("Microphone access denied or not available.");
        console.error("Microphone access error:", error);
        if (error.name === "NotAllowedError") {
          console.error("Permission to access microphone was denied.");
        } else if (error.name === "NotFoundError") {
          console.error("No microphone device found.");
        } else if (error.name === "NotReadableError") {
          console.error("Microphone is already in use.");
        } else {
          console.error("An unknown error occurred: ", error);
        }
      });
  };

  const stopRecording = () => {
    mediaRecorderRef.current?.stop();
    setRecording(false);
  };

  const uploadFileToS3 = async (file: File): Promise<string> => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`,
        { file_type: file.type },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("adminToken")}`
          }
        }
      );

      const { status, data: signedUrl, Key } = response.data;

      if (status) {
        await axios.put(signedUrl, file, {
          headers: {
            "Content-Type": file.type
          }
        });

        return import.meta.env.VITE_S3_URL + "/" + Key;
      } else {
        notification.error({
          message: "There was some issue during uploading the file. Please try again later."
        });
      }
    } catch (err) {
      notification.error({
        message: "There was some issue during uploading the file. Please try again later."
      });
    }
    return "";
  };

  const togglePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  return (
    <div
      className="mic-uploader"
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        margin: "auto",
        border: "1px dashed #ccc",
        backgroundColor: "#f9f9f9",
        borderRadius: "4px"
      }}
    >
      {!isMobile && !audioUrl && (
        <select
          onChange={(e) => setSelectedDeviceId(e.target.value)}
          value={selectedDeviceId}
          style={{
            marginBottom: "10px",
            width: "100%",
            padding: "8px",
            borderRadius: "4px",
            border: "1px solid #ccc"
          }}
        >
          {audioDevices.map((device) => (
            <option key={device.deviceId} value={device.deviceId}>
              {device.label || `Microphone ${device.deviceId}`}
            </option>
          ))}
        </select>
      )}
      {audioUrl ? (
        <div
          className="relative"
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            width: "100%",
            padding: "10px",
            border: "1px solid #ccc",
            borderRadius: "4px",
            backgroundColor: "#f9f9f9"
          }}
        >
          {isMobile ? (
            <Button
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={togglePlayPause}
              style={{ marginRight: "10px" }}
            />
          ) : (
            <audio
              controls
              src={audioUrl}
              ref={audioRef}
              style={{ marginRight: "10px", flexGrow: 1 }}
            />
          )}
          <span
            className="uploaded-file-name"
            style={{ marginRight: "10px", flexGrow: 1, textAlign: "center" }}
          >
            {uploadedFileName}
          </span>
          <Button
            icon={<DeleteOutlined className="text-red-500" />}
            size="small"
            onClick={() => {
              setAudioUrl("");
              onChange?.("");
            }}
          />
        </div>
      ) : (
        <Button
          type="primary"
          icon={recording ? <LoadingOutlined /> : <AudioOutlined />}
          onClick={recording ? stopRecording : startRecording}
          style={{ width: `${isMobile ? "100%" : "50%"}` }}
        >
          {recording ? "Stop Recording" : "Start Recording"}
        </Button>
      )}
    </div>
  );
};

export default MicUploader;
