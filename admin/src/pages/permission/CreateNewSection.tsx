import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { <PERSON>ton, Card, Form, Input, notification, Select, Switch } from "antd";
import React, { useEffect, useState } from "react";
import { decodeToken } from "react-jwt";
import { useCreateNewSectionMutation, useGetSubmodulesQuery } from "../../__generated__";

interface NewModuleData {
  module_id: number | null;
  icon_name: string;
  module_name: string;
  visible_in_sp: boolean;
  visible_in_users: boolean;
}

interface SubmoduleData {
  submodule_id: number;
  submodule_name: string;
  submodule_url_user?: string | null;
  submodule_url_sp?: string | null;
}

const CreateNewSection: React.FC = () => {
  const [selectedModule, setSelectedModule] = useState<NewModuleData>({
    module_id: null,
    icon_name: "",
    module_name: "",
    visible_in_users: false,
    visible_in_sp: false
  });
  const [submodules, setSubmodules] = useState<SubmoduleData[]>([]);
  const [selectedSubmodule, setSelectedSubmodule] = useState<number | null>(null);
  const [form] = Form.useForm();

  const [createNewSectionMutation] = useCreateNewSectionMutation();
  const moduleToken = localStorage.getItem("moduleToken");
  const moduleData:
    | {
        module_id: number;
        module_name: string;
        icon_name: string;
        visible_in_users: boolean;
        visible_in_sp: boolean;
        [key: string]: any;
      }[]
    | null = moduleToken ? decodeToken(moduleToken) : [];

  // Query for fetching submodules
  const { data: submoduleData, loading: submoduleLoading } = useGetSubmodulesQuery({
    variables: { moduleId: selectedModule.module_id || 0 },
    skip: !selectedModule.module_id
  });

  // Update submodules when data is fetched
  useEffect(() => {
    if (submoduleData?.getSubmodules) {
      // Map the GraphQL response to our interface type
      const mappedSubmodules: SubmoduleData[] = submoduleData.getSubmodules.map((submodule) => ({
        submodule_id: submodule.submodule_id,
        submodule_name: submodule.submodule_name,
        submodule_url_user: submodule.submodule_url_user || undefined,
        submodule_url_sp: submodule.submodule_url_sp || undefined
      }));
      setSubmodules(mappedSubmodules);
      // Reset form fields when submodules change
      form.resetFields(["protectedFeatures"]);
    }
  }, [submoduleData, form]);

  const handleSubmit = async (values: any) => {
    try {
      const hasSections = (values.protectedFeatures || []).length > 0;
      if (!hasSections) {
        notification.error({
          message: "At least one section required"
        });
      } else if (!selectedSubmodule) {
        notification.error({
          message: "Please select a submodule"
        });
      } else {
        const allHaveAtLeastOneUrl = values.protectedFeatures.every(
          (feature: any) => feature.section_url_user?.trim() || feature.section_url_sp?.trim()
        );
        if (!allHaveAtLeastOneUrl) {
          notification.error({
            message: "Each section must have at least one URL (Individual or SP)"
          });
          return;
        }

        if (selectedModule.module_id && selectedSubmodule) {
          console.log(values, "values");
          await createNewSectionMutation({
            variables: {
              moduleId: selectedModule.module_id,
              submoduleId: selectedSubmodule,
              sections: values?.protectedFeatures.map((feature: any) => ({
                section_id: feature.section_id,
                section_name: feature.section_name,
                section_url_user: feature.section_url_user,
                section_url_sp: feature.section_url_sp,
                sub_module_name: feature.sub_module_name
              }))
            },
            onCompleted: (data) => {
              if (data.createNewSection.result) {
                notification.success({
                  message: data.createNewSection.message || "Sections created successfully"
                });
                form.resetFields();
                setSelectedModule({
                  module_id: null,
                  icon_name: "",
                  module_name: "",
                  visible_in_users: false,
                  visible_in_sp: false
                });
                setSelectedSubmodule(null);
              } else {
                notification.error({
                  message: data.createNewSection.message || "Failed to create sections"
                });
              }
            },
            onError: (error) => {
              notification.error({
                message: `Error: ${error.message}`
              });
            }
          });
        }
      }
    } catch (err) {
      notification.error({
        message: `Error: ${err}`
      });
    }
  };

  return (
    <div className="py-6 px-4 flex flex-col items-center">
      <Card className="w-full max-w-3xl">
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-4">Module Details</h3>

          <Form form={form} layout="vertical" onFinish={handleSubmit}>
            <div className="flex gap-x-6 mb-7 pt-1">
              <div className="flex-1">
                <div className="flex items-center gap-x-2">
                  <Switch disabled value={selectedModule?.visible_in_users} />
                  <span>Visible in Individual</span>
                </div>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-x-2">
                  <Switch disabled defaultChecked value={selectedModule?.visible_in_sp} />
                  <span>Visible in SP</span>
                </div>
              </div>
            </div>

            <div className="flex gap-x-6 w-full justify-between">
              <Form.Item className="flex-1" name="name" label="Module" rules={[{ required: true }]}>
                <Select
                  placeholder="Choose a module"
                  onChange={(module) => {
                    const selectedModule = Object.values(moduleData || {})?.find(
                      (moduleData: any) => moduleData.module_name === module
                    );
                    if (selectedModule) {
                      setSelectedModule({
                        module_id: selectedModule.module_id,
                        module_name: selectedModule.module_name,
                        icon_name: selectedModule.icon_name,
                        visible_in_sp: selectedModule.visible_in_sp,
                        visible_in_users: selectedModule.visible_in_users
                      });
                      form.setFieldsValue({
                        icon_name: selectedModule.icon_name
                      });
                    }
                  }}
                >
                  {Object.values(moduleData || {})?.map((module: any) => (
                    <Select.Option key={module.module_id} value={module.module_name}>
                      <div className="capitalize">{module.module_name}</div>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                className="flex-1"
                name="icon_name"
                rules={[{ required: true }]}
                label={<div className="flex items-center gap-x-2">Icon Name</div>}
              >
                <Input disabled value={selectedModule?.icon_name} />
              </Form.Item>
            </div>

            {/* Submodule Selection - New addition */}
            <Form.Item
              name="submodule"
              label="Submodule"
              rules={[{ required: true, message: "Please select a submodule" }]}
            >
              <Select
                placeholder="Select a submodule"
                loading={submoduleLoading}
                disabled={!selectedModule.module_id}
                onChange={(value) => setSelectedSubmodule(value)}
              >
                {submodules.map((submodule) => (
                  <Select.Option key={submodule.submodule_id} value={submodule.submodule_id}>
                    {submodule.submodule_name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <Form.List name="protectedFeatures">
              {(fields, { add, remove }) => (
                <div className="mt-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">Section Details</h3>
                    <Button
                      type="dashed"
                      icon={<PlusOutlined />}
                      onClick={() => {
                        // Get the selected submodule
                        const selectedSubmoduleData = submodules.find(
                          (submodule) => submodule.submodule_id === selectedSubmodule
                        );

                        // Add new section with prefilled URLs from the submodule
                        console.log(selectedSubmoduleData, "selectedSubmoduleData");
                        add({
                          section_url_user: selectedSubmoduleData?.submodule_url_user || null,
                          section_url_sp: selectedSubmoduleData?.submodule_url_sp || null,
                          sub_module_name: selectedSubmoduleData?.submodule_name || "",
                          section_id: ""
                        });
                      }}
                    >
                      Add Section
                    </Button>
                  </div>

                  {fields.map(({ key, name, ...restField }) => (
                    <Card
                      key={key}
                      className="mb-4 p-4 border border-dashed border-gray-300 bg-white"
                      size="small"
                      title={`Section ${key + 1}`}
                      extra={
                        <MinusCircleOutlined
                          className="text-red-500 cursor-pointer"
                          onClick={() => remove(name)}
                        />
                      }
                    >
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Form.Item
                          {...restField}
                          name={[name, "section_id"]}
                          label="Section ID"
                          rules={[{ required: true, message: "Required" }]}
                        >
                          <Input placeholder="Enter section ID" />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, "section_name"]}
                          label="Section Name"
                          rules={[{ required: true, message: "Required" }]}
                        >
                          <Input className="capitalize" placeholder="Enter section name" />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, "sub_module_name"]}
                          label="Sub Module Name"
                          hidden
                        >
                          <Input />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, "section_url_user"]}
                          label="Section Url : Individual "
                        >
                          <Input disabled={true} placeholder="/dashboard/..." />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, "section_url_sp"]}
                          label="Section Url : Service Provider "
                        >
                          <Input disabled={true} placeholder="/dashboard/sp/..." />
                        </Form.Item>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </Form.List>

            <div className="text-center mt-6">
              <Button
                type="primary"
                htmlType="submit"
                className="bg-purple-500 hover:bg-purple-600 border-none"
              >
                Submit
              </Button>
            </div>
          </Form>
        </div>
      </Card>
    </div>
  );
};

export default CreateNewSection;
