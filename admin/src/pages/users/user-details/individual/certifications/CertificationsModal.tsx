import { LuAward, LuX } from "react-icons/lu";

interface CertificateModalProps {
  isOpen: boolean;
  onClose: () => void;
  certificate: {
    name: string;
    issueDate: string;
    credentialId: string;
    issuedBy: string;
  };
}

const CertificateModal = ({ isOpen, onClose, certificate }: CertificateModalProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[100] overflow-y-auto">
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 z-[9999] " onClick={onClose}></div>

        <div className="relative transform overflow-hidden rounded-lg bg-white shadow-xl transition-all sm:w-full sm:max-w-3xl">
          <div className="absolute right-0 top-0 pr-[6.5px] pt-[6px]">
            <LuX className="h-6 w-6  text-gray-400 hover:text-gray-500" onClick={onClose} />
          </div>

          {/* Certificate Content */}
          <div className="p-8">
            <div
              style={{ border: "4px solid", borderColor: "#7c3aed" }}
              className="border-4 border-indigo-600 p-8 rounded-lg z-10"
            >
              <div className="text-center">
                <div className="flex justify-center mb-6">
                  <LuAward className="h-16 w-16 text-[#7c3aed]" />
                </div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Certificate of Achievement
                </h1>
                <p className="text-lg text-gray-600 mb-8">This is to certify that</p>
                <p className="text-2xl font-semibold text-[#7c3aed] mb-8">Manikant sharma</p>
                <p className="text-lg text-gray-600 mb-8">
                  has successfully completed the certification for
                </p>
                <p className="text-2xl font-bold text-gray-900 mb-8">{certificate.name}</p>

                <div
                  style={{ borderTop: "2px solid", borderColor: "#E5E7EB" }}
                  className="border-t-2 border-gray-200 pt-6 mt-8"
                >
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Issue Date</p>
                      <p className="font-medium">{certificate.issueDate}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Credential ID</p>
                      <p className="font-medium">{certificate.credentialId}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Issued By</p>
                      <p className="font-medium">{certificate.issuedBy}</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8">
                  <img
                    src="https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=https://wify.com/verify/cert/{certificate.credentialId}"
                    alt="Certificate QR Code"
                    className="mx-auto h-24 w-24"
                  />
                  <p className="text-sm text-gray-500 mt-2">Scan to verify certificate</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default CertificateModal;
