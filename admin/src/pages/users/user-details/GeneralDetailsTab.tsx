import { useLocation } from "react-router-dom";
import SPGeneralDetails from "../../sp_dashboard/SPGeneralDetails";
import TechnicianGeneralDetails from "./individual/TechnicianGeneralDetails";

const GeneralDetailsTab: any = ({
  setActiveKey,
  isTransferred
}: {
  setActiveKey: any;
  isTransferred: boolean;
}) => {
  //Hooks
  const { pathname } = useLocation();

  return (
    <div className="mb-6 grid grid-cols-1 md:grid-cols-12  overflow-hidden">
      <div className="md:col-span-12  ">
        {pathname.includes("sp") ? (
          <SPGeneralDetails />
        ) : (
          <TechnicianGeneralDetails setActiveKey={setActiveKey} isTransferred={isTransferred} />
        )}
      </div>
    </div>
  );
};

export default GeneralDetailsTab;
