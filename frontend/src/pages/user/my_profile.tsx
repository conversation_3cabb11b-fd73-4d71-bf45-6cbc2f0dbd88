import { Capacitor } from "@capacitor/core";
import { Directory, Filesystem } from "@capacitor/filesystem";
import { Share } from "@capacitor/share";
import { EditIcon, EmailIcon, PhoneIcon } from "@chakra-ui/icons";
import { Avatar, Box, Button, Divider, Tag, useToast } from "@chakra-ui/react";
import React, { useContext, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { GrDocument } from "react-icons/gr";
import { useNavigate } from "react-router-dom";
import { useUserDetailsQuery } from "../../__generated__";
import DocumentsPreview from "../../components/DocumentsPreview";
import Error from "../../components/Error";
import PageTransition from "../../components/PageTransition";
import VisitingCard from "../../components/VisitingCard";
import MultiBanner from "../../components/banner";
import { PolicyCard } from "../../components/compliance/PolicyCard";
import { PolicyReminderManager } from "../../components/compliance/PolicyReminderManager";
import { TechnicianFeedbackCard } from "../../components/compliance/TechnicianFeedbackCard";
import ReferAndEarn from "../../components/referAndEarn/ReferAndEarn";
import SpTmsProfile from "../../components/serviceProvider/profile/SpTmsProfile";
import SpMyTeamMembers from "../../components/serviceProvider/team_member/SpMyTeamMembers";
import SpMyTeamMembersCount from "../../components/serviceProvider/team_member/SpMyTeamMembersCount";
import TechnicianInterviewDetailsUpdated from "../../components/technician/interview/TechnicianInterviewDetailsUpdated";
import { OneAppIntegrationContext } from "../../providers/OneAppIntegration";
import { getUserMetaLocalStorage } from "../../utils/auth";
import { createElementToImage } from "../../utils/common";

const MyProfile: React.FC = () => {
  const { data: userDetails, error: userDetailsError } = useUserDetailsQuery();

  const navigate = useNavigate();
  const { t } = useTranslation();
  const ttt_data = useContext(OneAppIntegrationContext);
  const [user_type, setUserType] = useState<string | null>(null);
  const [isServiceProviderOwner, setIsServiceProviderOwner] = useState<boolean>(false);
  const [userTransferredToTMS, setUserTransferredTOTms] = useState<boolean | undefined | null>(
    false
  );

  const user_data = userDetails?.userDetail?.data;

  const toast = useToast();
  const cardRef = React.createRef<HTMLDivElement>();

  const checkIfTheUserIsTransferredToTms = async () => {
    const transferred_to_tms = await getUserMetaLocalStorage("transferred_to_tms");
    setUserTransferredTOTms(transferred_to_tms === "true" ? true : false);
  };

  const download = (dataUrl: string, filename: string) => {
    const a = document.createElement("a");
    a.href = dataUrl;
    a.download = filename;
    a.click();
  };

  const shareVisitingCard = async () => {
    if (!cardRef.current) {
      console.error("Element not found.");
      return;
    }

    try {
      const dataUrl = await createElementToImage(cardRef.current);

      if (!dataUrl) {
        console.error("Error generating the image.");
        return;
      }

      if (Capacitor.getPlatform() === "android") {
        const file = await Filesystem.writeFile({
          path: "file.png",
          directory: Directory.Documents,
          data: dataUrl.replace("data:image/jpeg;base64,", "")
        });
        toast({
          title: t("visiting_card_downl"),
          description: t("visiting_card_downl_descp"),
          status: "success",
          duration: 1000,
          isClosable: true,
          position: "top",
          variant: "subtle"
        });
        await Share.share({
          title: t("visiting_card_title"),
          text: t("visiting_card_title_desc"),
          url: `file://${file.uri}`,
          dialogTitle: t("visiting_card_title_dialog")
        });
      } else {
        download(dataUrl, "my_visiting_card.png");
        toast({
          title: t("visiting_card_downl"),
          description: t("visiting_card_downl_descp"),
          status: "success",
          duration: 1000,
          isClosable: true,
          position: "top",
          variant: "subtle"
        });
      }
    } catch (error) {
      console.log(error);
      console.error("Error generating or downloading the image:", error);
    }
  };

  useEffect(() => {
    const fetchUserType = async () => {
      const user_type = await getUserMetaLocalStorage("user_type");
      setUserType(user_type);
    };
    fetchUserType();
  }, []);

  useEffect(() => {
    const is_service_provider_owner = user_data?.id === user_data?.organization?.org_owner_id;
    setIsServiceProviderOwner(is_service_provider_owner);
  }, [user_data]);
  useEffect(() => {
    // setIsAndroid(checkIfAndroid());
    checkIfTheUserIsTransferredToTms();
  }, []);
  if (userDetailsError) {
    return <Error error={userDetailsError} />;
  }

  let allExpertise = "";
  user_data?.expertise?.forEach((eachExpertise) => {
    if (eachExpertise) {
      allExpertise += ", " + eachExpertise.name;
    }
  });
  allExpertise = allExpertise.substring(2);

  return (
    <PageTransition>
      <Box className="mb-[230px]">
        <div
          className={`bg-white/45 py-4 z-50 w-[90%] mx-auto top-[50px] backdrop-blur-xl gap-4 flex justify-center items-center rounded-xl shadow-md
        `}
        >
          <div className="relative">
            <Avatar size={"xl"} name={user_data?.name || ""} src={user_data?.photoUrl || ""} />
            <div
              className="absolute bg-white p-1.5 w-8 h-8 flex justify-center items-center rounded-full -top-1 -right-1 shadow-md hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => {
                navigate("/edit_profile");
              }}
            >
              <EditIcon className="text-xs text-gray-600" />
            </div>
          </div>
          <div className="p-2">
            <div className="text-2xl font-semibold mb-2 text-gray-800">{user_data?.name}</div>
            <div className={`flex gap-2 flex-col items-start`}>
              <Tag size="sm" variant="subtle" colorScheme="blue">
                <PhoneIcon className="mr-1" /> {user_data?.phone}
              </Tag>
              <Tag size="sm" variant="subtle" colorScheme="blue">
                <EmailIcon className="mr-1" />
                {user_data?.email ?? user_data?.email}
              </Tag>
            </div>
          </div>
        </div>
        <Box p={5}>
          {!isServiceProviderOwner && <ReferAndEarn userId={user_data?.id || undefined} />}

          <Box backgroundColor={"#fff"} p={"6"} my={10} mb={5} rounded={"xl"} shadow="sm">
            <Box className="flex justify-between items-center mb-4">
              <Box fontWeight={"semibold"} fontSize={"xl"} color="gray.800">
                {t("profile_card")}
              </Box>
              <Button
                leftIcon={<GrDocument />}
                size={"sm"}
                colorScheme="gray"
                variant="outline"
                onClick={shareVisitingCard}
                className="hover:bg-gray-50"
              >
                {t("profile_download")}
              </Button>
            </Box>
            <Divider my={"4"} />
            <Box fontSize={"sm"} className="mb-6 text-gray-600">
              {t("profile_download_txt")}
            </Box>
            <VisitingCard
              cardRef={cardRef}
              name={user_data?.name || ""}
              photoUrl={user_data?.photoUrl || ""}
              expertise={allExpertise || ""}
            />
          </Box>
          {user_type === "SERVICE_PROVIDER" ? (
            isServiceProviderOwner ? (
              userTransferredToTMS && <PolicyCard />
            ) : (
              <></>
            )
          ) : (
            userTransferredToTMS && <PolicyCard />
          )}
          {user_type === "SERVICE_PROVIDER" ? (
            isServiceProviderOwner ? (
              userTransferredToTMS && <TechnicianFeedbackCard />
            ) : (
              <></>
            )
          ) : (
            userTransferredToTMS && <TechnicianFeedbackCard />
          )}

          {/* Technician  */}
          {ttt_data !== null && !isServiceProviderOwner && user_type !== "SERVICE_PROVIDER" && (
            <>
              <div className="pb-5">
                <MultiBanner />
              </div>
              <TechnicianInterviewDetailsUpdated />
              <DocumentsPreview />
            </>
          )}
          {/* Service Provider */}
          {isServiceProviderOwner && ttt_data !== null && (
            <div className="space-y-4">
              <div className="pb-5">
                <MultiBanner />
              </div>
              <div className="mt-2">
                <SpTmsProfile is_service_provider_owner={true} />
              </div>
              {isServiceProviderOwner && <SpMyTeamMembersCount />}
              {isServiceProviderOwner && <SpMyTeamMembers />}
            </div>
          )}
          {/* Service Provider Team Member */}
          {user_type == "SERVICE_PROVIDER" && !isServiceProviderOwner && (
            <>
              <div className="pb-5">
                <MultiBanner />
              </div>
              <SpTmsProfile is_service_provider_owner={false} />
            </>
          )}
        </Box>
      </Box>
      {user_type === "SERVICE_PROVIDER" ? (
        isServiceProviderOwner ? (
          userTransferredToTMS && <PolicyReminderManager />
        ) : (
          <></>
        )
      ) : (
        userTransferredToTMS && <PolicyReminderManager />
      )}
    </PageTransition>
  );
};

export default MyProfile;
