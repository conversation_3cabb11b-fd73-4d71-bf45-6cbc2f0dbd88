import { GoArrowRight } from "react-icons/go";
import { <PERSON><PERSON><PERSON> } from "react-icons/lu";

import { useNavigate, useParams } from "react-router-dom";
import {
  useGetProviderDetailsAdminQuery,
  useGetSingleUserDetailFrUserManagementQuery
} from "../../../../__generated__";
import ErrorComponent from "../../../../components/error/Error";
import Loading from "../../../../components/loading/Loading";
import UserTimeline from "../../../../components/user/UserTimeline";
import { toTitleCase } from "../../../../utils/utils";
import AssessmentSection from "./assessment/AssessmentSection";
import CertificationsSection from "./certifications/CertificationsSection";

const interviewRatings = [
  {
    id: 1,
    rating: 4.5,
    date: "2024-12-24",
    interviewer: "<PERSON>",
    comments:
      "Strong technical skills, good communication, shows initiative and problem-solving ability"
  },
  {
    id: 2,
    rating: 3.0,
    date: "2024-12-25",
    interviewer: "<PERSON>",
    comments:
      "Technical knowledge is adequate but needs improvement in customer interaction and professional communication"
  },
  {
    id: 3,
    rating: 3.5,
    date: "2024-12-26",
    interviewer: "Mike Wilson",
    comments:
      "Good practical knowledge but needs to work on time management and organization skills"
  }
];
const testResults = [
  {
    id: 1,
    skillName: "Carpentry",
    testName: "Basic Woodworking Skills",
    percentage: 85,
    completedAt: "2024-12-24"
  },
  {
    id: 2,
    skillName: "MKW",
    testName: "Modular Kitchen Installation",
    percentage: 65,
    completedAt: "2024-12-24"
  },
  {
    id: 3,
    skillName: "Electrical",
    testName: "Basic Electrical Safety",
    percentage: 78,
    completedAt: "2024-12-25"
  },
  {
    id: 4,
    skillName: "MKW",
    testName: "Advanced Wardrobe Assembly",
    percentage: 58,
    completedAt: "2024-12-26"
  },
  {
    id: 5,
    skillName: "Carpentry",
    testName: "Power Tools Operation",
    percentage: 72,
    completedAt: "2024-12-26"
  }
];

const OnboardingView = ({ setActiveKey }: { setActiveKey: any }) => {
  const navigate = useNavigate();
  const params = useParams();
  const { data, loading, error } = useGetSingleUserDetailFrUserManagementQuery({
    variables: {
      userId: Number(params?.user_id)
    }
  });
  const user_data = data?.getSingleUserDetailFrUserManagement;

  const {
    data: providerDetails,
    loading: providerLoading,
    error: providerError
  } = useGetProviderDetailsAdminQuery({
    variables: {
      userId: Number(params?.user_id)
    },
    onCompleted(data) {
      console.log(data);
    }
  });
  const sp_data = providerDetails?.getProviderDetailsAdmin;
  if (loading || providerLoading) {
    return <Loading tip="Loading..." />;
  }
  if (error || providerError) {
    return <ErrorComponent error={error?.message || providerError?.message} />;
  }

  return (
    <div className="flex w-[100%] gap-x-6 overflow-y-scroll ">
      {/* General details */}
      <div className="flex flex-col w-[60%] max-w-[60%]  gap-y-8 ">
        <div className="flex flex-col  w-full bg-white rounded-lg shadow">
          <div className="px-6 py-5 " style={{ borderBottom: "1px solid #e5e7eb" }}>
            <h2 className="text-xl font-semibold text-gray-900">General Details</h2>
          </div>
          <div className="p-6">
            <dl className="space-y-4">
              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">User Code</dt>
                <dd className="w-2/3 text-sm text-gray-900">{user_data?.id}</dd>
              </div>

              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">Owner Name</dt>
                <dd className="w-2/3 text-sm text-gray-900"> {user_data?.name}</dd>
              </div>
              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">Company Name</dt>
                <dd className="w-2/3 text-sm text-gray-900"> {user_data?.organization?.name}</dd>
              </div>

              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">Email</dt>
                <dd className="w-2/3 text-sm text-gray-900 break-all">{user_data?.email}</dd>
              </div>

              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">Location</dt>
                <dd className="w-2/3 text-sm text-gray-900">{user_data?.meta?.primary_city}</dd>
              </div>

              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">Phone</dt>
                <dd className="w-2/3 text-sm text-gray-900">{user_data?.phone}</dd>
              </div>

              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">Skills</dt>
                <dd className="w-2/3">
                  {user_data?.user_expertise_map?.length
                    ? user_data?.user_expertise_map?.map((skill) => (
                        <span className="inline-flex items-center px-3 py-1 mr-1 mb-1  max-w-[200px]  rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                          <div className="  whitespace-nowrap overflow-hidden  truncate">
                            {skill.expertise?.name}
                          </div>
                        </span>
                      ))
                    : ""}
                </dd>
              </div>

              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">Years of Experience</dt>
                <dd className="w-2/3 flex items-center">
                  {user_data?.user_professional_data &&
                    user_data?.user_professional_data[0]?.year_of_experience && (
                      <>
                        <LuClock className="h-5 w-5 text-indigo-600 mr-2" />
                        <span className="text-sm text-gray-900">
                          {user_data?.user_professional_data[0]?.year_of_experience || ""}
                        </span>
                      </>
                    )}
                </dd>
              </div>
              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">POC</dt>
                <>{console.log(user_data)}</>
                <dd className="w-2/3 text-sm text-gray-900">
                  {" "}
                  {sp_data?.org_owner?.user_onboarding_data?.[0]?.poc || ""}
                </dd>
              </div>
              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">Source</dt>
                <dd className="w-2/3 text-sm text-gray-900">
                  {sp_data?.org_owner?.user_onboarding_data?.[0]?.source || ""}
                </dd>
              </div>
              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">Service Type</dt>
                <dd className="w-2/3 text-sm text-gray-900">
                  {sp_data?.organization_service_type_map?.map(
                    (type, index, array) =>
                      type?.service_type?.name + (index === array.length - 1 ? "" : ", ")
                  ) || ""}
                </dd>
              </div>

              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">Documents Updated</dt>
                <dd className="w-2/3 text-sm text-gray-900">
                  {" "}
                  {sp_data?.org_owner?.doc_verification_state
                    ? toTitleCase(sp_data?.org_owner?.doc_verification_state)
                    : "Not available"}
                </dd>
              </div>

              <div
                className="flex items-center py-3 "
                style={{ borderBottom: "1px solid #e5e7eb" }}
              >
                <dt className="w-1/3 text-sm font-medium text-gray-500">User Profile</dt>
                <dd className="w-2/3 text-sm text-gray-900">
                  {toTitleCase(sp_data?.org_owner?.onboarding_stage || "Unavailable")}
                </dd>
              </div>
            </dl>

            <div
              className="mt-6 flex gap-x-2 items-center justify-end text-colorPrimary cursor-pointer"
              onClick={() => {
                setActiveKey("documents");
                navigate(`/dashboard/sp/details/${params.user_id}/documents`);
              }}
            >
              <span>View Documents</span>
              <GoArrowRight />
            </div>
          </div>
        </div>
        {/* Assessment Section */}
        <AssessmentSection
          setActiveKey={setActiveKey}
          org_id={sp_data?.id}
          averageRating={5}
          interviewRatings={interviewRatings}
          testResults={testResults}
          totalInterviews={interviewRatings.length}
        />
        {/* Certifications Section */}
        <CertificationsSection />
      </div>
      <div className=" w-full hidden">
        <UserTimeline user_id={Number(params.user_id)} />
      </div>
    </div>
  );
};

export default OnboardingView;
