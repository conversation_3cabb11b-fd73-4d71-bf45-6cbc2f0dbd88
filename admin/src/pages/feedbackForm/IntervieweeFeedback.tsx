import { <PERSON><PERSON>, <PERSON>, Col, Divider, Form, Layout, message, Row, Typography } from "antd";
import FormBuilder from "antd-form-builder";
import { useForm } from "antd/es/form/Form";
import React from "react";
import { useMediaQuery } from "react-responsive";
import { useParams } from "react-router-dom";
import {
  GetIntervieweeFeedbackFormDataDocument,
  useGetIntervieweeFeedbackFormDataQuery,
  useUpdateIntervieweeFeedbackFormMutation
} from "../../__generated__";
import Loading from "../../components/loading/Loading";
import { createAntdFormMeta } from "../../utils/formbuilder.helper";
import IntervieweeFeedbackSubmitted from "./IntervieweeFeedbackSubmitted";
const { Content } = Layout;
const { Title } = Typography;

// Define a type for the FormBuilder meta prop that matches antd-form-builder's expected shape
interface FieldType {
  key: string;
  label?: string;
  widget?: string | React.ComponentType<any>;
  widgetProps?: Record<string, any>;
  options?: Array<{ label: string; value: string | number }>;
  colSpan?: number;
  required?: boolean;
  [key: string]: any;
}

const IntervieweeFeedback: React.FC = () => {
  //hooks
  const [previewForm] = useForm();
  const params = useParams();
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  //Graphql Query
  const { data, loading, error } = useGetIntervieweeFeedbackFormDataQuery({
    variables: {
      templateFormId: params?.feedback_id || "-1"
    }
  });

  //Graphql Mutation
  const [updateInterviewerFeedbackForm, { loading: updateLoading }] =
    useUpdateIntervieweeFeedbackFormMutation();

  //constants
  const feedbackFormData = data?.getIntervieweeFeedbackFormData;
  const feedbackTemplate = feedbackFormData?.template?.interviewee_feedback_meta;

  // Generate preview data
  // Handle potential errors when template is invalid or missing
  let formMeta: FieldType[] = [];
  try {
    if (feedbackTemplate) {
      formMeta = createAntdFormMeta(feedbackTemplate) as FieldType[];
    }
  } catch (error) {
    console.error("Error parsing feedback template:", error);
  }

  const handleSubmit = (values: unknown) => {
    updateInterviewerFeedbackForm({
      variables: {
        data: {
          feedback_form_id: params?.feedback_id || "-1",
          interviewee_feedback_result: values
        }
      },
      refetchQueries: [GetIntervieweeFeedbackFormDataDocument],
      onError: () => {
        message.error("Something went wrong, Please Contact Admin");
      }
    });
  };

  if (loading) return <Loading tip="Loading Template..." />;
  if (error) return <Loading tip="Loading Template..." />;
  if (!(feedbackFormData?.interviewee_feedback_state === "PENDING")) {
    return <IntervieweeFeedbackSubmitted />;
  }

  return (
    <Layout className="min-h-screen bg-gray-50" style={{ userSelect: "none" }}>
      {/* Centered Form Content */}
      <Content className="pt-8 px-2 pb-12 overflow-y-auto h-full">
        <Row justify="center">
          <Col xs={24} sm={22} md={20} lg={18} xl={16}>
            <Card
              className="shadow-md"
              style={{ borderRadius: "8px" }}
              title={
                <div
                  style={{
                    textAlign: "center",
                    padding: "16px 0",
                    borderBottom: "1px solid #f0f0f0",
                    width: "100%"
                  }}
                >
                  <Title
                    level={3}
                    className="text-center"
                    style={{
                      wordBreak: "break-word",
                      padding: isMobile ? "0 16px" : "0",
                      fontSize: isMobile ? "20px" : "24px"
                    }}
                  >
                    {isMobile ? (
                      <>Please provide your feedback</>
                    ) : (
                      <>Please provide your feedback </>
                    )}
                  </Title>
                </div>
              }
            >
              <div>
                <Form
                  form={previewForm}
                  layout="vertical"
                  onFinish={handleSubmit}
                  requiredMark="optional"
                  size="large"
                  className="p-4"
                >
                  <FormBuilder form={previewForm} meta={formMeta} />

                  <Divider style={{ marginTop: 36 }} />

                  <Form.Item className="text-center" style={{ marginBottom: 0 }}>
                    <Button
                      loading={updateLoading}
                      type="primary"
                      htmlType="submit"
                      size="large"
                      style={{
                        minWidth: "150px",
                        height: "42px",
                        fontSize: "16px",
                        borderRadius: "8px",
                        transition: "background-color 0.3s ease"
                      }}
                    >
                      Submit Feedback
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </Card>
          </Col>
        </Row>
      </Content>
    </Layout>
  );
};

export default IntervieweeFeedback;
