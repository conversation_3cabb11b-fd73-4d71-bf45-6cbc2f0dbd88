import { Button, Modal, Space, Table, message } from "antd";
import Search from "antd/es/input/Search";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  AssignmentType,
  AssignmentUserType,
  GetAllAssignmentConfigDocument,
  useAssignQuestionToConfigMutation,
  useGetAllQuestionsQuery,
  useGetSingleConfigDetailsQuery
} from "../../__generated__";
import { debounce } from "../../utils/utils";
import ErrorComponent from "../error/Error";
import Loading from "../loading/Loading";

const SpSelectQuestionsFrAssessment: React.FC = () => {
  //router stuff
  const { assignment_config_id } = useParams();
  const nav = useNavigate();

  //state
  const [selectedQuestions, setSelectedQuestions] = React.useState<string[]>([]);
  const [search, setSearch] = useState("");

  //network stuff
  const { data, loading, error, refetch } = useGetAllQuestionsQuery({
    variables: {
      filter: {
        assign_to: AssignmentUserType.ServiceProvider
      }
    }
  });
  const {
    data: configData,
    loading: configLoading,
    error: configError
  } = useGetSingleConfigDetailsQuery({
    variables: {
      id: assignment_config_id || ""
    },
    onCompleted(data) {
      if (data.getSingleAssignmentConfig?.questions) {
        setSelectedQuestions(data.getSingleAssignmentConfig.questions.map((q) => q?.id || ""));
      }
    }
  });
  const [assignQuestionToConfig] = useAssignQuestionToConfigMutation();

  //functions
  function handleAssignQuestions(): void {
    if (selectedQuestions.length === 0) {
      message.warning("Please select questions");
      return;
    }
    assignQuestionToConfig({
      variables: {
        config_id: assignment_config_id || "",
        question_ids: selectedQuestions
      },
      onCompleted: (data) => {
        if (data.addQuestionsToAssignmentConfig.result) {
          message.success("Questions saved successfully");
          nav(-1);
        } else {
          message.error(data.addQuestionsToAssignmentConfig.message);
        }
      },
      refetchQueries: [GetAllAssignmentConfigDocument]
    });
  }

  useEffect(() => {
    debounce(
      refetch,
      1000
    )({
      search
    });
  }, [search]);

  if (error || configError) return <ErrorComponent error={error} />;
  if (configLoading) return <Loading tip="loading ..." />;

  return (
    <Modal
      closeIcon={false}
      onOk={() => handleAssignQuestions()}
      okText={"Save"}
      width={"80%"}
      open
      okButtonProps={{
        hidden:
          configData?.getSingleAssignmentConfig?.questions_assignment_type ===
          AssignmentType.Automatic
      }}
      onCancel={() => {
        nav(-1);
      }}
    >
      {configData?.getSingleAssignmentConfig?.questions_assignment_type ===
      AssignmentType.Automatic ? (
        <div>
          <div className="text-2xl">Automatic assignment</div>
          <div className="text-lg text-center h-[300px] flex justify-center items-center flex-col">
            <span>
              This assignment configuration is automatic. You can not add questions to this
              configuration.
            </span>
          </div>
        </div>
      ) : (
        <>
          <div className="text-2xl">
            Add questions to assignment configuration
            <br />
            <span className="text-sm text-green-500 font-bold">
              Config name - {configData?.getSingleAssignmentConfig?.name}
            </span>
          </div>
          <div className="flex items-center py-3 justify-between">
            <Space>
              <Search
                value={search}
                allowClear
                onChange={(e) => setSearch(e.target.value)}
                placeholder="Search questions"
              />
            </Space>
            <Button
              onClick={() => {
                nav("/dashboard/sp/assignment/questions/new");
              }}
              type="primary"
              hidden
            >
              Add New Questions
            </Button>
          </div>
          <div className="text-xl">
            Total questions selected - {selectedQuestions.length}/
            {configData?.getSingleAssignmentConfig?.no_of_questions}
          </div>
          <Table
            loading={loading}
            columns={[
              {
                title: "Question",
                dataIndex: "question",
                align: "center"
              },
              {
                title: "Difficulty",
                dataIndex: "created_at",
                align: "center"
              },
              {
                title: "Bucket",
                dataIndex: "bucketName",
                align: "center"
              }
            ]}
            rowSelection={{
              type: "checkbox",
              selectedRowKeys: selectedQuestions,
              getCheckboxProps: () => ({
                disabled: configData?.getSingleAssignmentConfig?.draft_mode === false
              }),
              onChange: (selectedRowKeys) => {
                if (
                  selectedRowKeys.length >
                  (configData?.getSingleAssignmentConfig?.no_of_questions || 0)
                ) {
                  message.warning("You can not select more than the number of questions");
                  return;
                }
                setSelectedQuestions(selectedRowKeys as string[]);
              }
            }}
            dataSource={data?.getAllQuestions.map((q) => ({
              id: q.id,
              question: q.question_text,
              created_at: q.difficulty,
              bucketName: q.bucket?.name
            }))}
            rowKey={"id"}
          />
        </>
      )}
    </Modal>
  );
};

export default SpSelectQuestionsFrAssessment;
