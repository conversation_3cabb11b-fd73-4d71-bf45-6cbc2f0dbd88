import {
  DeleteOutlined,
  DownloadOutlined,
  LoadingOutlined,
  UploadOutlined
} from "@ant-design/icons";
import { Button, message, notification, Upload, UploadProps } from "antd";
import { RcFile, UploadChangeParam, UploadFile } from "antd/es/upload";
import axios from "axios";
import React, { useEffect } from "react";
import { useMediaQuery } from "react-responsive";

const { Dragger } = Upload;

interface FileUploaderProps {
  value?: string;
  onChange?: (url: string) => void;
  disabled?: boolean;
}

const FileUploader: React.FC<FileUploaderProps> = ({ value, onChange, disabled = false }) => {
  const [fileLoading, setLoading] = React.useState(false);
  const [uploadedFileName, setUploadedFileName] = React.useState("");
  const fileUrl = value || "";
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  useEffect(() => {
    if (fileUrl) {
      let fileName = fileUrl.split("/").pop() || "uploaded-file";

      fileName = fileName.split("?")[0];

      try {
        fileName = decodeURIComponent(fileName);
      } catch (e) {
        // If decoding fails, use the original name
      }

      setUploadedFileName(fileName);
    } else {
      setUploadedFileName("");
    }
  }, [fileUrl]);

  const uploadFileToS3 = async (file: File): Promise<string> => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`,
        { file_type: file.type },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("adminToken")}`
          }
        }
      );

      const { status, data: signedUrl, Key } = response.data;

      if (status) {
        await axios.put(signedUrl, file, {
          headers: {
            "Content-Type": file.type
          }
        });

        return import.meta.env.VITE_S3_URL + "/" + Key;
      } else {
        notification.error({
          message: "There was some issue during uploading the file. Please try again later."
        });
      }
    } catch (err) {
      notification.error({
        message: "There was some issue during uploading the file. Please try again later."
      });
    }
    return "";
  };

  const handleChange: UploadProps["onChange"] = async (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === "uploading") {
      setLoading(true);
      return;
    }
    if (info.file.status === "done") {
      try {
        const url = await uploadFileToS3(info.file.originFileObj as File);
        onChange?.(url);
        setLoading(false);
        setUploadedFileName(info.file.name);
        message.success(`${info.file.name} file uploaded successfully`);
      } catch (error) {
        console.error("errr", error);
      }
    }
  };

  const beforeUpload = (file: RcFile) => {
    const isLt50M = file.size / 1024 / 1024 < 50;
    if (!isLt50M) message.error("File must be smaller than 50MB!");

    return isLt50M;
  };

  const handleDownload = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    if (fileUrl) {
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = uploadedFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const uploadButton = (
    <button
      style={{ border: 0, background: "none", cursor: "pointer" }}
      type="button"
      hidden={!!fileUrl}
    >
      {fileLoading ? <LoadingOutlined /> : <UploadOutlined />}
      <div style={{ marginTop: 8 }}>Click Here to Upload File</div>
    </button>
  );

  return (
    <Dragger
      onChange={handleChange}
      beforeUpload={beforeUpload}
      listType="text"
      className="file-uploader"
      disabled={disabled ? false : !!fileUrl}
      showUploadList={false}
      multiple={false}
      action={`${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`}
    >
      {fileUrl ? (
        <div className="relative">
          <span className="uploaded-file-name">{uploadedFileName}</span>
          {/* Download is hidden due to component not working on supply app */}
          <Button
            hidden={isMobile}
            className="absolute top-0 right-8"
            icon={<DownloadOutlined className="text-blue-500" />}
            size="small"
            onClick={handleDownload}
            disabled={false}
          />
          <Button
            className="absolute top-0 right-0"
            icon={<DeleteOutlined className="text-red-500" />}
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onChange?.("");
            }}
            disabled={disabled}
          />
        </div>
      ) : (
        uploadButton
      )}
    </Dragger>
  );
};

export default FileUploader;
