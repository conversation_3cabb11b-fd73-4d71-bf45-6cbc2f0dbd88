import { Capacitor } from "@capacitor/core";
import { Box, Button, Flex, Text } from "@chakra-ui/react";
import { HiExclamationTriangle } from "react-icons/hi2";
import Lottie from "react-lottie-player";
import NoInternetAntennaLottie from "../../src/animation/NoInternetAntenna.json";
import { useNetworkStatus } from "../hooks/useNetworkStatus";

export function NetworkStatus() {
  const { isOffline, isInitialized } = useNetworkStatus();
  const isNative = Capacitor.isNativePlatform();

  if (isNative) return null;
  if (!isOffline || !isInitialized) return null;

  return (
    <div className="fixed inset-0 z-[999] bg-white flex items-center justify-center overflow-hidden">
      {/* Background Antenna Animation */}
      <Lottie
        animationData={NoInternetAntennaLottie}
        loop
        play
        speed={0.5}
        style={{ width: "90%", height: "90%" }}
        className="absolute top-2 z-0 left-9"
      />

      {/* Content Overlay */}
      <Box className="absolute bottom-[8%]  z-10 text-center px-6 max-w-md">
        <Flex align={"center"} justify={"center"} mb={3}>
          <HiExclamationTriangle size={24} className="mr-2 text-yellow-400" />

          <Text className="text-2xl font-bold text-red-400 shadow-2xl">No Internet Connection</Text>
        </Flex>
        <Text className="text-gray-600 text-base leading-relaxed mb-3">
          Please check your connection and try again
        </Text>
        <Button
          bg={"yellow.100"}
          textColor={"black"}
          onClick={() => window.location.reload()}
          className="shadow-lg text-white font-medium px-2 py-1 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Try Again
        </Button>
      </Box>
    </div>
  );
}
