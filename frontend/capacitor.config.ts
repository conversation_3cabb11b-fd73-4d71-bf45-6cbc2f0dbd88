import { CapacitorConfig } from "@capacitor/cli";

const config: CapacitorConfig = {
  appId: "co.in.wify.wifytech",
  appName: "Wify Partners App",
  webDir: "dist",
  server: {
    // url: "http://192.168.0.103:5173/",
    // url: "https://uat-onboardingv2.wify.co.in/",
    // url: "https://work.wify.co.in/",
    url: "http://192.168.1.182:5173/",
    cleartext: true,
    androidScheme: "http"
  },
  bundledWebRuntime: false,
  plugins: {
    PushNotifications: {
      presentationOptions: ["badge", "sound", "alert"]
    },
    // GoogleAuth: {
    //   scopes: ["profile", "email"],
    //   serverClientId: "517953729864-0mhci5rdvunph7dnvrjn8o1hmd5t1f8u.apps.googleusercontent.com",
    //   forceCodeForRefreshToken: true
    // },
    SplashScreen: {
      launchShowDuration: 0
    }
  }
};

export default config;
