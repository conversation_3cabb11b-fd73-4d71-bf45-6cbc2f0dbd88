import { Button } from "antd";
import { useDrag } from "react-dnd";
import { GrDrag } from "react-icons/gr";

function DragButtonFrBanner({ banner_config_id }: { banner_config_id: string }) {
  // eslint-disable-next-line no-empty-pattern
  const [_, dragRef] = useDrag(() => ({
    type: "banner",
    item: {
      banner_config_id
    }
  }));

  return <Button ref={dragRef} style={{ border: "none" }} icon={<GrDrag />} />;
}

export default DragButtonFrBanner;
