import { Button } from "@chakra-ui/react";
import { Share2, X } from "lucide-react";
import { useTranslation } from "react-i18next";

interface Contact {
  id: string;
  name: string;
  phone: string;
}

interface ReferConfirmModalProps {
  contact: Contact | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export function ReferConfirmModal({ contact, isOpen, onClose, onConfirm }: ReferConfirmModalProps) {
  if (!isOpen || !contact) return null;
  const { t } = useTranslation();

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl w-full max-w-sm">
        <div className="p-4 flex justify-between items-start border-b">
          <h3 className="text-lg font-semibold">{t("referral_confirm_modal")}</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 p-1">
            <X size={20} />
          </button>
        </div>
        <div className="p-4">
          <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mx-auto mb-4">
            <Share2 className="w-8 h-8 text-blue-600" />
          </div>
          <p className="text-center text-gray-600 mb-2">{t("referral_confirm")}</p>
          <p className="text-center font-medium mb-6">{contact.name}</p>
          <div className="flex gap-3">
            <Button
              onClick={onClose}
              className="flex-1 py-2 px-4 text-gray-600 border border-gray-200 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
            >
              {t("cancel")}
            </Button>
            <Button
              onClick={onConfirm}
              colorScheme="blue.600"
              className="flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              {t("referral_send_invite_btn")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
