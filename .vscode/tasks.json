{"version": "2.0.0", "tasks": [{"label": "Start frontend", "type": "shell", "command": "yarn dev", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start backend", "type": "shell", "command": "yarn dev", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start Admin", "type": "shell", "command": "yarn dev", "options": {"cwd": "${workspaceFolder}/admin"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start all", "dependsOn": ["Start frontend", "Start backend", "Start Admin"], "group": {"kind": "build", "isDefault": true}}]}