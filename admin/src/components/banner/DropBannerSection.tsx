import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON>, notification } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { isEmpty } from "lodash";
import { useState } from "react";
import { useDrop } from "react-dnd";
import { FcExpired } from "react-icons/fc";
import { RiDeleteBin6Line } from "react-icons/ri";
import {
  BannerConfigsDocument,
  useBannerQuery,
  useCreateBannerMutation,
  useUpdateBannerMutation
} from "../../__generated__";
import Loading from "../loading/Loading";

function DropBannerSection({ section_id }: { section_id: number }) {
  const [expiry_time, setExpiryTime] = useState<Dayjs | null>(null);
  const [open_date_picker, setOpenDatePicker] = useState(false);
  //Graphql Query
  const { data, refetch } = useBannerQuery({
    variables: {
      filter: {
        order: section_id
      }
    },
    fetchPolicy: "no-cache"
  });

  //graphql mutation
  const [createBanner, { loading: createLoading }] = useCreateBannerMutation();
  const [updateBanner, { loading: updateLoading }] = useUpdateBannerMutation();

  // Handle drop event
  const [, dropRef] = useDrop({
    accept: "banner", // Specify the drag type that this drop target accepts
    drop: async ({ banner_config_id }: { banner_config_id: string }) => {
      if (!isEmpty(data?.banner)) {
        notification.error({ message: "Please delete previous banner before adding new one" });
        return;
      }
      await createBanner({
        variables: {
          data: {
            banner_config_id: banner_config_id,
            order: section_id
          }
        },
        onError(error) {
          notification.error({ message: error.message });
        },
        refetchQueries: [BannerConfigsDocument],
        onCompleted: () => {
          refetch();
        }
      });
    }
  });

  const handleExpiryBanner = async () => {
    if (!expiry_time) {
      return;
    }
    await updateBanner({
      variables: {
        data: {
          id: data?.banner?.id as string,
          expiry_time: expiry_time
        }
      },
      refetchQueries: [BannerConfigsDocument],
      onCompleted: () => {
        setOpenDatePicker(false);
        setExpiryTime(null);
        refetch();
      }
    });
  };

  const handleDeleteBanner = async () => {
    try {
      await updateBanner({
        variables: {
          data: {
            id: data?.banner?.id as string,
            is_deleted: true
          }
        },
        refetchQueries: [BannerConfigsDocument],
        onCompleted: () => {
          refetch();
        }
      });
    } catch (error) {
      console.log(error);
    }
  };

  if (createLoading) {
    return (
      <div className="relative h-32 rounded-lg cursor-pointer  border-purple-400 border border-solid">
        <div className="h-full w-full flex justify-center items-center ">
          <div className="flex flex-col items-center justify-center gap-2">
            <Loading tip="Loading..." />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={dropRef} className="relative h-32 rounded-lg cursor-pointer">
      {" "}
      {!isEmpty(data?.banner) ? (
        <div
          style={{
            backgroundImage: `url(${data?.banner?.banner_config?.hero_image})`
          }}
          className={`w-full shadow-md  h-32 rounded-lg bg-cover bg-no-repeat `}
        >
          <div className="flex p-3 items-center">
            <div className="flex flex-col justify-around">
              <div className="text-black font-semibold">
                {data?.banner?.banner_config?.description || " "}
                <div>
                  {!isEmpty(data?.banner?.banner_config?.cta_link) &&
                    !isEmpty(data?.banner?.banner_config?.cta_text) && (
                      <p>
                        <button className="text-xs shadow-md border mt-2 border-solid inline-block px-2 py-1 text-orange-500 border-orange-600 rounded-lg bg-white cursor-pointer">
                          {data?.banner.banner_config?.cta_text}
                        </button>
                      </p>
                    )}
                </div>
              </div>
            </div>
            <div className="flex items-center justify-center h-[100px]">
              {data?.banner.banner_config?.image && (
                <img
                  src={data?.banner?.banner_config?.image}
                  alt="hero"
                  className="rounded-lg w-[85px]"
                />
              )}
            </div>
          </div>
          <div
            className="absolute bottom-1 left-0 cursor-pointer"
            onClick={() => setOpenDatePicker(true)}
          >
            <Popover
              trigger="click"
              placement="bottom"
              onOpenChange={() => setOpenDatePicker(false)}
              open={open_date_picker}
              content={
                <div className="w-56 flex flex-col gap-4">
                  <DatePicker
                    value={expiry_time}
                    format="YYYY-MM-DD HH:mm:ss"
                    use12Hours
                    disabledDate={(day) => day.isBefore(dayjs())}
                    showTime={{ defaultValue: dayjs("00:00:00", "HH:mm:ss") }}
                    onChange={(value) => {
                      setExpiryTime(value);
                    }}
                  />
                  <Button
                    className="hover:bg-purple-500 hover:text-white"
                    type="primary"
                    onClick={() => {
                      handleExpiryBanner();
                    }}
                  >
                    Save
                  </Button>
                </div>
              }
            >
              <div className="text-xs font-semibold mt-2 ml-2 bg-white border border-solid border-slate-200 p-1 rounded-lg">
                {data?.banner.expiry_time ? (
                  <span className="flex items-center gap-1">
                    {dayjs(data?.banner.expiry_time).format("DD MMM YYYY hh:mm A")}
                    <FcExpired size={16} />
                  </span>
                ) : (
                  <span>Add End Date and Time</span>
                )}
              </div>
            </Popover>
          </div>

          <div className="absolute top-0 bg-white border  rounded-xl right-0 ">
            <Tooltip>
              <Popconfirm
                title="Are you sure you want to delete this slot?"
                placement="topRight"
                okButtonProps={{ loading: updateLoading }}
                onConfirm={handleDeleteBanner}
              >
                <RiDeleteBin6Line size={20} className="cursor-pointer" />
              </Popconfirm>
            </Tooltip>
          </div>
        </div>
      ) : (
        <div className="h-full w-ful border border-orange-500 border-dashed rounded-lg bg-gray-50">
          <div className="h-full w-full flex justify-center items-center">
            <div className="flex flex-col items-center justify-center gap-2">
              <Typography.Text className="text-1xl text-gray-400 ">
                Drop Here to Add Banner
              </Typography.Text>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default DropBannerSection;
