import { DeleteOutlined, LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  Divider,
  Form,
  Image,
  Input,
  Row,
  Select,
  Upload,
  message,
  notification
} from "antd";
import FormItem from "antd/es/form/FormItem";
import TextArea from "antd/es/input/TextArea";
import { RcFile, UploadChangeParam, UploadFile, UploadProps } from "antd/es/upload";
import axios from "axios";
import React, { useContext, useState } from "react";
import "react-quill/dist/quill.snow.css";
import { useNavigate } from "react-router-dom";
import {
  GetAllQuestionsDocument,
  QuestionDifficulty,
  useCreateQuestionMutation,
  useGetAllBucketsQuery
} from "../../../__generated__";
import { hasDuplicates } from "../../../utils/utils";
import Loading from "../../loading/Loading";
import { AddQuestionFormContext } from "./AddQuestionModal";

interface AddSingleQuestionProps {
  okConfirmLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

const AddSingleQuestion: React.FC<AddSingleQuestionProps> = ({ okConfirmLoading }) => {
  //Hooks

  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();

  //navigation

  const nav = useNavigate();

  //form
  const form = useContext(AddQuestionFormContext);

  //Query
  const { data: buckets, loading: bucketDataLoading } = useGetAllBucketsQuery();

  //Mutation
  const [createQuestion] = useCreateQuestionMutation();

  //Functions
  const onCreateQuestion = (values: any) => {
    okConfirmLoading(true); //Confirm Button Loading
    const { option_1, option_2, option_3, option_4, correct_option } = values;
    const optionarray = [option_1, option_2, option_3, option_4];
    if (hasDuplicates(...optionarray)) {
      message.error("Options must have unique values");
      okConfirmLoading(false); //Confirm Button Loading
      return;
    }
    const options = [
      {
        option_text: option_1,
        is_right_option: correct_option === option_1
      },
      {
        option_text: option_2,
        is_right_option: correct_option === option_2
      },
      {
        option_text: option_3,
        is_right_option: correct_option === option_3
      },
      {
        option_text: option_4,
        is_right_option: correct_option === option_4
      }
    ];

    createQuestion({
      variables: {
        data: {
          bucket_id: values.bucket,
          difficulty: values.difficulty,
          options: options,
          question_text: values.question,
          ...(imageUrl
            ? {
                images: [imageUrl] as any
              }
            : {})
        }
      },
      onCompleted() {
        message.success("Question created successfully");
        okConfirmLoading(false); //Confirm Button Loading

        nav(-1);
      },
      onError(err) {
        console.error(err);
        message.error(err?.message || "There was some issue while creating the question");
        okConfirmLoading(false); //Confirm Button Loading
      },
      refetchQueries: [GetAllQuestionsDocument]
    });
  };

  const uploadFileToS3 = async (file: File): Promise<string> => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`,
        {
          file_type: file.type
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("adminToken")}`
          }
        }
      );

      const { status, data: signedUrl, Key } = response.data;

      if (status) {
        await axios.put(signedUrl, file, {
          headers: {
            "Content-Type": file.type
          }
        });

        const url = import.meta.env.VITE_S3_URL + "/" + Key;
        return url;
      } else {
        console.error(response);
        notification.error({
          message: "There was some issue during uploading the file. Please try again later."
        });
      }
    } catch (err) {
      console.error(err);
      notification.error({
        message: "There was some issue during uploading the file. Please try again later."
      });
    }

    return "";
  };

  const handleChange: UploadProps["onChange"] = async (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === "uploading") {
      setLoading(true);
      return;
    }
    if (info.file.status === "done") {
      try {
        const url = await uploadFileToS3(info.file.originFileObj as File);
        setImageUrl(url);
        setLoading(false);
      } catch (error) {
        console.error("errr", error);
      }
    }
  };

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) {
      message.error("You can only upload JPG/PNG file!");
    }
    const isLt2M = file.size / 1024 / 1024 < 5;
    if (!isLt2M) {
      message.error("Image must smaller than 5MB!");
    }
    return isJpgOrPng && isLt2M;
  };

  const uploadButton = (
    <button
      style={{ border: 0, background: "none" }}
      type="button"
      hidden={imageUrl ? true : false}
    >
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Image </div>
    </button>
  );

  if (bucketDataLoading) {
    return <Loading tip="Loading ..." />;
  }

  return (
    <Form onFinish={onCreateQuestion} form={form}>
      <div className="flex justify-start items-end gap-2 w-full">
        <div className="w-11/12 ">
          <FormItem
            rules={[
              {
                required: true,
                message: "Question is required"
              }
            ]}
            name={"question"}
          >
            <TextArea placeholder="Question" />
          </FormItem>
        </div>
        <div className="w-1/12">
          <FormItem name={"image"}>
            <Upload
              listType="picture-card"
              onChange={handleChange}
              className="avatar-uploader"
              showUploadList={false}
              multiple={false}
              beforeUpload={beforeUpload}
              disabled={imageUrl ? true : false}
              action={`${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`}
            >
              {imageUrl ? (
                <>
                  <Image src={imageUrl} alt="no image" width={"100%"} />
                  <Button
                    icon={
                      <DeleteOutlined
                        onClick={() => {
                          setImageUrl("");
                        }}
                      />
                    }
                  />
                </>
              ) : (
                uploadButton
              )}
            </Upload>
          </FormItem>
        </div>
      </div>

      <Row gutter={10}>
        <Col span={12}>
          <FormItem
            rules={[
              {
                required: true,
                message: "Option this required"
              }
            ]}
            name={"option_1"}
            label={"Option 1"}
          >
            <Input />
          </FormItem>
        </Col>
        <Col span={12}>
          <FormItem
            rules={[
              {
                required: true,
                message: "Option this required"
              }
            ]}
            name={"option_2"}
            label={"Option 2"}
          >
            <Input />
          </FormItem>
        </Col>
      </Row>
      <Row gutter={10}>
        <Col span={12}>
          <FormItem
            rules={[
              {
                required: true,
                message: "Option this required"
              }
            ]}
            name={"option_3"}
            label={"Option 3"}
          >
            <Input />
          </FormItem>
        </Col>
        <Col span={12}>
          <FormItem
            rules={[
              {
                required: true,
                message: "Option this required"
              }
            ]}
            name={"option_4"}
            label={"Option 4"}
          >
            <Input />
          </FormItem>
        </Col>
      </Row>
      <Divider />
      <Row gutter={10}>
        <Col span={12}>
          <FormItem
            rules={[
              { required: true, message: "This cannot be empty" },
              ({ getFieldValue }) => ({
                validator: (_, value) => {
                  const options = [
                    getFieldValue("option_1"),
                    getFieldValue("option_2"),
                    getFieldValue("option_3"),
                    getFieldValue("option_4")
                  ];
                  if (options.includes(value) || value === "") {
                    return Promise.resolve();
                  } else {
                    return Promise.reject("Correct answer should be one of the options");
                  }
                }
              })
            ]}
            name={"correct_option"}
            label={"Correct Answer"}
          >
            <Input />
          </FormItem>
        </Col>
        <Col span={12}>
          <FormItem
            name={"bucket"}
            label={"Bucket"}
            rules={[{ required: true, message: "Please select a bucket" }]}
          >
            <Select
              options={buckets?.getAllBuckets?.data?.map((b) => ({
                value: b?.id,
                label: b?.name
              }))}
            />
          </FormItem>
        </Col>
      </Row>
      <Row gutter={10}>
        <Col span={12}>
          <FormItem required name={"difficulty"} label={"Difficulty"}>
            <Select
              options={[
                {
                  value: QuestionDifficulty.Hard,
                  label: "Hard"
                },
                {
                  value: QuestionDifficulty.Medium,
                  label: "Medium"
                },
                {
                  value: QuestionDifficulty.Easy,
                  label: "Easy"
                }
              ]}
            />
          </FormItem>
        </Col>
      </Row>
    </Form>
  );
};

export default AddSingleQuestion;
