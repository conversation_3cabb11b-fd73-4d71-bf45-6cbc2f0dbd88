import { Contacts } from "@capacitor-community/contacts";
import { Capacitor } from "@capacitor/core";
import { ArrowRightIcon } from "@chakra-ui/icons";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Text,
  useToast,
  VStack
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import mixpanel from "mixpanel-browser";
import React, { useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { PiChalkboardTeacher } from "react-icons/pi";
import { useNavigate } from "react-router-dom";
import {
  Gender,
  ReferrerInfo,
  useCheckUnregisteredReferralQuery,
  useCreateContactsMutation,
  UserDetailsDocument,
  useReferralCodeValidateMutation,
  useUpdateUserMutation,
  useUserDetailsQuery
} from "../../../__generated__";
import Error from "../../../components/Error";
import { useAnalytics } from "../../../hooks/useAnalytics";
import { updateUserMetaLocalStorage } from "../../../utils/auth";
import { chunkArray } from "../../../utils/common";
import { createNavigationHandlers } from "../../../utils/route.utils";
import ReferredUsersPopup from "../../user/referred_users_popup";

interface ProfileProps {
  setProfileTab: (tab: number) => void;
  profileTab: number;
}

interface ProfileSetupForm {
  name: string;
  phone: string;
  gender: Gender;
  email: string;
  referral_code: string;
}

const Profile: React.FC<ProfileProps> = ({ setProfileTab, profileTab }) => {
  const { data: userDetails, error: userDetailsError } = useUserDetailsQuery({
    fetchPolicy: "no-cache"
  });

  const [_, setGender] = useState<Gender>(Gender.Male);
  const [activeGenderIndex, setActiveGenderIndex] = useState<Gender>(Gender.Male);
  const originalFormValues = useRef<ProfileSetupForm | null>(null);

  const {
    handleSubmit,
    formState: { errors },
    control,
    setValue,
    getValues
  } = useForm<ProfileSetupForm>({
    defaultValues: {
      referral_code: "",
      name: "",
      phone: "",
      email: ""
    }
  });

  const [updateUser, { loading }] = useUpdateUserMutation();
  const [createContacts] = useCreateContactsMutation();

  const { t } = useTranslation();

  const toast = useToast();

  const userData = userDetails?.userDetail?.data;
  const navigate = useNavigate();
  const { navigateToPrevious, getPreviousRoute } = createNavigationHandlers({
    navigate
  });
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const [verifyReferral] = useReferralCodeValidateMutation();
  const { trackUserAction } = useAnalytics();

  const { data: referralCheck } = useCheckUnregisteredReferralQuery({
    variables: { phone_number: userData?.phone || "" },
    skip: !userData?.phone
  });

  //setting the deafult value of the form
  useEffect(() => {
    if (userData) {
      userData?.name && setValue("name", userData?.name);
      userData?.email && setValue("email", userData?.email);
      userData?.phone && setValue("phone", userData?.phone);
      const ref_code = localStorage.getItem("referral_code") || "";
      setValue("referral_code", ref_code);

      // Store original values
      originalFormValues.current = {
        name: userData?.name || "",
        email: userData?.email || "",
        phone: userData?.phone || "",
        gender: userData?.gender || Gender.Male,
        referral_code: ref_code
      };
    }
  }, [userData?.name, userData?.email, userData?.phone]);

  useEffect(() => {
    userData?.phone && setValue("phone", userData?.phone);

    if (userData?.gender) {
      setValue("gender", userData?.gender);
      setActiveGenderIndex(userData?.gender);
      setGender(userData?.gender);
    }
  }, [userDetails?.userDetail?.data?.gender]);

  const hasFormChanged = (): boolean => {
    if (!originalFormValues.current) return true;

    const current = getValues();
    return (
      current.name !== originalFormValues.current.name ||
      current.email !== originalFormValues.current.email ||
      current.phone !== originalFormValues.current.phone ||
      current.gender !== originalFormValues.current.gender ||
      current.referral_code !== originalFormValues.current.referral_code
    );
  };

  const handleUpdateUserData = async (data: ProfileSetupForm) => {
    const { name, phone, gender = Gender.Male, email, referral_code } = data;

    // Skip API call if no changes were made
    if (!hasFormChanged()) {
      setProfileTab(profileTab + 1);
      return;
    }

    const ip_address = await fetch("https://api.ipify.org?format=json")
      .then((res) => res.json())
      .then((res) => res.ip);
    const updateUserVariables = {
      data: {
        name,
        phone,
        gender,
        email,
        meta: {
          last_profile_update: new Date().toISOString(),
          browser: navigator.userAgent,
          ip_address,
          profile_update_stage: "address",
          is_user_journey_completed: false
        }
      }
    };

    const response = referral_code
      ? await verifyReferral({
          variables: {
            data: {
              referral_code,
              id: userData?.id as number
            }
          }
        })
      : { data: { referralCodeValidate: { validated: true } } };

    if (response?.data?.referralCodeValidate?.validated) {
      await updateUser({
        variables: updateUserVariables,
        onError(error) {
          toast({
            title: t("error"),
            description: error.message,
            status: "error",
            duration: 4000,
            isClosable: true,
            position: "top",
            variant: "subtle"
          });
        },
        async onCompleted() {
          await syncContact();
          setProfileTab(profileTab + 1);

          // Track user activity
          await trackUserAction("PROFILE_UPDATE", {
            name,
            phone,
            gender,
            email,
            referral_code,
            profile_update_stage: "address"
          });

          //handle mixpanel
          mixpanel.track("profile_setup", {
            Gender: gender,
            "Mobile Number": phone,
            Name: name,
            "Refer Code": referral_code || "No Referral Submitted",
            "Login Status": "Success",
            "User Type": "New"
          });
        },
        refetchQueries: [UserDetailsDocument]
      });
    } else {
      toast({
        title:
          response?.data?.referralCodeValidate?.message === "Referrer code does not exist"
            ? t("referror_err")
            : response?.data?.referralCodeValidate?.message,
        status: "error",
        duration: 4000,
        isClosable: true,
        position: "top",
        variant: "subtle"
      });
    }
  };

  const syncContact = async () => {
    try {
      if (Capacitor.getPlatform() !== "android") return null;
      const hasPermission = await Contacts.checkPermissions();
      if (hasPermission.contacts !== "granted") return;
      const contacts = await Contacts.getContacts({
        projection: {
          phones: true,
          emails: true,
          name: true
        }
      });

      // Batch size for sending contacts
      const batchSize = 500;
      const contactChunks = chunkArray(contacts.contacts, batchSize);

      for (const chunk of contactChunks) {
        await createContacts({
          variables: {
            contacts: JSON.stringify(chunk)
          }
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  if (userDetailsError) {
    return <Error error={userDetailsError} />;
  }

  return (
    <Box className="bg-white p-3 rounded-md">
      {window.location.pathname.includes("/technician/registration/profile/edit") ? (
        <Box>
          <Box py={3} className="p-2 flex flex-col items-center bg-white rounded-md mt-3 ">
            <Box
              display={"flex"}
              padding={3}
              gap={3}
              bg={"gray.100"}
              width={"full"}
              rounded={"md"}
              alignItems={"start"}
            >
              <PiChalkboardTeacher className="text-[20px]" />
              <Box className="relative w-full">
                <Box display={"flex"} flexDirection={"column"}>
                  <Box className="flex items-center justify-between">
                    <Box fontWeight={"bold"}>{t("contact_admin")}</Box>
                  </Box>
                  <Text mt={2} fontSize={12}>
                    {t("connect_admin")} <br />
                    <Box
                      className="text-colorPrimaryDark font-semibold"
                      onClick={() => {
                        navigate("/contact");
                      }}
                    >
                      <u>{t("contact")}</u>
                    </Box>
                  </Text>
                </Box>
              </Box>
            </Box>
          </Box>
          <Box className="fixed md:relative md:pt-10 flex justify-center md:justify-end bottom-0 left-0 right-0 p-3 bg-gray-50">
            <Button
              isLoading={loading}
              loadingText={t("saving")}
              onClick={() => {
                setProfileTab(profileTab + 1);
              }}
              rightIcon={<ArrowRightIcon />}
              size={"md"}
              className="w-full"
              colorScheme="blue"
            >
              {t("go_to_address")}
            </Button>
          </Box>
        </Box>
      ) : (
        <Box>
          <Box className="mb-2" fontSize={"lg"}>
            {t("general_inforamtion")}
          </Box>
          <Divider />
          <form onSubmit={handleSubmit(handleUpdateUserData)}>
            <VStack className="mt-2 grid grid-cols-2">
              <FormControl className="my-2" isInvalid={!!errors?.phone}>
                <FormLabel>{t("phone_number")}</FormLabel>
                <Controller
                  control={control}
                  name="phone"
                  rules={{
                    required: true,
                    pattern: /^[0-9]{10}$/
                  }}
                  defaultValue={userData?.phone || ""}
                  render={({ field }) => (
                    <>
                      <Input disabled={userData?.phone ? true : false} type="text" {...field} />
                      {errors?.phone && (
                        <FormErrorMessage>{errors?.phone?.message as string}</FormErrorMessage>
                      )}
                      {errors.phone?.type === "required" && (
                        <FormErrorMessage>{t("error_phone")}</FormErrorMessage>
                      )}
                      {errors.phone?.type === "pattern" && (
                        <FormErrorMessage>{t("error_invalid_number")}</FormErrorMessage>
                      )}
                    </>
                  )}
                />
              </FormControl>
              <FormControl className="my-2" isInvalid={!!errors.name}>
                <FormLabel>{t("full_name")}</FormLabel>
                <Controller
                  name="name"
                  rules={{
                    required: true
                  }}
                  defaultValue={userData?.name || ""}
                  control={control}
                  render={({ field }) => (
                    <>
                      <Input {...field} />
                      {errors?.name && (
                        <FormErrorMessage>{errors?.name?.message as string}</FormErrorMessage>
                      )}
                      {errors.name?.type === "required" && (
                        <FormErrorMessage>{t("error_name")}</FormErrorMessage>
                      )}
                    </>
                  )}
                />
              </FormControl>
              {/* If the User email Already exist in the system it will give error I am not doing this now because of timeline  */}
              <FormControl className="my-2" isInvalid={!!errors.email}>
                {/* <FormLabel>{t("email")}</FormLabel> Multi Lingual Pending  */}
                <FormLabel>
                  {t("email")} ({t("optional")})
                </FormLabel>

                <Controller
                  name="email"
                  rules={{
                    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/
                  }}
                  control={control}
                  render={({ field }) => (
                    <>
                      <Input type="email" {...field} disabled={userData?.email ? true : false} />
                      {errors?.email && (
                        <FormErrorMessage>{errors?.email?.message as string}</FormErrorMessage>
                      )}
                      {errors.email?.type === "pattern" && (
                        <FormErrorMessage>{t("error_email")}</FormErrorMessage>
                      )}
                    </>
                  )}
                />
              </FormControl>
              <FormControl className="my-2">
                <FormLabel>
                  {t("referral_code")} ({t("optional")})
                </FormLabel>
                <Controller
                  name="referral_code"
                  control={control}
                  render={({ field }) => (
                    <>
                      <Input
                        type="text"
                        {...field}
                        disabled={localStorage.getItem("referral_code") ? true : false}
                      />
                    </>
                  )}
                />
                {referralCheck?.checkUnregisteredReferral?.is_referred && (
                  <Flex gap={1} wrap="wrap">
                    <Text
                      fontSize="sm"
                      color="blue.500"
                      cursor="pointer"
                      onClick={() =>
                        referralCheck?.checkUnregisteredReferral?.is_referred &&
                        setIsPopupOpen(true)
                      }
                    >
                      {t("referral_onboarding_prompt_before")}
                      <Box as="span" ml={1} textDecoration="underline">
                        {t("referral_onboarding_prompt_clickable")}
                      </Box>{" "}
                      {t("referral_onboarding_prompt_after")}
                    </Text>
                  </Flex>
                )}
              </FormControl>
              <FormControl className="">
                <FormLabel>{t("gender")}</FormLabel>
                <Breadcrumb separator="">
                  <div className="flex w-full ">
                    {[Gender.Male, Gender.Female, Gender.NotSpecified].map((status) => (
                      <motion.div
                        initial={"rest"}
                        whileTap={"clicked"}
                        animate={"rest"}
                        variants={{
                          rest: { scale: 1 },
                          clicked: { scale: 0.95 }
                        }}
                        key={status}
                        className={`  border rounded gap-2 w-1/3 flex justify-center items-center p-2 *:
                        ${activeGenderIndex === status ? "border-blue-400 bg-blue-50 " : ""}
                        `}
                        onClick={() => {
                          setGender(status);
                          setValue("gender", status);
                          setActiveGenderIndex(status);
                        }}
                      >
                        <BreadcrumbItem>
                          <BreadcrumbLink style={{ textDecoration: "none" }}>
                            <div
                              className={`font-semibold ${activeGenderIndex === status ? "" : "text-gray-400"} w-full `}
                            >
                              {status === Gender.Male
                                ? t("male")
                                : status === Gender.Female
                                  ? t("female")
                                  : t("others")}
                            </div>
                          </BreadcrumbLink>
                        </BreadcrumbItem>
                      </motion.div>
                    ))}
                  </div>
                </Breadcrumb>
              </FormControl>
            </VStack>
          </form>
          <Box className="fixed z-50 md:relative md:pt-10 flex justify-center md:justify-end bottom-0 left-0 right-0 p-3 bg-gray-50">
            <div className="w-full flex gap-3">
              <motion.div
                whileTap="clicked"
                initial="rest"
                animate="rest"
                variants={{
                  rest: { scale: 1 },
                  clicked: { scale: 0.95 }
                }}
                className="w-full"
              >
                <Button
                  onClick={() => {
                    navigateToPrevious();
                    updateUserMetaLocalStorage({
                      key: "last_location",
                      value: getPreviousRoute()?.path || ""
                    });
                  }}
                  size={"md"}
                  className="w-full"
                  variant="outline"
                  colorScheme="blue"
                >
                  {t("back")}
                </Button>
              </motion.div>

              <motion.div
                whileTap="clicked"
                initial="rest"
                animate="rest"
                variants={{
                  rest: { scale: 1 },
                  clicked: { scale: 0.95 }
                }}
                className="w-full"
              >
                <Button
                  isLoading={loading}
                  loadingText={t("saving")}
                  onClick={() => {
                    handleSubmit(handleUpdateUserData)();
                  }}
                  rightIcon={<ArrowRightIcon />}
                  size={"md"}
                  className="w-full"
                  colorScheme="blue"
                >
                  {t("save_and_continue")}
                </Button>
              </motion.div>
            </div>
          </Box>
        </Box>
      )}
      {referralCheck?.checkUnregisteredReferral?.is_referred && (
        <ReferredUsersPopup
          isOpen={isPopupOpen}
          onClose={() => setIsPopupOpen(false)}
          onSubmit={(couponCode) => {
            setValue("referral_code", couponCode);
          }}
          referredUsers={
            referralCheck.checkUnregisteredReferral.referrers?.map((r: ReferrerInfo) => ({
              id: r.id || 0,
              name: r.name || "Unknown"
            })) || []
          }
          couponCode={referralCheck.checkUnregisteredReferral.referrers?.[0]?.referral_code || ""}
          benefitText="You and your friend will earn points."
        />
      )}
    </Box>
  );
};
export default Profile;
