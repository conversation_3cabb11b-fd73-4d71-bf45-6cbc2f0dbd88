import dayjs from "dayjs";
import { useEffect } from "react";
import { BiRupee } from "react-icons/bi";
import { GoArrowRight } from "react-icons/go";
import { <PERSON><PERSON><PERSON>, LuGraduationCap } from "react-icons/lu";
import { useParams } from "react-router-dom";
import { useGetWorkExpAdminLazyQuery, UserDetailsQueryQuery } from "../../../../../__generated__";
import ErrorComponent from "../../../../../components/error/Error";
import Loading from "../../../../../components/loading/Loading";

const GeneralDetails = ({
  setActiveKey,
  data
}: {
  setActiveKey: any;
  data: UserDetailsQueryQuery["userDetailsForAdmin"];
}) => {
  const { user_id } = useParams();
  const userOnboardingData = data?.user_onboarding_data?.[0] || {};
  const maritalStatus = userOnboardingData?.martial_status?.toLowerCase();
  console.log(data, maritalStatus, "data");
  const [fetchWorkExp, { data: workExpData, loading, error }] = useGetWorkExpAdminLazyQuery();

  useEffect(() => {
    if (user_id) {
      fetchWorkExp({
        variables: {
          userId: parseInt(user_id)
        }
      });
    }
  }, [user_id]);
  if (error) {
    return <ErrorComponent error={error} />;
  }

  if (loading) {
    return <Loading tip="Loading ..." />;
  }
  return (
    <div className="p-6">
      <dl className="space-y-4">
        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">User ID</dt>
          <dd className="w-2/3 text-sm text-gray-900">{data?.id}</dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Name</dt>
          <dd className="w-2/3 text-sm text-gray-900 capitalize">{data?.name || ""}</dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Email</dt>
          <dd className="w-2/3 text-sm text-gray-900 break-all">{data?.email || ""}</dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Location</dt>
          <dd className="w-2/3 text-sm text-gray-900">{data?.location?.city || ""}</dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Phone</dt>
          <dd className="w-2/3 text-sm text-gray-900">{data?.phone || ""}</dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Skills</dt>
          <dd className="w-2/3">
            {data?.expertise?.length
              ? data?.expertise?.map((skill) => (
                  <span className="inline-flex items-center px-3 py-1 mr-1 mb-1  max-w-[200px]  rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                    <div className="  whitespace-nowrap overflow-hidden  truncate">
                      {skill?.name}
                    </div>
                  </span>
                ))
              : ""}
          </dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Years of Experience</dt>
          <dd className="w-2/3 flex items-center">
            {workExpData?.getWorkExpAdmin?.user_professional_data?.year_of_experience && (
              <LuClock className="h-5 w-5 text-indigo-600 mr-2" />
            )}
            <span className="text-sm text-gray-900">
              {workExpData?.getWorkExpAdmin?.user_professional_data?.year_of_experience || ""}
            </span>
          </dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Highest Education</dt>
          {workExpData?.getWorkExpAdmin?.user_professional_data?.highest_education ? (
            <dd className="w-2/3 flex items-center">
              <LuGraduationCap className="h-5 w-5 text-indigo-600 mr-2" />
              <span className="text-sm text-gray-900">
                {workExpData?.getWorkExpAdmin?.user_professional_data?.highest_education || ""}
              </span>
            </dd>
          ) : (
            <></>
          )}
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Salary Offered</dt>
          <dd className="w-2/3  text-gray-900 flex items-center">
            {data?.user_onboarding_data && data.user_onboarding_data[0]?.salary_offered != null && (
              <BiRupee className="h-4 w-4 text-gray-900 mr-1" />
            )}

            {data?.user_onboarding_data && data?.user_onboarding_data[0]?.salary_offered
              ? data?.user_onboarding_data[0]?.salary_offered
              : ""}
          </dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Salary (VP)</dt>
          <dd className="w-2/3 text-sm text-gray-900  flex items-center">
            {data?.user_onboarding_data && data.user_onboarding_data[0]?.salary_vp != null && (
              <BiRupee className="h-4 w-4 text-gray-900 mr-1" />
            )}

            {data?.user_onboarding_data && data?.user_onboarding_data[0]?.salary_vp
              ? data?.user_onboarding_data[0]?.salary_vp
              : ""}
          </dd>
        </div>
        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Date of Joining</dt>
          <dd className="w-2/3 text-sm text-gray-900">
            {data?.user_onboarding_data && data?.user_onboarding_data[0]?.date_of_joining
              ? dayjs(data?.user_onboarding_data[0]?.date_of_joining).format("DD-MM-YYYY")
              : ""}
          </dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Mother's Name</dt>
          <dd className="w-2/3 text-sm text-gray-900">
            {data?.user_onboarding_data && data?.user_onboarding_data[0]?.mothers_name
              ? data?.user_onboarding_data[0]?.mothers_name
              : ""}
          </dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Father's Name</dt>
          <dd className="w-2/3 text-sm text-gray-900">
            {data?.user_onboarding_data && data?.user_onboarding_data[0]?.fathers_name
              ? data?.user_onboarding_data[0]?.fathers_name
              : ""}
          </dd>
        </div>

        <div className="flex items-center py-3 " style={{ borderBottom: "1px solid #e5e7eb" }}>
          <dt className="w-1/3 text-sm font-medium text-gray-500">Marital Status</dt>
          <dd className="w-2/3 text-sm text-gray-900">
            {data?.user_onboarding_data && data?.user_onboarding_data[0]?.martial_status
              ? data?.user_onboarding_data[0]?.martial_status
              : ""}
          </dd>
        </div>
      </dl>

      <div
        className="mt-6 flex gap-x-2 items-center justify-end text-colorPrimary cursor-pointer"
        onClick={() => setActiveKey("work_experience")}
      >
        <span>View Work Experience</span>
        <GoArrowRight />
      </div>
    </div>
  );
};

export default GeneralDetails;
