type User_Policy_Tracking {
    created_at: Date
    accepted: Boolean
}

type Policy {
    id: String!
    policy_name: String!
    policy_name_hindi: String
    url: String
    is_active: Boolean!
    created_at: Date!
    updated_at: Date!
    user_policy_tracking: [User_Policy_Tracking]
}

type PolicyResponse {
    result: Boolean!
    message: String!
    data: [Policy]
}

type PolicyUpdateResponse {
    result: Boolean!
    message: String!
    data: Policy
}

input PolicyAcceptInput {
    policy_id: String!
}

input CreatePolicyInput {
    policy_name: String!
    policy_name_hindi: String
    url: String
    is_active: Boolean
}

input UpdatePolicyInput {
    id: String!
    policy_name: String
    policy_name_hindi: String
    url: String
    is_active: Boolean
}

type UserPolicyStatus {
    user_id: Int!
    user_name: String
    accepted: Boolean!
    accepted_on: Date
}

type PolicyWithUserStatus {
    id: String!
    policy_name: String!
    policy_name_hindi: String
    url: String
    is_active: Boolean!
    created_at: Date!
    updated_at: Date!
    user_status: [UserPolicyStatus]
}

type PolicyWithUserStatusResponse {
    result: Boolean!
    message: String!
    data: [PolicyWithUserStatus]
}

type PolicyData {
    users: [UserPolicyTrackingData]
    totalCount: Int
    totalOnboardedUsers: Int
    usersWithAllPoliciesAccepted: Int
}

type PolicyTrackingResponse {
    result: Boolean!
    message: String!
    data: PolicyData
}

type UserOnboardingData {
    meta: JSON
}

type UserPolicyTrackingData {
    id: Int!
    name: String!
    onboarded_date: Date!
    policies_accepted: Int!
    total_policies: Int!
    last_accepted_date: Date
    all_accepted: Boolean!
    user_onboarding_data: [UserOnboardingData]
}

input PolicyTrackingFilter {
    startDate: String
    endDate: String
    allAccepted: Boolean
}

type Query {
    # Get all policies with user acceptance status for the authenticated user
    getPoliciesWithUserStatus(user_id: Int!): PolicyResponse!

    # Get all policies for frontend user list (admin view)
    getAllPolicies: PolicyResponse!

    # Admin: Get policy by ID
    getPolicyById(id: String!): PolicyUpdateResponse!

    # Admin: Get policy with user acceptance status
    getPolicyWithUserStatus(policy_id: String!): PolicyWithUserStatusResponse!
    getUserPolicyTracking(
        userType: String
        search: String
        pagination: Pagination
        filter: PolicyTrackingFilter
    ): PolicyTrackingResponse!
    getExportUserPolicyTracking(
        userType: String
        search: String
        filter: PolicyTrackingFilter
    ): PolicyTrackingResponse!
    getSpPolicyTracking(
        userType: String
        search: String
        pagination: Pagination
        filter: PolicyTrackingFilter
    ): PolicyTrackingResponse!
    getExportSpPolicyTracking(
        userType: String
        search: String
        filter: PolicyTrackingFilter
    ): ExportPolicyTrackingResponse!
}

type ExportPolicyTrackingResponse {
    result: Boolean!
    message: String!
    sheet_id: String
}

input ExportUserPolicyTrackingInput {
    data: [UserPolicyData]
}
input UserPolicyData {
    id: Int
    name: String
    user_code: String
    onboarded_date: String
    policies_accepted: Int
    total_policies: Int
    last_accepted_date: String
    all_accepted: Boolean
}

type Mutation {
    # Update policy acceptance status when user accepts reading the policy
    updatePolicyAcceptance(data: PolicyAcceptInput!): PolicyUpdateResponse!

    # Export user policy tracking data to Excel
    exportUserPolicyTrackingData(
        data: ExportUserPolicyTrackingInput
    ): ExportPolicyTrackingResponse!
}
