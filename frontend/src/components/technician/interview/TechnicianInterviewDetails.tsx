// This component is a work in progress and will be connected to APIs in the future
import {
  Box,
  Button,
  Container,
  Divider,
  Flex,
  Heading,
  HStack,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Tag,
  Text,
  useColorModeValue,
  VStack
} from "@chakra-ui/react";
import { Check, ChevronUp, MapPin, MessageSquare, Phone } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { InterviewType, useGetInterviewsFrUserQuery } from "../../../__generated__";

const TechnicianInterviewDetails = () => {
  const { t } = useTranslation();

  //GraphQl Query
  const { data, loading, error, refetch } = useGetInterviewsFrUserQuery();

  const userInterview = data?.getInterviewsFrUser?.[0];
  // const navigate = useNavigate();

  // State variables for feedback status and modals
  const [showAllInterviews, setShowAllInterviews] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [showPastFeedbackModal, setShowPastFeedbackModal] = useState(false);
  // State to track which past interview is selected for feedback
  const [selectedPastInterviewId, setSelectedPastInterviewId] = useState<number | null>(null);

  // Function to generate feedback URL with the dynamic feedback_form_id
  const getFeedbackUrl = (feedbackFormId?: string | null) => {
    if (!feedbackFormId) return "";
    return `${import.meta.env.VITE_ADMIN_LINK || ""}/feedbacks/technician/submit/${feedbackFormId}`;
  };

  // Get the feedback URL for the current interview
  const currentFeedbackUrl = getFeedbackUrl(userInterview?.feedback?.feedback_form_id);

  // Determine if feedback has been submitted based on the interview data
  const feedbackSubmitted = userInterview?.feedback?.interviewee_feedback_state === "COMPLETED";

  // Format date and time from the interview data
  const formatDateTime = (startTime?: string | null, endTime?: string | null) => {
    if (!startTime || !endTime) return "";

    const start = new Date(startTime);
    const end = new Date(endTime);

    const options: Intl.DateTimeFormatOptions = {
      weekday: "short",
      month: "short",
      day: "numeric"
    };
    const dateStr = start.toLocaleDateString("en-US", options);

    const startTimeStr = start.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true
    });
    const endTimeStr = end.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true
    });

    return `${dateStr}, ${startTimeStr} - ${endTimeStr}`;
  };

  // Get address and scheduled time from the interview data
  const address = userInterview?.interviewer?.location?.work_address || "Address not available";
  const scheduledTime = formatDateTime(userInterview?.start_time, userInterview?.end_time);

  // const handleBackClick = () => {
  //   navigate(-1);
  // };

  // Colors
  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.100", "gray.700");
  const headingColor = useColorModeValue("gray.900", "white");
  const textColor = useColorModeValue("gray.700", "gray.300");
  const labelBgColor = useColorModeValue("gray.50", "gray.700");
  const labelColor = useColorModeValue("gray.500", "gray.400");
  const buttonBorderColor = useColorModeValue("gray.200", "gray.600");
  const buttonHoverBgColor = useColorModeValue("gray.50", "gray.700");
  const blueBorderColor = useColorModeValue("blue.200", "blue.700");
  const blueTextColor = useColorModeValue("blue.700", "blue.300");
  const blueHoverBgColor = useColorModeValue("blue.50", "blue.900");
  const greenBorderColor = useColorModeValue("green.200", "green.700");
  const greenTextColor = useColorModeValue("green.600", "green.300");

  // Handle loading and error states
  if (loading) {
    return (
      <Box minH="100vh" bg={bgColor} display="flex" justifyContent="center" alignItems="center">
        <Text>Loading interview details...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Box minH="100vh" bg={bgColor} display="flex" justifyContent="center" alignItems="center">
        <Text color="red.500">Error loading interview details. Please try again later.</Text>
      </Box>
    );
  }

  // If no interviews are found
  if (!userInterview) {
    return (
      <Box minH="100vh" bg={bgColor} display="flex" justifyContent="center" alignItems="center">
        <Text>No upcoming interviews found.</Text>
      </Box>
    );
  }

  return (
    <>
      <Box minH="100vh" bg={bgColor}>
        {/* Header */}
        <Box
          position="sticky"
          top={0}
          bg={bgColor}
          borderBottom="1px"
          borderColor={borderColor}
          zIndex={10}
        >
          {/* <Container maxW="md" px={3} py={3}>
            <Flex align="center" gap={2}>
              <IconButton
                aria-label="Go back"
                icon={<ArrowLeft size={20} />}
                onClick={handleBackClick}
                variant="ghost"
                borderRadius="full"
                color={textColor}
                _hover={{ bg: buttonHoverBgColor }}
              />
              <Text fontSize="lg" fontWeight="semibold" color={headingColor}>
                Interviews
              </Text>
            </Flex>
          </Container> */}
        </Box>

        {/* Main Content */}
        <Container maxW="md" px={3} py={4}>
          <VStack spacing={4} align="stretch">
            {/* Current Interview Card */}
            <Box border="1px" borderColor={blueBorderColor} borderRadius="2xl" p={5}>
              <Flex align="flex-start" justify="space-between" mb={4}>
                <Heading
                  as="h2"
                  fontSize="xl"
                  fontWeight="semibold"
                  lineHeight="tight"
                  color={headingColor}
                >
                  {userInterview?.feedback?.template?.title || "WIFY Office"}
                </Heading>
                <div>
                  <Tag
                    px={2.5}
                    py={1}
                    border="1px"
                    borderColor={blueBorderColor}
                    color={blueTextColor}
                    borderRadius="full"
                    fontSize="xs"
                    fontWeight="medium"
                    bg="transparent"
                    bgColor={userInterview.status === "CANCELLED" ? "red.200" : "none"}
                  >
                    {userInterview.status === "SCHEDULED"
                      ? "Upcoming"
                      : userInterview.status === "COMPLETED"
                        ? "Completed"
                        : userInterview.status === "CANCELLED"
                          ? "Cancelled"
                          : userInterview.status}
                  </Tag>
                </div>
              </Flex>

              <VStack spacing={4} align="stretch">
                <Box>
                  <Tag
                    mb={1}
                    bg={labelBgColor}
                    color={labelColor}
                    fontSize="xs"
                    fontWeight="medium"
                    textTransform="uppercase"
                    letterSpacing="wider"
                    borderRadius="lg"
                    px={3}
                    py={1}
                  >
                    {t("address")}
                  </Tag>
                  <Text color={textColor} fontSize="sm" lineHeight="relaxed">
                    {address}
                  </Text>
                </Box>

                <Box>
                  <Tag
                    mb={1}
                    bg={labelBgColor}
                    color={labelColor}
                    fontSize="xs"
                    fontWeight="medium"
                    textTransform="uppercase"
                    letterSpacing="wider"
                    borderRadius="lg"
                    px={3}
                    py={1}
                  >
                    {t("scheduled_at")}
                  </Tag>
                  <Text color={textColor} fontSize="sm">
                    {scheduledTime}
                  </Text>
                </Box>
              </VStack>

              {/* Action Buttons */}
              <HStack spacing={3} mt={5}>
                <Button
                  flex={1}
                  variant="outline"
                  borderColor={buttonBorderColor}
                  color={textColor}
                  py={2.5}
                  px={4}
                  borderRadius="xl"
                  fontSize="sm"
                  leftIcon={<MapPin size={16} />}
                  _hover={{ bg: buttonHoverBgColor }}
                  as="a"
                  href={`https://maps.google.com/?q=${encodeURIComponent(address)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  View Map
                </Button>
                {userInterview.interview_type === InterviewType.InterviewOnline &&
                userInterview.google_meet_link ? (
                  <Button
                    flex={1}
                    variant="outline"
                    borderColor={blueBorderColor}
                    color={blueTextColor}
                    py={2.5}
                    px={4}
                    borderRadius="xl"
                    fontSize="sm"
                    fontWeight="medium"
                    leftIcon={<MessageSquare size={16} />}
                    _hover={{ bg: blueHoverBgColor }}
                    as="a"
                    href={`https://meet.google.com/${userInterview.google_meet_link}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Join Meeting
                  </Button>
                ) : (
                  <Button
                    flex={1}
                    variant="outline"
                    borderColor={blueBorderColor}
                    color={blueTextColor}
                    py={2.5}
                    px={4}
                    borderRadius="xl"
                    fontSize="sm"
                    fontWeight="medium"
                    leftIcon={<Phone size={16} />}
                    _hover={{ bg: blueHoverBgColor }}
                    as="a"
                    href={`tel:+91 7021901010`}
                  >
                    Call HR
                  </Button>
                )}
              </HStack>

              <Box hidden={!userInterview.feedback?.template?.interviewee_feedback_enabled}>
                {/* Feedback Button */}
                {feedbackSubmitted ? (
                  <Button
                    w="full"
                    mt={4}
                    py={2.5}
                    variant="outline"
                    borderColor={greenBorderColor}
                    borderRadius="xl"
                    fontSize="sm"
                    color={greenTextColor}
                    cursor="default"
                    leftIcon={<Check size={16} />}
                  >
                    {t("feedback_submitted")}
                  </Button>
                ) : (
                  <Button
                    w="full"
                    mt={4}
                    py={2.5}
                    variant="outline"
                    borderColor={buttonBorderColor}
                    borderRadius="xl"
                    fontSize="sm"
                    color={textColor}
                    leftIcon={<MessageSquare size={16} />}
                    _hover={{ bg: buttonHoverBgColor }}
                    onClick={() => setShowFeedbackModal(true)}
                    disabled={
                      userInterview.status === "CANCELLED" || userInterview.status !== "COMPLETED"
                    }
                  >
                    {t("share_your_feedback")}
                  </Button>
                )}
              </Box>
            </Box>

            {/* Past Interviews Section */}
            <Box border="1px" borderColor={buttonBorderColor} borderRadius="2xl" overflow="hidden">
              <Button
                w="full"
                px={5}
                py={4}
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                textAlign="left"
                variant="ghost"
                _hover={{ bg: buttonHoverBgColor }}
                onClick={() => setShowAllInterviews(!showAllInterviews)}
              >
                <Text fontSize="base" fontWeight="medium" color={headingColor}>
                  {t("past_interviews")}
                </Text>
                <Box
                  as={ChevronUp}
                  transform={showAllInterviews ? "rotate(0deg)" : "rotate(180deg)"}
                  transition="transform 0.2s"
                  color={labelColor}
                  boxSize={4}
                />
              </Button>

              {/* Past Interviews */}
              {showAllInterviews && (
                <Box px={5} pb={5} borderTop="1px" borderColor={borderColor}>
                  {data?.getInterviewsFrUser && data.getInterviewsFrUser.length > 1 ? (
                    // Map through past interviews (excluding the first one which is shown above)
                    data.getInterviewsFrUser.slice(1).map((interview, index) => (
                      <Box pt={4} key={interview?.id || index}>
                        <VStack spacing={4} align="stretch">
                          <Flex justify="space-between" align="center">
                            <Text fontSize="base" fontWeight="medium" color={headingColor}>
                              {interview?.feedback?.template?.title || "WIFY Office"}
                            </Text>
                            <Tag
                              px={2.5}
                              py={1}
                              border="1px"
                              borderColor={
                                interview.status === "CANCELLED" ? "red.200" : blueBorderColor
                              }
                              color={blueTextColor}
                              borderRadius="full"
                              fontSize="xs"
                              fontWeight="medium"
                              bg="transparent"
                              textTransform="capitalize"
                              bgColor={interview.status === "CANCELLED" ? "red.200" : "none"}
                            >
                              {interview.status === "SCHEDULED"
                                ? "Upcoming"
                                : interview?.status === "CANCELLED"
                                  ? "Cancelled"
                                  : interview?.status === "COMPLETED"
                                    ? "Completed"
                                    : interview?.status}
                            </Tag>
                          </Flex>

                          <VStack spacing={4} align="stretch">
                            <Box>
                              <Tag
                                mb={1}
                                bg={labelBgColor}
                                color={labelColor}
                                fontSize="xs"
                                fontWeight="medium"
                                textTransform="uppercase"
                                letterSpacing="wider"
                                borderRadius="lg"
                                px={3}
                                py={1}
                              >
                                {t("address")}
                              </Tag>
                              <Text color={textColor} fontSize="sm" lineHeight="relaxed">
                                {interview?.interviewer?.location?.work_address ||
                                  "Address not available"}
                              </Text>
                            </Box>

                            <Box>
                              <Tag
                                mb={1}
                                bg={labelBgColor}
                                color={labelColor}
                                fontSize="xs"
                                fontWeight="medium"
                                textTransform="uppercase"
                                letterSpacing="wider"
                                borderRadius="lg"
                                px={3}
                                py={1}
                              >
                                {t("scheduled_at")}
                              </Tag>
                              <Text color={textColor} fontSize="sm">
                                {formatDateTime(interview?.start_time, interview?.end_time)}
                              </Text>
                            </Box>

                            {/* Past Interview Feedback Button */}
                            <Box
                              hidden={!interview?.feedback?.template?.interviewee_feedback_enabled}
                            >
                              {interview?.feedback?.interviewee_feedback_state === "COMPLETED" ? (
                                <Button
                                  w="full"
                                  py={2.5}
                                  variant="outline"
                                  borderColor={greenBorderColor}
                                  borderRadius="xl"
                                  fontSize="sm"
                                  color={greenTextColor}
                                  cursor="default"
                                  leftIcon={<Check size={16} />}
                                >
                                  {t("feedback_submitted")}
                                </Button>
                              ) : (
                                <Button
                                  w="full"
                                  py={2.5}
                                  variant="outline"
                                  borderColor={buttonBorderColor}
                                  borderRadius="xl"
                                  fontSize="sm"
                                  color={textColor}
                                  leftIcon={<MessageSquare size={16} />}
                                  _hover={{ bg: buttonHoverBgColor }}
                                  onClick={() => {
                                    setSelectedPastInterviewId(interview?.id || 0);
                                    setShowPastFeedbackModal(true);
                                  }}
                                >
                                  {t("share_your_feedback")}
                                </Button>
                              )}
                            </Box>
                          </VStack>
                        </VStack>
                        <Divider className="my-4" />
                      </Box>
                    ))
                  ) : (
                    <Box pt={4}>
                      <Text color={textColor} fontSize="sm">
                        {t("no_past_interviews_found")}
                      </Text>
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          </VStack>
        </Container>
      </Box>

      {/* Feedback Modal for Web */}
      <Modal
        isOpen={showFeedbackModal}
        onClose={() => {
          refetch();
          setShowFeedbackModal(false);
        }}
        isCentered
        size="xl"
      >
        <ModalOverlay />
        <ModalContent maxH="90vh">
          <ModalCloseButton />
          <ModalBody pt={2} pb={6} pl={2} pr={2}>
            <iframe
              src={currentFeedbackUrl}
              style={{ width: "100%", height: "calc(90vh - 120px)", border: "none" }}
              title="Feedback Form"
              allow="camera; microphone"
            />
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Past Feedback Modal for Web */}
      <Modal
        isOpen={showPastFeedbackModal}
        onClose={() => {
          refetch();
          setShowPastFeedbackModal(false);
        }}
        isCentered
        size="xl"
      >
        <ModalOverlay />
        <ModalContent maxH="90vh">
          <ModalCloseButton />
          <ModalBody pt={2} pb={6} pl={2} pr={2}>
            <iframe
              src={getFeedbackUrl(
                data?.getInterviewsFrUser?.find(
                  (interview) => interview?.id === selectedPastInterviewId
                )?.feedback?.feedback_form_id
              )}
              style={{ width: "100%", height: "calc(90vh - 120px)", border: "none" }}
              title="Past Interview Feedback Form"
              allow="camera; microphone"
            />
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default TechnicianInterviewDetails;
