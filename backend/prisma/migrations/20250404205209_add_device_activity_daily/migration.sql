-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';

-- CreateTable
CREATE TABLE "device_activity_daily" (
    "id" TEXT NOT NULL,
    "device_id" TEXT NOT NULL,
    "app_installation_id" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "user_id" INTEGER,
    "platform_type" "AppPlatformType" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "device_activity_daily_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "device_activity_daily_date_idx" ON "device_activity_daily"("date");

-- CreateIndex
CREATE INDEX "device_activity_daily_device_id_idx" ON "device_activity_daily"("device_id");

-- CreateIndex
CREATE INDEX "device_activity_daily_user_id_idx" ON "device_activity_daily"("user_id");

-- CreateIndex
CREATE INDEX "device_activity_daily_platform_type_idx" ON "device_activity_daily"("platform_type");

-- CreateIndex
CREATE UNIQUE INDEX "device_activity_daily_device_id_date_key" ON "device_activity_daily"("device_id", "date");

-- AddForeignKey
ALTER TABLE "device_activity_daily" ADD CONSTRAINT "device_activity_daily_app_installation_id_fkey" FOREIGN KEY ("app_installation_id") REFERENCES "app_installation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "device_activity_daily" ADD CONSTRAINT "device_activity_daily_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;
