import "@fontsource/montserrat";
import "react-circular-progressbar/dist/styles.css";
import SubPageContainer from "./components/SubPageContainer";
import AssessmentCompletedAverage from "./components/assessment/AssessmentCompletedAverage";
import { default as AssessmentCompletedExcellent } from "./components/assessment/AssessmentCompletedExcellent";
import AssessmentCompletedFail from "./components/assessment/AssessmentCompletedFail";
import CustomYouTubePlayer from "./components/learning/SingleVideoPlayerPage";
import VideoListPage from "./components/learning/VideosByCategoryId";
import "./lib/multiLanguage";
import Authentication from "./pages/auth/Sup/auth";
import SubmitPhoneNumber from "./pages/auth/phone_number";
import Notification from "./pages/notification/notification";
import RegisterYourself from "./pages/registration";
import SignIn from "./pages/signin";
import SignUp from "./pages/signup";
import Banned from "./pages/technician/banned";
import Contactpage from "./pages/technician/contact_us";
import DocumentVerified from "./pages/technician/document_verified";
import DocumentUpload from "./pages/technician/docx_upload";
import FAQandHelp from "./pages/technician/faq_and_help";
import HelpPolicies from "./pages/technician/help_policies";
import {
  default as Congratulations,
  default as InterviewCleared
} from "./pages/technician/interview_cleared";
import WaitingRoom from "./pages/technician/interview_waiting_room";
import MissedInterview from "./pages/technician/missed_interview";
import MultipleInterviewOfUser from "./pages/technician/multi_interview";
import Page404 from "./pages/technician/page404";
import Rejected from "./pages/technician/rejected";
import Rescheduled from "./pages/technician/rescheduled";
import EarningAndRewards from "./pages/technician/rewards";
import WorkExperience from "./pages/technician/work_experience";
import TrainingMenu from "./pages/training/training";
import TrainingUserCourses from "./pages/training/training_user_courses";
import TrainingUserCourse from "./pages/training/training_user_single_course";
import YoutubeCategoriesList from "./pages/training/youtube_categories_list";
import TrainingYoutubePopularVideosList from "./pages/training/youtube_popular_videos_list";
import AboutUs from "./pages/user/about_us";
import Assessment from "./pages/user/assessment";
import StartAssessment from "./pages/user/assessmentStart";
import BlogsPage from "./pages/user/blogs";
import EditProfile from "./pages/user/edit_profile";
import FullFlow from "./pages/user/full_flow";
import Home from "./pages/user/home";
import JobsPage from "./pages/user/jobs";
import MyProfile from "./pages/user/my_profile";
import PostOnboardingDocs from "./pages/user/postOnboardingDocs";
import ProfileCompletion from "./pages/user/profile_completion";
import SingleAssessment from "./pages/user/single_assessment";
import SingleBlog from "./pages/user/single_blog";
import VideosBlogsPage from "./pages/user/videos_blogs";
// Register yourself
// Team Skills Page
// Select City
import DesktopPlayStoreLink from "./components/DesktopPlayStoreLink";
import MainPageContainer from "./components/MainAppContainer";
import TechnicianContainer from "./components/TechnicianContainer";
import TechnicianRegistrationContainer from "./components/TechnicianRegistrationContainer";
import BookViewer from "./components/pdf/BookViewer";
import ContactsPage from "./components/referAndEarn/SelectContactsPage";
import SPAssessmentResult from "./components/serviceProvider/assessment/SpAssessmentResult";
import TechnicianFeedback from "./pages/TechnicianFeedback";
import SpAssessment from "./pages/serviceProvider/SpAssessments";
import { default as SpCities } from "./pages/serviceProvider/SpCities";
import SpContactDetails from "./pages/serviceProvider/SpContactDetails";
import SpDashboardContainer from "./pages/serviceProvider/SpDashboardContainer";
import SPDocUpload from "./pages/serviceProvider/SpDocUpload";
import { default as SpInviteTeamMemberContainer } from "./pages/serviceProvider/SpInviteTeamMemberContainer";
import SpJourneyCompleted from "./pages/serviceProvider/SpJourneyCompleted";
import SpNotification from "./pages/serviceProvider/SpNotification";
import SpOnBoarding from "./pages/serviceProvider/SpOnBoarding";
import SpOnboardingContainer from "./pages/serviceProvider/SpOnboardingContainer";
import SpRegistrationJourneyContainer from "./pages/serviceProvider/SpRegistrationJourneyContainer";
import SpSelectionProcess from "./pages/serviceProvider/SpSelectionProcess";
import SpSingleAssessment from "./pages/serviceProvider/SpSingleAssessment";
import SpSkills from "./pages/serviceProvider/SpSkills";
import SpStartAssessment from "./pages/serviceProvider/SpStartAssessment";
import SpTeamMemberContactDetails from "./pages/serviceProvider/SpTeamMemberContactDetails";
import SpTeamMemberSkills from "./pages/serviceProvider/SpTeamMemberSkills";
import SpWorkExperienceDetails from "./pages/serviceProvider/SpWorkExperienceDetails";
import TechnicianJourneyCompleted from "./pages/technician/TechnicianJourneyCompleted";
import ProfileSetup from "./pages/technician/profile";
import PolicyList from "./pages/user/PolicyList";
import WelcomeUser from "./pages/user/welcome-user";

export const routes = [
  { path: "/", element: <Authentication /> },
  { path: "register-yourself", element: <RegisterYourself /> },
  { path: "play-store-link", element: <DesktopPlayStoreLink /> },
  { path: "welcome-user/:type_id", element: <WelcomeUser /> },
  { path: "technician-feedback/:user_id", element: <TechnicianFeedback /> },
  { path: "/404", element: <Page404 /> },
  {
    path: "/sp",
    element: <SpOnboardingContainer />, //this will handle if the user is under registration or dashboard
    children: [
      {
        path: "registration-journey",
        element: <SpRegistrationJourneyContainer />,
        exact: true,
        children: [
          { path: "tm-contact-details", element: <SpTeamMemberContactDetails />, exact: true },
          { path: "tm-skills", element: <SpTeamMemberSkills />, exact: true },
          { path: "contact-details", element: <SpContactDetails />, exact: true },
          { path: "work-details", element: <SpWorkExperienceDetails />, exact: true },
          { path: "skills", element: <SpSkills />, exact: true },
          { path: "cities", element: <SpCities />, exact: true },
          { path: "journey-completed", element: <SpJourneyCompleted />, exact: true },
          { path: "sign_up", element: <SignUp />, exact: true },
          {
            path: "signup",
            element: <SignUp />,
            exact: true,
            children: [{ path: ":referral_code", element: <SignUp /> }]
          },
          { path: "signin", element: <SignIn />, exact: true },
          { path: "sign_up_with_phone", element: <SubmitPhoneNumber />, exact: true }
        ]
      },
      {
        path: "home",
        element: <SpDashboardContainer />,
        exact: true,
        children: [
          { path: "", element: <SpSelectionProcess />, exact: true },
          { path: "notification", element: <SpNotification /> },
          { path: "onboarding", element: <SpOnBoarding />, exact: true },
          // { path: "onboarding", element: <SpTeamMemberProfile />, exact: true },
          { path: "doc_upload", element: <SPDocUpload />, exact: true },
          { path: "invite", element: <SpInviteTeamMemberContainer />, exact: true }
        ]
      }
    ]
  },
  // this routes won't have navbar and footer
  { path: "/sp/assessment", element: <SpAssessment /> },
  { path: "/sp/assessment_config/:id", element: <SpSingleAssessment /> },
  {
    path: "/sp/assessment/start_test/:assignment_config_id/:assignment_id",
    element: <SpStartAssessment />
  },
  {
    path: "/sp/assessment/complete",
    element: <SPAssessmentResult />
  },
  { path: "/sp/home/<USER>/popular", element: <TrainingYoutubePopularVideosList /> },

  {
    path: "/technician",
    element: <TechnicianContainer />,
    children: [
      {
        path: "registration", //Handle Technician Registration
        element: <TechnicianRegistrationContainer />,
        children: [
          { path: "profile", element: <ProfileSetup /> },
          { path: "profile/edit", element: <ProfileSetup /> }, //TODO: @vandna22 remove this but check the existing functionality
          { path: "work_experience", element: <WorkExperience /> },
          { path: "docx_upload", element: <DocumentUpload /> },
          { path: "journey_completed", element: <TechnicianJourneyCompleted /> },
          { path: "banned", element: <Banned /> },
          { path: "congratulations", element: <Congratulations /> },
          { path: "interview_cleared", element: <InterviewCleared /> },
          { path: "rejected", element: <Rejected /> },
          { path: "rescheduled", element: <Rescheduled /> },
          { path: "waiting_room", element: <WaitingRoom /> },
          { path: "missed_interview", element: <MissedInterview /> },
          { path: "document_verified", element: <DocumentVerified /> }
        ]
      }
    ]
  },
  {
    element: <SubPageContainer />,
    children: [
      { path: "/muti_interview_ui", element: <MultipleInterviewOfUser /> },
      { path: "/edit_profile", element: <EditProfile /> },
      { path: "/interview", element: <MultipleInterviewOfUser /> },
      { path: "/interview_cleared", element: <InterviewCleared /> },
      { path: "/interview_rejected", element: <Rejected /> },
      { path: "account_creation", element: <FullFlow /> },
      { path: "/about-us", element: <AboutUs /> },
      { path: "/profile_completion", element: <ProfileCompletion /> },
      { path: "/contact_us", element: <Contactpage /> },
      { path: "/blogs", element: <BlogsPage /> },
      { path: "/blogs/:id", element: <SingleBlog /> },
      { path: "/faq_and_help", element: <FAQandHelp /> },
      { path: "/help_policies", element: <HelpPolicies /> },
      { path: "/home_learn", element: <VideosBlogsPage /> },
      { path: "/video_player/:videoId", element: <CustomYouTubePlayer /> },
      { path: "/by_playlist/:playlistId", element: <VideoListPage /> },
      { path: "/post_onboarding_docs", element: <PostOnboardingDocs /> },
      { path: "/learning/course/:id", element: <TrainingUserCourse /> },
      { path: "/policies", element: <PolicyList /> },
      { path: "/select-contact/:userId", element: <ContactsPage /> },
      {
        path: "/invite",
        element: <SpInviteTeamMemberContainer />
      },
      { path: "/me/profile", element: <MyProfile /> }
    ]
  },
  { path: "/assessment", element: <Assessment /> },
  { path: "/assessment_config/:id", element: <SingleAssessment /> },
  {
    path: "/assessment/start_test/:assignment_config_id/:assignment_id",
    element: <StartAssessment />
  },
  {
    element: <MainPageContainer />,
    children: [
      { path: "/profile", element: <MyProfile /> },
      { path: "/home", element: <Home /> },
      { path: "/videos_blogs", element: <VideosBlogsPage /> },
      { path: "/jobs", element: <JobsPage /> },
      { path: "/rewards", element: <EarningAndRewards /> },
      { path: "assessment/complete/excellent", element: <AssessmentCompletedExcellent /> },
      { path: "assessment/complete/average", element: <AssessmentCompletedAverage /> },
      { path: "assessment/complete/below_average", element: <AssessmentCompletedFail /> },
      { path: "/learning", element: <TrainingMenu /> },
      { path: "/learning/courses", element: <TrainingUserCourses /> }
    ]
  },
  { path: "/learning/youtube/category/:id", element: <YoutubeCategoriesList /> },
  { path: "/learning/youtube/popular", element: <TrainingYoutubePopularVideosList /> },
  { path: "/notification", element: <Notification /> },
  { path: "*", element: <Page404 /> },
  //unauth routes
  {
    path: "/culture/:id",
    element: <BookViewer pdfUrl={"/pdf/handbook.pdf"} />
  },
  {
    path: "/team",
    element: <SpInviteTeamMemberContainer />
  }
];
// Define the stages and their associated routes
export const stages: { [key: string]: string[] } = {
  register: ["/sign_up", "/signup", "/signin", "/otp", "/sign_up_with_phone"],
  onboarding: [
    "/technician",
    "/technician/registration/profile",
    "/technician/registration/profile/edit",
    "/technician/docx_upload",
    "/technician/work_experience",
    "/technician/banned",
    "/technician/journey_completed",
    "/technician/congratulations",
    "/technician/interview_cleared",
    "/technician/rejected",
    "/technician/rescheduled",
    "/technician/waiting_room",
    "/technician/missed_interview",
    "/technician/document_verified"
  ],
  subPage: [
    "/muti_interview_ui",
    "/edit_profile",
    "/interview",
    "/interview_cleared",
    "/interview_rejected",
    "/account_creation",
    "/about-us",
    "/profile_completion",
    "/contact_us",
    "/blogs",
    "/blogs/:id",
    "/faq_and_help",
    "/help_policies",
    "/home_learn",
    "/video_player/:videoId",
    "/by_playlist/:playlistId",
    "/post_onboarding_docs",
    "/learning/course/:id",
    "/sp/home/<USER>",
    "/me/profile"
  ],
  mainPage: [
    "/profile",
    "/home",
    "/videos_blogs",
    "/jobs",
    "/rewards",
    "/assessment/complete/excellent",
    "/assessment/complete/average",
    "/assessment/complete/below_average",
    "/learning",
    "/learning/courses"
  ],
  assessment: [
    "/assessment",
    "/assessment_config/:id",
    "/assessment/start_test/:assignment_config_id/:assignment_id"
  ],
  culture: ["/culture/:id", "/team", "/test/profile"],
  learning: ["/learning/youtube/category/:id", "/learning/youtube/popular"],
  page404: ["*"]
};

// utils/routes.js

export const authRoutes = [
  { path: "/" },
  { path: "/welcome-user/:typeId" } //TODO: update type Id to type_id
];
export const unauthRotes = [
  { path: "/culture/:id" },
  { path: "/team" },
  { path: "/test/profile", element: <MyProfile /> },
  { path: "/me/profile" }
];
export const spRoutes = [
  { path: "/register-yourself" },
  { path: "/sp" },
  { path: "/sp/registration-journey" },
  { path: "/sp/registration-journey/contact-details" },
  { path: "/sp/registration-journey/work-details" },
  { path: "/sp/registration-journey/skills" },
  { path: "/sp/registration-journey/cities" },
  { path: "/sp/registration-journey/journey-completed" },
  { path: "/sp/registration-journey/sign_up" },
  { path: "/sp/registration-journey/signup" },
  { path: "/sp/registration-journey/signup/:referral_code" },
  { path: "/sp/registration-journey/signin" },
  { path: "/sp/registration-journey/sign_up_with_phone" },
  { path: "/sp/home" },
  { path: "/sp/home/<USER>" },
  { path: "/sp/home/<USER>" },
  { path: "/sp/assessment" },
  { path: "/sp/assessment_config/:id" },
  {
    path: "/sp/assessment/start_test/:assignment_config_id/:assignment_id"
  },
  {
    path: "/sp/assessment/complete"
  },
  { path: "/sp/home/<USER>/popular" }
];

export const technicianRoutes = [
  { path: "/register-yourself" },
  { path: "/technician" },
  { path: "/technician/registration" },
  { path: "/technician/registration/profile" },
  { path: "/technician/registration/profile/edit" },
  { path: "/technician/registration/docx_upload" },
  { path: "/technician/registration/work_experience" },
  { path: "/technician/registration/banned" },
  { path: "/technician/registration/journey_completed" },
  { path: "/technician/registration/congratulations" },
  { path: "/technician/registration/interview_cleared" },
  { path: "/technician/registration/rejected" },
  { path: "/technician/registration/rescheduled" },
  { path: "/technician/registration/waiting_room" },
  { path: "/technician/registration/missed_interview" },
  { path: "/technician/registration/document_verified" },
  { path: "/muti_interview_ui" },
  { path: "/edit_profile" },
  { path: "/interview" },
  { path: "/interview_cleared" },
  { path: "/interview_rejected" },
  { path: "/account_creation" },
  { path: "/about-us" },
  { path: "/profile_completion" },
  { path: "/contact_us" },
  { path: "/blogs" },
  { path: "/blogs/:id" },
  { path: "/faq_and_help" },
  { path: "/help_policies" },
  { path: "/home_learn" },
  { path: "/video_player/:videoId" },
  { path: "/by_playlist/:playlistId" },
  { path: "/post_onboarding_docs" },
  { path: "/learning/course/:id" },
  { path: "/assessment" },
  { path: "/assessment_config/:id" },
  { path: "/assessment/start_test/:assignment_config_id/:assignment_id" },
  { path: "/profile" },
  { path: "/home" },
  { path: "/videos_blogs" },
  { path: "/jobs" },
  { path: "/rewards" },
  { path: "assessment/complete/excellent" },
  { path: "assessment/complete/average" },
  { path: "assessment/complete/below_average" },
  { path: "/learning" },
  { path: "/learning/courses" },
  { path: "/learning/youtube/category/:id" },
  { path: "/learning/youtube/popular" },
  { path: "/notification" },
  { path: "/invite" }
];

// Combine all routes into an object for easier lookup
const routesMap = {
  authRoutes,
  spRoutes,
  technicianRoutes,
  unauthRotes
};

export { routesMap };
