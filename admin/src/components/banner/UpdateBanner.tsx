import { BackwardOutlined } from "@ant-design/icons";
import { Button, Checkbox, Form, Input, Radio, Select, notification } from "antd";
import { useForm } from "antd/es/form/Form";
import { isEmpty } from "lodash";
import { useState } from "react";
import { Outlet, useNavigate, useParams } from "react-router-dom";
import {
  BannerConfigsDocument,
  useBannerConfigsQuery,
  useUpdateBannerConfigMutation
} from "../../__generated__";
import { banner_images, banner_links } from "../../utils/utils";
import ErrorComponent from "../error/Error";
import ImageUploaderS3 from "../fileUploader/ImageUploadS3";
import Banner from "./Banner";

function UpdateBanner() {
  const [banner_form] = useForm();
  const [showCTALink, setShowCTALink] = useState<boolean>(false);
  const [preview, setPreviewContentData] = useState({
    title: "",
    description: "",
    hero_image: "",
    image: "",
    cta_link: ""
  });

  //navigate
  const navigate = useNavigate();

  //params
  const params = useParams();

  //Graphql Query
  const { error } = useBannerConfigsQuery({
    variables: {
      filter: {
        banner_config_id: params?.banner_id
      }
    },
    fetchPolicy: "no-cache",
    onCompleted(data) {
      const config = data?.bannerConfigs?.data?.[0];
      banner_form.setFieldsValue({
        title: config?.title,
        description: config?.description,
        hero_image: config?.hero_image,
        image: config?.image,
        cta_link: config?.cta_link
      });

      setPreviewContentData({
        title: config?.title || "",
        description: config?.description || "",
        hero_image: config?.hero_image || "",
        image: config?.image || "",
        cta_link: config?.cta_link || ""
      });

      if (config?.cta_required) {
        setShowCTALink(true);
      }
    }
  });

  //Graphql Mutation
  const [updateBannerConfig, { loading: updateLoading }] = useUpdateBannerConfigMutation();

  const handleFormSubmit = async () => {
    try {
      banner_form.validateFields();

      await updateBannerConfig({
        variables: {
          data: {
            banner_config_id: params?.banner_id as string,
            title: banner_form.getFieldValue("title"),
            description: banner_form.getFieldValue("description"),
            hero_image: banner_form.getFieldValue("hero_image"),
            image: isEmpty(banner_form.getFieldValue("image"))
              ? null
              : banner_form.getFieldValue("image"),
            cta_required: showCTALink,
            cta_link: banner_form.getFieldValue("cta_link")
          }
        },
        onCompleted: () => {
          navigate("/dashboard/banners");
          notification.success({
            message: "Banner Updated Successfully"
          });
        },
        refetchQueries: [BannerConfigsDocument],
        onError: (error) => {
          notification.error({
            message: "Error while updating banner \n" + error.message
          });
        }
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handlePreview = async () => {
    try {
      banner_form.setFieldsValue({
        title: banner_form.getFieldValue("title"),
        description: banner_form.getFieldValue("description"),
        hero_image: banner_form.getFieldValue("hero_image")
      });

      // rerender the preview content
      setPreviewContentData({
        title: banner_form.getFieldValue("title"),
        description: banner_form.getFieldValue("description"),
        hero_image: banner_form.getFieldValue("hero_image"),
        image: banner_form.getFieldValue("image"),
        cta_link: banner_form.getFieldValue("cta_link")
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleGetCtaTextFromLink = () => {
    if (banner_form.getFieldValue("cta_link")) {
      const cta_text = banner_links.find(
        (link) => link.value === banner_form.getFieldValue("cta_link")
      )?.label;
      if (cta_text) {
        banner_form.setFieldsValue({
          cta_text
        });
      }
    }
  };

  if (error) {
    <ErrorComponent error={error} />;
  }

  return (
    <div>
      <div className="p-8">
        <div className="">
          <div className="flex items-center gap-3 bg-grey border-purple-400 shadow-sm p-2 rounded-md border border-solid border-l-4 border-r-0 border-b-0 border-t-0 mb-4">
            <img src={"/images/vector.png"} alt="icon" />
            <div className="text-2xl">Update Banner </div>
          </div>
        </div>
        <Button
          onClick={() => {
            navigate("/dashboard/banners");
          }}
          className="mb-2 text-colorPrimary"
          icon={<BackwardOutlined />}
        >
          Go back
        </Button>
        <div className="grid grid-cols-6 gap-4 h-[80vh]">
          <div className="col-span-3 lg:col-span-4 border border-solid border-slate-200 p-4 rounded bg-white h-full overflow-auto overflow-x-hidden overflow-y-auto  ">
            <Form
              layout="vertical"
              form={banner_form}
              onFinish={handleFormSubmit}
              onChange={handlePreview}
              className="mt-5"
            >
              <Form.Item
                name={"title"}
                label="Banner Title"
                rules={[{ required: true, message: "Enter Banner Title" }]}
                extra="Limit to 100 characters"
              >
                <Input maxLength={100} placeholder="Enter Banner Title" />
              </Form.Item>
              <Form.Item name={"hero_image"}>
                <div className="grid grid-cols-4 gap-3 ">
                  <Radio.Group
                    value={banner_form.getFieldValue("hero_image")}
                    defaultValue={banner_images[0].url}
                  >
                    <div className="flex  gap-2 ">
                      {banner_images.map((img) => (
                        <label key={img.id} className=" items-center cursor-pointer relative ">
                          <Radio
                            value={img.url}
                            onChange={(e) => {
                              banner_form.setFieldsValue({ hero_image: e.target.value });
                              handlePreview();
                            }}
                            name="image"
                            className="absolute top-0 right-0 "
                          />
                          <div className="border border-solid border-slate-200 p-2 rounded ">
                            <img
                              className=" object-cover rounded-md  w-[160px] h-[90px]"
                              src={img.url}
                              alt={"no image"}
                              width={64}
                              height={64}
                            />
                          </div>
                        </label>
                      ))}
                    </div>
                  </Radio.Group>
                </div>
              </Form.Item>
              <Form.Item
                name={"description"}
                label={"Banner Content"}
                rules={[{ required: true, message: "Enter Banner Description " }]}
              >
                <Input placeholder="Write your description" />
              </Form.Item>
              <Form.Item name={"image"}>
                <ImageUploaderS3
                  setImageUrl={(url) => {
                    if (url === "deleted") {
                      banner_form.setFieldValue("image", undefined);
                      handlePreview();
                    } else {
                      banner_form.setFieldValue("image", url);
                      handlePreview();
                    }
                  }}
                  text={"Upload an image"}
                  imgUrl={banner_form.getFieldValue("image")}
                />
              </Form.Item>
              <Form.Item>
                <Checkbox
                  checked={showCTALink}
                  onChange={(e) => {
                    setShowCTALink(e.target.checked);
                  }}
                >
                  CTA Required
                </Checkbox>
              </Form.Item>
              <Form.Item
                name={"cta_link"}
                rules={[{ required: showCTALink, message: "CTA Link is required" }]}
              >
                <Select
                  onChange={() => {
                    handlePreview();
                    handleGetCtaTextFromLink();
                  }}
                  disabled={!showCTALink}
                  options={banner_links}
                  placeholder="Select CTA"
                />
              </Form.Item>
            </Form>
            <div className="flex justify-center">
              <Button
                className="mx-auto"
                type="primary"
                loading={updateLoading}
                onClick={() => {
                  banner_form.submit();
                }}
              >
                Save as Draft
              </Button>
            </div>
          </div>
          <div className="col-span-2 flex justify-center items-center ">
            <Banner
              bgUrl={preview.hero_image}
              title={preview.description}
              sideImgUrl={preview.image}
              ctaLink={preview.cta_link}
              ctaRequired={showCTALink}
            />
          </div>
        </div>
      </div>
      <Outlet />
    </div>
  );
}

export default UpdateBanner;
