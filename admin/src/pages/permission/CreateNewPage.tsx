import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { <PERSON>ton, Card, Form, Input, notification, Select, Switch } from "antd";
import React, { useState } from "react";
import { decodeToken } from "react-jwt";
import { useCreateNewPageMutation } from "../../__generated__";

interface NewModuleData {
  module_id: number | null;
  icon_name: string;
  module_name: string;
  visible_in_sp: boolean;
  visible_in_users: boolean;
}
const CreateNewPage: React.FC = () => {
  const [selectedModule, setSelectedModule] = useState<NewModuleData>({
    module_id: null,
    icon_name: "",
    module_name: "",
    visible_in_users: false,
    visible_in_sp: false
  });
  const [form] = Form.useForm();

  const [createNewPageMutation] = useCreateNewPageMutation();
  const moduleToken = localStorage.getItem("moduleToken");
  const moduleData:
    | {
        module_id: number;
        module_name: string;
        icon_name: string;
        visible_in_users: boolean;
        visible_in_sp: boolean;
        [key: string]: any;
      }[]
    | null = moduleToken ? decodeToken(moduleToken) : [];

  const handleSubmit = async (values: any) => {
    try {
      const hasPages = (values.protectedFeatures || []).length > 0;
      console.log(values.protectedFeatures);
      if (!hasPages) {
        notification.error({
          message: "At least one page required"
        });
      } else {
        const allHaveAtLeastOneUrl = values.protectedFeatures.every(
          (feature: any) => feature.sub_module_url_user?.trim() || feature.sub_module_url_sp?.trim()
        );
        if (!allHaveAtLeastOneUrl) {
          notification.error({
            message: "Each page must have at least one URL (Individual or SP)"
          });
          return;
        }
        const newModuleData: NewModuleData = {
          module_id: selectedModule.module_id,
          icon_name: values.icon_name,
          module_name: values.name,
          visible_in_users: values.visible_in_users,
          visible_in_sp: values.visible_in_sp
        };
        if (newModuleData.module_id) {
          await createNewPageMutation({
            variables: {
              moduleId: newModuleData.module_id,
              page: values?.protectedFeatures.map((feature: any) => ({
                is_module: false,
                sub_module_name: feature.sub_module_name,
                sub_module_url_user: feature.sub_module_url_user,
                sub_module_url_sp: feature.sub_module_url_sp
              }))
            },
            onCompleted: () => {
              notification.success({
                message: "Features created successfully"
              });
              form.resetFields();
              setSelectedModule({
                module_id: null,
                icon_name: "",
                module_name: "",
                visible_in_users: false,
                visible_in_sp: false
              });
            }
          });
        }
      }
    } catch (err) {
      notification.error({
        message: `Error: ${err}`
      });
    }
  };
  return (
    <div className="py-6 px-4 flex flex-col items-center">
      <Card className="w-full max-w-3xl">
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-4">Module Details</h3>

          <Form form={form} layout="vertical" onFinish={handleSubmit}>
            <div className="flex gap-x-6 mb-7 pt-1">
              <div className="flex-1">
                <div className="flex items-center gap-x-2">
                  <Switch disabled value={selectedModule?.visible_in_users} />
                  <span>Visible in Individual</span>
                </div>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-x-2">
                  <Switch disabled defaultChecked value={selectedModule?.visible_in_sp} />
                  <span>Visible in SP</span>
                </div>
              </div>
            </div>

            <div className="flex gap-x-6 w-full justify-between">
              <Form.Item className="flex-1" name="name" label="Module" rules={[{ required: true }]}>
                <Select
                  placeholder="Choose a module"
                  onChange={(module) => {
                    const selectedModule = Object.values(moduleData || {})?.find(
                      (moduleData: any) => moduleData.module_name === module
                    );
                    if (selectedModule) {
                      setSelectedModule({
                        module_id: selectedModule.module_id,
                        module_name: selectedModule.module_name,
                        icon_name: selectedModule.icon_name,
                        visible_in_sp: selectedModule.visible_in_sp,
                        visible_in_users: selectedModule.visible_in_users
                      });
                      form.setFieldsValue({
                        icon_name: selectedModule.icon_name
                      });
                    }
                  }}
                >
                  {Object.values(moduleData || {})?.map((module: any) => (
                    <Select.Option key={module.module_id} value={module.module_name}>
                      <div className="capitalize">{module.module_name}</div>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                className="flex-1"
                name="icon_name"
                rules={[{ required: true }]}
                label={<div className="flex items-center gap-x-2">Icon Name</div>}
              >
                <Input disabled value={selectedModule?.icon_name} />
              </Form.Item>
            </div>

            <Form.List name="protectedFeatures">
              {(fields, { add, remove }) => (
                <div className="mt-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">Page Details</h3>
                    <Button type="dashed" icon={<PlusOutlined />} onClick={() => add()}>
                      Add Page
                    </Button>
                  </div>

                  {fields.map(({ key, name, ...restField }) => (
                    <Card
                      key={key}
                      className="mb-4 p-4 border border-dashed border-gray-300 bg-white"
                      size="small"
                      title={`Page ${key + 1}`}
                      extra={
                        <MinusCircleOutlined
                          className="text-red-500 cursor-pointer"
                          onClick={() => remove(name)}
                        />
                      }
                    >
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Form.Item
                          {...restField}
                          name={[name, "sub_module_name"]}
                          label="Page Name"
                          rules={[{ required: true, message: "Required" }]}
                        >
                          <Input className="capitalize" placeholder="Enter sub-module name" />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, "sub_module_url_user"]}
                          label="Page Url : Individual "
                          rules={[
                            {
                              required:
                                selectedModule.visible_in_users && !selectedModule.visible_in_sp,
                              message: "Required"
                            },
                            {
                              pattern: /^\/dashboard\//,
                              message: "URL must start with '/dashboard/'"
                            }
                          ]}
                        >
                          <Input
                            disabled={selectedModule?.visible_in_users ? false : true}
                            defaultValue="/dashboard/"
                            placeholder="/dashboard/..."
                          />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, "sub_module_url_sp"]}
                          label="Page Url : Service Provider "
                          rules={[
                            {
                              required:
                                !selectedModule.visible_in_users && selectedModule.visible_in_sp,
                              message: "Required"
                            },
                            {
                              pattern: /^\/dashboard\/sp\//,
                              message: "URL must start with '/dashboard/sp/'"
                            }
                          ]}
                        >
                          <Input
                            disabled={!selectedModule?.visible_in_sp}
                            placeholder="/dashboard/sp/..."
                            defaultValue="/dashboard/sp/"
                          />
                        </Form.Item>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </Form.List>

            <div className="text-center mt-6">
              <Button
                type="primary"
                htmlType="submit"
                className="bg-purple-500 hover:bg-purple-600 border-none"
              >
                Submit
              </Button>
            </div>
          </Form>
        </div>
      </Card>
    </div>
  );
};

export default CreateNewPage;
