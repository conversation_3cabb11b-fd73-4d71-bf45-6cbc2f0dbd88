import { InfoCircleOutlined, MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Card, Form, Input, notification, Select, Switch, Tooltip } from "antd";
import React, { useState } from "react";
import { NewModuleData, useCreateNewModuleMutation } from "../../__generated__";
import { iconMap } from "../../utils/renderIcon";

const CreateNewModule: React.FC = () => {
  const [moduleData, setModuleData] = useState<NewModuleData>({
    icon_name: "",
    module_name: "",
    visible_in_users: false,
    visible_in_sp: false
  });
  const [modulePage, setModulePage] = useState<number | null>(null);
  const [form] = Form.useForm();
  const [createNewModuleMutation] = useCreateNewModuleMutation();
  const handleSubmit = (values: any) => {
    if (!values?.protectedFeatures || values?.protectedFeatures?.length === 0) {
      notification.error({
        message: "At least one page required"
      });
      return;
    }
    if (!values?.visible_in_sp && !values?.visible_in_users) {
      notification.error({
        message: "Module should be visible at least in one section : Individual or SP"
      });
      return;
    }
    const hasModule = values?.protectedFeatures?.some((feature: any) => feature.is_module);
    if (!hasModule) {
      notification.error({
        message: "Please set one feature page as module's landing page"
      });
      return;
    }
    const allHaveAtLeastOneUrl = values.protectedFeatures.every(
      (feature: any) => feature.sub_module_url_user?.trim() || feature.sub_module_url_sp?.trim()
    );
    if (!allHaveAtLeastOneUrl) {
      notification.error({
        message: "Each page must have at least one URL (Individual or SP)"
      });
      return;
    }
    const newModuleData: NewModuleData = {
      icon_name: values?.icon_name,
      module_name: values?.name,
      visible_in_users: values?.visible_in_users ? true : false,
      visible_in_sp: values?.visible_in_sp ? true : false
    };
    createNewModuleMutation({
      variables: {
        module: newModuleData,
        features: values?.protectedFeatures.map((feature: any) => ({
          is_module: feature.is_module,
          sub_module_name: feature.sub_module_name,
          sub_module_url_user: feature.sub_module_url_user,
          sub_module_url_sp: feature.sub_module_url_sp
        }))
      },
      onCompleted: () => {
        setModulePage(null);
        form.resetFields();
        setModuleData({
          icon_name: "",
          module_name: "",
          visible_in_users: false,
          visible_in_sp: false
        });
        notification.success({ message: "New Module Created Successfully" });
      }
    });
  };

  return (
    <div className="  py-6 px-4 flex flex-col items-center">
      <Card className="w-full max-w-3xl ">
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-4">Module Details</h3>

          <Form form={form} layout="vertical" onFinish={handleSubmit}>
            <div className="w-full">
              <div className="flex gap-x-6">
                <Form.Item className="flex-1" name="visible_in_users" valuePropName="checked">
                  <div className="flex items-center gap-x-2">
                    <Switch
                      onChange={(selected) => {
                        setModuleData((prev) => ({
                          ...prev,
                          visible_in_users: selected
                        }));
                        form.setFieldsValue({
                          visible_in_users: selected
                        });
                      }}
                    />
                    <span>Visible in Individual</span>
                  </div>
                </Form.Item>
                <Form.Item className="flex-1" name="visible_in_sp" valuePropName="checked">
                  <div className="flex items-center gap-x-2">
                    <Switch
                      onChange={(selected) => {
                        setModuleData((prev) => ({
                          ...prev,
                          visible_in_sp: selected
                        }));
                        form.setFieldsValue({
                          visible_in_sp: selected
                        });
                      }}
                    />
                    <span>Visible in SP</span>
                  </div>
                </Form.Item>
              </div>
            </div>
            <div className="flex gap-x-6 w-full justify-between">
              <Form.Item className="flex-1" name="name" label="Module" rules={[{ required: true }]}>
                <Input placeholder="Enter new module name" />
              </Form.Item>
              <Form.Item
                className="flex-1"
                name="icon_name"
                rules={[{ required: true }]}
                label={
                  <div className="flex items-center gap-x-2">
                    Icon Name
                    <Tooltip
                      overlayStyle={{ minWidth: 450 }}
                      title={
                        <div>
                          1. Import the icon at the top of <br />
                          <code>src/utils/renderIcon.ts</code>
                          <br />
                          <br />
                          <strong>Example:</strong>
                          <br />
                          <code style={{ whiteSpace: "nowrap" }}>
                            {`import { AiOutlineSetting } from 'react-icons/ai'`}
                          </code>
                          <br />
                          <br />
                          2. Add to <code>iconMap</code> like:
                          <br />
                          <code>{`const iconMap = {`}</code>
                          <br />
                          <code>{`  ...`}</code>
                          <br />
                          <code style={{ whiteSpace: "nowrap" }}>
                            {`  AiOutlineSetting: AiOutlineSetting,`}
                          </code>
                          <br />
                          <code>{`};`}</code>
                        </div>
                      }
                    >
                      <InfoCircleOutlined className="text-gray-700 cursor-pointer" />
                    </Tooltip>
                  </div>
                }
              >
                <Select placeholder="Click info to setup an icon">
                  {Object.keys(iconMap || {})?.map((icon: any) => (
                    <Select.Option key={icon} value={icon}>
                      <div className="capitalize">{icon}</div>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </div>

            <Form.List name="protectedFeatures">
              {(fields, { add, remove }) => (
                <div className="mt-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">Page Details</h3>
                    <Button type="dashed" icon={<PlusOutlined />} onClick={() => add()}>
                      Add Page
                    </Button>
                  </div>

                  {fields.map(({ key, name, ...restField }) => (
                    <Card
                      key={key}
                      className="mb-4 p-4 border border-dashed border-gray-300 bg-white"
                      size="small"
                      title={`Page ${key + 1}`}
                      extra={
                        <MinusCircleOutlined
                          className="text-red-500 cursor-pointer"
                          onClick={() => {
                            setModulePage(null);
                            remove(name);
                          }}
                        />
                      }
                    >
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Form.Item
                          {...restField}
                          name={[name, "is_module"]}
                          label="Is Module's Landing Page"
                          valuePropName="checked"
                        >
                          <Switch
                            disabled={modulePage !== null && modulePage !== key}
                            onChange={(checked) => {
                              if (checked) {
                                setModulePage(key);
                              } else {
                                setModulePage(null);
                              }
                              console.log(
                                modulePage,
                                key,
                                modulePage ? modulePage !== key : false,
                                "Module and Key"
                              );
                            }}
                          />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, "sub_module_name"]}
                          label="Page Name"
                          rules={[{ required: true, message: "Required" }]}
                        >
                          <Input placeholder="Enter sub-module name" />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, "sub_module_url_user"]}
                          label="Page Url : Individual "
                          rules={[
                            {
                              required: moduleData.visible_in_users && !moduleData.visible_in_sp,
                              message: "Required"
                            },
                            {
                              pattern: /^\/dashboard\//,
                              message: "URL must start with '/dashboard/'"
                            }
                          ]}
                        >
                          <Input
                            disabled={!moduleData.visible_in_users}
                            defaultValue="/dashboard/"
                            placeholder="/dashboard/..."
                          />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, "sub_module_url_sp"]}
                          label="Page Url : Service Provider "
                          rules={[
                            {
                              required: !moduleData.visible_in_users && moduleData.visible_in_sp,
                              message: "Required"
                            },
                            {
                              pattern: /^\/dashboard\/sp\//,
                              message: "URL must start with '/dashboard/sp/'"
                            }
                          ]}
                        >
                          <Input
                            disabled={!moduleData.visible_in_sp}
                            defaultValue="/dashboard/sp/"
                            placeholder="/dashboard/sp/..."
                          />
                        </Form.Item>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </Form.List>

            <div className="text-center mt-6">
              <Button
                type="primary"
                htmlType="submit"
                className="bg-purple-500 hover:bg-purple-600 border-none"
              >
                Submit
              </Button>
            </div>
          </Form>
        </div>
      </Card>
    </div>
  );
};

export default CreateNewModule;
