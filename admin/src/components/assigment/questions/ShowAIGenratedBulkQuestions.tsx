import { Form, Select, Table, Tabs, Tag, notification } from "antd";
import FormItem from "antd/es/form/FormItem";
import { TableRowSelection } from "antd/es/table/interface";
import TabPane from "antd/es/tabs/TabPane";
import { ColumnType } from "antd/lib/table";
import isEmpty from "is-empty";
import { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  GetAllQuestionsDocument,
  useCreateQuestionBulkMutation,
  useGetAllBucketsQuery
} from "../../../__generated__";
import { AddQuestionFormContext } from "./AddQuestionModal";
import { BulkQuestionAIProps } from "./AiGenerateQuestions";

interface ShowBulkUploadQuestionWithAIProps {
  generateQuestionsFromAI: Array<BulkQuestionAIProps>;
}

const bulkAdditionWithAITableColumn: Array<ColumnType<any>> = [
  {
    title: "Question",
    key: "question_text",
    dataIndex: "question_text",
    align: "center"
  },
  {
    title: "Option 1",
    key: "option_1",
    dataIndex: "option_1",
    align: "center"
  },
  {
    title: "Option 2",
    key: "option_2",
    dataIndex: "option_2",
    align: "center"
  },
  {
    title: "Option 3",
    key: "option_3",
    dataIndex: "option_3",
    align: "center"
  },
  {
    title: "Option 4",
    key: "option_4",
    dataIndex: "option_4",
    align: "center"
  },
  {
    title: "Correct Answer",
    key: "correct_option",
    dataIndex: "correct_option",
    align: "center",
    render: (data: string) => {
      return (
        <>
          {" "}
          <b>{data} </b>
        </>
      );
    }
  },
  {
    title: "Difficulty",
    key: "difficulty",
    dataIndex: "difficulty",
    align: "center",
    render: (data: string) => {
      return (
        <>
          {" "}
          <Tag color={data == "EASY" ? "green" : data == "MEDIUM" ? "yellow" : "red"}>{data}</Tag>
        </>
      );
    }
  }
];

const ShowAIGeneratedBulkQuestion: React.FC<ShowBulkUploadQuestionWithAIProps> = ({
  generateQuestionsFromAI
}) => {
  //Hooks
  const [selectedQuestions, setSelectedQuestion] = useState<Array<BulkQuestionAIProps>>([]);

  //form
  const ai_form = useContext(AddQuestionFormContext);

  //Query
  const { data: buckets } = useGetAllBucketsQuery();

  //Mutation
  const [createQuestionBulk] = useCreateQuestionBulkMutation();

  //Navigation
  const nav = useNavigate();

  //Ignore this at first attempt it will look big
  const handleFormSubmit = async (bucket_id: string) => {
    try {
      if (isEmpty(selectedQuestions)) {
        throw new Error("No Questions Selected");
      }
      const updatedQuestion = selectedQuestions.map((question) => {
        return {
          question_text: question.question_text, //Question Name
          bucket_id,
          options: [
            {
              option_text: question.option_1,
              is_right_option: question.option_1 == question.correct_option ? true : false
            },
            {
              option_text: question.option_2,
              is_right_option: question.option_2 == question.correct_option ? true : false
            },
            {
              option_text: question.option_3,
              is_right_option: question.option_3 == question.correct_option ? true : false
            },
            {
              option_text: question.option_4,
              is_right_option: question.option_4 == question.correct_option ? true : false
            }
          ],
          difficulty: question.difficulty
        };
      });
      await createQuestionBulk({
        variables: {
          data: updatedQuestion as any
        },
        onCompleted() {
          notification.success({ message: "All Questions Uploaded Successfully " });
          nav(-1);
        },
        onError: (error) => {
          notification.error({ message: error.message });
        },
        refetchQueries: [GetAllQuestionsDocument]
      });
    } catch (error: any) {
      notification.error({
        message: error.message
      });
    }
  };

  const rowSelection: TableRowSelection<BulkQuestionAIProps> = {
    onSelect: (_, __, selectedRows) => {
      setSelectedQuestion(selectedRows);
    },
    onSelectAll: (_, __, changeRows) => {
      setSelectedQuestion(changeRows);
    }
  };
  return (
    <>
      <Form
        form={ai_form}
        onFinishFailed={(data) => {
          const bucket_id = data.values?.bucket_id_ai;
          bucket_id && handleFormSubmit(bucket_id);
        }}
      >
        <FormItem
          name={"bucket_id_ai"}
          label={"Bucket"}
          rules={[{ required: true, message: "Please select a bucket" }]}
        >
          <Select
            options={buckets?.getAllBuckets?.data?.map((b) => ({
              value: b?.id,
              label: b?.name
            }))}
          />
        </FormItem>
      </Form>
      <Tabs defaultActiveKey="tab1">
        <TabPane key="tab1">
          <Table
            bordered
            scroll={{ y: "max-content", x: "max-content" }}
            rowSelection={rowSelection}
            dataSource={generateQuestionsFromAI.map((data, dataIndex) => {
              return {
                id: dataIndex,
                key: dataIndex,
                ...data
              };
            })}
            columns={bulkAdditionWithAITableColumn}
            pagination={{ showSizeChanger: true }}
          />
        </TabPane>
      </Tabs>
    </>
  );
};

export default ShowAIGeneratedBulkQuestion;
