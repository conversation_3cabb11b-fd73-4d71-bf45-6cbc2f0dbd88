mutation SendRefferalInvite($data: SendRefferalInviteInput!) {
  sendRefferalInvite(data: $data) {
    id
    name
    phone_number
    meta
    referrer {
      id
      name
    }
    created_at
    updated_at
  }
}

query GetMyUnregisteredReferrals($pagination: Pagination, $search: String) {
  getMyUnregisteredReferrals(pagination: $pagination, search: $search) {
    data {
      id
      name
      phone_number
      meta
      created_at
      updated_at
    }
    total_count
  }
}

query GenerateReferralCodeFrUser {
  generateReferralCodeFrUser
}

query GetMyReferrals($search: String, $pagination: Pagination) {
  getMyReferrals(search: $search, pagination: $pagination) {
    data {
      created_at
      status
      referrer_code
      referred_name
      referred_id
      referred_phone
      url
    }
    total_count
  }
}

query CheckUnregisteredReferral($phone_number: String!) {
  checkUnregisteredReferral(phone_number: $phone_number) {
    is_referred
    referrers {
      id
      name
      referral_code
    }
  }
}