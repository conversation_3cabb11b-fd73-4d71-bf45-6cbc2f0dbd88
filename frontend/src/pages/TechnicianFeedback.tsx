import { Capacitor } from "@capacitor/core";
import { ArrowBackIcon } from "@chakra-ui/icons";
import { Button, useToast } from "@chakra-ui/react";
import isEmpty from "is-empty";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useGetFeedbackFormStatusQuery } from "../__generated__";
import Storage from "../utils/Storage";
import TechnicianFeedbackSubmitted from "./TechnicianFeedbackSubmitted";

const TechnicianFeedback = () => {
  const params = useParams();
  const userId = params.user_id;
  const navigate = useNavigate();
  const toast = useToast();
  const isAndroid = Capacitor.getPlatform() === "android";
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);

  const [isAlreadySubmitted, setIsAlreadySubmitted] = useState<boolean>(false);
  const [toastShown, setToastShown] = useState(false);

  // URL for the feedback form in admin
  const feedbackUrl = `${import.meta.env.VITE_ADMIN_LINK || ""}/feedbacks/technician-feedback/${userId}?language=english`;
  // const isAndroid = Capacitor.getPlatform() === "android";

  const { loading: feedbackStatusLoading, data } = useGetFeedbackFormStatusQuery({
    variables: { userId: Number(userId) },
    onCompleted: (data) => {
      setIsAlreadySubmitted(data?.getFeedbackFormStatus?.submitted);
    },
    onError: (error) => {
      // Error handling moved to useEffect
      toast({
        variant: "subtle",
        title: `${error.message}`,
        status: "error",
        position: "top"
      });
    },
    fetchPolicy: "no-cache"
  });

  const handleFeedback = async () => {
    const isLogIn = !isEmpty(await Storage.get("onboarding_token"));
    setIsLoggedIn(isLogIn);
    if (!feedbackStatusLoading && !toastShown) {
      // Handle error cases
      // if (!isLoggedIn) {
      if (!isLogIn && isAndroid) {
        await Storage.set("feedback_url_redirect", userId || "");
        toast({
          variant: "subtle",
          title: "Sign in First",
          status: "error",
          position: "top"
        });
        setToastShown(true);
        navigate("/");
        return;
      }
      // const token: string | null = await Storage.get("onboarding_token");
      // if (token) {
      //   const decodedToken = atob(token?.split(".")[1]);
      //   const parsedToken = JSON.parse(decodedToken);
      //   if (userId && !isEmpty(parsedToken) && parsedToken?.user_id !== +userId) {
      //     toast({
      //       variant: "subtle",
      //       title: "You are not authorized to view this page",
      //       status: "error",
      //       position: "top"
      //     });
      //     setToastShown(true);
      //     if (!isLogIn) {
      //       navigate("/");
      //     } else {
      //       navigate("/profile");
      //     }
      //     return;
      //   }
      // }
      // if (!data?.getFeedbackFormStatus?.onboarded || !data?.getFeedbackFormStatus?.user_id) {
      //   await Storage.remove("feedback_url_redirect");
      //   toast({
      //     variant: "subtle",
      //     title: !data?.getFeedbackFormStatus?.user_id
      //       ? "User not found"
      //       : "You are not onboarded yet. Please contact admin.",
      //     status: "error",
      //     position: "top"
      //   });
      //   setToastShown(true);
      //   if (!isLogIn) {
      //     navigate("/");
      //   } else {
      //     navigate("/profile");
      //   }
      //   return;
      // }
    }
  };
  // Handle all navigation and toast logic in a single useEffect
  useEffect(() => {
    handleFeedback();
  }, [feedbackStatusLoading, isLoggedIn, data, userId, toast, navigate, toastShown]);

  // Handle loading state
  if (feedbackStatusLoading) {
    return <div>Loading...</div>;
  }

  // Handle already submitted state
  if (isAlreadySubmitted) {
    return (
      <TechnicianFeedbackSubmitted language={data?.getFeedbackFormStatus?.language?.language} />
    );
  }

  // // If not logged in or not onboarded, show loading while navigation happens
  // if (
  //   (!isLoggedIn && isAndroid) ||
  //   !data?.getFeedbackFormStatus?.onboarded ||
  //   !data?.getFeedbackFormStatus?.user_id
  // ) {
  //   return <div>Redirecting...</div>;
  // }
  return (
    <>
      <div>
        <Button
          className=" mt-5 ml-5 mb-2"
          leftIcon={<ArrowBackIcon />}
          size="sm"
          variant="outline"
          onClick={() => {
            if (isLoggedIn) {
              navigate("/profile");
            } else {
              navigate("/");
            }
          }}
        >
          Back
        </Button>
        <iframe
          allowFullScreen
          src={feedbackUrl}
          style={{ border: "none", width: "100%", height: "100vh" }}
          title="Technician Feedback Form"
        />
      </div>
    </>
  );
};

export default TechnicianFeedback;
