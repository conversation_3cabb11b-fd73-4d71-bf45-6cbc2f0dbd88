import {
  Box,
  Flex,
  Modal,
  Modal<PERSON>ody,
  Modal<PERSON>ontent,
  ModalOverlay,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Ta<PERSON>,
  <PERSON>
} from "@chakra-ui/react";
import dayjs from "dayjs";
import isEmpty from "is-empty";
import <PERSON><PERSON> from "lottie-react";
import { Gift, Users } from "lucide-react";
import mixpanel from "mixpanel-browser";
import { useEffect, useState } from "react";
import { FaWhatsapp } from "react-icons/fa";
import { MdChatBubbleOutline } from "react-icons/md";
import {
  InterviewState,
  OnboardingStage,
  ReferredUser,
  useGenerateReferralCodeQuery,
  useGetAllReferredUsersOfUserQuery,
  useGetMyReferralsQuery,
  useGetReferralConfigurationForUserQuery
} from "../../__generated__";
import comingSoonLottieReferral from "../../animation/coming_soon.json";
import Loading from "../../components/Loading";

const getReferredUsersStatus = (interview: any) => {
  const allCompleted = interview.every(
    (status: any) => status?.status === InterviewState?.Completed
  );
  const allRejected = interview.every((status: any) => status?.status === InterviewState?.Rejected);

  if (allCompleted) {
    return (
      <Tag variant="subtle" colorScheme="green">
        Accepted
      </Tag>
    );
  } else if (allRejected) {
    return (
      <Tag variant="subtle" colorScheme="red">
        Rejected
      </Tag>
    );
  } else {
    return (
      <Tag variant="subtle" colorScheme="blue">
        In Progress
      </Tag>
    );
  }
};

const handleSMSAndroid = (phone: string, referralCode: string, referredUser: any) => {
  const referralLink = referredUser?.url || "";
  const message = `Hey ${referredUser?.referred_name}! Use my referral code ${referralCode} to sign up with Wify and get your welcome rewards!🎉  ${referralLink}`;
  mixpanel.track("referral_sms_clicked", {
    Source: "Refer and Earn",
    ReferralCode: referralCode,
    PhoneNumber: phone
  });

  // Format phone number
  const formattedPhone = phone.replace(/[^0-9]/g, "");

  // Construct SMS URI
  const smsLink = `sms:${formattedPhone}?body=${encodeURIComponent(message)}`;

  // Open SMS app
  window.open(smsLink, "_blank");
};

const EarningAndRewards: React.FC = () => {
  const { data, loading, refetch } = useGetAllReferredUsersOfUserQuery({
    onCompleted: () => {
      refetch();
    }
  });
  const {
    data: myReferrals,
    loading: myReferralsLoading,
    refetch: myReferralsRefetch
  } = useGetMyReferralsQuery({
    onCompleted: () => {
      myReferralsRefetch();
    }
  });

  const { data: referral } = useGenerateReferralCodeQuery();

  const { data: referralConfig, loading: referralConfigLoading } =
    useGetReferralConfigurationForUserQuery();
  const [onboardedUsersLength, setOnboardedUsersLength] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const usersLength = data?.getAllReferredUsersOfUser?.filter(
      (user) => user?.referred_to?.onboarding_stage === OnboardingStage?.Onboard
    ).length;

    setOnboardedUsersLength(usersLength || 0);
  }, [data?.getAllReferredUsersOfUser]);

  const handleWhatsappShare = (user: ReferredUser) => {
    const referredUser = myReferrals?.getMyReferrals?.data?.find(
      (ref) => ref && user?.referred_to?.id === ref?.referred_id
    );
    const referralCode = referral?.generateReferralCodeFrUser || "";
    const referralLink = referredUser?.url || "";
    const message = `Hey ${referredUser?.referred_name || user?.referred_to?.name}! Use my referral code ${referralCode} to sign up with Wify and get your welcome rewards! \n\n ${referralLink}`;
    const encodedMessage = encodeURIComponent(message);
    const phoneNumber = referredUser?.referred_phone?.toString().trim().slice(-10);
    const phoneWithCountryCode = `+91-${phoneNumber}`;

    const url = `https://wa.me/${phoneWithCountryCode}?text=${encodedMessage}`;

    mixpanel.track("referral_whatsapp_clicked", {
      Source: "Refer and Earn",
      ReferralCode: referral?.generateReferralCodeFrUser || "",
      SubmitPhoneNumber: referredUser?.referred_phone || ""
    });
    window.open(url, "_blank");
  };

  if (loading || referralConfigLoading || myReferralsLoading) {
    return <Loading />;
  }
  return (
    <>
      <Box backgroundColor={"#fff"} mx={"2"} mb="10" rounded={"lg"}>
        <Flex gap={"4px"} mt={3}>
          {/* //reward points */}
          <Box
            className="bg-gradient-to-br from-blue-500 to-blue-600"
            borderRadius="xl"
            p={4}
            color="white"
            minW={"35%"}
          >
            <Box className="flex items-center gap-2 mb-2">
              <Gift size={20} />
              Rewards
            </Box>
            <Flex>
              <Box
                flex="1"
                className="bg-gradient-to-br from-blue-400 to-blue-600 "
                borderRadius="xl"
                p={4}
                color="white"
              >
                <Box className="flex flex-col justify-center items-center gap-3 font-semibold ">
                  <Box className="text-2xl">
                    {isEmpty(referralConfig?.getReferralConfigurationForUser)
                      ? 0
                      : onboardedUsersLength *
                        (referralConfig?.getReferralConfigurationForUser?.existing_user_points ||
                          1)}
                  </Box>
                  Points
                </Box>
              </Box>
            </Flex>
          </Box>
          {/* //onboarded */}
          <Box
            flex="1"
            className="bg-gradient-to-br from-green-500 to-green-600 "
            borderRadius="xl"
            p={4}
            color="white"
          >
            <Box className="flex items-center gap-2 mb-2">
              <Users size={20} /> Friends
            </Box>
            <Flex gap={3} justify={"space-between"}>
              <Box
                flex="1"
                className="bg-gradient-to-br from-green-400 to-green-600 "
                borderRadius="xl"
                maxW={"100px"}
                p={4}
                color="white"
              >
                <Box className="flex flex-col justify-center items-center gap-3 font-semibold ">
                  <Box className="text-2xl">{onboardedUsersLength}</Box>
                  Onboarded
                </Box>
              </Box>
              <Box
                flex="1"
                className="bg-gradient-to-br from-green-400 to-green-600 "
                borderRadius="xl"
                maxW={"100px"}
                p={4}
                color="white"
              >
                <Box className="flex flex-col justify-center items-center gap-3 font-semibold ">
                  <Box className="text-2xl">{data?.getAllReferredUsersOfUser?.length || 0}</Box>
                  Referred
                </Box>
              </Box>
            </Flex>
          </Box>
        </Flex>

        <Tabs colorScheme="blue" isFitted isLazy className="mt-4">
          <TabList>
            <Tab className="bg-green-900">My Referral</Tab>
            <Tab>Rewards</Tab>
          </TabList>
          <TabPanels>
            <TabPanel>
              <Box>
                {!isEmpty(data?.getAllReferredUsersOfUser) ? (
                  data?.getAllReferredUsersOfUser?.map((user: any) => {
                    if (user?.referred_to?.onboarding_stage !== OnboardingStage?.Onboard) {
                      const referredUser = myReferrals?.getMyReferrals?.data?.find(
                        (ref) => ref && user?.referred_to?.id === ref?.referred_id
                      );

                      return (
                        <Box>
                          <Box className="flex justify-between w-full items-center border-gray-100 border-b-2 py-3">
                            <Box className="flex flex-col gap-3">
                              <Box className="font-semibold ">{user?.referred_to?.name}</Box>
                              <Box className="text-sm">
                                {dayjs(user?.referred_to?.created_at).format("DD MMM YYYY")}
                              </Box>
                              {/* {referredUser?.status !== "JOINED" && ( */}
                              <Flex
                                onClick={async () => {
                                  // if (user) {
                                  handleWhatsappShare(user);
                                  // }
                                }}
                              >
                                <FaWhatsapp
                                  aria-label="Send WhatsApp message"
                                  color="#25D366"
                                  size={16}
                                />
                              </Flex>
                              {/* )} */}
                              {/* {referredUser?.status !== "JOINED" && ( */}
                              <Flex gap={1}>
                                <MdChatBubbleOutline
                                  aria-label="Send SMS"
                                  color="gray.500"
                                  onClick={() => {
                                    handleSMSAndroid(
                                      referredUser?.referred_phone || "",
                                      referral?.generateReferralCodeFrUser || "",
                                      referredUser
                                    );
                                  }}
                                  size={16}
                                />
                              </Flex>
                              {/* )} */}
                            </Box>
                            <Box>
                              {isEmpty(user?.referred_to?.interview) ? (
                                <Tag variant="subtle" colorScheme="blue">
                                  In Progress
                                </Tag>
                              ) : (
                                getReferredUsersStatus(user?.referred_to?.interview)
                              )}
                            </Box>
                          </Box>
                        </Box>
                      );
                    }
                  })
                ) : (
                  <Box>No user has been referred by you</Box>
                )}
              </Box>
            </TabPanel>
            <TabPanel>
              <Box>
                {!isEmpty(data?.getAllReferredUsersOfUser) ? (
                  data?.getAllReferredUsersOfUser?.map((user: any, index: number) => {
                    if (user?.referred_to?.onboarding_stage === OnboardingStage?.Onboard) {
                      return (
                        <>
                          <Box
                            key={index}
                            className={`flex items-center justify-between ${
                              index === 0 ? "" : "pt-4 "
                            }`}
                          >
                            <Box>
                              <Box className="font-semibold ">{user?.referred_to?.name}</Box>
                              <Box className="text-sm">
                                Onboard Date :{" "}
                                {dayjs(user?.referred_to?.updated_at).format("DD MMM YYYY")}
                              </Box>
                            </Box>
                            <Box className="flex flex-col justify-end items-end cursor-pointer">
                              <Box onClick={() => setIsModalOpen(true)}>
                                {!isEmpty(referralConfig) && (
                                  <Box className="font-bold text-green-600 text-xl">
                                    +
                                    {isEmpty(referralConfig?.getReferralConfigurationForUser)
                                      ? 0
                                      : referralConfig?.getReferralConfigurationForUser
                                          ?.existing_user_points}
                                  </Box>
                                )}
                              </Box>
                              <Box className="underline text-xs text-gray-500">Claim Rewards</Box>
                            </Box>
                          </Box>
                        </>
                      );
                    }
                  })
                ) : (
                  <Box>No Rewards Sorry</Box>
                )}
              </Box>
            </TabPanel>
          </TabPanels>
        </Tabs>
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          isCentered={true}
          size="md"
        >
          <ModalOverlay />
          <ModalContent>
            <ModalBody>
              <Lottie animationData={comingSoonLottieReferral} loop={true} />
            </ModalBody>
          </ModalContent>
        </Modal>
      </Box>
    </>
  );
};

export default EarningAndRewards;
