import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Radio, Space } from "antd";
import TextArea from "antd/es/input/TextArea";
import { useState } from "react";
import useDocumentStore from "../../stores/documentStore";

interface DocumentRejectModalProps {
  showRejectModal: boolean;
  setShowRejectModal: (val: boolean) => void;
  updateUserDocument: (
    status: string,
    document_id: number,
    reject_doc_msg: string,
    updateName: string
  ) => void;
}

const DocumentRejectModal: React.FC<DocumentRejectModalProps> = ({
  showRejectModal,
  setShowRejectModal,
  updateUserDocument
}) => {
  //Hook
  const [rejectMessage, setRejectMessage] = useState("");

  //Store
  const { currentDocument } = useDocumentStore();
  const { document } = currentDocument || {};

  return (
    <>
      <Modal
        onCancel={() => setShowRejectModal(false)}
        key={document?.[0]?.id}
        open={showRejectModal}
        footer={null}
      >
        <h2 className="text-colorPrimary text-center my-2 text-2xl"> Reject Document</h2>
        <div>
          <Radio.Group
            className="my-2"
            onChange={(e) => {
              setRejectMessage(e.target.value);
            }}
          >
            <Space direction="vertical">
              <Radio value="Document is not visible">Document is not visible</Radio>
              <Radio value="Wrong Document Submitted">Wrong document submitted</Radio>
              <Radio value="Data mismatch with the given data ">
                Data mismatch with the given data
              </Radio>
            </Space>
          </Radio.Group>
          <Divider>Or</Divider>
          <TextArea
            onChange={(e) => {
              setRejectMessage(e.target.value);
            }}
            className="w-full"
            placeholder="Type your reason to reject this document"
          />
          <Button
            type="primary"
            htmlType="submit"
            onClick={() => {
              updateUserDocument(
                "REJECTED",
                document?.[0]?.id || -1,
                rejectMessage || "Not Approved",
                `${currentDocument?.name} Rejected`
              );
              setShowRejectModal(false);
            }}
            className="mt-3"
          >
            Submit
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default DocumentRejectModal;
