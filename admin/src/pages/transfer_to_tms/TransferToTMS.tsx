import { Button, Space, Table, Tag } from "antd";
import ButtonGroup from "antd/es/button/button-group";
import Search from "antd/es/input/Search";
import Paragraph from "antd/es/typography/Paragraph";
import dayjs from "dayjs";
import isEmpty from "is-empty";
import { useCallback, useState } from "react";
import { BsListOl } from "react-icons/bs";
import { MdNotInterested } from "react-icons/md";
import { RiSendPlane2Line } from "react-icons/ri";
import { Outlet, useNavigate, useSearchParams } from "react-router-dom";
import { useGetUserForTttQuery } from "../../__generated__";
import ErrorComponent from "../../components/error/Error";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import { debounce } from "../../utils/utils";

const TransferToTMS = () => {
  //Hooks
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [selectedStatus, setSelectedStatus] = useState();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  //graphql queries
  const {
    data: userData,
    loading,
    error
  } = useGetUserForTttQuery({
    variables: {
      search: searchParams.get("search") || undefined,
      filter: {
        transfer_to_tms_status:
          searchParams.get("transfer_to_tms_status") === "true"
            ? true
            : searchParams.get("transfer_to_tms_status") === "false"
            ? false
            : undefined
      },
      pagination: {
        take: pageSize,
        skip: currentPage - 1
      }
    },
    fetchPolicy: "cache-and-network"
  });

  // Calculate the total number of data pages based on search results
  const totalDataPages = Math.ceil(userData?.getUserForTTT?.total_count || 0 / pageSize);

  //Handle Debounce Search
  const debounceSearch = useCallback(
    debounce((val: string, status: string) => {
      setCurrentPage(1);
      if (!isEmpty(status)) {
        setSearchParams({ search: val, transfer_to_tms_status: status });
      } else {
        setSearchParams({ search: val });
      }
    }, 500),
    []
  );

  const handleStatusChange = (status: any) => {
    const searched_text = searchParams.get("search");
    setCurrentPage(1);
    setSelectedStatus(status);
    if (!isEmpty(searched_text)) {
      setSearchParams({ transfer_to_tms_status: status, search: searched_text || "" });
    } else {
      setSearchParams({ transfer_to_tms_status: status });
    }
  };

  const handleCurrentPage = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  if (error) {
    return <ErrorComponent error={error} />;
  }

  return (
    <ProtectedRoute disableChildAuth parent_url="/dashboard/transfer_to_tms">
      {({ permissions }) => (
        <>
          <div>
            <div className="text-xl mt-2 flex items-center p-2 mb-3">
              <RiSendPlane2Line className=" text-purple-500 text-3xl  mr-4" />{" "}
              <span className="font-semibold">Transfer to TMS</span>
            </div>
            <Space direction="horizontal">
              <Search
                allowClear
                placeholder="Search by name, email, phone"
                defaultValue={searchParams.get("search") as string}
                onChange={(e) => {
                  const status = searchParams.get("transfer_to_tms_status");
                  debounceSearch(e.target.value, status);
                }}
              />
              <ButtonGroup>
                <Button
                  className={`flex items-center ${
                    selectedStatus === "" ? "ant-btn-primary" : "ant-btn-default"
                  }`}
                  onClick={() => handleStatusChange("")}
                >
                  <BsListOl className="text-blue-500 mr-2" />
                  All
                </Button>
                <Button
                  className={`flex items-center ${
                    selectedStatus === "true" ? "ant-btn-primary" : "ant-btn-default"
                  }`}
                  onClick={() => handleStatusChange("true")}
                >
                  <RiSendPlane2Line className="text-green-500 mr-2" /> Transferred
                </Button>
                <Button
                  className={`flex items-center ${
                    selectedStatus === "false" ? "ant-btn-primary" : "ant-btn-default"
                  }`}
                  onClick={() => handleStatusChange("false")}
                >
                  <MdNotInterested className="text-red-500 mr-2" /> Not Transferred
                </Button>
              </ButtonGroup>
            </Space>
          </div>
          <Table
            loading={loading}
            pagination={{
              current: currentPage,
              pageSize,
              onChange: (page, pageSize) => {
                handleCurrentPage(page, pageSize);
              },
              total: totalDataPages || 1
            }}
            dataSource={userData?.getUserForTTT?.data as Array<any>}
            columns={[
              {
                title: "Name",
                width: 150,
                fixed: "left",
                dataIndex: "name",
                key: "name",
                align: "center",
                render: (data: any, item: any) => {
                  return <p> {item.name}</p>;
                }
              },
              {
                title: "Phone",
                width: 150,
                dataIndex: "phone",
                key: "phone",
                align: "center"
              },
              {
                title: "Email",
                width: 150,
                dataIndex: "email",
                key: "email",
                align: "center",
                render: (data: any, item: any) => {
                  return <p> {item.email || "-"}</p>;
                }
              },
              {
                title: "Hiring Criteria",
                width: 150,
                dataIndex: "hiring_criteria",
                key: "hiring_criteria",
                align: "center",
                render: (_: any, item: any) => {
                  return (
                    <div className="capitalize">
                      {item.user_onboarding_data?.[0]?.hiring_criteria
                        ? item.user_onboarding_data?.[0]?.hiring_criteria
                            .toLowerCase()
                            .replace(/_/g, " ")
                        : "-"}
                    </div>
                  );
                }
              },
              {
                title: "Designation",
                width: 150,
                dataIndex: "designation",
                key: "designation",
                align: "center",
                render: (_: any, item: any) => {
                  return <>{item?.designation?.name || "-"} </>;
                }
              },
              {
                title: "Salary Offered",
                width: 150,
                dataIndex: "salary_offered",
                key: "salary_offered",
                align: "center",
                render: (_: any, item: any) => {
                  return <>{item.user_onboarding_data?.[0]?.salary_offered || "-"} </>;
                }
              },
              {
                title: "Transferred To TMS ",
                width: 150,
                dataIndex: "transferred_to_tms",
                key: "transferred_to_tms",
                align: "center",
                render: (_: any, item: any) => {
                  return (
                    <Tag color={item.transfer_to_tms_status ? "green" : "red"}>
                      {item.transfer_to_tms_status === true ? "Yes" : "No"}
                    </Tag>
                  );
                }
              },
              {
                title: "Transferred Date",
                width: 150,
                dataIndex: "transfer_to_tms_date",
                key: "transfer_to_tms_date",
                render: (_: any, item: any) => {
                  return (
                    <p>
                      {item.user_onboarding_data?.[0]?.meta?.transfer_to_tms_date
                        ? dayjs(item.user_onboarding_data[0]?.meta?.transfer_to_tms_date).format(
                            "MMM DD, YYYY"
                          )
                        : "-"}
                    </p>
                  );
                }
              },
              {
                title: "Assessed By",
                width: 150,
                dataIndex: "assessed_by",
                key: "assessed_by",
                align: "center",
                render: (_: any, item: any) => {
                  return <>{item.user_onboarding_data?.[0]?.assessed_by || "-"} </>;
                }
              },
              {
                title: "Date Of Joining",
                width: 150,
                dataIndex: "date_of_joining",
                key: "date_of_joining",
                align: "center",
                render: (_: any, item: any) => {
                  const date =
                    item &&
                    item.user_onboarding_data &&
                    item.user_onboarding_data[0] &&
                    item.user_onboarding_data[0].date_of_joining
                      ? new Date(item.user_onboarding_data[0].date_of_joining).toLocaleDateString()
                      : null;
                  return <>{date ? date.toString() : "-"} </>;
                }
              },
              {
                title: "Other Remark",
                width: 150,
                dataIndex: "remark",
                key: "remark",
                align: "center",
                render: (_: any, item: any) => {
                  return <>{item.user_onboarding_data?.[0]?.remark || "-"} </>;
                }
              },
              {
                title: "Address As Per Aadhar",
                width: 150,
                dataIndex: "aadhar_address",
                key: "aadhar_address",
                align: "center",
                render: (_: any, item: any) => {
                  return (
                    <Paragraph ellipsis={{ rows: 2, expandable: true, symbol: "more" }}>
                      {item.user_onboarding_data?.[0]?.aadhar_address || "-"}{" "}
                    </Paragraph>
                  );
                }
              },
              {
                title: "Employee Id",
                width: 150,
                dataIndex: "employee_id",
                key: "employee_id",
                align: "center",
                render: (_: any, item: any) => {
                  return <>{item.user_onboarding_data?.[0]?.employee_id || "-"} </>;
                }
              },
              {
                title: "User Bank Details ",
                width: 150,
                dataIndex: "bank_details",
                align: "center",
                children: [
                  {
                    title: "Bank Name",
                    width: 150,
                    dataIndex: "bank_name",
                    key: "bank_name",
                    render: (_: any, item: any) => {
                      return <>{item.bank_details?.bank_name || "-"} </>;
                    }
                  },
                  {
                    title: "Account Number",
                    width: 150,
                    dataIndex: "account_number",
                    key: "account_number",
                    render: (_: any, item: any) => {
                      return <>{item.bank_details?.account_number || "-"} </>;
                    }
                  },
                  {
                    title: "IFSC Code",
                    width: 150,
                    dataIndex: "ifsc",
                    key: "ifsc",
                    render: (_: any, item: any) => {
                      return <>{item.bank_details?.ifsc || "-"} </>;
                    }
                  }
                ]
              },
              {
                title: "Action",
                width: 175,
                key: "id",
                fixed: "right",
                dataIndex: "id",
                align: "center",
                render: (value: number) => (
                  <div>
                    <Button
                      disabled={!permissions.update}
                      type="primary"
                      onClick={() =>
                        navigate(`/dashboard/transfer_to_tms/add_technician?tab=1&user_id=${value}`)
                      }
                    >
                      View/Update
                    </Button>
                  </div>
                )
              }
            ]}
            size="small"
            bordered
            scroll={{ x: "calc(700px + 50%)" }}
          />
          <Outlet />
        </>
      )}
    </ProtectedRoute>
  );
};

export default TransferToTMS;
