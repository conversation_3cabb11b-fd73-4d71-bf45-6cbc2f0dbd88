import { Segmented } from "antd";
import { useState } from "react";
import { LuClipboardList } from "react-icons/lu";
import { MdOutlineDashboard } from "react-icons/md";
import { RiSendPlane2Line } from "react-icons/ri";
import UpcomingFeatureBadge from "../../../../components/UpcomingFeatureBadge";
import OnboardingView from "./OnboardingView";
import PerformanceView from "./PerformanceView";

type View = "Onboarding" | "Live" | "Exit";

const TechnicianGeneralDetails = ({
  setActiveKey,
  isTransferred
}: {
  setActiveKey: any;
  isTransferred: boolean;
}) => {
  const [view, setView] = useState<View>("Onboarding");

  const views = [
    {
      label: (
        <div
          className={`flex gap-x-2 min-w-[100px]  justify-center w-full items-center px-1 py-2 rounded-md text-sm font-medium  `}
        >
          <LuClipboardList size={16} />
          <span>Onboarding</span>
        </div>
      ),
      value: "Onboarding"
    },
    {
      label: (
        <div
          className={`flex gap-x-2 min-w-[100px]  justify-center w-full items-center px-1 py-2 rounded-md text-sm font-medium`}
        >
          <MdOutlineDashboard size={16} />
          <span>Live</span>
        </div>
      ),
      value: "Live",
      disabled: !isTransferred
    },
    {
      label: (
        <div
          className={`flex gap-x-2 min-w-[100px]  justify-center w-full items-center px-1 py-2 rounded-md text-sm font-medium`}
        >
          <RiSendPlane2Line size={16} />
          <span>Exit</span>
        </div>
      ),
      value: "Exit",
      disabled: true
    }
  ];
  const renderCurrentView = (view: View) => {
    switch (view) {
      case "Onboarding":
        return <OnboardingView setActiveKey={setActiveKey} />;
      case "Live":
        return <PerformanceView />;
      case "Exit":
        return <></>;
      default:
        return null;
    }
  };

  return (
    <div className=" w-full ">
      {" "}
      <div className="flex w-full items-center justify-between">
        <Segmented
          height={46}
          className="custom-technician-segment"
          value={view}
          onChange={(val) => setView(val as View)}
          options={views}
        />
        {view === "Live" && (
          <div className="mr-6">
            <UpcomingFeatureBadge className={"h-[30px] w-[150px] gap-x-2"} />
          </div>
        )}
      </div>
      <div className="flex  my-2 overflow-y-scroll max-h-[calc(100vh-420px)]">
        {renderCurrentView(view)}
      </div>
    </div>
  );
};

export default TechnicianGeneralDetails;
