import { useLazyQuery } from "@apollo/client";
import { notification } from "antd";
import Checkbox from "antd/es/checkbox/Checkbox";
import Modal from "antd/es/modal/Modal";
import { useEffect, useState } from "react";
import ErrorComponent from "../error/Error";
import Loading from "../loading/Loading";
import { downloadXLSXFromJson } from "../../utils/XlsxDataDownloader";

/**
 * @param open  Send True to open the modal
 * @param close Send False to close the modal
 * @param query GraphQL query that returns array of objects.
 * @param variable GraphQL variable to send the query
 * @param queryName The Query result name like 'getUserData' or 'data'
 */

interface InputModal {
  open: boolean;
  close: any;
  query: any;
  variable: any;
  queryName: string;
  fileName: string;
  sheetName: string;
}
const ExportModal: React.FC<InputModal> = ({
  open,
  close,
  query,
  variable,
  queryName,
  fileName,
  sheetName
}) => {
  //Hooks
  const [exportData, setExportData] = useState<any>([]);
  const [checklist, setChecklist] = useState([]);
  const [finalChecklist, setFinalChecklist] = useState<any>([]);

  //Graphql query
  const [runQuery, { loading, error }] = useLazyQuery(query, {
    onCompleted(data) {
      try {
        setExportData(data?.[queryName]);
        const checkList: any = [];
        //Takes first object and saves in array to show available export check list
        for (const [key] of Object.entries(data?.[queryName][0])) {
          switch (key) {
            case "id":
              break;
            case "__typename":
              break;
            default:
              checkList.push(key);
          }
        }
        setChecklist(checkList);
      } catch (error) {
        console.error(error);
      }
    },
    onError: (err) => {
      console.error(err);
      notification.error({
        message: "There was some issue during fetching the data"
      });
    }
  });
  //Handle Check List
  const handleChecklist = (event: any) => {
    try {
      if (event.target.checked) {
        setFinalChecklist([...finalChecklist, event.target.value]);
      }
      if (!event.target.checked) {
        setFinalChecklist(finalChecklist.filter((item: any) => item !== event.target.value));
      }
    } catch (error) {
      notification.error({ message: "Something went wrong!" });
    }
  };

  //Handle Download
  const handleDownload = () => {
    try {
      let id = 1;
      const result = exportData.map((obj: any) => {
        const newObj: any = {};
        newObj.id = id;
        id++;
        finalChecklist.forEach((item: any) => {
          if (Object.prototype.hasOwnProperty.call(obj, item)) {
            if (Array.isArray(obj[item])) {
              newObj[item] = JSON.stringify(obj[item]);
            } else if (typeof obj[item] === "object") {
              newObj[item] = JSON.stringify(obj[item]);
            } else {
              newObj[item] = obj[item];
            }
          }
        });
        return newObj;
      });
      downloadXLSXFromJson(result || [], formatDate() + "_" + fileName + ".xlsx", sheetName);
      close(!true); //Close Modal
    } catch (error) {
      notification.error({ message: "Something went wrong!" });
      close(!true);
    }
  };

  //Today date in format like 2-feb-2023
  const formatDate = () => {
    const date = new Date();
    const options: any = {
      day: "2-digit",
      month: "short",
      year: "numeric"
    };
    const formattedDate = date.toLocaleDateString("en-US", options);
    const result = formattedDate.replace(/,/g, "").replace(/\s/g, "_");
    return result;
  };
  useEffect(() => {
    if (open) {
      runQuery({ variables: variable ? variable : {} });
    }
  }, [open, loading, finalChecklist]);

  //Handle Loading
  if (loading) {
    return <Loading tip="Loading..." />;
  }
  //Handle Error
  if (error) {
    return <ErrorComponent error={error} />;
  }
  return (
    <Modal
      title={"Export Data "}
      open={open}
      onCancel={() => close(false)}
      okText={"Download"}
      onOk={() => handleDownload()}
    >
      {checklist &&
        checklist.map((el: any, index: any) => {
          return (
            <>
              <Checkbox value={el} id={index} name={el} onChange={(e: any) => handleChecklist(e)} />
              {el}
            </>
          );
        })}
    </Modal>
  );
};
export default ExportModal;
