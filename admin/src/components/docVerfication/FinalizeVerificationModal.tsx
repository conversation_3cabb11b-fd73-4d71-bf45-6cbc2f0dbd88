import { Button, Modal, Space } from "antd";
import ButtonGroup from "antd/es/button/button-group";
import { useNavigate } from "react-router-dom";

interface FinalizeVerificationModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

const FinalizeVerificationModal: React.FC<FinalizeVerificationModalProps> = ({ open, setOpen }) => {
  const navigate = useNavigate();

  return (
    <Modal footer={null} open={open} onCancel={() => setOpen(false)}>
      <div className="flex flex-col items-center">
        <img width={120} src="/images/check.png" alt="tick" />
        <div className="text-2xl pt-6">Document verification completed</div>
        <div className="mt-6">
          <Space>
            <ButtonGroup>
              <Button
                onClick={() => {
                  navigate("/dashboard/interviews");
                }}
                type="primary"
              >
                Schedule interview
              </Button>
              <Button
                onClick={() => {
                  navigate("/dashboard/documents?verification_status=");
                }}
              >
                Go to users
              </Button>
            </ButtonGroup>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default FinalizeVerificationModal;
