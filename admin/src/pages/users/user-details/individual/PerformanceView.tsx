import { useEffect, useRef, useState } from "react";
import {
  LuBriefcase,
  LuBuilding2,
  LuCheckCircle,
  LuClock,
  LuGraduationCap,
  LuMapPin,
  LuStar,
  LuTrophy,
  LuWrench,
  LuX,
  LuXCircle
} from "react-icons/lu";

interface LocationHistory {
  hub: string;
  period: string;
  status: "current" | "past";
}

interface SkillJobStats {
  skillName: string;
  completed: number;
  cancelled: number;
  totalHours: number;
}

interface JobStats {
  total: number;
  completed: number;
  cancelled: number;
  totalHours: number;
  completionRate: number;
  bySkill: SkillJobStats[];
}

interface RatingBreakdown {
  total: number;
  average: number;
  distribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

interface SkillRating {
  skillName: string;
  ratings: RatingBreakdown;
}

interface RatingStats {
  overall: RatingBreakdown;
  bySkill: SkillRating[];
}

interface EmploymentHistory {
  type: "CONTRACTOR" | "IN_HOUSE";
  startDate: string;
  endDate?: string;
  duration: string;
  company: string;
}

interface ExperienceDetails {
  totalExperience: {
    years: number;
    months: number;
  };
  startDate: string;
  employmentHistory: EmploymentHistory[];
}

interface TrainingResult {
  id: number;
  name: string;
  completedAt: string;
  score: number;
  status: "PASS" | "NEED_MORE_TRAINING" | "FAIL";
  feedback?: string;
}

interface TrainingStats {
  total: number;
  completed: number;
  passed: number;
  needsImprovement: number;
  byYear: {
    year: number;
    trainings: TrainingResult[];
  }[];
}

interface StatDetails {
  id: string;
  title: string;
  icon: typeof LuMapPin;
  value: string;
  subtext: string;
  details?: {
    label: string;
    value: string;
  }[];
  locationHistory?: LocationHistory[];
  jobStats?: JobStats;
  ratingStats?: RatingStats;
  experienceDetails?: ExperienceDetails;
  trainingStats?: TrainingStats;
}

const jobStats: JobStats = {
  total: 48,
  completed: 47,
  cancelled: 1,
  totalHours: 127.5,
  completionRate: 98,
  bySkill: [
    {
      skillName: "Modular Kitchen & Wardrobe",
      completed: 25,
      cancelled: 1,
      totalHours: 68.5
    },
    {
      skillName: "Carpentry",
      completed: 15,
      cancelled: 0,
      totalHours: 42.0
    },
    {
      skillName: "Interior Design",
      completed: 7,
      cancelled: 0,
      totalHours: 17.0
    }
  ]
};

const ratingStats: RatingStats = {
  overall: {
    total: 42,
    average: 4.8,
    distribution: {
      5: 35,
      4: 5,
      3: 2,
      2: 0,
      1: 0
    }
  },
  bySkill: [
    {
      skillName: "Modular Kitchen & Wardrobe",
      ratings: {
        total: 25,
        average: 4.9,
        distribution: {
          5: 23,
          4: 2,
          3: 0,
          2: 0,
          1: 0
        }
      }
    },
    {
      skillName: "Carpentry",
      ratings: {
        total: 12,
        average: 4.7,
        distribution: {
          5: 9,
          4: 2,
          3: 1,
          2: 0,
          1: 0
        }
      }
    },
    {
      skillName: "Interior Design",
      ratings: {
        total: 5,
        average: 4.6,
        distribution: {
          5: 3,
          4: 1,
          3: 1,
          2: 0,
          1: 0
        }
      }
    }
  ]
};

const experienceDetails: ExperienceDetails = {
  totalExperience: {
    years: 5,
    months: 3
  },
  startDate: "2019-09-15",
  employmentHistory: [
    {
      type: "IN_HOUSE",
      startDate: "2022-03-01",
      company: "Current Company",
      duration: "2 years, 1 month"
    },
    {
      type: "CONTRACTOR",
      startDate: "2019-09-15",
      endDate: "2022-02-28",
      company: "Previous Agency",
      duration: "2 years, 5 months"
    }
  ]
};

const trainingStats: TrainingStats = {
  total: 12,
  completed: 12,
  passed: 10,
  needsImprovement: 2,
  byYear: [
    {
      year: 2024,
      trainings: [
        {
          id: 1,
          name: "Advanced MKW Installation",
          completedAt: "2024-12-24",
          score: 92,
          status: "PASS",
          feedback: "Excellent understanding of complex installations"
        },
        {
          id: 2,
          name: "Customer Service Excellence",
          completedAt: "2024-12-15",
          score: 78,
          status: "NEED_MORE_TRAINING",
          feedback: "Needs improvement in handling customer objections"
        }
      ]
    },
    {
      year: 2023,
      trainings: [
        {
          id: 3,
          name: "Basic Carpentry Skills",
          completedAt: "2023-06-15",
          score: 95,
          status: "PASS"
        },
        {
          id: 4,
          name: "Safety Standards & Protocols",
          completedAt: "2023-03-10",
          score: 88,
          status: "PASS"
        }
      ]
    }
  ]
};

const stats: StatDetails[] = [
  {
    id: "location",
    title: "Current Location",
    icon: LuMapPin,
    value: "Gurugram",
    subtext: "Active since Dec 2024",
    locationHistory: [
      {
        hub: "West BLR Hub",
        period: "Jan 2024 - Present",
        status: "current"
      },
      {
        hub: "South BLR Hub",
        period: "Jun 2023 - Dec 2023",
        status: "past"
      },
      {
        hub: "East BLR Hub",
        period: "Jan 2023 - May 2023",
        status: "past"
      }
    ],
    details: [
      { label: "Current Hub", value: "West BLR Hub" },
      { label: "Service Area", value: "Gurugram, Sector 1-50" },
      { label: "Preferred Work Radius", value: "15 km" },
      { label: "Registration Date", value: "December 24, 2024" }
    ]
  },
  {
    id: "jobs",
    title: "Total Jobs",
    icon: LuBriefcase,
    value: `${jobStats.completed} Completed`,
    subtext: `${jobStats.completionRate}% completion rate`,
    jobStats,
    details: [
      { label: "Total Jobs Assigned", value: jobStats.total.toString() },
      { label: "Total Hours Worked", value: `${jobStats.totalHours} hours` },
      { label: "Average Time per Job", value: "2.5 hours" },
      { label: "Last Job Completed", value: "Dec 24, 2024" }
    ]
  },
  {
    id: "rating",
    title: "Average Rating",
    icon: LuStar,
    value: `${ratingStats.overall.average}/5`,
    subtext: `Based on ${ratingStats.overall.total} reviews`,
    ratingStats
  },
  {
    id: "experience",
    title: "Experience Level",
    icon: LuTrophy,
    value: "Senior Technician",
    subtext: "5 years, 3 months experience",
    experienceDetails,
    details: [
      { label: "Total Experience", value: "5 years, 3 months" },
      { label: "Started Career", value: "September 15, 2019" },
      { label: "Certifications", value: "3 Active" },
      { label: "Training Hours", value: "240" }
    ]
  },
  {
    id: "skills",
    title: "Skills",
    icon: LuWrench,
    value: "3 Skills",
    subtext: "Modular Kitchen & Wardrobe, Carpentry, Interior Design",
    details: [
      { label: "Primary Skill", value: "Modular Kitchen & Wardrobe" },
      { label: "Secondary Skills", value: "Carpentry, Interior Design" },
      { label: "Skill Assessment Score", value: "92%" },
      { label: "Last Assessment", value: "Dec 24, 2024" }
    ]
  },
  {
    id: "training",
    title: "Training History",
    icon: LuGraduationCap,
    value: `${trainingStats.passed} Completed`,
    subtext: `${trainingStats.total} total trainings`,
    trainingStats,
    details: [
      { label: "Total Trainings", value: trainingStats.total.toString() },
      {
        label: "Pass Rate",
        value: `${((trainingStats.passed / trainingStats.total) * 100).toFixed(0)}%`
      },
      { label: "Latest Training", value: "Advanced MKW Installation" },
      { label: "Next Training", value: "Scheduled for Jan 2025" }
    ]
  }
];

const renderJobStats = (stats: JobStats, selectedStat: StatDetails) => (
  <div className="space-y-6">
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700">Overall Completion Rate</span>
        <span className="text-sm font-bold text-green-600">{stats.completionRate}%</span>
      </div>
      <div className="w-full  bg-gray-200 rounded-full h-2.5">
        <div
          className=" h-2.5 rounded-full transition-all duration-500 bg-green-500"
          style={{ width: `${stats.completionRate}%` }}
        />
      </div>
    </div>

    <div className="space-y-4">
      <h3 className="text-sm font-medium text-gray-900">Jobs by Skill</h3>
      {stats.bySkill.map((skillStat, index) => (
        <div key={index} className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <span className="font-medium text-gray-900">{skillStat.skillName}</span>
            <span className="text-sm text-gray-500">{skillStat.totalHours} hours</span>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-green-50 rounded-lg p-3">
              <div className="flex items-center mb-1">
                <LuCheckCircle className="h-4 w-4 text-green-500 mr-2" />
                <span className="text-sm font-medium text-green-700">Completed</span>
              </div>
              <span className="text-xl font-bold text-green-700">{skillStat.completed}</span>
            </div>
            <div className="bg-red-50 rounded-lg p-3">
              <div className="flex items-center mb-1">
                <LuXCircle className="h-4 w-4 text-red-500 mr-2" />
                <span className="text-sm font-medium text-red-700">Cancelled</span>
              </div>
              <span className="text-xl font-bold text-red-700">{skillStat.cancelled}</span>
            </div>
          </div>
        </div>
      ))}
    </div>

    <div className="space-y-4">
      {selectedStat?.details?.map((detail, index) => (
        <div key={index} className="border-b border-gray-100 pb-4 last:border-0">
          <div className="text-sm text-gray-500">{detail.label}</div>
          <div className="text-base font-medium text-gray-900">{detail.value}</div>
        </div>
      ))}
    </div>
  </div>
);

const renderRatingStats = (stats: RatingStats) => {
  const renderStarDistribution = (distribution: RatingBreakdown["distribution"], total: number) => (
    <div className="space-y-2">
      {[5, 4, 3, 2, 1].map((stars) => (
        <div key={stars} className="flex items-center gap-2">
          <div className="flex items-center w-20">
            <span className="text-sm font-medium text-gray-700">{stars}</span>
            <LuStar className="h-4 w-4 text-yellow-400 ml-1" />
          </div>
          <div className="flex-1">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-yellow-400 h-2 rounded-full transition-all duration-500"
                style={{
                  width: `${(distribution[stars as keyof typeof distribution] / total) * 100}%`
                }}
              />
            </div>
          </div>
          <div className="w-16 text-right">
            <span className="text-sm text-gray-600">
              {distribution[stars as keyof typeof distribution]}
            </span>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="space-y-8">
      <div
        style={{
          background: "linear-gradient(to bottom right, #fefce8, #fff7ed)"
        }}
        className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg p-6"
      >
        <div className="flex items-center justify-between mb-4">
          <div className="text-lg font-semibold text-gray-900">Overall Rating</div>
          <div className="flex items-center">
            <span className="text-3xl font-bold text-yellow-600">{stats.overall.average}</span>
            <span className="text-lg text-gray-500 ml-1">/5</span>
          </div>
        </div>
        {renderStarDistribution(stats.overall.distribution, stats.overall.total)}
      </div>

      <div className="space-y-6">
        <div className="text-lg font-semibold text-gray-900">Ratings by Skill</div>
        {stats.bySkill.map((skillRating, index) => (
          <div key={index} className="bg-white rounded-lg border border-solid border-gray-100 p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="font-medium text-gray-900">{skillRating.skillName}</h4>
                <p className="text-sm text-gray-500">{skillRating.ratings.total} reviews</p>
              </div>
              <div className="flex items-center">
                <span className="text-2xl font-bold text-yellow-600">
                  {skillRating.ratings.average}
                </span>
                <span className="text-gray-500 ml-1">/5</span>
              </div>
            </div>
            {renderStarDistribution(skillRating.ratings.distribution, skillRating.ratings.total)}
          </div>
        ))}
      </div>
    </div>
  );
};

const renderExperienceDetails = (details: ExperienceDetails) => (
  <div className="space-y-8">
    <div className="text-lg font-medium text-gray-900">Employment History</div>
    <div className="relative">
      <div className="absolute top-0 bottom-0 left-[7px] w-0.5 bg-gray-200"></div>
      <div className="space-y-8">
        {details.employmentHistory.map((employment, index) => (
          <div key={index} className="relative flex items-start">
            <div
              className={`absolute left-[-1] w-4 h-4 rounded-full border-2 transform -translate-x-1/2 ${
                employment.type === "IN_HOUSE"
                  ? "bg-green-100 border border-solid border-green-500"
                  : "bg-blue-100 border border-solid border-blue-500"
              }`}
            />
            <div className="ml-6">
              <div
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mb-2 ${
                  employment.type === "IN_HOUSE"
                    ? "bg-green-50 text-green-700"
                    : "bg-blue-50 text-blue-700"
                }`}
              >
                {employment.type === "IN_HOUSE" ? "In-House" : "Contractor"}
              </div>
              <div className="flex items-center text-sm font-medium text-gray-900">
                <LuBuilding2 className="h-4 w-4 text-gray-400 mr-2" />
                {employment.company}
              </div>
              <div className="mt-1 text-sm text-gray-500">
                {new Date(employment.startDate).toLocaleDateString("en-US", {
                  month: "long",
                  year: "numeric"
                })}
                {" - "}
                {employment.endDate
                  ? new Date(employment.endDate).toLocaleDateString("en-US", {
                      month: "long",
                      year: "numeric"
                    })
                  : "Present"}
                <span className="mx-2">•</span>
                {employment.duration}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

const renderTrainingStats = (stats: TrainingStats) => (
  <div className="space-y-8">
    <div className="grid grid-cols-2 gap-4">
      <div className="bg-green-50 rounded-lg p-4">
        <div className="flex items-center mb-1">
          <LuCheckCircle className="h-4 w-4 text-green-500 mr-2" />
          <span className="text-sm font-medium text-green-700">Passed</span>
        </div>
        <span className="text-2xl font-bold text-green-700">{stats.passed}</span>
      </div>
      <div className="bg-yellow-50 rounded-lg p-4">
        <div className="flex items-center mb-1">
          <LuClock className="h-4 w-4 text-yellow-500 mr-2" />
          <span className="text-sm font-medium text-yellow-700">Needs Improvement</span>
        </div>
        <span className="text-2xl font-bold text-yellow-700">{stats.needsImprovement}</span>
      </div>
    </div>

    <div className="space-y-6">
      {stats.byYear.map((yearData) => (
        <div key={yearData.year} className="space-y-4">
          <div className="text-lg font-medium text-gray-900">{yearData.year}</div>
          <div className="space-y-4">
            {yearData.trainings.map((training) => (
              <div key={training.id} className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <div className="font-medium text-gray-900">{training.name}</div>
                    <div className="text-sm text-gray-500">{training.completedAt}</div>
                  </div>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      training.status === "PASS"
                        ? "bg-green-100 text-green-800"
                        : training.status === "NEED_MORE_TRAINING"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {training.status === "PASS" ? "Passed" : "Needs Improvement"}
                  </span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Score</span>
                    <span className="font-medium text-gray-900">{training.score}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        training.score >= 90
                          ? "bg-green-500"
                          : training.score >= 80
                          ? "bg-green-400"
                          : training.score >= 70
                          ? "bg-yellow-500"
                          : "bg-red-500"
                      }`}
                      style={{ width: `${training.score}%` }}
                    />
                  </div>
                  {training.feedback && (
                    <div className="text-sm text-gray-600 mt-2">{training.feedback}</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  </div>
);

const renderLocationDetails = (details: StatDetails) => (
  <div className="space-y-6">
    <div className="bg-green-50 rounded-lg p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <LuBuilding2 className="h-5 w-5 text-green-600 mr-2" />
          <span className="text-sm font-medium text-green-900">Current Hub</span>
        </div>
        <span className="text-sm font-medium text-green-900">West BLR Hub</span>
      </div>
    </div>

    <div className="grid grid-cols-2 gap-4">
      {details.details?.map((detail, index) => (
        <div key={index} className="bg-gray-50 rounded-lg p-4">
          <div className="text-sm text-gray-500">{detail.label}</div>
          <div className="text-base font-medium text-gray-900 mt-1">{detail.value}</div>
        </div>
      ))}
    </div>

    {details.locationHistory && (
      <div className="mt-8">
        <h4 className="text-sm font-medium text-gray-900 mb-4">Hub History</h4>
        <div className="space-y-4">
          {details.locationHistory.map((location, index) => (
            <div
              key={index}
              className={`flex items-start p-3 rounded-lg ${
                location.status === "current" ? "bg-green-50" : "bg-gray-50"
              }`}
            >
              <LuClock
                className={`h-5 w-5 mt-0.5 ${
                  location.status === "current" ? "text-green-600" : "text-gray-500"
                }`}
              />
              <div className="ml-3">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-900">{location.hub}</span>
                  {location.status === "current" && (
                    <span className="text-xs font-medium text-green-600 bg-green-100 px-2 py-0.5 rounded-full">
                      Current
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-500">{location.period}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}
  </div>
);

const PerformanceView = () => {
  const [selectedStat, setSelectedStat] = useState<StatDetails | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (selectedStat && scrollRef.current && scrollContainerRef.current) {
      const container = scrollContainerRef.current;

      container.scrollTo({
        top: 0,
        behavior: "smooth"
      });
    }
  }, [selectedStat]);
  return (
    <div
      id="performance-view-container "
      className="flex w-screen gap-6 overflow-y-scroll relative"
      ref={scrollContainerRef}
    >
      <div className="absolute inset-0 bg-gray-50 bg-opacity-50 backdrop-blur-sm z-10 pointer-events-none" />

      <div className="w-1/3 space-y-4 overflow-y-scroll custom-scroll-live-view">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div
              key={stat.id}
              onClick={() => {
                setSelectedStat(stat);
              }}
              className={`bg-white rounded-lg h-[116px] p-6 cursor-pointer transition-all duration-200 ${
                selectedStat?.id === stat.id
                  ? "ring-2 ring-indigo-500 shadow-lg border-2 border-solid border-indigo-500"
                  : "hover:shadow-md"
              }`}
            >
              <div className="flex items-center">
                <div className="p-2 bg-indigo-50 rounded-lg">
                  <Icon className="h-6 w-6 text-indigo-600" />
                </div>
                <div className="ml-4 max-w-[90%]">
                  <div className="text-sm font-medium text-gray-500">{stat.title}</div>
                  <div className="text-lg font-semibold text-gray-900">{stat.value}</div>
                  <div className="text-sm whitespace-nowrap overflow-hidden truncate  text-gray-500">
                    {stat.subtext}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="w-2/3 overflow-y-scroll custom-scroll-live-view" ref={scrollRef}>
        {selectedStat ? (
          <div className="bg-white rounded-lg shadow-lg p-6 sticky top-4 ">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="p-2 bg-indigo-50 rounded-lg">
                  <selectedStat.icon className="h-6 w-6 text-indigo-600" />
                </div>
                <div className="ml-3 text-xl font-semibold text-gray-900">{selectedStat.title}</div>
              </div>

              <LuX
                className="h-5 w-5 text-gray-400 hover:text-gray-500"
                onClick={() => setSelectedStat(null)}
              />
            </div>

            {selectedStat.id === "location" ? (
              renderLocationDetails(selectedStat)
            ) : selectedStat.jobStats ? (
              renderJobStats(selectedStat.jobStats, selectedStat)
            ) : selectedStat.ratingStats ? (
              renderRatingStats(selectedStat.ratingStats)
            ) : selectedStat.experienceDetails ? (
              renderExperienceDetails(selectedStat.experienceDetails)
            ) : selectedStat.trainingStats ? (
              renderTrainingStats(selectedStat.trainingStats)
            ) : (
              <div className="space-y-4">
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-gray-900">{selectedStat.value}</h3>
                  <p className="text-gray-600">{selectedStat.subtext}</p>
                </div>

                {selectedStat.details?.map((detail, index) => (
                  <div
                    key={index}
                    className="border-0 border-solid border-b border-gray-100 pb-4 last:border-0"
                  >
                    <div className="text-sm text-gray-500">{detail.label}</div>
                    <div className="text-base font-medium text-gray-900">{detail.value}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="bg-gray-50 rounded-lg border-2 border-dashed border-gray-200 p-6 flex items-center justify-center h-full">
            <p className="text-gray-500 text-center">Select a card to view detailed information</p>
          </div>
        )}
      </div>
    </div>
  );
};
export default PerformanceView;
