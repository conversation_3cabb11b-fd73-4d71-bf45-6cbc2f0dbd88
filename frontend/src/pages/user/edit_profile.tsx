import { EditIcon } from "@chakra-ui/icons";
import {
  Box,
  Button,
  Divider,
  FormControl,
  FormLabel,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Tag,
  Text,
  useDisclosure,
  useToast
} from "@chakra-ui/react";
import mixpanel from "mixpanel-browser";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { IoIosArrowForward } from "react-icons/io";
import { useNavigate } from "react-router-dom";
import {
  UserDetailsDocument,
  useUpdateUserMutation,
  useUserDetailsLazyQuery,
  useUserDetailsQuery
} from "../../__generated__";
import Ripple from "../../components/Ripple";
import { FilePicker } from "../../utils/FilePicker";
import uploadFileToS3 from "../../utils/s3Upload";
import Skills from "../technician/profile/skills";

const getInitials = (name: string) => {
  let initials: any = name.match(/\b\w/g) || [];
  initials = ((initials.shift() || "") + (initials.pop() || "")).toUpperCase();
  return initials;
};

interface ProfilePicUploadProps {
  profilePic?: string | null;
  name: string;
}

const ProfilePicUpload: React.FC<ProfilePicUploadProps> = ({ profilePic, name }) => {
  const [updateUserDetails] = useUpdateUserMutation();

  const toast = useToast();

  const { t } = useTranslation();

  const handleFileUpload = async () => {
    try {
      const result = await FilePicker.pickFiles({
        types: ["image/png", "image/jpeg", "image/jpg", "image/svg", "image/webp"],
        multiple: false,
        readData: true
      });

      const url = await uploadFileToS3(result, toast);

      updateUserDetails({
        variables: {
          data: {
            photoUrl: url
          }
        },
        onCompleted: () => {
          toast({
            title: t("error_profile_updatd"),
            description: t("error_profile_updatd_descp"),
            status: "success",
            duration: 2000,
            isClosable: true,
            position: "top"
          });
        },
        refetchQueries: [UserDetailsDocument]
      });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Box className="py-5 rounded-3xl flex flex-col items-center">
      {profilePic ? (
        <Box className="relative w-full h-40">
          <Box
            style={{
              backgroundImage: `url(${profilePic})`
            }}
            className="animate-pulse absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 blur-lg w-36 h-36 text-5xl flex justify-center items-center rounded-3xl bg-cover bg-center"
          ></Box>
          <Box
            onClick={handleFileUpload}
            style={{
              backgroundImage: `url(${profilePic})`
            }}
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-32 h-32 text-5xl flex justify-center items-center rounded-3xl bg-cover bg-center"
          ></Box>
        </Box>
      ) : (
        <Box
          onClick={handleFileUpload}
          className="w-32 h-32 bg-blue-500/40 text-5xl flex justify-center items-center text-blue-500 rounded-3xl"
        >
          {getInitials(name)}
        </Box>
      )}
      <Text className="text-gray-400" mt={4}>
        {t("click_img")}
      </Text>
    </Box>
  );
};

interface EditFieldProps {
  label: string;
  value: string;
  onChange?: () => void;
  editable?: boolean;
}

const EditField: React.FC<EditFieldProps> = ({ value, label, onChange, editable = false }) => {
  return (
    <Box>
      <Box className="flex justify-between items-center px-2 py-1">
        <small>{label}</small>
        {editable && <EditIcon onClick={onChange} />}
      </Box>
      <Box className="w-full bg-white p-3 rounded-2xl">
        <Box>
          <Text>{value}</Text>
        </Box>
      </Box>
    </Box>
  );
};

enum EditModalType {
  TEXT,
  ADDRESS,
  EXPERTISE
}

const CustomScrollbar: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <div style={{ overflow: "auto", height: "500px" }}> {children}</div>;
};

const EditModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  type: EditModalType;
  editState: {
    db_label_name: string;
    lable: string;
    value: string;
  };
}> = ({ isOpen, onClose, editState, type }) => {
  const [updateUserDetails, { loading }] = useUpdateUserMutation();
  const { data: userDetails } = useUserDetailsQuery();
  const [userDetailsExp] = useUserDetailsLazyQuery({
    fetchPolicy: "no-cache"
  });
  const { t } = useTranslation();
  const [values, setValues] = useState(() => ({
    text: editState.value
  }));

  const userData = userDetails?.userDetail?.data;

  const toast = useToast();

  const handeTypeTextSave = () => {
    updateUserDetails({
      variables: {
        data: {
          [editState.db_label_name]: values.text
        }
      },
      onCompleted: () => {
        toast({
          title: t("error_profile_updatd"),
          description: t("error_profile_updatd_descp"),
          status: "success",
          duration: 2000,
          isClosable: true,
          position: "top"
        });
        onClose();
      },
      refetchQueries: [UserDetailsDocument]
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent m={3}>
        <ModalHeader>
          {t("profile_edit")} {editState.lable}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody style={{ overflow: "auto" }}>
          {type === EditModalType.TEXT && (
            <FormControl>
              <FormLabel>{editState.lable}</FormLabel>
              <Input
                value={values.text}
                onChange={(e) => setValues({ ...values, text: e.target.value })}
              />
            </FormControl>
          )}
          {type === EditModalType.EXPERTISE && (
            <CustomScrollbar>
              <Skills />
            </CustomScrollbar>
          )}
        </ModalBody>
        <ModalFooter>
          <Button mr={3} onClick={onClose}>
            {t("close")}
          </Button>
          <Button
            loadingText={t("saving")}
            isLoading={loading}
            onClick={async () => {
              if (type === EditModalType.TEXT) {
                handeTypeTextSave();
              }
              if (type === EditModalType.EXPERTISE) {
                onClose();
                if (type === EditModalType.EXPERTISE && userData?.expertise) {
                  await userDetailsExp({
                    onCompleted(data) {
                      mixpanel.track("user_skill", {
                        Skills: data.userDetail?.data?.expertise?.map((skill) => skill?.name || "")
                      });
                    }
                  });
                }
              }
            }}
            colorScheme="blue"
            variant="solid"
          >
            {t("save")}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

const EditProfile: React.FC = () => {
  const [editState, setEditState] = useState({
    db_label_name: "",
    lable: "",
    value: "",
    type: EditModalType.TEXT
  });
  const { t } = useTranslation();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { data: userDetails } = useUserDetailsQuery();
  const userData = userDetails?.userDetail?.data;
  const navigate = useNavigate();

  return (
    <>
      <Box className="container">
        <Box className="overflow-scroll pb-24">
          <Text fontSize={"2xl"} py={5} fontWeight={"bold"}>
            {t("edit_profile")}
          </Text>
          <ProfilePicUpload
            name={userData?.name || ""}
            profilePic={userData?.photoUrl || undefined}
          />
          <Box className="text-xl pt-3 font-semibold py-2">{t("general_details")}</Box>
          <Divider mb={3} />
          <Box className="space-y-3">
            <EditField
              onChange={() => {
                setEditState({
                  db_label_name: "name",
                  lable: t("full_name"),
                  value: userData?.name || "",
                  type: EditModalType.TEXT
                });
                onOpen();
              }}
              label={t("full_name")}
              value={userData?.name || "not available"}
            />
            <EditField
              onChange={() => {
                setEditState({
                  db_label_name: "phone",
                  lable: t("phone_number"),
                  value: userData?.phone || "Not available",
                  type: EditModalType.TEXT
                });
                onOpen();
              }}
              label={t("phone_number")}
              value={userData?.phone || "not available"}
            />
            <EditField label={t("email")} value={userData?.email || "Not available"} />
            <EditField
              label={t("flat_no")}
              value={userData?.location?.landmark?.split(",")[0] || "not available"}
            />
            <EditField
              label={t("bldg_name")}
              value={
                userData?.location?.landmark?.split(",").slice(1).join(", ") || "not available"
              }
            />
            <EditField
              label={t("address")}
              value={userData?.location?.work_address || "not available"}
            />
            <EditField label={t("gender")} value={userData?.gender || "not available"} />
            <Box className="flex justify-between">
              <small className="text-xs pl-2">{t("expertise")}</small>
              <EditIcon
                onClick={() => {
                  setEditState({
                    db_label_name: "expertise",
                    lable: t("expertise"),
                    value: "",
                    type: EditModalType.EXPERTISE
                  });
                  onOpen();
                }}
              />
            </Box>
            {userData?.expertise?.length === 0 && (
              <Box className="bg-white p-3 rounded-2xl text-gray-400 text-center">
                {t("no_skills_added")}{" "}
              </Box>
            )}
            <Box className="space-x-3">
              {userData?.expertise?.map((e) => <Tag colorScheme="blue">{e?.name}</Tag>)}
            </Box>
            <Box className="text-xl pt-3 font-semibold py-2">{t("others")}</Box>
            <Divider mb={3} />
            <Ripple
              className="p-3 w-full bg-blue-500/10 rounded-lg flex justify-between items-center cursor-pointer"
              onClick={() => {
                navigate("/post_onboarding_docs");
              }}
            >
              <Text className="text-xl text-black">{t("documents")}</Text>
              <IoIosArrowForward className={"text-black "} size={24} />
            </Ripple>
            <Ripple
              className="p-3 w-full bg-blue-500/10 rounded-lg flex justify-between items-center cursor-pointer"
              onClick={() => {
                navigate("/interview");
              }}
            >
              <Text className="text-xl text-black">{t("interview")}</Text>
              <IoIosArrowForward className={"text-black"} size={24} />
            </Ripple>
            <Ripple
              className="p-3 w-full bg-blue-500/10 rounded-lg flex justify-between items-center cursor-pointer"
              onClick={() => {
                navigate("/assessment");
              }}
            >
              <Text className="text-xl text-black">{t("assessment")}</Text>
              <IoIosArrowForward className={"text-black"} size={24} />
            </Ripple>
          </Box>
          <EditModal
            type={editState.type}
            isOpen={isOpen}
            onClose={onClose}
            editState={editState}
          />
        </Box>
      </Box>
    </>
  );
};
export default EditProfile;
