import { Capacitor } from "@capacitor/core";
import { Device } from "@capacitor/device";
import { Preferences } from "@capacitor/preferences";
import { useCallback, useEffect, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import {
  AnalyticsType,
  AppInstallationStatus,
  AppPlatformType,
  UserFunnelStage,
  useTrackAnalyticsMutation
} from "../__generated__";
import { getLocalStorageValue } from "../utils/auth";
import Storage from "../utils/Storage";

export const useAnalytics = () => {
  const [trackAnalytics] = useTrackAnalyticsMutation();

  // Create a ref to store recent analytics calls to prevent duplicates
  const recentCallsRef = useRef<Map<string, number>>(new Map());

  // Store the IP address once retrieved to avoid multiple requests
  const ipAddressRef = useRef<string | null>(null);
  const ipAddressInitializedRef = useRef<boolean>(false);

  const initializeIpAddress = async () => {
    // Skip if already initialized
    if (ipAddressInitializedRef.current) {
      return;
    }

    try {
      // First try to get IP from sessionStorage
      const storedIp = sessionStorage.getItem("analytics_ip_address");
      if (storedIp) {
        ipAddressRef.current = storedIp;
        ipAddressInitializedRef.current = true;
        return;
      }

      const response = await fetch("https://api.ipify.org?format=json");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      if (!data.ip) {
        throw new Error("No IP address in response");
      }

      ipAddressRef.current = data.ip;
      ipAddressInitializedRef.current = true;
      // Store IP in sessionStorage
      sessionStorage.setItem("analytics_ip_address", data.ip);
      return data.ip;
    } catch (error) {
      console.error("Failed to initialize IP address:", error);
    }
  };

  // Function to check if a similar call was recently made
  const shouldSkipDuplicateCall = (callType: string, key: string, timeWindow: number = 2000) => {
    const now = Date.now();
    const callId = `${callType}:${key}`;
    const lastCallTime = recentCallsRef.current.get(callId);

    if (lastCallTime && now - lastCallTime < timeWindow) {
      return true;
    }

    // Record this call
    recentCallsRef.current.set(callId, now);

    // Clean up old entries
    recentCallsRef.current.forEach((timestamp, id) => {
      if (now - timestamp > 10000) {
        // Remove entries older than 10 seconds
        recentCallsRef.current.delete(id);
      }
    });

    return false;
  };

  // Function to get the user's IP address
  const getIpAddress = useCallback(async (): Promise<string | null> => {
    // Return cached IP if already retrieved
    if (ipAddressRef.current) {
      return ipAddressRef.current;
    }
    // Try to get from sessionStorage if not in ref
    const storedIp = sessionStorage.getItem("analytics_ip_address");
    if (storedIp) {
      ipAddressRef.current = storedIp;
      return storedIp;
    } else {
      const ip = await initializeIpAddress();
      return ip;
    }
  }, []);

  // Determine current platform
  const getPlatformType = useCallback((): AppPlatformType => {
    const platform = Capacitor.getPlatform();

    switch (platform) {
      case "android":
        return AppPlatformType.Android;
      case "web":
      default:
        return AppPlatformType.Web;
    }
  }, []);

  // Get or create session ID
  const getSessionId = useCallback(async () => {
    if (typeof window === "undefined") return uuidv4();

    const existingSessionId = sessionStorage.getItem("analytics_session_id");
    if (existingSessionId) return existingSessionId;

    const newSessionId = uuidv4();
    sessionStorage.setItem("analytics_session_id", newSessionId);
    return newSessionId;
  }, []);

  // Get or create device ID
  const getDeviceId = useCallback(async () => {
    if (Capacitor.getPlatform() === "android") {
      try {
        const { identifier } = await Device.getId();
        if (identifier) {
          await Preferences.set({ key: "device_id", value: identifier });
          return identifier;
        }
      } catch (error) {
        console.error("Failed to get device ID:", error);
      }
    } else {
      // Use existing code for non-Android platforms or if device ID fails
      const deviceId = await localStorage.getItem("device_id");
      if (deviceId) return deviceId;
      const newDeviceId = uuidv4();
      localStorage.setItem("device_id", newDeviceId);
      return newDeviceId;
    }
  }, []);

  // Get device info with Capacitor platform details
  const getDeviceInfo = useCallback(async () => {
    if (typeof window === "undefined") return {};

    const platform = Capacitor.getPlatform();
    const isNative = platform !== "web";

    if (isNative) {
      const info = await Device.getInfo();
      return {
        browser: navigator.userAgent,
        language: await getAppLanguage(),
        screenResolution: `${window.screen.width}x${window.screen.height}`,
        colorDepth: window.screen.colorDepth,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        hardwareConcurrency: navigator.hardwareConcurrency,
        deviceMemory: (navigator as any).deviceMemory,
        pixelRatio: window.devicePixelRatio,
        touchSupport: "ontouchstart" in window,
        cookiesEnabled: navigator.cookieEnabled,
        isNative,
        nativePlatform: isNative ? platform : undefined,
        ...info
      };
    } else {
      return {
        browser: navigator.userAgent,
        platform,
        operatingSystem: navigator.platform,
        language: await getAppLanguage(),
        screenResolution: `${window.screen.width}x${window.screen.height}`,
        colorDepth: window.screen.colorDepth,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        hardwareConcurrency: navigator.hardwareConcurrency,
        deviceMemory: (navigator as any).deviceMemory,
        pixelRatio: window.devicePixelRatio,
        touchSupport: "ontouchstart" in window,
        cookiesEnabled: navigator.cookieEnabled,
        isNative,
        nativePlatform: isNative ? platform : undefined
      };
    }
  }, []);

  const getAppLanguage = useCallback(async () => {
    if (Capacitor.getPlatform() === "android") {
      const { value } = await Preferences.get({ key: "user_app_language" });
      if (value === "en") {
        return "ENGLISH";
      } else if (value === "hi") {
        return "HINDI";
      }
    }
    return "ENGLISH";
  }, []);

  // Track user action with Apollo mutation
  const trackUserAction = useCallback(
    async (action: string, meta?: any) => {
      // Skip if this action was recently tracked to prevent duplicates
      if (shouldSkipDuplicateCall("action", action)) return;

      try {
        // Use cached IP address
        const ipAddress = ipAddressRef.current;
        const sessionId = await getSessionId();
        const deviceId = await getDeviceId();

        // Log the full input being sent to the backend for debugging
        const input = {
          type: AnalyticsType.UserActivity,
          activityType: action,
          sessionId,
          deviceId,
          platformType: await getPlatformType(),
          language: await getAppLanguage(),
          startTime: new Date().toISOString(),
          deviceInfo: await getDeviceInfo(),
          ipAddress,
          userAgent: navigator.userAgent,
          meta: {
            action,
            platform: Capacitor.getPlatform(),
            ...meta
          }
        };
        await trackAnalytics({
          variables: {
            input
          }
        });
      } catch (error: any) {
        console.error(`Failed to track user action ${action}:`, error);
      }
    },
    [trackAnalytics, getSessionId, getDeviceId, getDeviceInfo, getPlatformType, getIpAddress]
  );

  const trackSignup = useCallback(async () => {
    try {
      // Use cached IP address
      const ipAddress = await getIpAddress();
      const sessionId = await getSessionId();
      const deviceInfo = await getDeviceInfo();
      const deviceId = await getDeviceId();
      const platformType = await getPlatformType();
      const language = await getAppLanguage();
      const funnelStage = UserFunnelStage.SignupComplete;
      const startTime = new Date().toISOString();
      const userAgent = navigator.userAgent;

      await trackAnalytics({
        variables: {
          input: {
            type: AnalyticsType.AppAnalytics,
            activityType: "SIGN_UP",
            funnelStage,
            sessionId,
            userAgent,
            deviceId,
            platformType,
            language,
            startTime,
            ipAddress,
            deviceInfo
          }
        }
      });
    } catch (error) {
      console.log({});
    }
  }, []);

  const trackLogin = useCallback(async () => {
    try {
      // Use cached IP address
      const ipAddress = ipAddressRef.current;
      const sessionId = await getSessionId();
      const deviceId = await getDeviceId();
      const deviceInfo = await getDeviceInfo();
      const platformType = await getPlatformType();
      const language = await getAppLanguage();
      const funnelStage = UserFunnelStage.ActiveUser;
      const startTime = new Date().toISOString();
      const userAgent = navigator.userAgent;

      await trackAnalytics({
        variables: {
          input: {
            type: AnalyticsType.AppAnalytics,
            activityType: "LOGIN",
            funnelStage,
            sessionId,
            deviceId,
            platformType,
            language,
            startTime,
            ipAddress,
            deviceInfo,
            userAgent,
            meta: {
              platform: Capacitor.getPlatform()
            }
          }
        }
      });
    } catch (error) {
      console.error(`Failed to track login for user`, error);
    }
  }, []);

  const trackLogout = useCallback(async () => {
    try {
      // Use cached IP address - important to call before cleanup
      const ipAddress = ipAddressRef.current;
      const sessionId = await getSessionId();
      const deviceId = await getDeviceId();

      await trackAnalytics({
        variables: {
          input: {
            type: AnalyticsType.UserActivity,
            activityType: "LOGOUT",
            funnelStage: UserFunnelStage.Visitor,
            sessionId,
            deviceId,
            platformType: getPlatformType(),
            language: await getAppLanguage(),
            startTime: new Date().toISOString(),
            deviceInfo: await getDeviceInfo(),
            ipAddress,
            userAgent: navigator.userAgent,
            meta: {
              platform: Capacitor.getPlatform()
            }
          }
        }
      });
    } catch (error) {
      console.error(`Failed to track logout for user `, error);
    }
  }, [
    trackAnalytics,
    getSessionId,
    getDeviceId,
    getDeviceInfo,
    getPlatformType,
    getIpAddress,
    getAppLanguage
  ]);

  const trackAppOpen = useCallback(async () => {
    try {
      // Check if we've already tracked an app open today
      const lastOpenTracked = await Storage.get("last_app_open_tracked");
      const today = new Date().toISOString().split("T")[0]; // Get current date in YYYY-MM-DD format

      if (lastOpenTracked === today) {
        // Already tracked today, skip
        return;
      }

      const ipAddress = await getIpAddress();
      const sessionId = await getSessionId();
      const deviceId = await getDeviceId();
      const deviceInfo = await getDeviceInfo();
      const platformType = await getPlatformType();
      const language = await getAppLanguage();
      const startTime = new Date().toISOString();
      const userAgent = navigator.userAgent;

      await trackAnalytics({
        variables: {
          input: {
            type: AnalyticsType.AppAnalytics,
            activityType: "APP_INSTALL",
            sessionId,
            deviceId,
            platformType,
            language,
            startTime,
            ipAddress,
            deviceInfo,
            userAgent,
            meta: {
              platform: Capacitor.getPlatform()
            }
          }
        }
      });

      // Store today's date as the last time we tracked an app open
      Storage.set("last_app_open_tracked", today);
    } catch (error) {
      console.error(`Failed to track app open:`, error);
    }
  }, [
    trackAnalytics,
    getSessionId,
    getDeviceId,
    getDeviceInfo,
    getPlatformType,
    getIpAddress,
    getAppLanguage
  ]);

  const trackAppInstall = useCallback(
    async (additionalDeviceInfo: any) => {
      const isAppInstalled = await getLocalStorageValue("app_first_visit");
      if (isAppInstalled === "true") {
        return;
      }
      try {
        const ipAddress = await getIpAddress();
        const deviceInfo = await getDeviceInfo();
        const sessionId = await getSessionId();
        const deviceId = await getDeviceId();

        const input = {
          type: AnalyticsType.AppAnalytics,
          deviceId,
          installationStatus: AppInstallationStatus.Installed,
          sessionId,
          platformType: getPlatformType(),
          language: await getAppLanguage(),
          ipAddress,
          funnelStage: UserFunnelStage.LoginPage,
          activityType: "APP_INSTALL",
          deviceInfo: {
            ...deviceInfo,
            ...additionalDeviceInfo,
            platform: Capacitor.getPlatform()
          }
        };

        // Resolve the Promise for ipAddress before passing to the API
        const result = await trackAnalytics({
          variables: {
            input
          }
        });

        return result;
      } catch (error: any) {
        console.error(`Failed to track app installation on ${Capacitor.getPlatform()}:`, error);
        // Check for error details
        if (error.graphQLErrors) {
          console.error("GraphQL Errors:", error.graphQLErrors);
        }
        if (error.networkError) {
          console.error("Network Error:", error.networkError);
          // Check if network error contains response data
          if (error.networkError.result) {
            console.error("Network Error Response:", error.networkError.result);
          }
        }
        return { error: error.message || "Unknown error tracking app installation" };
      }
    },
    [trackAnalytics, getSessionId, getDeviceId, getDeviceInfo, getPlatformType, getIpAddress]
  );

  // Initialize IP address when component mounts
  useEffect(() => {
    initializeIpAddress();
  }, []); // Empty dependency array means this runs once on mount

  //Enable this when we have a way to track page visits
  // // Track page visit when route changes
  // useEffect(() => {
  //   let isTracking = false;

  //   const trackPageVisit = async () => {
  //     // Skip if already tracking or if this page was recently tracked
  //     if (isTracking || shouldSkipDuplicateCall("page-visit", location.pathname)) return;

  //     try {
  //       isTracking = true;
  //       const funnelStage = getFunnelStageFromPath(location.pathname);

  //       // Use cached IP address
  //       const ipAddress = ipAddressRef.current;
  //       const sessionId = await getSessionId();
  //       const deviceId = await getDeviceId();

  //       await trackAnalytics({
  //         variables: {
  //           input: {
  //             type: AnalyticsType.UserActivity,
  //             activityType: "PAGE_VISIT",
  //             urlPath: location.pathname,
  //             funnelStage,
  //             sessionId,
  //             deviceId,
  //             platformType: getPlatformType(),
  //             language: await getAppLanguage(),
  //             startTime: new Date().toISOString(),
  //             deviceInfo: await getDeviceInfo(),
  //             ipAddress,
  //             userAgent: navigator.userAgent,
  //             meta: {
  //               page: location.pathname,
  //               funnelStage,
  //               platform: Capacitor.getPlatform()
  //             }
  //           }
  //         }
  //       });
  //     } catch (error) {
  //       console.error(`Failed to track page visit for ${location.pathname}:`, error);
  //     } finally {
  //       isTracking = false;
  //     }
  //   };

  //   // Add a small delay to ensure we're not tracking during rapid navigation
  //   const timeoutId = setTimeout(trackPageVisit, 100);

  //   return () => {
  //     clearTimeout(timeoutId);
  //     // Clean up any pending tracking for this path
  //     const callId = `page-visit:${location.pathname}`;
  //     recentCallsRef.current.delete(callId);
  //   };
  // }, [location.pathname]);

  return {
    trackUserAction,
    trackSignup,
    trackLogin,
    trackLogout,
    trackAppInstall,
    trackAppOpen
  };
};
