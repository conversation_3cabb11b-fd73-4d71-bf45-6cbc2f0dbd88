import { App } from "@capacitor/app";
import { Capacitor } from "@capacitor/core";
import { ChevronRightIcon } from "@chakra-ui/icons";
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  CircularProgress,
  CircularProgressLabel,
  Icon,
  Text,
  useDisclosure
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import React, { useEffect, useState } from "react";
import { FaCheck } from "react-icons/fa";
import { FaCircleCheck, FaCircleExclamation } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import {
  AssignmentStatus,
  AssignmentUserType,
  DocVerificationStatus,
  MascotSectionType,
  useGetAllAssignmentsOfUserQuery,
  useGetServiceProviderOnboardingAnalyticsQuery,
  useGetTeamMemberInvitationsQuery,
  useGetUserMascotAlertQuery,
  useUpdateUserMascotAlertMutation,
  useUserDetailsQuery
} from "../../__generated__";
import MiddleDrawerModalNotification from "../../components/MiddleDrawerModalNotification";
import PageTransition from "../../components/PageTransition";
import ReferAndEarn from "../../components/referAndEarn/ReferAndEarn";
import SpMyTeamMembers from "../../components/serviceProvider/team_member/SpMyTeamMembers";
import SpMyTeamMembersCount from "../../components/serviceProvider/team_member/SpMyTeamMembersCount";
import TrainingBannerVideos from "../../components/training/TrainingBannerVideos";
import { getTopVideos, VideoInfo } from "../../lib/youtube";
import { updateUserMetaLocalStorage } from "../../utils/auth";
import { toTitleCase } from "../../utils/common";

interface AssessmentData {
  assessmentAssigned: number;
  assessmentAttempted: number;
}
const SpSelectionProcess: React.FC = () => {
  //Hooks
  const navigate = useNavigate();
  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });
  const [assessmentData, setAssessmentData] = useState<AssessmentData>({
    assessmentAssigned: 0,
    assessmentAttempted: 0
  });
  const [popular_videos_data, setPopularVideosData] = useState<VideoInfo[]>([]);

  //GraphQl Query
  const { data: mascotAlert, refetch } = useGetUserMascotAlertQuery({
    variables: {
      type: MascotSectionType.LaunchScreen
    },
    fetchPolicy: "no-cache"
  });
  useGetAllAssignmentsOfUserQuery({
    fetchPolicy: "no-cache",
    variables: {
      data: {
        assign_to: AssignmentUserType.ServiceProvider
      }
    },
    onCompleted(data) {
      const totalAssignmentLength =
        (data.getAllAssignmentsOfUser && data.getAllAssignmentsOfUser?.length) || 0;
      let completedAssignment = 0;
      data.getAllAssignmentsOfUser?.map((assignment) => {
        if (
          assignment?.assignment &&
          assignment?.assignment[0]?.status === AssignmentStatus.Completed
        ) {
          completedAssignment = completedAssignment + 1;
        }
        //DOUBT:why count ongoing test as complete this shows wrong status
        // if (
        //   assignment?.assignment &&
        //   assignment?.assignment[0]?.status === AssignmentStatus.OnGoing
        // ) {
        //   completedAssignment = completedAssignment + 1;
        // }
      });

      setAssessmentData({
        assessmentAssigned: totalAssignmentLength,
        assessmentAttempted: completedAssignment
      });
    }
  });
  const { data: teamMemberInvitations } = useGetTeamMemberInvitationsQuery({
    fetchPolicy: "no-cache"
  });

  const { data: onboardingAnalytics, loading: onboardingAnalyticsLoading } =
    useGetServiceProviderOnboardingAnalyticsQuery({
      fetchPolicy: "network-only"
    });

  const { data: userDataResponse, loading: userDataLoading } = useUserDetailsQuery();

  //GraphQl Mutation
  const [updateUserMascotAlert] = useUpdateUserMascotAlertMutation();

  //Constants
  const mascotGraphql = mascotAlert?.getUserMascotAlert;
  const userData = userDataResponse?.userDetail?.data;
  const onboardingAnalyticsData = onboardingAnalytics?.getServiceProviderOnboardingAnalytics;
  const isOrgOwner = userData?.id === onboardingAnalyticsData?.organization?.org_owner_id;

  const handleMascotOnClose = async () => {
    updateUserMascotAlert({
      variables: {
        updateUserMascotAlertId: mascotGraphql?.id || "-1"
      }
    });
    onClose();
    refetch();
  };

  const fetchPopularYoutubeVideosList = async () => {
    const list = await getTopVideos();
    setPopularVideosData(list);
  };
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    if (!isOrgOwner) {
      //this function should only be called if the User is Team Member
      fetchPopularYoutubeVideosList();
    }
  }, [userDataLoading, onboardingAnalyticsLoading]);

  //disable Capacitor back button
  useEffect(() => {
    if (Capacitor.getPlatform() === "android") {
      App.addListener("backButton", () => {
        //Do nothing
      });
    }

    // Cleanup listener when component unmounts
    return () => {
      if (Capacitor.getPlatform() === "android") {
        App.removeAllListeners();
      }
    };
  }, []);
  return (
    <PageTransition>
      <Box p={2} style={{ userSelect: "none" }}>
        <div className="pl-3">
          <Box className="text-gray-500 text-xs font-medium ">
            {isOrgOwner ? "Next steps to become " : "Welcome,"}
          </Box>
          <Box className="font-bold text-lg ">
            {isOrgOwner ? "Wify service provider" : userData?.name}
          </Box>
        </div>
        {/* TODO: Add user?.user_onboarded later */}
        {!userData?.onboarded ? (
          <>
            <Box className="mt-4 bg-white p-2 rounded-xl shadow-md">
              {/* Section 1 */}
              <div className="m-2 ">
                <div className="text-sm font-semibold css-0">Complete your profile,</div>
                <div className="text-sm font-semibold css-0">assessment and documents</div>
              </div>
              <motion.div
                className="border border-gray-300 rounded-2xl mb-4 cursor-pointer"
                whileTap="clicked"
                initial="rest"
                animate="rest"
                variants={{
                  rest: { scale: 1, opacity: 1 },
                  clicked: { scale: 0.9, opacity: 0.8 }
                }}
                onClick={async () => {
                  await updateUserMetaLocalStorage({
                    key: "last_location",
                    value: "/sp/home/<USER>"
                  });
                  navigate("/sp/home/<USER>");
                }}
              >
                <Box className="flex justify-end rounded-lg">
                  <Box
                    className={`${
                      onboardingAnalyticsData?.profileCompletion?.status === "Completed"
                        ? "text-green-400 bg-green-100 border-green-400"
                        : "text-[#DD2F2F] bg-red-100 border-[#DD2F2F]"
                    } text-sm text-center border border-1 rounded-se-2xl rounded-es-2xl py-1 px-2 flex h-6 justify-center items-center`}
                  >
                    <Box className="mr-1">
                      {onboardingAnalyticsData?.profileCompletion?.status === "Completed" ? (
                        <FaCircleCheck size={16} />
                      ) : (
                        <FaCircleExclamation size={16} />
                      )}
                    </Box>
                    <Box
                      className={`${
                        onboardingAnalyticsData?.profileCompletion?.status === "Completed"
                          ? "text-green-400"
                          : "text-[#DD2F2F]"
                      }`}
                    >
                      {onboardingAnalyticsData?.profileCompletion?.status}
                    </Box>
                  </Box>
                </Box>
                <Box className="flex items-center justify-between mb-2">
                  <Box display="flex" alignItems="center" gap={4} className="p-2">
                    <Box position="relative" display="inline-flex">
                      <CircularProgress
                        value={onboardingAnalyticsData?.profileCompletion?.percentage || 70}
                        color={"#00D34F"}
                        size="60px"
                        thickness="8px"
                        trackColor="gray.200"
                      />
                      <Box
                        position="absolute"
                        top="50%"
                        left="50%"
                        transform="translate(-50%, -50%)"
                        fontWeight="bold"
                        fontSize="sm"
                        className={`${"text-green-500"}`}
                      >
                        {onboardingAnalyticsData?.profileCompletion?.percentage || 70}%
                      </Box>
                    </Box>
                    <Box>
                      <Text fontWeight="semibold">
                        {isOrgOwner ? "Complete your company profile" : "Complete your profile"}
                      </Text>
                    </Box>
                  </Box>
                </Box>
              </motion.div>

              {/* Section 2 */}
              <motion.div
                className="border border-gray-300 rounded-2xl mb-4 cursor-pointer"
                whileTap="clicked"
                initial="rest"
                animate="rest"
                variants={{
                  rest: { scale: 1, opacity: 1 },
                  clicked: { scale: 0.9, opacity: 0.8 }
                }}
                onClick={async () => {
                  await updateUserMetaLocalStorage({
                    key: "last_location",
                    value: "/sp/home/<USER>"
                  });
                  navigate("/sp/home/<USER>");
                }}
              >
                <Box className="flex justify-end rounded-lg">
                  <Box
                    className={`${
                      onboardingAnalyticsData?.documentUpload?.status ===
                      DocVerificationStatus.Completed
                        ? "text-green-400 bg-green-100 border-green-400"
                        : "text-[#DD2F2F] bg-red-100 border-[#DD2F2F]"
                    } text-sm text-center border border-1 rounded-se-2xl rounded-es-2xl py-1 px-2 flex h-6 justify-center items-center`}
                  >
                    <Box className="mr-1">
                      {onboardingAnalyticsData?.documentUpload?.status ===
                      DocVerificationStatus.Completed ? (
                        <FaCircleCheck size={16} />
                      ) : (
                        <FaCircleExclamation size={16} />
                      )}
                    </Box>
                    <Box
                      className={`${
                        onboardingAnalyticsData?.documentUpload?.status ===
                        DocVerificationStatus.Completed
                          ? "text-green-400"
                          : "text-[#DD2F2F]"
                      }`}
                    >
                      {toTitleCase(onboardingAnalyticsData?.documentUpload?.status || "")}
                    </Box>
                  </Box>
                </Box>
                <Box className="flex items-center justify-between mb-2">
                  <Box display="flex" alignItems="center" gap={4} className="p-2">
                    <Box
                      className={`${
                        onboardingAnalyticsData?.documentUpload?.status ===
                        DocVerificationStatus.Completed
                          ? ""
                          : "bg-gray-100 border-gray-300 w-11 h-12 ml-2 rounded-lg flex justify-center items-center border border-1"
                      }`}
                    >
                      {onboardingAnalyticsData?.documentUpload?.status ===
                      DocVerificationStatus.Completed ? (
                        <Box className="flex items-center justify-between mb-2">
                          <Box position="relative" display="inline-flex">
                            <CircularProgress
                              value={100}
                              color="#00D34F"
                              size="60px"
                              thickness="8px"
                              trackColor="gray.200"
                            />
                            <Box
                              position="absolute"
                              top="50%"
                              left="50%"
                              transform="translate(-50%, -50%)"
                              fontWeight="bold"
                              fontSize="sm"
                              className="p-4 text-green-500"
                            >
                              <FaCheck size={20} />
                            </Box>
                          </Box>
                        </Box>
                      ) : (
                        <>
                          <span className="text-2xl font-semibold">
                            {onboardingAnalyticsData?.documentUpload?.uploaded_count}
                          </span>
                          /<span>{onboardingAnalyticsData?.documentUpload?.total}</span>
                        </>
                      )}
                    </Box>

                    <Box>
                      <Text className="ml-1" fontWeight="semibold">
                        Upload required documents
                      </Text>
                    </Box>
                  </Box>
                </Box>
              </motion.div>

              {/* Section 3 */}
              <motion.div
                className="border border-gray-300 rounded-2xl mb-4 cursor-pointer"
                whileTap="clicked"
                initial="rest"
                animate="rest"
                variants={{
                  rest: { scale: 1, opacity: 1 },
                  clicked: { scale: 0.9, opacity: 0.8 }
                }}
                onClick={() => {
                  updateUserMetaLocalStorage({
                    key: "last_location",
                    value: "/sp/assessment"
                  });
                  navigate("/sp/assessment");
                }}
              >
                <Box className="flex justify-end rounded-lg">
                  <Box
                    className={`${
                      assessmentData?.assessmentAssigned !== 0 &&
                      assessmentData?.assessmentAttempted !== 0 &&
                      assessmentData?.assessmentAssigned === assessmentData?.assessmentAttempted
                        ? "text-green-400 bg-green-100 border-green-400"
                        : "text-[#DD2F2F] bg-red-100 border-[#DD2F2F]"
                    } text-sm text-center border border-1 rounded-se-2xl rounded-es-2xl py-1 px-2 flex h-6 justify-center items-center`}
                  >
                    <Box className="mr-1">
                      {assessmentData?.assessmentAssigned !== 0 &&
                      assessmentData?.assessmentAttempted !== 0 &&
                      assessmentData?.assessmentAssigned === assessmentData?.assessmentAttempted ? (
                        <FaCircleCheck size={16} />
                      ) : (
                        <FaCircleExclamation size={16} />
                      )}
                    </Box>
                    <Box className="mr-1">
                      {assessmentData?.assessmentAssigned !== 0 &&
                      assessmentData?.assessmentAttempted !== 0 &&
                      assessmentData?.assessmentAssigned === assessmentData?.assessmentAttempted
                        ? "Completed"
                        : "Test Pending"}
                    </Box>
                  </Box>
                </Box>
                <Box display="flex" alignItems="center" gap={4} className="p-2 mb-2">
                  {assessmentData?.assessmentAssigned !== 0 &&
                  assessmentData?.assessmentAttempted !== 0 &&
                  assessmentData?.assessmentAssigned === assessmentData?.assessmentAttempted ? (
                    <Box position="relative" display="inline-flex">
                      <CircularProgress
                        value={100}
                        color="#00D34F"
                        size="60px"
                        thickness="8px"
                        trackColor="gray.200"
                      />
                      <Box
                        position="absolute"
                        top="50%"
                        left="50%"
                        transform="translate(-50%, -50%)"
                        fontWeight="bold"
                        fontSize="sm"
                        className="p-4 text-green-500"
                      >
                        <FaCheck size={20} />
                      </Box>
                    </Box>
                  ) : (
                    <div>
                      <Box className="bg-gray-100 w-11 h-12 ml-2 rounded-lg flex justify-center items-center border border-1 border-gray-300">
                        <span className="text-2xl font-semibold">
                          {assessmentData?.assessmentAttempted}
                        </span>
                        /<span>{assessmentData?.assessmentAssigned}</span>
                      </Box>
                    </div>
                  )}
                  <Box>
                    <Text fontWeight="semibold">Complete your joining test</Text>
                  </Box>
                </Box>
              </motion.div>
            </Box>

            {isOrgOwner ? (
              <Box p={2} mt={6} bg="white" borderRadius="md" boxShadow="sm">
                <span className="font-semibold ml-2">Now add your team members</span>
                <Box className="border border-gray-300 rounded-2xl mb-4 mt-4 opacity-50 cursor-not-allowed">
                  <Box className="flex items-center justify-between ">
                    <Box display="flex" alignItems="center" gap={4} className="p-2">
                      <Box className="bg-gray-100 w-14 h-16 rounded-lg flex justify-center items-center border border-1 border-gray-300">
                        <span className="text-2xl font-semibold">
                          {teamMemberInvitations?.getTeamMemberInvitations?.length}
                        </span>
                      </Box>

                      <Box className="">
                        <Text fontWeight="semibold">Add your team members</Text>
                      </Box>
                    </Box>
                  </Box>
                </Box>

                {/* <Box className="p-4 border bg-gray-100 border-gray-300 rounded-2xl mb-4 mt-4 flex justify-center flex-col items-center opacity-50 cursor-not-allowed">
                  <Box className="mb-6">
                    <svg width="100" height="100" viewBox="0 0 200 200">
                      <image href="/real_estate.png" x="0" y="0" width="200" height="200" />
                    </svg>
                  </Box>
                  <Box className="flex flex-col items-center mb-4 font-semibold">
                    <Box>You are now our esteemed</Box>
                    <Box>Service Provider</Box>
                  </Box>
                  <Box className="text-sm">A job will be assigned to you shortly</Box>
                </Box> */}
              </Box>
            ) : (
              <>
                <Box className="mb-6 mt-6">
                  <ReferAndEarn userId={Number(userData?.id) || -1} />
                </Box>
                <TrainingBannerVideos
                  bannerName={"Popular Videos"}
                  bannerVideos={popular_videos_data}
                  viewAll={() => {
                    navigate(`youtube/popular`);
                  }}
                />
              </>
            )}
          </>
        ) : (
          <div>
            <Box className="mt-4" style={{ userSelect: "none" }}>
              <Accordion allowToggle allowMultiple={false}>
                <AccordionItem className="border-none bg-white rounded-lg shadow">
                  <Box bg="white" borderRadius="md" boxShadow="sm">
                    <AccordionButton>
                      <Box flex="1" textAlign="left" fontWeight="semibold" p={2}>
                        <div className="flex gap-2  items-center relative  ">
                          <CircularProgress
                            value={onboardingAnalyticsData?.profileCompletion?.percentage || 70}
                            color="#00D34F"
                            size="35px"
                            thickness="6px"
                            className="mr-1"
                          >
                            <CircularProgressLabel>
                              <Box
                                position="absolute"
                                top="50%"
                                left="50%"
                                transform="translate(-50%, -50%)"
                                fontWeight="semibold"
                                fontSize="xs"
                                className="text-green-500"
                              >
                                {onboardingAnalyticsData?.profileCompletion?.percentage}%
                              </Box>
                            </CircularProgressLabel>
                          </CircularProgress>
                          {isOrgOwner ? "Your company profile " : "Your profile"}{" "}
                        </div>
                      </Box>
                      <AccordionIcon />
                    </AccordionButton>
                    <AccordionPanel>
                      {/* <Box className="mt-4 ">
                      <Box className="text-sm font-semibold ">Complete your Profile,</Box>
                      <Box className="text-sm font-semibold">Assessment and Documents</Box>
                    </Box> */}
                      <Box className="mt-4">
                        {/* Section 1 */}
                        <motion.div
                          className="border border-gray-300 rounded-2xl mb-2 cursor-pointer"
                          whileTap="clicked"
                          initial="rest"
                          animate="rest"
                          variants={{
                            rest: { scale: 1 },
                            clicked: { scale: 1.1 }
                          }}
                          onClick={async () => {
                            await updateUserMetaLocalStorage({
                              key: "last_location",
                              value: "/sp/home/<USER>"
                            });
                            navigate("/sp/home/<USER>");
                          }}
                        >
                          <Box className="flex justify-end rounded-lg">
                            <Box
                              className={`${
                                onboardingAnalyticsData?.profileCompletion?.status === "Completed"
                                  ? "text-green-400 bg-green-100 border-green-400"
                                  : "text-[#DD2F2F] bg-red-100 border-[#DD2F2F]"
                              } text-sm text-center border border-1 rounded-se-2xl rounded-es-2xl py-1 px-2 flex h-6 justify-center items-center`}
                            >
                              <Box className="mr-1">
                                {onboardingAnalyticsData?.profileCompletion?.status ===
                                "Completed" ? (
                                  <FaCircleCheck size={16} />
                                ) : (
                                  <FaCircleExclamation size={16} />
                                )}
                              </Box>
                              <Box
                                className={`${
                                  onboardingAnalyticsData?.profileCompletion?.status === "Completed"
                                    ? "text-green-400"
                                    : "text-[#DD2F2F]"
                                }`}
                              >
                                {onboardingAnalyticsData?.profileCompletion?.status}
                              </Box>
                            </Box>
                          </Box>

                          <Box className="flex items-center justify-between mb-2">
                            <Box display="flex" alignItems="center" gap={4} className="pb-3 ml-3">
                              <Box position="relative" display="inline-flex">
                                <CircularProgress
                                  value={
                                    onboardingAnalyticsData?.profileCompletion?.percentage || 70
                                  }
                                  color="#00D34F"
                                  size="50px"
                                  thickness="6px"
                                  trackColor="gray.200"
                                />
                                <Box
                                  position="absolute"
                                  top="50%"
                                  left="50%"
                                  transform="translate(-50%, -50%)"
                                  fontWeight="bold"
                                  fontSize="sm"
                                  className="p-4 text-green-500"
                                >
                                  {onboardingAnalyticsData?.profileCompletion?.percentage}%
                                </Box>
                              </Box>
                              <Box>
                                <Text fontWeight="semibold">
                                  {isOrgOwner
                                    ? "Complete your company profile"
                                    : "Complete your profile"}
                                </Text>
                              </Box>
                            </Box>
                          </Box>
                        </motion.div>

                        {/* Section 2 */}
                        <motion.div
                          className="border border-gray-300 rounded-2xl mb-4 cursor-pointer"
                          whileTap="clicked"
                          initial="rest"
                          animate="rest"
                          variants={{
                            rest: { scale: 1 },
                            clicked: { scale: 1.1 }
                          }}
                          onClick={async () => {
                            await updateUserMetaLocalStorage({
                              key: "last_location",
                              value: "/sp/home/<USER>"
                            });
                            navigate("/sp/home/<USER>");
                          }}
                        >
                          <Box className="flex justify-end rounded-lg">
                            <Box
                              className={`${
                                onboardingAnalyticsData?.documentUpload?.status ===
                                DocVerificationStatus.Completed
                                  ? "text-green-400 bg-green-100 border-green-400"
                                  : "text-[#DD2F2F] bg-red-100 border-[#DD2F2F]"
                              } text-sm text-center border border-1 rounded-se-2xl rounded-es-2xl py-1 px-2 flex h-6 justify-center items-center`}
                            >
                              <Box className="mr-1">
                                {onboardingAnalyticsData?.documentUpload?.status ===
                                DocVerificationStatus.Completed ? (
                                  <FaCircleCheck size={16} />
                                ) : (
                                  <FaCircleExclamation size={16} />
                                )}
                              </Box>
                              <Box
                                className={`${
                                  onboardingAnalyticsData?.documentUpload?.status ===
                                  DocVerificationStatus.Completed
                                    ? "text-green-400"
                                    : "text-[#DD2F2F]"
                                }`}
                              >
                                {toTitleCase(onboardingAnalyticsData?.documentUpload?.status || "")}
                              </Box>
                            </Box>
                          </Box>

                          <Box className="flex items-center justify-between mb-2">
                            <Box display="flex" alignItems="center" gap={4}>
                              <Box
                                className={`${
                                  onboardingAnalyticsData?.documentUpload?.status ===
                                  DocVerificationStatus.Completed
                                    ? ""
                                    : "bg-gray-100 border-gray-300 w-11 h-12 ml-4 mb-1 rounded-lg flex justify-center items-center border border-1"
                                }`}
                              >
                                {onboardingAnalyticsData?.documentUpload?.status ===
                                DocVerificationStatus.Completed ? (
                                  <Box className="flex items-center justify-between pb-2 ml-3">
                                    <Box position="relative" display="inline-flex">
                                      <CircularProgress
                                        value={100}
                                        color="#00D34F"
                                        size="50px"
                                        thickness="6px"
                                        trackColor="gray.200"
                                      />
                                      <Box
                                        position="absolute"
                                        top="50%"
                                        left="50%"
                                        transform="translate(-50%, -50%)"
                                        fontWeight="bold"
                                        fontSize="sm"
                                        className="p-4 text-green-500"
                                      >
                                        <FaCheck size={20} />
                                      </Box>
                                    </Box>
                                  </Box>
                                ) : (
                                  <>
                                    <span className="text-2xl font-semibold">
                                      {onboardingAnalyticsData?.documentUpload?.uploaded_count}
                                    </span>
                                    /<span>{onboardingAnalyticsData?.documentUpload?.total}</span>
                                  </>
                                )}
                              </Box>

                              <Box>
                                <Text className="ml-1" fontWeight="semibold">
                                  Upload required documents
                                </Text>
                              </Box>
                            </Box>
                          </Box>
                        </motion.div>
                        {/* Section 3 */}
                        <motion.div
                          className="border border-gray-300 rounded-2xl mb-4 cursor-pointer"
                          whileTap="clicked"
                          initial="rest"
                          animate="rest"
                          variants={{
                            rest: { scale: 1 },
                            clicked: { scale: 1.1 }
                          }}
                          onClick={() => {
                            updateUserMetaLocalStorage({
                              key: "last_location",
                              value: "/sp/assessment"
                            });
                            navigate("/sp/assessment");
                          }}
                        >
                          <Box className="flex justify-end rounded-lg">
                            <Box
                              className={`${
                                assessmentData?.assessmentAssigned != 0 &&
                                assessmentData?.assessmentAttempted != 0 &&
                                assessmentData?.assessmentAssigned ==
                                  assessmentData?.assessmentAttempted
                                  ? "text-green-400 bg-green-100 border-green-400"
                                  : "text-[#DD2F2F] bg-red-100 border-[#DD2F2F]"
                              } text-sm text-center border border-1 rounded-se-2xl rounded-es-2xl py-1 px-2 flex h-6 justify-center items-center`}
                            >
                              <Box className="mr-1">
                                {assessmentData?.assessmentAssigned != 0 &&
                                assessmentData?.assessmentAttempted != 0 &&
                                assessmentData?.assessmentAssigned ==
                                  assessmentData?.assessmentAttempted ? (
                                  <FaCircleCheck size={16} />
                                ) : (
                                  <FaCircleExclamation size={16} />
                                )}
                              </Box>
                              <Box className="mr-1">
                                {assessmentData?.assessmentAssigned != 0 &&
                                assessmentData?.assessmentAttempted != 0 &&
                                assessmentData?.assessmentAssigned ==
                                  assessmentData?.assessmentAttempted
                                  ? "Completed"
                                  : "Test Pending"}
                              </Box>
                            </Box>
                          </Box>

                          <Box display="flex" alignItems="center" gap={4} className="p-1 pb-3 ml-2">
                            {assessmentData?.assessmentAssigned != 0 &&
                            assessmentData?.assessmentAttempted != 0 &&
                            assessmentData?.assessmentAssigned ==
                              assessmentData?.assessmentAttempted ? (
                              <Box position="relative" display="inline-flex">
                                <CircularProgress
                                  value={100}
                                  color="#00D34F"
                                  size="50px"
                                  thickness="6px"
                                  trackColor="gray.200"
                                />
                                <Box
                                  position="absolute"
                                  top="50%"
                                  left="50%"
                                  transform="translate(-50%, -50%)"
                                  fontWeight="bold"
                                  fontSize="sm"
                                  className="p-4 text-green-500"
                                >
                                  <FaCheck size={20} />
                                </Box>
                              </Box>
                            ) : (
                              <div>
                                <Box className="bg-gray-100 w-11 h-12 ml-2 rounded-lg flex justify-center items-center border border-1 border-gray-300">
                                  <span className="text-2xl font-semibold">
                                    {assessmentData?.assessmentAttempted}
                                  </span>
                                  /<span>{assessmentData?.assessmentAssigned}</span>
                                </Box>
                              </div>
                            )}
                            <Box>
                              <Text fontWeight="semibold">Complete your joining test</Text>
                            </Box>
                          </Box>
                        </motion.div>
                      </Box>
                    </AccordionPanel>
                  </Box>
                </AccordionItem>
              </Accordion>

              {isOrgOwner ? (
                teamMemberInvitations?.getTeamMemberInvitations?.length === 0 ? (
                  <>
                    <Box p={2} mt={6} bg="white" borderRadius="md" boxShadow="sm">
                      <span className="font-semibold ml-2">Now add your team members</span>

                      <motion.div
                        className="border border-gray-300 rounded-2xl mb-4 mt-4 cursor-pointer bg-[#25A5ED] p-3"
                        whileTap="clicked"
                        initial="rest"
                        animate="rest"
                        variants={{
                          rest: { scale: 1 },
                          clicked: { scale: 1.1 }
                        }}
                      >
                        <Box className="flex items-center justify-between">
                          <Box display="flex" alignItems="center">
                            <Box className="bg-white w-12 h-10 rounded-lg flex justify-center items-center border border-1 border-gray-300">
                              <span className="text-2xl font-semibold">
                                {teamMemberInvitations?.getTeamMemberInvitations?.length}
                              </span>
                            </Box>
                            <Box
                              onClick={async () => {
                                await updateUserMetaLocalStorage({
                                  key: "last_location",
                                  value: "/sp/home/<USER>"
                                });
                                navigate("invite");
                              }}
                            >
                              <p className="font-semibold  text-white ml-2">
                                Add your team members
                              </p>
                            </Box>
                            <Box className="ml-4">
                              <Icon fontSize={"2xl"} color={"white"} as={ChevronRightIcon} />
                            </Box>
                          </Box>
                        </Box>
                      </motion.div>

                      <motion.div
                        className="p-4 border border-gray-300 rounded-2xl mb-4 mt-4 flex justify-center flex-col items-center cursor-pointer"
                        whileTap="clicked"
                        initial="rest"
                        animate="rest"
                        variants={{
                          rest: { scale: 1 },
                          clicked: { scale: 1.1 }
                        }}
                      >
                        <Box>
                          <svg width="100" height="100" viewBox="0 0 200 200">
                            <image href="/real_estate.png" width="150" height="150" />
                          </svg>
                        </Box>
                        <Box className="flex flex-col items-center mb-4 font-semibold">
                          <Box>You are now our esteemed</Box>
                          <Box>Service Provider</Box>
                        </Box>
                        <Box className="text-sm">A job will be assigned to you shortly</Box>
                      </motion.div>
                    </Box>
                  </>
                ) : (
                  <div className="space-y-4 mt-2">
                    <SpMyTeamMembersCount />
                    <SpMyTeamMembers />
                  </div>
                )
              ) : (
                <>
                  <Box className="mb-6 mt-6">
                    <ReferAndEarn userId={Number(userData?.id) || -1} />
                  </Box>
                  <TrainingBannerVideos
                    bannerName={"Popular Videos"}
                    bannerVideos={popular_videos_data}
                    viewAll={() => {
                      navigate(`youtube/popular`);
                    }}
                  />
                </>
              )}
            </Box>
          </div>
        )}
        {mascotGraphql && (
          <MiddleDrawerModalNotification
            button_text={mascotGraphql?.cta_link ? mascotGraphql?.cta_text || "Dekhiye" : ""}
            description={mascotGraphql.message}
            buttonOnClick={async () => {
              updateUserMascotAlert({
                variables: {
                  updateUserMascotAlertId: mascotGraphql.id
                }
              });
              await updateUserMetaLocalStorage({
                key: "last_location",
                value: "/sp/home/<USER>"
              });
              refetch();
              navigate(`${mascotGraphql.cta_link}`);
            }}
            isOpen={mascotGraphql?.cta_link ? true : isOpen}
            onClose={handleMascotOnClose}
            title={
              mascotGraphql?.title
                ? mascotGraphql.title
                : `Namaste ${userData?.name?.split(" ")[0]}`
            }
            show_mascot={true}
            bottom_drawer={true}
          />
        )}
      </Box>
    </PageTransition>
  );
};

export default SpSelectionProcess;
