import { useEffect, useState } from "react";
import { FeedBackState, useGetIntervieweeFeedbackFormDataQuery } from "../__generated__";

export const useIntervieweeFeedbackStatus = (feedbackFormId?: string | null) => {
  const [feedbackState, setFeedbackState] = useState<FeedBackState | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const {
    data,
    loading: queryLoading,
    error: queryError
  } = useGetIntervieweeFeedbackFormDataQuery({
    variables: {
      templateFormId: feedbackFormId || "-1"
    },
    skip: !feedbackFormId
  });

  useEffect(() => {
    if (!queryLoading) {
      setLoading(false);

      if (queryError) {
        setError(queryError);
      } else if (data?.getIntervieweeFeedbackFormData) {
        setFeedbackState(data.getIntervieweeFeedbackFormData.interviewee_feedback_state);
      }
    }
  }, [data, queryLoading, queryError]);

  return { feedbackState, loading, error };
};
