import { Contacts } from "@capacitor-community/contacts";
import { Capacitor } from "@capacitor/core";
import { Sim } from "@jonz94/capacitor-sim";

export const getSimCardsPermission = async () => {
  if (Capacitor.getPlatform() !== "android") return null;
  const hasPermission = await Sim.checkPermissions();

  if (hasPermission.readSimCard === "granted") {
    console.log("Contact Permission granted");
  } else {
    await Sim.requestPermissions();
  }
};
export const getContactPermission = async () => {
  if (Capacitor.getPlatform() !== "android") return null;
  //check permission
  const hasPermission = await Contacts.checkPermissions();
  if (hasPermission.contacts === "granted") return;
  await Contacts.requestPermissions();
};

export const getContactPermissionStatus = async (): Promise<
  "granted" | "denied" | "not-applicable" | "new_granted"
> => {
  if (Capacitor.getPlatform() !== "android") return "not-applicable";

  const hasPermission = await Contacts.checkPermissions();

  if (hasPermission.contacts === "granted") {
    return "granted";
  }

  const requested = await Contacts.requestPermissions();

  return requested.contacts === "granted" ? "new_granted" : "denied";
};
