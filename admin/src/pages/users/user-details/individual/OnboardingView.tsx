import { useParams } from "react-router-dom";
import UserTimeline from "../../../../components/user/UserTimeline";

import { useUserDetailsQueryQuery } from "../../../../__generated__";
import ErrorComponent from "../../../../components/error/Error";
import Loading from "../../../../components/loading/Loading";
import AssessmentSection from "./assessment/AssessmentSection";
import CertificationsSection from "./certifications/CertificationsSection";
import GeneralDetails from "./general-details/GeneralDetails";
import TrainingSection, { Training } from "./training/TrainingSection";

export const interviewRatings = [
  {
    id: 1,
    rating: 4.5,
    date: "2024-12-24",
    interviewer: "<PERSON>",
    comments:
      "Strong technical skills, good communication, shows initiative and problem-solving ability"
  },
  {
    id: 2,
    rating: 3.0,
    date: "2024-12-25",
    interviewer: "<PERSON>",
    comments:
      "Technical knowledge is adequate but needs improvement in customer interaction and professional communication"
  },
  {
    id: 3,
    rating: 3.5,
    date: "2024-12-26",
    interviewer: "<PERSON>",
    comments:
      "Good practical knowledge but needs to work on time management and organization skills"
  }
];
const testResults = [
  {
    id: 1,
    skillName: "Carpentry",
    testName: "Basic Woodworking Skills",
    percentage: 85,
    completedAt: "2024-12-24"
  },
  {
    id: 2,
    skillName: "MKW",
    testName: "Modular Kitchen Installation",
    percentage: 65,
    completedAt: "2024-12-24"
  },
  {
    id: 3,
    skillName: "Electrical",
    testName: "Basic Electrical Safety",
    percentage: 78,
    completedAt: "2024-12-25"
  },
  {
    id: 4,
    skillName: "MKW",
    testName: "Advanced Wardrobe Assembly",
    percentage: 58,
    completedAt: "2024-12-26"
  },
  {
    id: 5,
    skillName: "Carpentry",
    testName: "Power Tools Operation",
    percentage: 72,
    completedAt: "2024-12-26"
  }
];
const trainings: Training[] = [
  {
    id: 1,
    name: "Basic Carpentry Skills",
    durationDays: 5,
    location: "Training Center A, Delhi",
    assessor: "Rajesh Kumar",
    startDate: "2024-12-26",
    endDate: "2024-12-30",
    status: "PASS",
    feedback: "Excellent understanding of basic carpentry concepts and tools"
  },
  {
    id: 2,
    name: "Advanced MKW Installation",
    durationDays: 7,
    location: "Training Center B, Mumbai",
    assessor: "Priya Singh",
    startDate: "2025-01-02",
    endDate: "2025-01-08",
    status: "NEED_MORE_TRAINING",
    feedback: "Needs more practice with complex installations and measurements"
  },
  {
    id: 3,
    name: "Customer Service Excellence",
    durationDays: 3,
    location: "Training Center A, Delhi",
    assessor: "Amit Sharma",
    startDate: "2025-01-10",
    endDate: "2025-01-12",
    status: "NEED_MORE_TRAINING",
    feedback: "Requires improvement in handling customer objections and communication"
  }
];

const OnboardingView = ({ setActiveKey }: any) => {
  const params = useParams();

  const { data, error, loading } = useUserDetailsQueryQuery({
    variables: {
      userId: Number(params.user_id)
    }
  });
  if (error) {
    return <ErrorComponent error={error} />;
  }

  if (loading) {
    return <Loading tip="Loading ..." />;
  }
  return (
    <div className="flex w-[100%] gap-x-6  ">
      {/* General details */}
      <div className="flex flex-col w-[60%] max-w-[60%]   gap-y-8 ">
        <div className="flex flex-col  w-full bg-white rounded-lg shadow">
          <div className="px-6 py-5 " style={{ borderBottom: "1px solid #e5e7eb" }}>
            <h2 className="text-xl font-semibold text-gray-900">General Details</h2>
          </div>
          <GeneralDetails setActiveKey={setActiveKey} data={data?.userDetailsForAdmin} />
        </div>
        {/* Assessment Section */}
        <AssessmentSection
          userPhone={data?.userDetailsForAdmin?.phone || ""}
          setActiveKey={setActiveKey}
          averageRating={5}
          interviewRatings={interviewRatings}
          testResults={testResults}
          totalInterviews={interviewRatings.length}
        />
        {/* Training Section */}
        <TrainingSection setActiveKey={setActiveKey} trainings={trainings} />
        {/* Certifications Section */}
        <CertificationsSection />
      </div>
      <div className=" w-full hidden">
        <UserTimeline user_id={Number(params.user_id)} />
      </div>
    </div>
  );
};

export default OnboardingView;
