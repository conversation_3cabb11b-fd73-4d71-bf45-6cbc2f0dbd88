import { FaUser } from "react-icons/fa6";
import { RiTeamFill } from "react-icons/ri";
import { decodeToken } from "react-jwt";

// eslint-disable-next-line @typescript-eslint/no-unused-vars

interface ModuleData {
  module_id: number;
  module_name: string;
  icon_name: string;
  is_active: boolean;
  is_published: boolean;
  visible_in_sp: boolean;
  visible_in_users: boolean;
}

export const useNavbarItems = (selectedNav: "individual" | "service_provider") => {
  const permissionToken = localStorage.getItem("permissionsToken");
  const permissionsData: {
    adminPermissions: any[];
    adminData: any;
  } | null = permissionToken ? decodeToken(permissionToken) : null;

  //get modules
  const authorizedModules = permissionsData?.adminPermissions?.filter((perm) => {
    return perm.is_module;
  });

  const moduleToken = localStorage.getItem("moduleToken");

  const moduleResponse = moduleToken ? decodeToken(moduleToken) : {};

  let activeModules: ModuleData[] | [] = [];
  if (typeof moduleResponse === "object" && moduleResponse !== null) {
    activeModules = Object.keys(moduleResponse)
      .filter((key) => !isNaN(+key))
      .map((key) => (moduleResponse as Record<string, any>)[key]);
  }

  const spModules = authorizedModules
    ?.filter(({ is_active, permission_for_sp, module_id }) => {
      const isAuthorized = is_active && permission_for_sp?.read;
      const isActiveAndVisible = activeModules?.some(
        (active) =>
          active.module_id === module_id &&
          active.visible_in_sp &&
          active.is_active &&
          (active.is_published || +permissionsData?.adminData?.admin_role_id === 1)
      );
      return isAuthorized && isActiveAndVisible;
    })
    ?.map(({ module_name, icon_name, sub_module_url_sp, module_id }) => ({
      label: module_name
        .split("_")
        .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" "),
      key: module_id.toString(),
      icon: activeModules.find((active) => active.module_id === module_id)?.icon_name || icon_name,
      path: `${sub_module_url_sp}`,
      uuid: ""
    }));

  const userModules = authorizedModules
    ?.filter(({ is_active, permission_for_user, module_id }) => {
      const isAuthorized = is_active && permission_for_user?.read;
      const isActiveAndVisible = activeModules?.some(
        (active) =>
          active.module_id === module_id &&
          active.visible_in_users &&
          active.is_active &&
          (active.is_published || +permissionsData?.adminData?.admin_role_id === 1)
      );
      return isAuthorized && isActiveAndVisible;
    })
    ?.map(({ module_name, icon_name, sub_module_url_user, module_id }) => ({
      label: module_name
        .split("_")
        .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" "),
      key: module_id.toString(),
      icon: activeModules.find((active) => active.module_id === module_id)?.icon_name || icon_name,
      path: `${sub_module_url_user}`,
      uuid: ""
    }));

  // dashboard module should always be present even if it cant be accessed to avoid user confusion
  const hasDashboardModule = authorizedModules?.find((module) => +module?.module_id === 1);

  if (!hasDashboardModule) {
    const dashboardModule = activeModules?.find((module) => +module?.module_id === 1);
    if (dashboardModule) {
      userModules?.unshift({
        label: dashboardModule.module_name
          .split("_")
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" "),
        key: dashboardModule.module_id.toString(),
        icon: dashboardModule.icon_name,
        path: "/dashboard",
        uuid: ""
      });
      spModules?.unshift({
        label: dashboardModule.module_name
          .split("_")
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" "),
        key: dashboardModule.module_id.toString(),
        icon: dashboardModule.icon_name,
        path: "/dashboard/sp",
        uuid: ""
      });
    }
  }

  if (selectedNav === "individual" && moduleToken !== null && permissionToken !== null)
    return userModules;
  if (selectedNav === "service_provider" && moduleToken !== null && permissionToken !== null)
    return spModules;
  return null;
};

export const authNavbarConfig = () => {
  const permissions = localStorage.getItem("permissionsToken");
  const moduleToken = localStorage.getItem("moduleToken");
  if (permissions !== null && moduleToken !== null)
    return {
      individual: {
        id: "0",
        title: "Individual",
        base_path: "",
        icon: <FaUser />,
        menu: useNavbarItems("individual")
      },
      service_provider: {
        id: "1",
        title: "Service Provider",
        base_path: "sp",
        icon: <RiTeamFill />,
        menu: useNavbarItems("service_provider")
      }
    };
};
