import { Col, Form, InputNumber, Row, Select } from "antd";
import FormItem from "antd/es/form/FormItem";
import { isEmpty } from "lodash";
import <PERSON><PERSON> from "lottie-react";
import React, { useContext, useState } from "react";
import {
  QuestionDifficulty,
  useExpertiseQuery,
  useGetBulkQuestionWithAiLazyQuery
} from "../../../__generated__";
import animationData from "../../../utils/animation/aithinking.json";
import ErrorComponent from "../../error/Error";
import Loading from "../../loading/Loading";
import { AddQuestionFormContext } from "./AddQuestionModal";
import ShowAIGeneratedBulkQuestion from "./ShowAIGenratedBulkQuestions";

interface BulkAIGenerateQuestionInput {
  no_of_questions: number;
  difficulty: QuestionDifficulty;
  expertise: string;
  language: string;
}

export interface BulkQuestionAIProps {
  question_text: string;
  option_1: string;
  option_2: string;
  option_3: string;
  option_4: string;
  correct_option: string;
  difficulty: QuestionDifficulty.Easy | QuestionDifficulty.Medium | QuestionDifficulty.Hard;
}

const AIGeneratedQuestions: React.FC = () => {
  const {
    data: expertiseRes,
    loading: expertiseLoading,
    error: expertiseError
  } = useExpertiseQuery();
  //hooks
  const [generateQuestionsFromAI, setGeneratedQuestionsFromAI] = useState<
    Array<BulkQuestionAIProps>
  >([]);

  //form
  const ai_form = useContext(AddQuestionFormContext);

  //Query
  const [getBulkQuestionWithAi, { loading: getBulkQuestionLoading, error: ai_error }] =
    useGetBulkQuestionWithAiLazyQuery();

  const handleFormSubmit = async (data: BulkAIGenerateQuestionInput) => {
    try {
      const result = await getBulkQuestionWithAi({
        variables: {
          data: {
            ...data,
            no_of_questions: data.no_of_questions.toString()
          }
        }
      });
      result && setGeneratedQuestionsFromAI(result.data?.getBulkQuestionWithAI as any); //TODO : Fix this
    } catch (error: any) {
      throw new Error(error?.message || "Something went wrong");
    }
  };

  if (expertiseError) {
    return <ErrorComponent error={expertiseError} />;
  }

  if (expertiseLoading) {
    return <Loading tip="Loading expertise ..." />;
  }

  if (ai_error) {
    return <ErrorComponent error={ai_error.message} />;
  }
  if (getBulkQuestionLoading) {
    return (
      <div className="flex h-80 w-full justify-center">
        <Lottie animationData={animationData} loop autoplay />
      </div>
    );
  }

  if (!isEmpty(generateQuestionsFromAI)) {
    return <ShowAIGeneratedBulkQuestion generateQuestionsFromAI={generateQuestionsFromAI} />;
  }

  return (
    <Form
      layout="vertical"
      form={ai_form}
      onFinishFailed={(data) => {
        const no_of_questions = data.values?.no_of_questions;
        const difficulty = data.values?.difficulty;
        const expertise = data.values?.expertise;
        const language = data.values?.language;
        if (difficulty && no_of_questions && expertise && expertise && language) {
          handleFormSubmit({ no_of_questions, difficulty, expertise, language });
        }
      }}
    >
      <Row gutter={10}>
        <Col span={8}>
          <FormItem
            rules={[
              {
                required: true,
                message: "Number of question is required",
                type: "number"
              }
            ]}
            name={"no_of_questions"}
            label={"How many questions do you want ?"}
          >
            <InputNumber className="w-full" value={0} min={0} max={10} />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem
            rules={[
              {
                required: true,
                message: "Question difficulty is required"
              }
            ]}
            name={"difficulty"}
            label={"Select Question difficulty"}
          >
            <Select
              options={[
                {
                  value: QuestionDifficulty.Hard,
                  label: "Hard"
                },
                {
                  value: QuestionDifficulty.Medium,
                  label: "Medium"
                },
                {
                  value: QuestionDifficulty.Easy,
                  label: "Easy"
                }
              ]}
              allowClear
            />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem
            rules={[
              {
                required: true,
                message: "Skills is required"
              }
            ]}
            name={"expertise"}
            label={"Select skills"}
          >
            <Select
              options={expertiseRes?.expertise?.data?.map((exp) => ({
                label: exp?.name,
                value: exp?.name
              }))}
              allowClear
            />
          </FormItem>
        </Col>
        <FormItem
          initialValue={"ENGLISH"}
          rules={[
            {
              required: true,
              message: "Language is required "
            }
          ]}
          name={"language"}
          label={"Select Language "}
        >
          <Select
            defaultValue={"ENGLISH"}
            options={[
              {
                value: "ENGLISH",
                label: "English"
              },
              {
                value: "HINDI",
                label: "Hindi"
              }
            ]}
          />
        </FormItem>
      </Row>
    </Form>
  );
};

export default AIGeneratedQuestions;
