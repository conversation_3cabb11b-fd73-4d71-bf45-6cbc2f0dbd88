import { Form, Input, Modal, message, notification } from "antd";
import { useForm } from "antd/es/form/Form";
import FormItem from "antd/es/form/FormItem";
import React from "react";
import { useNavigate } from "react-router-dom";
import { GetAllBucketsDocument, useCreateBucketMutation } from "../../__generated__";

const AddBucketModal: React.FC = () => {
  //hooks
  const [form] = useForm();
  const nav = useNavigate();

  //network stuff
  const [createBucket, { loading }] = useCreateBucketMutation();

  const onCreateBucket = (values: { name: string }) => {
    createBucket({
      variables: {
        name: values.name
      },
      onError(err) {
        console.error(err);
        notification.error({
          message: err.message
        });
      },
      onCompleted() {
        message.success("Bucket created successfully");
        nav("/dashboard/assignment/buckets");
      },
      refetchQueries: [GetAllBucketsDocument]
    });
  };

  return (
    <Modal
      closeIcon={false}
      centered
      onOk={() => form.submit()}
      okButtonProps={{
        loading
      }}
      onCancel={() => nav(-1)}
      open
      okText={"Create"}
      title={"Create a new bucket"}
    >
      <Form onFinish={onCreateBucket} form={form}>
        <FormItem
          rules={[
            {
              required: true,
              message: "Bucket name is required"
            }
          ]}
          name={"name"}
          label={"Name"}
        >
          <Input />
        </FormItem>
      </Form>
    </Modal>
  );
};

export default AddBucketModal;
