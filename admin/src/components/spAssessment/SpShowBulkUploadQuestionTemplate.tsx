import { DownloadOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Spin, notification } from "antd";
import React, { useState } from "react";
import { useDropzone } from "react-dropzone";
import { read, utils } from "xlsx";
import { AssignmentUserType, useGetAllBucketsQuery } from "../../__generated__";
import {
  generateTemplateSheetForBulkQuestionUpload,
  readFileAsBinaryString
} from "../../utils/excelHelper";
import { BulkQuestionAdditionProps } from "./SpCreateBulkQuestion";

interface ShowBulkUploadQuestionTemplateProps {
  setIsTemplateUploaded: React.Dispatch<React.SetStateAction<boolean>>;
  setFilledQuestions: React.Dispatch<React.SetStateAction<Array<BulkQuestionAdditionProps>>>;
}

const SpShowBulkUploadQuestionTemplate: React.FC<ShowBulkUploadQuestionTemplateProps> = ({
  setIsTemplateUploaded,
  setFilledQuestions
}) => {
  //Hooks
  const [isLoading, setIsLoading] = useState(false);

  //GraphQL
  const { data: buckets, loading: bucketLoading } = useGetAllBucketsQuery({
    variables: {
      data: {
        assign_to: AssignmentUserType.ServiceProvider
      }
    }
  });

  const handleBulkQuestionUpload = async (file: File) => {
    const excelSheet = read(await readFileAsBinaryString(file), { type: "binary" });

    const jsonData = utils.sheet_to_json(excelSheet.Sheets[excelSheet.SheetNames[0]]);
    const uniqueQuestions = new Set();
    const updatedData = jsonData.map((questions: any) => {
      if (uniqueQuestions.has(questions.question)) {
        notification.error({
          message: `Duplicate question found: ${questions.question}, please re-upload once again`
        });
        setFilledQuestions([]);
        return null;
      }
      uniqueQuestions.add(questions.question);
      return {
        bucket_name: questions.bucket,
        ...questions,
        option_1:
          typeof questions.option_1 === "number" ? String(questions.option_1) : questions.option_1,
        option_2:
          typeof questions.option_2 === "number" ? String(questions.option_2) : questions.option_2,
        option_3:
          typeof questions.option_3 === "number" ? String(questions.option_3) : questions.option_3,
        option_4:
          typeof questions.option_4 === "number" ? String(questions.option_4) : questions.option_4,
        correct_option:
          typeof questions.correct_option === "number"
            ? String(questions.correct_option)
            : questions.correct_option
      };
    });
    if (updatedData.some((entry) => entry === null)) {
      setFilledQuestions([]);
    } else {
      setIsTemplateUploaded(true);

      setFilledQuestions(updatedData as BulkQuestionAdditionProps[]);
    }
    setIsLoading(false);
  };

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx", ".csv"],
      "text/csv": [".csv"]
    },
    maxFiles: 1,
    maxSize: 10000000, // 10 mb
    onDropRejected(fileRejections, event) {
      notification.error({ message: "Invalid file type. Please upload only .xlsx or .csv files." });
      setIsLoading(false);
      event.preventDefault();
    },
    onDropAccepted() {
      setIsLoading(true);
    },
    onDrop: (acceptedFiles) => {
      acceptedFiles.forEach((file) => {
        handleBulkQuestionUpload(file);
      });
    }
  });

  if (bucketLoading) {
    return <Spin />;
  }

  return (
    <>
      <div className="p-3">
        {isLoading ? (
          <Spin className="w-full h-500" /> //TODO : Replace this with 1/100 percentage UI
        ) : (
          <>
            <ol>
              <li>
                <span className="flex">
                  <Button
                    className="border-none font-bold text-purple-500"
                    onClick={() =>
                      generateTemplateSheetForBulkQuestionUpload(buckets?.getAllBuckets?.data)
                    }
                    icon={<DownloadOutlined />}
                  >
                    Template from here
                  </Button>
                </span>
              </li>
              <li>Please download this file as a template for uploading questions</li>
              <li>
                Make sure your have{" "}
                <pre>
                  Question , option 1 , option 2 , option 3, option 4, difficulty, correct Answer
                  and Bucket
                </pre>
                columns in your table and it contains only one sheet.
              </li>
            </ol>
            {isLoading ? (
              <div className="w-full h-[270px] border-dashed border-2 border-gray-500 flex justify-center items-center">
                <div className="text-2xl">Loading...</div>
              </div>
            ) : (
              <div
                {...getRootProps()}
                className="w-full h-[270px] border-dashed border-2 border-gray-500 flex justify-center items-center"
              >
                <input {...getInputProps()} />
                <img src="/images/upload_image.png" alt="upload image" width={120} />
                <div className="flex justify-center items-center flex-col">
                  <div className="text-2xl">Click or drop your file here</div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </>
  );
};

export default SpShowBulkUploadQuestionTemplate;
