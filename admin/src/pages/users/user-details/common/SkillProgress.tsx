interface SkillProgressProps {
  percentage: number | null;
  className?: string;
}

const SkillProgress = ({ percentage, className = "" }: SkillProgressProps) => {
  const getColorClass = (score: number | null) => {
    if (score === null) return "bg-gray-200";
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  return (
    <div className={`w-full h-3 bg-gray-200 rounded-full overflow-hidden ${className}`}>
      <div
        className={`h-full transition-all duration-500 ${getColorClass(percentage)}`}
        style={{ width: `${percentage}%`, minWidth: "1px" }}
      />
    </div>
  );
};
export default SkillProgress;
