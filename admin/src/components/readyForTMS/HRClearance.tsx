import { <PERSON><PERSON>, DatePicker, Form, Input, InputN<PERSON>ber, Result } from "antd";
import TextArea from "antd/es/input/TextArea";
import { FormInstance } from "antd/lib/form/Form";
import dayjs from "dayjs";
import isEmpty from "is-empty";
import { Link, useSearchParams } from "react-router-dom";
import { useGetUserForTttQuery } from "../../__generated__";
import ErrorComponent from "../error/Error";
import Loading from "../loading/Loading";

const HRClearance: React.FC<{ form: FormInstance<any>; handleSubmit: any }> = ({
  form,
  handleSubmit
}) => {
  //Search Params
  const [searchParams] = useSearchParams();

  //GraphQL Data Handler
  const { data, loading, error } = useGetUserForTttQuery({
    variables: {
      userId: Number(searchParams.get("user_id")) || -1
    },
    onCompleted(data) {
      data.getUserForTTT?.data?.[0]?.user_onboarding_data?.map((data) => {
        //Setting General Details
        form.setFieldsValue({
          employee_id: data?.employee_id,
          salary_offered: data?.salary_offered,
          aadhar_address: data?.aadhar_address,
          pancard_number: data?.pancard_number,
          esic_number: data?.esic_number,
          date_of_joining: data?.date_of_joining ? dayjs(data?.date_of_joining) : undefined,
          designation: data?.designation,
          aadhar_number: data?.aadhar_number
        });
      });
      //Setting Bank Account Details
      form.setFieldsValue({
        bank_name: data.getUserForTTT?.data?.[0]?.bank_details?.bank_name,
        account_number: data.getUserForTTT?.data?.[0]?.bank_details?.account_number,
        ifsc: data.getUserForTTT?.data?.[0]?.bank_details?.ifsc
      });
    }
  });

  //If data is empty from Graphql return Error
  if (isEmpty(data?.getUserForTTT)) {
    return (
      <Result
        status="404"
        title="404"
        subTitle="User Not Found "
        extra={
          <Link to="/dashboard/transfer_to_tms">
            <Button type="primary">Back Home</Button>
          </Link>
        }
      />
    );
  }

  if (loading) {
    return <Loading tip="Loading..." />;
  }

  //Return error if error
  if (error) {
    return <ErrorComponent error={error} />;
  }

  return (
    <div>
      <Form
        form={form}
        layout="vertical"
        name="basic"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        onFinish={handleSubmit}
        autoComplete="off"
      >
        <Form.Item label={"Employee Id"} name={"employee_id"}>
          <InputNumber addonBefore={"WMUM"} maxLength={20} />
        </Form.Item>
        <Form.Item
          label={"Salary Offered"}
          name={"salary_offered"}
          rules={[
            {
              pattern: new RegExp(/^\d+$/),
              message: "Only Numbers allowed"
            }
          ]}
        >
          <Input maxLength={20} />
        </Form.Item>
        <Form.Item
          label={"Designation"}
          name={"designation"}
          rules={[
            {
              pattern: new RegExp(/^[A-Za-z]+$/),
              message: "Invalid Designation "
            }
          ]}
        >
          <Input></Input>
        </Form.Item>
        <Form.Item label={"Address As Per Aadhar"} name={"aadhar_address"}>
          <TextArea />
        </Form.Item>
        <Form.Item
          label={"Aadhar"}
          name={"aadhar_number"}
          rules={[
            {
              pattern: new RegExp(/^\d{12}$/),
              message: "Invalid Aadhar Number "
            }
          ]}
        >
          <Input maxLength={12} />
        </Form.Item>
        <Form.Item
          label={"Pancard"}
          name={"pancard_number"}
          rules={[
            {
              message: "Please enter your PAN card number in capital format"
            },
            {
              pattern: new RegExp(/^([A-Z]){5}([0-9]){4}([A-Z]){1}$/),
              message: "Invalid PAN card number"
            }
          ]}
        >
          <Input maxLength={10} />
        </Form.Item>
        <Form.Item label={"ESIC Number"} name={"esic_number"}>
          <Input maxLength={17} />
        </Form.Item>
        <Form.Item label={"Date of Joining"} name={"date_of_joining"}>
          <DatePicker />
        </Form.Item>
        <Form.Item
          label={"Bank name"}
          name={"bank_name"}
          rules={[
            {
              pattern: new RegExp(/^[a-zA-Z]{2,50}$/),
              message: "Invalid Bank Name"
            }
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label={"Account Number "}
          name={"account_number"}
          rules={[
            {
              pattern: new RegExp(/^\d{8,20}$/),
              message: "Invalid Account Number"
            }
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label={"IFSC Code "}
          name={"ifsc"}
          rules={[
            {
              pattern: new RegExp(/^[A-Z]{4}0[A-Z0-9]{6}$/),
              message: "Invalid IFSC Code "
            }
          ]}
        >
          <Input />
        </Form.Item>
      </Form>
    </div>
  );
};

export default HRClearance;
