import {
  Box,
  <PERSON>ton,
  <PERSON>vider,
  <PERSON>lex,
  <PERSON>ing,
  HStack,
  <PERSON>dal,
  Modal<PERSON>ody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalOverlay,
  Radio,
  RadioGroup,
  Text,
  VStack
} from "@chakra-ui/react";
import { User } from "lucide-react";
import { useState } from "react";

interface ReferredUser {
  id: number;
  name: string;
  photoUrl?: string;
}

interface ReferredUsersPopupProps {
  isOpen: boolean;
  onClose: () => void;
  referredUsers: ReferredUser[];
  couponCode: string;
  benefitText: string;
  onSubmit?: (couponCode: string) => void;
}

const ReferredUsersPopup: React.FC<ReferredUsersPopupProps> = ({
  isOpen,
  onClose,
  referredUsers,
  couponCode,
  onSubmit
}) => {
  const [selectedReferrerId, setSelectedReferrerId] = useState<number | null>(
    referredUsers.length > 0 ? referredUsers[0].id : null
  );

  // GraphQL

  const handleSubmit = () => {
    if (selectedReferrerId !== null && onSubmit) {
      onSubmit(couponCode);
      onClose();
    }
  };

  return (
    <Modal isCentered isOpen={isOpen} onClose={onClose} size="sm">
      <ModalOverlay />
      <ModalContent className="m-4">
        <ModalCloseButton />
        <ModalHeader>
          <Heading as="h2" size="lg" className="text-center font-bold text-xl text-black">
            Use Referral Code
          </Heading>
        </ModalHeader>
        <Text size="sm" className="text-center  text-sm text-black">
          Boost your rewards for exciting benefits ! 🚀
        </Text>
        <Divider my={2} />

        <ModalBody pb={6}>
          <VStack spacing={4} align="stretch">
            {referredUsers.length > 0 ? (
              <Box>
                <Text fontWeight="medium" mb={2}>
                  Choose a referrer:
                </Text>
                <RadioGroup
                  value={selectedReferrerId?.toString() || ""}
                  onChange={(value) => setSelectedReferrerId(parseInt(value))}
                >
                  <VStack spacing={3} align="stretch">
                    {referredUsers.map((user) => (
                      <HStack key={user.id} spacing={3}>
                        <Radio value={user.id.toString()}>
                          <Flex alignItems="center" gap={3}>
                            <Flex
                              w="10"
                              h="10"
                              bg="blue.50"
                              rounded="full"
                              align="center"
                              justify="center"
                              flexShrink={0}
                            >
                              <User size={20} color="#2563eb" />
                            </Flex>
                            <Text>{user.name}</Text>
                          </Flex>
                        </Radio>
                        <Flex ml="auto" gap={2} alignItems="center">
                          <Text fontWeight="bold" letterSpacing="wider">
                            {couponCode}
                          </Text>
                        </Flex>
                      </HStack>
                    ))}
                  </VStack>
                </RadioGroup>
              </Box>
            ) : (
              <Text>No referrals found</Text>
            )}

            <Divider my={2} />

            <Box className="text-sm" margin={"auto"}>
              You and your friend will earn points.{" "}
            </Box>

            <Box className="text-center ">
              <Button
                colorScheme="blue"
                width="full"
                onClick={handleSubmit}
                isDisabled={selectedReferrerId === null}
              >
                Use Selected Referral Code
              </Button>
            </Box>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default ReferredUsersPopup;
