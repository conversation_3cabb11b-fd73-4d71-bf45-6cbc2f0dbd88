import { Button, Table, Tabs, TabsProps } from "antd";
import { useState } from "react";
import { Outlet, useParams } from "react-router-dom";
import { useGetSingleUserTrainingPerformanceQuery } from "../../__generated__";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import AssignTrainingInUserDetails from "./AssignTrainingInUserDetails";

const UserTrainingDetails = () => {
  //Hooks
  const [openAssignModal, setOpenAssignModal] = useState(false);
  //   const { user_id } = useParams();

  //search params
  // const [searchParams, setSearchParams] = useSearchParams();

  //params
  const params = useParams();

  // const onTabChange = (key: string) => {
  //   setSearchParams({
  //     tab: key
  //   });
  // };

  //Graphql Query
  const { data } = useGetSingleUserTrainingPerformanceQuery({
    variables: {
      userId: Number(params?.user_id) || -1
    },
    fetchPolicy: "no-cache"
  });

  const user_training_data =
    data?.getSingleUserTrainingPerformance?.[data?.getSingleUserTrainingPerformance?.length - 1];

  const UserTrainingTable = () => (
    <>
      <div className="w-full flex gap-6 mt-[62px] mb-8 ml-2 ">
        <div className="bg-slate-100 p-2 rounded-md flex justify-center gap-2 items-center shadow min-h-full">
          <span
            className="font-bold bg-yellow-100 w-12 h-12 p-2 rounded-full flex justify-center items-center border border-solid border-[#FFDA1D]
"
          >
            {user_training_data?.total_course ? (
              <span className="">
                {user_training_data?.completed_course || 0} / {user_training_data?.total_course}
              </span>
            ) : (
              <span>-</span>
            )}
          </span>
          <span className="font-semibold"> Number of Courses Completed</span>
        </div>
        <div className="bg-slate-100 p-2 rounded-md flex justify-center gap-2 items-center shadow min-h-full">
          <span className="font-bold bg-yellow-100 w-12 h-12 p-2 rounded-full flex justify-center items-center border border-solid border-[#FFDA1D]">
            <span className="">
              {user_training_data?.total_course_test_marks_percentage
                ? user_training_data?.total_course_test_marks_percentage
                : "-"}
            </span>
          </span>
          <span className="font-semibold">Average Marks Achieved</span>
        </div>
      </div>
      <div className="mt-4">
        <Table
          bordered
          columns={[
            {
              title: "Course Name",
              dataIndex: "course_name",
              key: "course_name",
              render: (text) => {
                return <p>{text}</p>;
              }
            },
            {
              title: "Lessons Completed",
              dataIndex: "completed_lessons",
              key: "completed_lessons",
              render: (text, record) => <p>{`${text} / ${record?.total_lessons}`}</p>
            },
            {
              title: "Chapters Completed",
              dataIndex: "completed_chapter",
              key: "completed_chapter",
              render: (text, record) => <p>{`${text} / ${record?.total_chapter}`}</p>
            },
            {
              title: "Marks Received (%)",
              dataIndex: "average_marks_percentage",
              key: "average_marks_percentage",
              render: (text) => <p>{`${text ? text : "-"} `}</p>
            }
          ]}
          dataSource={data?.getSingleUserTrainingPerformance?.map((d, index) => ({
            key: index,
            course_name: d?.course_name,
            completed_chapter: d?.completed_chapter,
            total_course_test_marks_percentage: d?.total_course_test_marks_percentage,
            total_chapter: d?.total_chapter,
            average_marks_percentage: d?.average_marks_percentage,
            completed_lessons: d?.completed_lessons,
            total_lessons: d?.total_lessons
          }))}
        />
      </div>
    </>
  );
  //Tabs Props
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: `Theoretical Training`,
      children: <UserTrainingTable />
    },
    {
      key: "2",
      disabled: true,
      label: `Physical Training`
    }
  ];

  return (
    <ProtectedRoute>
      {({ permissions }) => (
        <div className="bg-white w-[calc(100vw-40%)]">
          <div className="p-2  flex justify-between ">
            <div className="flex  w-full">
              <Tabs type="card" activeKey={"1"} className="flex w-full  relative" items={items} />
              <Button
                disabled={!permissions?.create}
                onClick={() => setOpenAssignModal(true)}
                className="mb-6 right-0  "
              >
                Assign a Training
              </Button>
            </div>
          </div>

          {openAssignModal && (
            <AssignTrainingInUserDetails
              open={openAssignModal}
              close={() => setOpenAssignModal(false)}
            />
          )}
          <Outlet />
        </div>
      )}
    </ProtectedRoute>
  );
};

export default UserTrainingDetails;
