import { EditOutlined } from "@ant-design/icons";
import { Button, Form, Modal, notification, Select, Table, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import { MdPublish } from "react-icons/md";
import {
  SubmoduleData,
  useGetAllAdminRolesQuery,
  useGetUnpublishedModulesQuery,
  usePublishModuleMutation
} from "../../__generated__";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import { getIconComponent } from "../../utils/renderIcon";
import PermissionTable from "./PermissionTable";

export interface PermissionSet {
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
}

interface ModulePermissions {
  [module_id: string]: PermissionSet & {
    submodules: { [module_id: string]: PermissionSet };
  };
}

interface PermissionsState {
  selectAll: boolean;
  modules: ModulePermissions;
}

export interface RolePermissions {
  permission_for_sp: PermissionsState;
  permission_for_user: PermissionsState;
}
interface subModuleRoleData {
  feature_id: number;
  sub_module_name: string;
  permission_for_sp: PermissionSet;
  permission_for_user: PermissionSet;
}

export interface RolePermissionData {
  admin_role_id: number;
  feature_id: number;
  role_name: string;
  module_id: number;
  module_name: string;
  permission_for_sp: PermissionSet;
  permission_for_user: PermissionSet;
  submodules: subModuleRoleData[] | null;
}
const PublishModules = () => {
  const [editRolesModal, setEditRolesModal] = useState(false);
  const [publishModalValues, setPublishModalValues] = useState({} as any);
  const [editRolesData, setEditRolesData] = useState({} as any);
  const [publishModal, setPublishModal] = useState(false);
  const defaultPermission = {
    create: true,
    read: true,
    update: true,
    delete: true
  };

  const [rolePermissions, setRolePermissions] = useState<RolePermissionData[]>([]);
  const [moduleIcon, setModuleIcon] = useState<React.ElementType | null>(null);
  const { data: unpublishedModulesData } = useGetUnpublishedModulesQuery();
  const unpublishedModules = unpublishedModulesData?.getUnpublishedModules;
  const [publishModule, { loading: publishModuleLoading }] = usePublishModuleMutation({
    onCompleted() {
      setPublishModal(false);
      notification.success({ message: "Module Published successfully" });
    },
    onError() {
      notification.error({ message: "Failed to publish module " });
    },
    refetchQueries: ["GetUnpublishedModules"]
  });

  const [modulePermissionInfo, setModulePermissionInfo] = useState(() => {
    ///is one of the role and is currently on the screen to be alloted permission
    const activePerm = rolePermissions.find(
      (role) => +role.admin_role_id === +editRolesData?.admin_role_id
    );
    return activePerm
      ? {
          admin_role_id: +editRolesData?.admin_role_id,
          feature_id: publishModalValues?.feature_id,
          role_name: editRolesData?.name,
          module_id: publishModalValues?.module_id,
          module_name: publishModalValues?.module_name,
          permission_for_sp: defaultPermission,
          permission_for_user: defaultPermission,
          submodules:
            unpublishedModules?.data
              ?.find((module) => module?.module_id === publishModalValues?.module_id)
              ?.submodules?.filter((sub): sub is SubmoduleData => sub !== null) // 🔹 Remove nulls
              ?.map(
                (sub): subModuleRoleData => ({
                  feature_id: sub.feature_id ?? 0,
                  sub_module_name: sub.sub_module_name ?? "",
                  permission_for_sp: defaultPermission,
                  permission_for_user: defaultPermission
                })
              ) ?? null
        }
      : {
          admin_role_id: 0,
          role_name: "",
          feature_id: 0,
          module_id: 0,
          module_name: "",
          permission_for_sp: defaultPermission,
          permission_for_user: defaultPermission,
          submodules: []
        };
  });

  useEffect(() => {
    setModulePermissionInfo(
      rolePermissions.find((role) => +role.admin_role_id === +editRolesData?.admin_role_id) ?? {
        admin_role_id: +editRolesData?.admin_role_id,
        role_name: editRolesData?.name,
        feature_id: publishModalValues?.feature_id,
        module_id: publishModalValues?.module_id,
        module_name: publishModalValues?.module_name,
        permission_for_sp: defaultPermission,
        permission_for_user: defaultPermission,
        submodules:
          unpublishedModules?.data
            ?.find((module) => module?.module_id === publishModalValues?.module_id)
            ?.submodules?.filter((sub): sub is SubmoduleData => sub !== null) // 🔹 Remove nulls
            ?.map(
              (sub): subModuleRoleData => ({
                feature_id: sub.feature_id ?? 0,
                sub_module_name: sub.sub_module_name ?? "",
                permission_for_sp: defaultPermission,
                permission_for_user: defaultPermission
              })
            ) ?? null
      }
    );
    const icon = getIconComponent(publishModalValues?.icon_name);
    setModuleIcon(icon);
  }, [publishModalValues, editRolesData]);

  const [selectedRoles, setSelectedRoles] = useState<
    { key: number; name: string; label: string }[]
  >([]);
  const [publishModuleForm] = Form.useForm();

  const { data: adminRolesData } = useGetAllAdminRolesQuery();

  const adminRoles = adminRolesData?.getAllAdminRoles;
  const rootAdmin = adminRoles?.data.find((role) => role.is_root_admin);

  const handleChange = (values: string[]) => {
    const selected = values
      .map((val) => adminRoles?.data?.find((role) => role.name === val))
      .filter(Boolean) as any;

    setSelectedRoles(selected);
  };

  const handlePublishModule = async () => {
    try {
      if (rolePermissions.length !== selectedRoles.length) {
        notification.error({
          message: "Please verify permissions for all the selected roles , see edit icon"
        });
        return;
      }

      const rolePermissionFlatMap = rolePermissions.flatMap((role) => {
        // Create the main entry object with the role properties
        const mainRole = {
          admin_role_id: role.admin_role_id,
          feature_id: role.feature_id,

          permission_for_sp: role.permission_for_sp,
          permission_for_user: role.permission_for_user
        };

        // If there are submodules, create an entry for each submodule as well
        const submoduleEntries =
          role.submodules?.map((submodule) => ({
            admin_role_id: role.admin_role_id,
            feature_id: submodule.feature_id,

            permission_for_sp: submodule.permission_for_sp,
            permission_for_user: submodule.permission_for_user
          })) ?? [];

        // Return both the main role and submodule entries
        return [mainRole, ...submoduleEntries];
      });
      const modulesData = unpublishedModules?.data?.find(
        (module) => module?.module_id === publishModalValues?.module_id
      );
      const rootPermissions = [
        {
          admin_role_id: rootAdmin?.admin_role_id ? +rootAdmin?.admin_role_id : 0,
          feature_id: 0,

          permission_for_sp: defaultPermission,
          permission_for_user: defaultPermission
        },
        ...(modulesData?.submodules?.map((submodule) => ({
          admin_role_id: rootAdmin?.admin_role_id ? +rootAdmin?.admin_role_id : 0,
          feature_id: submodule?.feature_id,

          permission_for_sp: defaultPermission,
          permission_for_user: defaultPermission
        })) || [])
      ];
      await publishModule({
        variables: {
          moduleId: publishModalValues?.module_id,
          rolePermission: [
            ...rolePermissionFlatMap,
            ...rootPermissions.filter((perm) => perm !== null)
          ]
        }
      });
    } catch (err) {
      console.log(err);
    }
  };

  const handleSaveRolePermission = () => {
    setRolePermissions((prev) => {
      const roleIndex = prev.findIndex(
        (role) => +role.admin_role_id === +editRolesData?.admin_role_id
      );

      if (roleIndex !== -1) {
        // Role exists, update it with the fields of modulePermissionInfo
        const updatedRolePermissions = [...prev];
        updatedRolePermissions[roleIndex] = {
          ...updatedRolePermissions[roleIndex], // Spread existing properties
          ...modulePermissionInfo // Directly spread the contents of modulePermissionInfo
        };

        return updatedRolePermissions;
      } else {
        return [
          ...prev,
          {
            ...modulePermissionInfo
          }
        ];
      }
    });

    setEditRolesModal(false);
  };

  const handleEditRolesCancel = () => {
    setModulePermissionInfo(
      rolePermissions.find((role) => +role.admin_role_id === +editRolesData?.admin_role_id) ?? {
        admin_role_id: +editRolesData?.admin_role_id,
        feature_id: publishModalValues?.feature_id,
        role_name: editRolesData?.name,
        module_id: publishModalValues?.module_id,
        module_name: publishModalValues?.module_name,
        permission_for_sp: defaultPermission,
        permission_for_user: defaultPermission,
        submodules:
          unpublishedModules?.data
            ?.find((module) => module?.module_id === publishModalValues?.module_id)
            ?.submodules?.filter((sub): sub is SubmoduleData => sub !== null) // 🔹 Remove nulls
            ?.map(
              (sub): subModuleRoleData => ({
                feature_id: sub.feature_id ?? 0,
                sub_module_name: sub.sub_module_name ?? "",
                permission_for_sp: defaultPermission,
                permission_for_user: defaultPermission
              })
            ) ?? null
      }
    );
    setEditRolesModal(false);
  };

  return (
    <ProtectedRoute>
      {({ permissions }) => (
        <div className="p-3">
          <div className="text-xl mt-2 flex items-center p-2 mb-3">
            <MdPublish className=" text-purple-500 text-3xl  mr-4" />{" "}
            <span className="font-semibold">Publish Modules</span>
          </div>

          <div className="mt-3">
            <Table
              dataSource={(unpublishedModules?.data ?? []).filter(
                (module): module is NonNullable<typeof module> => module !== null
              )}
              columns={[
                {
                  title: "Module Name",
                  dataIndex: "module_name",
                  align: "center",
                  className: "capitalize "
                },

                {
                  title: "Actions",
                  key: "actions",
                  align: "center",
                  render: (data) => (
                    <div>
                      <Button
                        disabled={!permissions.update || !permissions.create}
                        onClick={() => {
                          setPublishModalValues(data);
                          setPublishModal(true);
                        }}
                      >
                        Publish
                      </Button>
                    </div>
                  )
                }
              ]}
            />
          </div>
          <Modal
            title="Publish Module"
            open={publishModal}
            okText="Publish"
            onOk={publishModuleForm.submit}
            onCancel={() => {
              setPublishModalValues({});
              setEditRolesData({});
              setSelectedRoles([]);
              setPublishModal(false);
            }}
          >
            <Form layout="vertical" form={publishModuleForm} onFinish={handlePublishModule}>
              <div className="flex my-3">
                <div className="flex items-center bg-violet-50 font-bold text-violet-500  p-2 rounded-md  ">
                  <>{moduleIcon}</>

                  <span className="ml-2 text-base  capitalize">
                    {publishModalValues?.module_name?.toLowerCase()}
                  </span>
                </div>
              </div>
              <div className="my-2 ">Roles with access</div>

              <Select
                placeholder="Select roles"
                mode="tags"
                className="w-full"
                onChange={handleChange}
                tokenSeparators={[","]}
                value={selectedRoles.map((role) => role.name)}
                options={
                  adminRoles?.data
                    ?.filter((role) => !role.is_root_admin)
                    .map((role) => ({
                      key: role.admin_role_id,
                      value: role.name,
                      label: role.name
                    })) ?? []
                }
              />
              <div className="mt-4 space-y-2 p-2">
                <Tooltip title="Root Admin has access to all modules " className="">
                  <div
                    key={"root"}
                    className="flex cursor-pointer justify-between items-center bg-violet-50 p-2 rounded-md"
                  >
                    <span className="text-base text-gray-500 font-medium">{rootAdmin?.name}</span>

                    <div className="flex gap-2">
                      <Button size="small" type="primary" icon={<EditOutlined />} disabled />
                    </div>
                  </div>
                </Tooltip>
                {selectedRoles.map((role) => (
                  <div
                    key={role.key}
                    className="flex justify-between items-center bg-violet-50 p-2 rounded-md"
                  >
                    <span className="text-base font-medium">{role.name}</span>
                    <div className="flex gap-2">
                      <Button
                        size="small"
                        type="primary"
                        icon={<EditOutlined />}
                        onClick={() => {
                          setEditRolesModal(true);
                          setEditRolesData({ ...role });
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </Form>
          </Modal>
          <Modal
            title="Edit Role Permissions"
            className="min-w-[580px]"
            open={editRolesModal}
            closable={false}
            width={800}
            okText="Save"
            onOk={handleSaveRolePermission}
            okButtonProps={{ loading: publishModuleLoading }}
            onCancel={handleEditRolesCancel}
          >
            <div className="flex flex-col justify-start   h-[500px]">
              <div className="flex flex-col items-start ">
                <div className="flex items-center mt-2">
                  <span className="text-base min-w-28 ">Role Name:</span>
                  <span className="flex text-base bg-violet-50 p-0.5 ml-1 mt-1 mb-2 rounded-md justify-center min-w-[100px] capitalize">
                    {editRolesData?.name}
                  </span>
                </div>
                <div className="flex items-center ">
                  <span className="text-base min-w-28">Module Name:</span>
                  <span className="flex text-base bg-violet-50 p-0.5 ml-1 mt-1 mb-2 rounded-md justify-center min-w-[100px] capitalize">
                    {publishModalValues.module_name}
                  </span>
                </div>
              </div>
              <PermissionTable
                modulesData={
                  unpublishedModules?.data?.find(
                    (module) => module?.module_id === publishModalValues?.module_id
                  ) || {}
                }
                modulePermissionInfo={modulePermissionInfo}
                setModulePermissionInfo={setModulePermissionInfo}
              />
            </div>
          </Modal>
        </div>
      )}
    </ProtectedRoute>
  );
};

export default PublishModules;
