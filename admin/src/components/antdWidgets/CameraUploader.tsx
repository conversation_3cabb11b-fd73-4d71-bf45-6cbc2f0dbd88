import {
  CameraOutlined,
  CloseOutlined,
  DeleteOutlined,
  LoadingOutlined,
  PictureOutlined,
  UploadOutlined
} from "@ant-design/icons";
import { Button, Drawer, Image, message, notification, Tooltip, Upload, UploadProps } from "antd";
import { RcFile, UploadChangeParam, UploadFile } from "antd/es/upload";
import axios from "axios";
import React from "react";
import { useMediaQuery } from "react-responsive";
const { Dragger } = Upload;

interface CameraUploaderProps {
  value?: string;
  onChange?: (url: string) => void;
}

const CameraUploader: React.FC<CameraUploaderProps> = ({ value, onChange }) => {
  const [imageLoading, setLoading] = React.useState(false);
  const image = value || "";
  const [isDrawerVisible, setIsDrawerVisible] = React.useState(false);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  const uploadFileToS3 = async (file: File): Promise<string> => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/upload/signed-url`,
        { file_type: file.type },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("adminToken")}`
          }
        }
      );

      const { status, data: signedUrl, Key } = response.data;

      if (status) {
        await axios.put(signedUrl, file, {
          headers: {
            "Content-Type": file.type
          }
        });

        return import.meta.env.VITE_S3_URL + "/" + Key;
      } else {
        notification.error({
          message: "There was some issue during uploading the file. Please try again later."
        });
      }
    } catch (err) {
      notification.error({
        message: "There was some issue during uploading the file. Please try again later."
      });
    }
    return "";
  };

  const handleChange: UploadProps["onChange"] = async (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === "uploading") {
      setLoading(true);
      return;
    }
    if (info.file.status === "done") {
      try {
        const url = await uploadFileToS3(info.file.originFileObj as File);
        onChange?.(url);
        setLoading(false);
        message.success(`${info.file.name} file uploaded successfully`);
      } catch (error) {
        console.error("errr", error);
      }
    }
  };

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
    if (!isJpgOrPng) message.error("You can only upload JPG/PNG file!");

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) message.error("Image must be smaller than 10MB!");

    return isJpgOrPng && isLt10M;
  };

  const openCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      const video = document.createElement("video");
      video.srcObject = stream;
      video.play();

      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");

      // Wait for the video to be ready
      video.onloadedmetadata = () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context?.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Stop the video stream
        stream.getTracks().forEach((track) => track.stop());

        // Convert the canvas to a blob and handle it as a file
        canvas.toBlob(async (blob) => {
          if (blob) {
            const file = new File([blob], "photo.jpg", { type: "image/jpeg" });
            const url = await uploadFileToS3(file);
            onChange?.(url);
          }
        }, "image/jpeg");
      };
    } catch (error) {
      console.error("Error accessing the camera", error);
      notification.error({
        message: "Unable to access the camera. Please check your permissions."
      });
    }
  };

  const showDrawer = () => {
    console.log("Show drawer called");
    setIsDrawerVisible(true);
  };

  const closeDrawer = () => {
    console.log("Drawer closed");
    setIsDrawerVisible(false);
  };

  const handleCameraClick = () => {
    console.log("Camera option selected");
    openCamera();
    setIsDrawerVisible(false);
  };

  const handleGalleryClick = () => {
    console.log("Gallery option selected");
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        const url = await uploadFileToS3(file);
        onChange?.(url);
      }
    };
    input.click();
    setIsDrawerVisible(false);
  };

  const uploadButton = (
    <button
      style={{ border: 0, background: "none", cursor: "pointer" }}
      type="button"
      hidden={!!image}
      onClick={(e) => {
        e.stopPropagation();
        showDrawer();
      }}
    >
      {imageLoading ? <LoadingOutlined /> : <UploadOutlined />}
      <div style={{ marginTop: 8 }}>Upload Image</div>
    </button>
  );

  return (
    <>
      <Drawer
        title={
          <div style={{ textAlign: "center", width: `${!isMobile ? "100%" : "50%"}` }}>
            Select Option
          </div>
        }
        placement="bottom"
        closable={true}
        onClose={closeDrawer}
        open={isDrawerVisible}
        height={200}
        closeIcon={<CloseOutlined style={{ position: "absolute", right: "20px", top: "20px" }} />}
      >
        <div style={{ display: "flex", justifyContent: "center", gap: "16px" }}>
          <div
            onClick={handleCameraClick}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              cursor: "pointer",
              padding: "16px"
            }}
          >
            <CameraOutlined style={{ fontSize: "36px", color: "#1890ff" }} />
            <span style={{ marginTop: "8px" }}>Camera</span>
          </div>
          <div
            onClick={handleGalleryClick}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              cursor: "pointer",
              padding: "16px"
            }}
          >
            <PictureOutlined style={{ fontSize: "36px", color: "#52c41a" }} />
            <span style={{ marginTop: "8px" }}>Gallery</span>
          </div>
        </div>
      </Drawer>
      <Dragger
        onChange={handleChange}
        beforeUpload={beforeUpload}
        listType="picture-card"
        className="avatar-uploader"
        disabled={!!image}
        showUploadList={false}
        multiple={false}
        customRequest={({ onSuccess }) => {
          // Prevent default upload behavior
          if (onSuccess) onSuccess("ok");
        }}
        openFileDialogOnClick={false}
      >
        {image ? (
          <div className="relative">
            <Image src={image} alt="uploaded" width={"100px"} height={"100px"} />
            <Button
              className="absolute top-0 right-0"
              icon={
                <Tooltip title="Remove Image">
                  <DeleteOutlined className="text-red-500" />
                </Tooltip>
              }
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onChange?.(""); // 👈 clears the form field
              }}
            />
          </div>
        ) : (
          uploadButton
        )}
      </Dragger>
    </>
  );
};

export default CameraUploader;
