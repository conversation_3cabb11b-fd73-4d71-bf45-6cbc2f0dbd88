import { UserOutlined } from "@ant-design/icons";
import { Button, Modal, Table, Tooltip, notification } from "antd";
import ButtonGroup from "antd/es/button/button-group";
import { useEffect, useState } from "react";
import { GrDocumentOutlook } from "react-icons/gr";
import { useNavigate, useSearchParams } from "react-router-dom";
import { DashboardStates, useGetUsersByTypeLazyQuery } from "../../__generated__";
import { downloadXLSXFromJson } from "../../utils/XlsxDataDownloader";
import { toTitleCase } from "../../utils/utils";

interface CardProps {
  type: string;
  total: string;
  cardOpen: boolean;
  setCardClicked: (cardOpen: boolean) => void;
  setError: (error: string) => void;
  permissions?: any;
}
const CardDetails: React.FC<CardProps> = ({
  type,
  total,
  cardOpen,
  setCardClicked,
  setError,
  permissions
}) => {
  //Hooks
  const navigate = useNavigate();
  const [params] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  //Graphql Query
  const [fetchUsers, { data, error, loading }] = useGetUsersByTypeLazyQuery();
  const [fetchUsersToDownload, { loading: downloadLoading }] = useGetUsersByTypeLazyQuery();

  const handleSetCurrentPage = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const onDownload = () => {
    fetchUsersToDownload({
      variables: {
        type: type?.toUpperCase() as DashboardStates,
        filter: params.get("filter") ? JSON.parse(params.get("filter") || "") : {}
      },
      onCompleted(data) {
        downloadXLSXFromJson(
          data?.getUsersByType?.map((d) => ({
            name: d?.name,
            phone: d?.phone,
            onboardingStage:
              d?.onboarding_stage == "DASHBOARD"
                ? "SIGN UP COMPLETED"
                : d?.onboarding_stage?.split("_").join(" ")
          })) || [],
          `users-${type || ""}.xlsx`,
          "Users"
        );
      },
      onError() {
        notification.error({ message: "There was some error while downloading" });
      }
    });
  };

  useEffect(() => {
    fetchUsers({
      variables: {
        type: type?.toUpperCase() as DashboardStates,
        pagination: {
          take: pageSize,
          skip: currentPage - 1
        },
        filter: params.get("filter") ? JSON.parse(params.get("filter") || "") : {}
      }
    });
  }, [cardOpen, loading, currentPage, pageSize]);

  if (error) {
    setError(error.message);
  }

  return (
    <Modal
      width={1000}
      open={cardOpen}
      title={toTitleCase(String(type))}
      okButtonProps={{ style: { display: "none" } }}
      cancelText={"Close"}
      onCancel={() => {
        setCardClicked(false);
      }}
    >
      <div>
        {data?.getUsersByType && data?.getUsersByType?.length > 0 && (
          <div className="flex justify-end py-3">
            {permissions?.adminData?.can_download && (
              <Button onClick={onDownload} loading={downloadLoading}>
                Download data
              </Button>
            )}
          </div>
        )}
        <Table
          loading={loading}
          pagination={{
            defaultCurrent: 1,
            total: parseInt(total || "0"),
            current: currentPage,
            onChange: (page, pageSize) => {
              handleSetCurrentPage(page, pageSize);
            }
          }}
          rowKey={"phone"}
          dataSource={data?.getUsersByType?.map((d) => ({
            name: d?.name,
            phone: d?.phone,
            onboardingStage: d?.onboarding_stage?.split("_").join(" "),
            id: d?.id
          }))}
          columns={[
            {
              title: "Name",
              dataIndex: "name",
              key: "name",
              align: "center",
              render: (data) => (data ? data : "-")
            },
            {
              title: "Phone",
              dataIndex: "phone",
              key: "phone",
              align: "center",
              render: (data) => (data ? data : "-")
            },
            {
              title: "Onboarding stage",
              dataIndex: "onboardingStage",
              key: "email",
              align: "center",
              render: (data) => {
                const revamped_data = data === "DASHBOARD" ? "SIGN UP COMPLETED" : data;
                return revamped_data ? revamped_data : "-";
              }
            },
            {
              title: "Action",
              key: "action",
              align: "center",
              render: (e) => (
                <ButtonGroup>
                  <Tooltip title={"User details"}>
                    <Button
                      icon={<UserOutlined />}
                      onClick={() => {
                        navigate(`/dashboard/users/${e.id}`);
                      }}
                    />
                  </Tooltip>
                  <Tooltip title={"Documents"}>
                    <Button
                      onClick={() => {
                        navigate(`/dashboard/documents/${e.id}`);
                      }}
                      title="Documents"
                      icon={<GrDocumentOutlook />}
                    />
                  </Tooltip>
                </ButtonGroup>
              )
            }
          ]}
        />
      </div>
    </Modal>
  );
};

export default CardDetails;
