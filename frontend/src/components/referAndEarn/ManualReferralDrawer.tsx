import { useToast } from "@chakra-ui/react";
import { X } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useSendRefferalInviteMutation } from "../../__generated__";
import Loading from "../Loading";
export interface ReferredContact {
  id: string;
  name: string;
  phone: string;
  status: "pending" | "joined";
  referredAt: string;
}
interface ManualReferralDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  referralCode: string;
}

const ManualReferralDrawer = ({ isOpen, onClose, referralCode }: ManualReferralDrawerProps) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  const [sendRefferalInvite, { loading: sendRefferalInviteLoading }] =
    useSendRefferalInviteMutation();
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim() || !phone.trim()) {
      toast({
        position: "top",
        title: !name.trim() ? t("error_name") : t("error_phone"),
        status: "error"
      });
      return;
    }

    if (!/^\+?\d{10,12}$/.test(phone.replace(/[\s-]/g, ""))) {
      toast({
        position: "top",
        title: t("error_invalid_number"),
        status: "error"
      });
      return;
    }

    sendRefferalInvite({
      variables: {
        data: {
          name: name,
          phone_number: phone.trim(),
          meta: {},
          referral_code: referralCode
        }
      },
      onCompleted: () => {
        toast({
          position: "top",
          title: t("success_referral"),
          status: "success"
        });
        setName("");
        setPhone("");
        onClose();
        navigate("/profile");
      },
      onError: (error) => {
        if (error.message.includes("Referral invite already sent")) {
          toast({
            position: "top",
            title: t("error_contact_already_referred"),
            status: "error"
          });
        } else {
          toast({
            position: "top",
            title: error.message,
            status: "error"
          });
        }
        setName("");
        setPhone("");
        return;
      }
    });
  };
  if (sendRefferalInviteLoading) {
    return <Loading />;
  }

  return (
    <div className="fixed inset-0 bg-black/50 z-50 mb-[68px]">
      <div className="absolute inset-x-0 bottom-0 bg-white rounded-t-2xl max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b z-10">
          <div className="flex items-center justify-between p-4">
            <h2 className="text-lg font-semibold">Enter Details</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Name
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter friend's name"
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              placeholder="Enter phone number"
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <button
            type="submit"
            className="w-full py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Send Invite
          </button>
        </form>
      </div>
    </div>
  );
};

export default ManualReferralDrawer;
