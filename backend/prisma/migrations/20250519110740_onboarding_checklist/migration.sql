-- AlterTable
ALTER TABLE "team_member_invitations"
ALTER COLUMN "expires_at"
SET DEFAULT NOW() + interval '7 days';
-- CreateTable
CREATE TABLE "checklist" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "checklist_pkey" PRIMARY KEY ("id")
);
-- CreateTable
CREATE TABLE "user_checklist_data" (
    "id" TEXT NOT NULL,
    "user_id" INTEGER NOT NULL,
    "checklist_id" TEXT NOT NULL,
    "hiring_completed" BOOLEAN NOT NULL DEFAULT false,
    "hiring_completed_at" TIMESTAMP(3),
    "technician_completed" BOOLEAN NOT NULL DEFAULT false,
    "technician_completed_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" INTEGER,
    "edited_by" INTEGER,
    CONSTRAINT "user_checklist_data_pkey" PRIMARY KEY ("id")
);
-- CreateIndex
CREATE UNIQUE INDEX "user_checklist_data_user_id_checklist_id_key" ON "user_checklist_data"("user_id", "checklist_id");
-- AddForeignKey
ALTER TABLE "user_checklist_data"
ADD CONSTRAINT "user_checklist_data_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- AddForeignKey
ALTER TABLE "user_checklist_data"
ADD CONSTRAINT "user_checklist_data_checklist_id_fkey" FOREIGN KEY ("checklist_id") REFERENCES "checklist"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- AddForeignKey
ALTER TABLE "user_checklist_data"
ADD CONSTRAINT "user_checklist_data_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "admin"("id") ON DELETE
SET NULL ON UPDATE CASCADE;
-- AddForeignKey
ALTER TABLE "user_checklist_data"
ADD CONSTRAINT "user_checklist_data_edited_by_fkey" FOREIGN KEY ("edited_by") REFERENCES "admin"("id") ON DELETE
SET NULL ON UPDATE CASCADE;