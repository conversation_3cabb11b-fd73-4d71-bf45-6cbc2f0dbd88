import { DeleteOutlined } from "@ant-design/icons";
import { useJsApiLoader } from "@react-google-maps/api";
import { Button, Collapse, Drawer, Form, Input, notification, Result } from "antd";
import FormBuilder from "antd-form-builder";
import { FormInstance } from "antd/lib/form/Form";
import dayjs from "dayjs";
import isEmpty from "is-empty";
import { useEffect, useMemo, useState } from "react";
import { Link, useLocation, useNavigate, useSearchParams } from "react-router-dom";
import {
  GetUserForTttDocument,
  OnboardingStage,
  useCreateOrUpdateUsersOnboardingDataMutation,
  useGetUserForTttQuery,
  UserDetailsQueryDocument,
  useUpdateUserAdminMutation
} from "../../__generated__";
import ErrorComponent from "../error/Error";
import Loading from "../loading/Loading";
import { SelectLocationFromGoogleMap } from "../map/GoogleMap";
import SelectLocationFromGoogleMapInput from "../map/GoogleMapInputSearch";

const TransferToTms: React.FC<{
  form: FormInstance<any>;
  setCreateUserInTmsLoading: (loading: boolean) => void;
  onClose?: () => void;
}> = ({ form, setCreateUserInTmsLoading, onClose }) => {
  //Hooks
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [userNotFoundError, setUserNotFoundError] = useState(false);
  const [data, setData] = useState<any>();
  const [userExistInTMS, setUserExistInTMS] = useState<boolean>(false);
  const [openDrawer, setOpenDrawer] = useState<boolean>(false);
  const location = useLocation();

  //navigate
  const navigate = useNavigate();

  //Map Configuration
  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_API_KEY || "YOUR_API_KEY",
    libraries: ["places"]
  });

  //GraphQL
  const [updateUserData] = useCreateOrUpdateUsersOnboardingDataMutation();
  const {
    data: userData,
    loading: userLoading,
    error: userFetchError
  } = useGetUserForTttQuery({
    variables: {
      userId: Number(searchParams.get("user_id")) || -1
    },
    onCompleted(data) {
      if (isEmpty(data.getUserForTTT)) {
        setUserNotFoundError(true);
      }
      //Setting Form Fields Value From User Data
      form.setFieldsValue({
        user_name: data?.getUserForTTT?.data?.[0]?.name,
        user_mobile: data?.getUserForTTT?.data?.[0]?.phone,
        user_email: data?.getUserForTTT?.data?.[0]?.email,
        cust_line_1: data?.getUserForTTT?.data?.[0]?.location?.landmark,
        line_1: data?.getUserForTTT?.data?.[0]?.meta?.formatted_address || "",
        cust_pincode: data?.getUserForTTT?.data?.[0]?.location?.pincode,
        cust_city: data?.getUserForTTT?.data?.[0]?.location?.city,
        cust_state: data?.getUserForTTT?.data?.[0]?.location?.state,
        location_latitude: data?.getUserForTTT?.data?.[0]?.meta?.lat,
        location_Longitude: data?.getUserForTTT?.data?.[0]?.meta?.lng
      });

      data.getUserForTTT?.data?.[0]?.user_onboarding_data?.map((data) => {
        if (data?.user_created_in_tms) setUserExistInTMS(true);
        //Setting Form Fields Value From User Data
        form.setFieldValue("user_code", data?.employee_id ? "WMUM" + data?.employee_id : "");
      });
    }
  });
  const [updateUser] = useUpdateUserAdminMutation();

  //Variables
  const token = useMemo(() => {
    return localStorage.getItem("adminToken");
  }, []);

  //Functions
  const createUserInTms = async (data: any) => {
    try {
      setCreateUserInTmsLoading(true);
      //TMS API is expecting date in this format , YYYY-MM-DD
      if (!isEmpty(data["6fa26065-4659-404f-985e-d402a854cdc6"])) {
        data["6fa26065-4659-404f-985e-d402a854cdc6"] = dayjs(
          data["6fa26065-4659-404f-985e-d402a854cdc6"]
        ).format("YYYY-MM-DD");
      }
      if (isEmpty(data["user_code"])) {
        data["user_code"] = undefined;
      }
      data["user_id"] = Number(searchParams.get("user_id")) || -1;
      //will add a utility for this
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/tms/user`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });
      if (await response.ok) {
        if (data?.user_email) {
          await updateUser({
            variables: {
              data: {
                email: data?.user_email,
                onboarding_stage: OnboardingStage.Onboard
              },
              userId: Number(searchParams.get("user_id")) || -1
            }
          });
        }
        //Updating in Onboarding that the user has been created in TMS
        await updateUserData({
          variables: {
            userOnboardingData: {
              user_created_in_tms: true,
              user_id: Number(searchParams.get("user_id")) || -1
            }
          },
          onCompleted() {
            notification.success({ message: "User Transferred Successfully" });
            location.pathname.includes("users")
              ? onClose && onClose()
              : navigate("/dashboard/transfer_to_tms");
          },
          refetchQueries: [GetUserForTttDocument, UserDetailsQueryDocument]
        });
        setCreateUserInTmsLoading(false);
      } else {
        setCreateUserInTmsLoading(false);
        const error = await response.json();
        notification.error({ message: error?.message || "Something went wrong" });
      }
    } catch (error: any) {
      setCreateUserInTmsLoading(false);
      notification.error({ message: "Something went wrong" });
      console.error(error);
    }
  };

  /**
   * Sets the address value in the form.
   */
  const setAddress = (address: google.maps.GeocoderResult) => {
    form.setFieldsValue({
      location_latitude: address?.geometry?.location?.lat(),
      location_Longitude: address?.geometry?.location?.lng(),
      cust_city: address?.address_components.find((component) =>
        component.types.includes("locality")
      )?.long_name,
      cust_state: address?.address_components.find((component) =>
        component.types.includes("administrative_area_level_1")
      )?.long_name,
      cust_pincode: address?.address_components.find((component) =>
        component.types.includes("postal_code")
      )?.long_name,
      cust_line_1: address?.address_components.find(
        (component) =>
          component.types.includes("sublocality_level_1") ||
          component.types.includes("sublocality_level_2")
      )?.long_name
    });
  };

  const clearAddress = () => {
    form.setFieldsValue({
      location_latitude: null,
      location_Longitude: null,
      cust_city: null,
      cust_state: null,
      cust_pincode: null,
      cust_line_1: null
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/tms/user/proto`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        const json = await response.json();
        setData(json);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        setError(true);
        console.error(error);
      }
    };
    fetchData();
  }, []);

  const meta = {
    columns: 1,
    formItemLayout: null,
    fields: [
      {
        key: "user_name",
        label: "Full Name",
        placeholder: "Eg: john",
        required: true,
        rules: [
          {
            max: 100,
            message: "Name must be max 100 characters."
          }
        ]
      },
      {
        key: "user_code",
        label: "Code",
        placeholder: "Eg: WMUM10295",
        rules: [{ max: 20, message: "User code must be max 20 characters." }]
      },
      {
        key: "user_role",
        label: "Select specific role",
        widget: "select",
        required: true,
        widgetProps: {
          mode: "multiple",
          optionFilterProp: "children"
        },
        placeholder: "Please select specific role",
        options: data?.form_proto?.roleList || []
      },
      {
        key: "user_loc_group",
        label: "Select Location Group",
        widget: "select",
        widgetProps: {
          mode: "multiple",
          optionFilterProp: "children"
        },
        options: data?.form_proto?.locGrpList || [],
        placeholder: "Please select location group"
      },
      {
        key: "user_reporting_to",
        label: "Select Reporting to",
        widget: "select",
        options: data?.form_proto?.userList || [],
        widgetProps: {
          showSearch: true,
          optionFilterProp: "children",
          allowClear: true
        },
        placeholder: "Please select reporting to"
      },
      {
        key: "user_mobile",
        label: "Mobile Number (+91)",
        placeholder: "Eg: 9876543210",
        required: true,
        rules: [
          {
            pattern: new RegExp("^[0-9]*$"),
            message: "Please enter your right mobile number!"
          },
          { min: 10, message: "Mobile no must be min 10 characters." },
          { max: 10, message: "Mobile no must be max 10 characters." }
        ]
      },
      {
        key: "user_email",
        label: "Email",
        placeholder: "Eg: <EMAIL>",
        required: true,
        rules: [
          {
            type: "email"
          }
        ]
      },
      {
        key: "map_address_to_users_collapse",
        render: () => {
          return (
            <div className="gx-mb-2">
              <Collapse className="my-3">
                <Collapse.Panel
                  forceRender={true}
                  key={"address_details"}
                  header={"Address Details"}
                >
                  {" "}
                  {isLoaded && (
                    <div className="">
                      <Form.Item label={"Search your building/street name"} hidden>
                        <SelectLocationFromGoogleMapInput setLocation={setAddress} />
                        <div className="mt-3">
                          <Button
                            className="mt-2"
                            onClick={() => {
                              setOpenDrawer(true);
                            }}
                            type={"primary"}
                            hidden
                          >
                            {" "}
                            Pick on Map{" "}
                          </Button>
                          <Button
                            icon={<DeleteOutlined />}
                            onClick={clearAddress}
                            className="ml-2 "
                            hidden
                          >
                            {" "}
                            Clear
                          </Button>
                        </div>
                      </Form.Item>
                      <Form.Item>
                        <Form.Item name={"cust_line_0"} label={"Flat no"}>
                          <Input disabled />
                        </Form.Item>
                        <Form.Item name={"cust_line_1"} label={"Building/Apartment name"}>
                          <Input disabled />
                        </Form.Item>
                        <Form.Item name={"line_1"} label={"Line 1"}>
                          <Input disabled />
                        </Form.Item>
                        <Form.Item name={"line_2"} label={"Line 2"}>
                          <Input disabled />
                        </Form.Item>
                        <Form.Item name={"cust_pincode"} label={"Pincode"}>
                          <Input disabled />
                        </Form.Item>
                        <Form.Item name={"cust_city"} label={"City"}>
                          <Input disabled />
                        </Form.Item>
                        <Form.Item name={"cust_state"} label={"State"}>
                          <Input disabled />
                        </Form.Item>
                        <Form.Item name={"location_latitude"} label={"Latitude"}>
                          <Input disabled />
                        </Form.Item>
                        <Form.Item name={"location_Longitude"} label={"Longitude"}>
                          <Input disabled />
                        </Form.Item>
                      </Form.Item>
                    </div>
                  )}
                </Collapse.Panel>
              </Collapse>
            </div>
          );
        }
      },
      {
        key: "user_designation",
        label: "Designation",
        placeholder: "Eg: Technician Incharges",
        rules: [
          {
            max: 50,
            message: "Designation must be max 50 characters."
          }
        ]
      },
      ...(data?.form_proto?.srvc_type_list?.length > 0
        ? [
            {
              key: "srvc_types_assigned_to_user",
              label: "Service Types",
              widget: "select",
              widgetProps: {
                mode: "multiple",
                optionFilterProp: "children"
              },
              options: data?.form_proto?.srvc_type_list || []
            }
          ]
        : []),
      ...(data?.form_proto?.verticals_list?.length > 0
        ? [
            {
              key: "user_vertical",
              label: "Vertical",
              widget: "select",
              widgetProps: {
                mode: "multiple",
                optionFilterProp: "children"
              },
              options: data?.form_proto?.verticals_list || []
            }
          ]
        : []),
      {
        key: "password",
        label: "Password",
        placeholder: "Enter your password",
        widget: "password",
        required: true,
        onChange: () => {
          if (form.isFieldTouched("user_confirm_pass")) {
            form.validateFields(["user_confirm_pass"]);
          }
        }
      },
      {
        key: "user_confirm_pass",
        label: "Confirm Password",
        widget: "password",
        placeholder: "Enter your confirm password",
        required: true,
        rules: [
          {
            validator: (_rule: any, value: any) => {
              return new Promise<void>((resolve, reject) => {
                if (!value || value === form.getFieldValue("password")) {
                  resolve();
                } else {
                  reject(new Error("The password and confirm password do not match !"));
                }
              });
            }
          }
        ]
      },
      {
        key: "is_active",
        label: "Active",
        widget: "switch",
        initialValue: true
      },
      ...(data?.custom_fields?.length > 0 ? data?.custom_fields || [] : [])
    ]
  };
  //If data is empty from Graphql return 404 Error
  if (userNotFoundError) {
    return (
      <Result
        status="404"
        title="404"
        subTitle="User Not Found "
        extra={
          <Link to="/dashboard/transfer_to_tms">
            <Button type="primary">Back Home</Button>
          </Link>
        }
      />
    );
  }
  if (loading || userLoading) {
    return <Loading tip="Loading..." />;
  }
  if (error || userFetchError) {
    return <ErrorComponent error={"Something Went Wrong"} />;
  }

  return userExistInTMS ? (
    <div>
      <div className="text-center ">
        <div className="text-6xl ">🎉</div>
        <div className="text-3xl my-2">{userData?.getUserForTTT?.data?.[0]?.name || "User"} </div>
        <div className="text-xl ">Is Already Transferred</div>
      </div>
    </div>
  ) : (
    <div>
      <Form layout="vertical" onFinish={createUserInTms} form={form}>
        <FormBuilder meta={meta as any} form={form} />
      </Form>
      {isLoaded && (
        <Drawer
          width={"50%"}
          title={"Select Location"}
          open={openDrawer}
          placement="right"
          closable
          onClose={() => setOpenDrawer(false)}
          destroyOnClose
        >
          <SelectLocationFromGoogleMap
            setLocation={setAddress}
            lat={form.getFieldValue("location_latitude") || ""}
            lng={form.getFieldValue("location_Longitude") || ""}
          />
        </Drawer>
      )}
    </div>
  );
};

export default TransferToTms;
