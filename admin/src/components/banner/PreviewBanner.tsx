import { Modal } from "antd";
import { useState } from "react";
import { useBannerConfigsQuery } from "../../__generated__";
import ErrorComponent from "../error/Error";
import Banner from "./Banner";
interface PreviewBannerProps {
  open: boolean;
  close: () => void;
  banner_id: string;
}

function PreviewBanner({ open, close, banner_id }: PreviewBannerProps) {
  //Hooks
  const [preview, setPreviewContentData] = useState({
    title: "",
    description: "",
    hero_image: "",
    image: "",
    cta_link: ""
  });

  //Graphql Query
  const { error } = useBannerConfigsQuery({
    variables: {
      filter: {
        banner_config_id: banner_id
      }
    },
    fetchPolicy: "no-cache",
    onCompleted(data) {
      const config = data?.bannerConfigs?.data?.[0];
      setPreviewContentData({
        title: config?.title || "",
        description: config?.description || "",
        hero_image: config?.hero_image || "",
        image: config?.image || "",
        cta_link: config?.cta_link || ""
      });
    }
  });

  if (error) {
    return <ErrorComponent error={error.message} />;
  }

  return (
    <Modal
      open={open}
      centered
      footer={null}
      onCancel={() => {
        close();
      }}
    >
      <div className="ml-8">
        <Banner
          bgUrl={preview.hero_image}
          title={preview.description}
          sideImgUrl={preview.image}
          ctaLink={preview.cta_link}
          ctaRequired
        />
      </div>
    </Modal>
  );
}

export default PreviewBanner;
