import dayjs from 'dayjs'
import Queues from '../queue'
import { Context } from '../utils/types'
import { downloadXLSXFromJson } from '../utils/xlsxDataDownloader'

export const startPolicyTrackingExportQueue = async () => {
    await Queues.policyTrackingExportQueue.process(async (job, done) => {
        try {
            const data = job.data
            const { data: users, ctx } = data

            const sheets = [
                {
                    data: users || [],
                    sheetName: 'Policy_Tracking',
                },
            ]
            const fileName = `Policy_Tracking_${dayjs()
                .format('MMMM D, YYYY')
                .replace(/,/g, '')
                .replace(/\s/g, '_')}.xlsx`

            const s3Url = await downloadXLSXFromJson(sheets, fileName)

            //Adding in Email Queue
            await Queues.emailQueue.add({
                receiver: ctx?.admin?.email || '',
                subject: `Policy Tracking Data Export`,
                template_name: 'EXPORT_POLICY_TRACKING_RESULT',
                variables: {
                    admin_name: ctx?.admin?.name,
                    export_link: s3Url || '',
                },
                user_id: ctx?.admin?.id,
                org_id: ctx?.admin?.organization_id,
            })

            done()
        } catch (error) {
            console.error('Error sending policy tracking data to admin', error)
            throw new Error(error)
        }
    })
}

export const sendPolicyTrackingDataToAdmin = async (
    data: any,
    ctx: Context
) => {
    try {
        await Queues.policyTrackingExportQueue.add({
            data,
            ctx,
        })
    } catch (error) {
        console.error('Error sending policy tracking data to admin', error)
        throw new Error(error)
    }
}
