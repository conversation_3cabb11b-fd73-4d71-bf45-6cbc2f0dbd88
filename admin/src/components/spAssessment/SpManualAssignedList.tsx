import { DeleteOutlined } from "@ant-design/icons";
import { Button, Empty, Table, Tag, Tooltip, message } from "antd";
import Search from "antd/es/input/Search";
import dayjs from "dayjs";
import isEmpty from "is-empty";
import { useCallback, useRef, useState } from "react";
import { Link, useParams, useSearchParams } from "react-router-dom";
import {
  Expertise,
  GetManualAssignmentsDocument,
  useGetManualAssignmentsQuery,
  useUnassignUsersFromAssignmentMutation
} from "../../__generated__";
import { debounce, getRandomTagBgColor, toTitleCase } from "../../utils/utils";
import ErrorComponent from "../error/Error";

interface ManuallyAssignedUsersTableProps {
  searchTerm: string;
  setSearchTerm: (searchTerm: string) => void;
}
const SpAssessmentAssignManualList: React.FC<ManuallyAssignedUsersTableProps> = ({
  searchTerm,
  setSearchTerm
}) => {
  //Hooks
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const { assignment_config_id } = useParams();
  const userRef = useRef<number | null>(null);
  const [searchParams, setSearchParams] = useSearchParams("");

  //GraphQl Query
  const {
    data: manualAssignments,
    loading: manualAssignmentLoading,
    error: manualAssignmentError
  } = useGetManualAssignmentsQuery({
    variables: {
      assignmentConfigId: assignment_config_id || "",
      pagination: {
        take: pageSize,
        skip: currentPage - 1
      },
      filter: {
        search: searchParams.get("keyword")
      }
    }
  });

  // Calculate the total number of data pages based on search results
  const totalDataPages = Math.ceil(
    manualAssignments?.getManualAssignments?.[0]?.total_count || 0 / pageSize
  );

  //GraphQl Mutation
  const [unAssignUserFromAssignmentConfig, { loading: unassign }] =
    useUnassignUsersFromAssignmentMutation();

  //Handle Debounce Search
  const debounceSearch = useCallback(
    debounce((val: string) => {
      setCurrentPage(1);
      setSearchParams({ keyword: val });
    }, 500),
    []
  );

  const handleCurrentPage = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  if (manualAssignmentError) {
    return <ErrorComponent error={manualAssignmentError} />;
  }

  return (
    <>
      <div className="w-4/12">
        <Search
          allowClear
          value={searchTerm}
          autoFocus
          onChange={(e) => {
            debounceSearch(e.target.value);
            setSearchTerm(e.target.value);
          }}
          placeholder="search user by name"
          className="mb-8"
        />
      </div>
      {manualAssignments?.getManualAssignments?.length === 0 ? (
        <Empty />
      ) : (
        <Table
          loading={manualAssignmentLoading}
          pagination={{
            current: currentPage,
            pageSize,
            onChange: (page, pageSize) => {
              handleCurrentPage(page, pageSize);
            },

            total: totalDataPages || 1
          }}
          columns={[
            {
              title: "Name",
              dataIndex: "name",
              key: "name",

              render: (record) => (
                <div>
                  <Link to={`/dashboard/users/${record?.id}`}>
                    <span className="text-purple-500 underline">{record?.name}</span>
                  </Link>
                </div>
              )
            },
            {
              title: "Expertise",
              dataIndex: "expertise",
              align: "center",
              render: (expertise: Expertise[]) => (
                <Tooltip
                  title={
                    <ul>
                      {expertise.map((e) => (
                        <li key={e.id || e.name}>{e.name}</li>
                      ))}
                    </ul>
                  }
                >
                  <div className="">
                    {expertise?.length > 0
                      ? expertise.slice(0, 2).map((e) => (
                          <Tag
                            color={getRandomTagBgColor(e?.name || "")}
                            key={e.id || e.name}
                            className="mr-2"
                          >
                            {e?.name}
                          </Tag>
                        ))
                      : "-"}
                    {expertise?.length > 2 && ( // Check if exceeding max tags
                      <span className="text-red-500">...</span> // Display custom truncation indicator
                    )}
                  </div>
                </Tooltip>
              )
            },
            {
              title: "Status",
              dataIndex: "assignment_status",
              key: "assignment_status",
              align: "center"
            },
            {
              title: "Assigned On",
              dataIndex: "created_on",
              key: "created_on",

              align: "center",
              render: (value) => {
                let created_on = "";

                value?.map((m_assignment: { assignment_config_id: string; created_on: string }) =>
                  m_assignment?.assignment_config_id === assignment_config_id
                    ? (created_on = m_assignment?.created_on)
                    : created_on
                );
                return !isEmpty(created_on) ? dayjs(created_on).format("MMM D, YYYY") : "-";
              }
            },
            {
              title: "Action",
              dataIndex: "action",
              key: "action",
              align: "center",
              render: (_, record) => (
                <Button
                  loading={userRef.current === record.user_id ? unassign : false}
                  //when a user is getting deleted, delete button for others should be disabled
                  disabled={userRef.current !== record.user_id && unassign ? true : false}
                  onClick={() => {
                    userRef.current = record.user_id || -1;
                    //if the user has not started the assignment then allowed_to_unassign will be []
                    if (isEmpty(record.allowed_to_unassign)) {
                      unAssignUserFromAssignmentConfig({
                        variables: {
                          assignment_config_id: assignment_config_id || "",
                          user_id: record.user_id || -1
                        },
                        onCompleted(data) {
                          if (data.unassignAssignmentToUser.result) {
                            message.success("Successfully unassigned");
                          } else {
                            message.error("Something went wrong");
                          }
                        },
                        onError() {
                          message.error("Something went wrong");
                        },
                        refetchQueries: [GetManualAssignmentsDocument]
                      });
                    } else {
                      message.error("User is not allowed to unassign");
                    }
                  }}
                  icon={<DeleteOutlined />}
                  danger
                />
              )
            }
          ]}
          dataSource={manualAssignments?.getManualAssignments?.map((a) => ({
            key: a?.user?.id,
            name: a?.user,
            expertise: a?.user?.expertise,
            user_id: a?.user?.id,
            allowed_to_unassign: a?.assignment?.assignment,
            assignment_status: !isEmpty(a?.assignment?.assignment)
              ? toTitleCase(a?.assignment?.assignment?.[0]?.status || "")
              : "NOT COMPLETED",
            created_on: a?.user?.manual_assignments
          }))}
        />
      )}
    </>
  );
};

export default SpAssessmentAssignManualList;
