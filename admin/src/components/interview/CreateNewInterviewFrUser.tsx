//import BlockButton from "@components/button/blockbutton";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Form,
  Modal,
  notification,
  Radio,
  Row,
  Select,
  TimePicker
} from "antd";

import { useForm } from "antd/es/form/Form";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { useEffect, useState } from "react";
import { Link, Outlet, useNavigate, useParams } from "react-router-dom";
import {
  CalendarEventType,
  GetUserInterviewDetailsForAdminDocument,
  GetUserInterviewsDocument,
  useAdminDetailsQuery,
  useAllInterviewerQuery,
  useGenerateGoogleAuthLinkQuery,
  useGetCalendarTimeSlotsQuery,
  useGetFeedbackTemplatesQuery,
  useScheduleInterviewMutation,
  useUserDetailsQueryQuery
} from "../../__generated__";
import { disablePastDates } from "../../utils/utils";
import ErrorComponent from "../error/Error";
import Loading from "../loading/Loading";
import InterviewerTimeline from "./InterviewerTimeline";

// Add this after the imports
dayjs.extend(customParseFormat);

const CreateNewInterviewFrUser: React.FC = () => {
  //Hooks
  const [form] = useForm();
  const interviewTypeValue = Form.useWatch("type", form);
  const { data } = useAdminDetailsQuery();
  const [interviewers, setInterviewers] = useState<any>([]);
  const [interviewer, setInterviewer] = useState<number>();
  const [templates, setTemplates] = useState<any>([]);
  const [datePickerDisabled, setDatePickerDisabled] = useState(true);
  const [date, setDate] = useState(dayjs(new Date()));
  const [addressToDisplay, setAddressToDisplay] = useState<string>("");

  //Parameters
  const params = useParams();

  //Navigate
  const navigate = useNavigate();
  //Graphql queries
  const { data: userDetails, loading: userLoading } = useUserDetailsQueryQuery({
    variables: {
      userId: Number(params?.user_id)
    },
    onCompleted(data) {
      if (data?.userDetailsForAdmin?.email) {
        form.setFieldsValue({ type: CalendarEventType.InterviewOnline });
      }
    }
  });

  //Graphql Mutation
  const [schedule, { loading: scheduleInterviewLoading }] = useScheduleInterviewMutation();

  const { data: allInterviewerData } = useAllInterviewerQuery({
    onCompleted(data) {
      const interviewers = data?.interviewersDetails?.data?.map((el: any) => {
        return {
          label: el.name,
          value: el.id
        };
      });
      setInterviewers(interviewers);
    }
  });
  useGetFeedbackTemplatesQuery({
    onCompleted(data) {
      const templates = data?.getFeedbackTemplates
        ?.map((el: any) => {
          if (el.status) {
            console.log("Included template:", el.id);
            return {
              value: el.id,
              label: el.title,
              status: el.status
            };
          } else {
            return null;
          }
        })
        .filter(Boolean);
      console.log(templates);
      setTemplates(templates);
    }
  });

  const {
    data: timeSlots,
    refetch,
    loading: timeSlotsLoading
  } = useGetCalendarTimeSlotsQuery({
    variables: {
      data: {
        interview_date: date,
        interviewer_id: interviewer
      }
    }
  });

  const { data: interviewerData, error: interviewerError } = useGenerateGoogleAuthLinkQuery();

  //Handles if the Admin is Registered to schedule the Interview
  const handleRegisterInterview = async () => {
    window.location.replace(interviewerData?.generateGoogleAuthLink?.link || "");
    if (interviewerError) {
      return <ErrorComponent error={interviewerError} />;
    }
  };

  //Handle Schedule Interview
  const scheduleInterview = async (data: any) => {
    try {
      //combine date and time
      const combinedDate = dayjs(data.date)
        .set("hour", data.time.start.hour())
        .set("minute", data.time.start.minute())
        .toDate();
      const combinedEndTime = dayjs(data.date)
        .set("hour", data.time.end.hour())
        .set("minute", data.time.end.minute())
        .toDate();

      // Continue with existing code
      await schedule({
        variables: {
          data: {
            user_id: Number(params.user_id),
            type: data.type,
            start: combinedDate,
            end: combinedEndTime,
            interviewer_id: data.interviewer,
            template_id: data.template_id,
            offline_interview_location: addressToDisplay
          }
        },
        onCompleted() {
          notification.success({ message: "Interview Scheduled Successfully" });
          navigate(-1);
        },
        onError(error) {
          notification.error({ message: error.message });
        },
        refetchQueries: [GetUserInterviewDetailsForAdminDocument, GetUserInterviewsDocument]
      });
    } catch (error) {
      // Add better error logging
      console.error("Schedule interview error:", {
        error,
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : "No stack trace"
      });
      notification.error({
        message: "Failed to schedule interview",
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    }
  };

  const handleOfficeAddressOnChange = () => {
    if (interviewer) {
      const address = allInterviewerData?.interviewersDetails?.data?.find(
        (interviewer_data) => interviewer_data?.id === interviewer
      );
      const work_address_with_landmark = address?.location?.landmark
        ? `${address.location.landmark}, ${address?.location?.work_address}`
        : address?.location?.work_address;
      setAddressToDisplay(work_address_with_landmark || "Not available");
    }
  };

  useEffect(() => {
    refetch();
    handleOfficeAddressOnChange();
  }, [interviewer]);

  // Add this function before the return statement
  const validateTimeRange = (_: any, value: any) => {
    if (value && value[0] && value[1]) {
      const startTime = value[0];
      const endTime = value[1];

      if (startTime.isAfter(endTime)) {
        return Promise.reject("End time cannot be before start time");
      }
    }
    return Promise.resolve();
  };

  //Handles if the Admin is Register to Schedule a Interview
  if (!data?.adminDetails?.meta?.google?.tokens) {
    return (
      <Modal
        open
        centered
        onOk={() => handleRegisterInterview()}
        okText={"Register"}
        onCancel={() => navigate(-1)}
      >
        <div className="bg-white px-8 py-6 text-center">
          <h1 className="text-xxl font-bold text-primary mb-4">
            You are Not Registered To Schedule An Interview
          </h1>
        </div>
      </Modal>
    );
  }

  //Handle Loading
  if (userLoading) {
    return <Loading tip="Loading.." />;
  }

  return (
    <>
      <Modal
        open
        onOk={() => {
          form.submit();
        }}
        closeIcon={false}
        width={"50%"}
        centered
        confirmLoading={scheduleInterviewLoading}
        onCancel={() => navigate(-1)}
        okText={"Schedule"}
        destroyOnClose={true}
        className="schedule-interview-modal"
      >
        <Row gutter={[24, 24]}>
          <Col span={14}>
            <div className="mb-6">
              <h1 className="text-2xl font-semibold text-primary text-center">
                Schedule Interview
              </h1>
              <p className="text-gray-500 text-center mt-2">
                Fill in the details below to schedule an interview
              </p>
            </div>
            <Form
              form={form}
              layout="vertical"
              onFinish={(formData) => {
                try {
                  if (formData.time && formData.time[0] && formData.time[1]) {
                    formData.time = {
                      start: formData.time[0],
                      end: formData.time[1]
                    };
                  }
                  scheduleInterview(formData);
                } catch (error) {
                  console.error("Form submission error:", error);
                  notification.error({ message: "Error processing form data" });
                }
              }}
              className="space-y-6"
            >
              <div className="bg-gray-50 p-6 rounded-lg">
                <div className="grid grid-cols-2 gap-6">
                  <Form.Item
                    name={"interviewer"}
                    label={<span className="font-medium text-gray-700">Interviewer</span>}
                    rules={[{ required: true, message: "Interviewer is required" }]}
                  >
                    <Select
                      onSelect={(val) => {
                        setInterviewer(val);
                        setDatePickerDisabled(false);
                        form.setFieldsValue({ time: undefined });
                      }}
                      className="w-full"
                      options={interviewers}
                      placeholder="Select interviewer"
                    />
                  </Form.Item>

                  <Form.Item
                    name={"date"}
                    label={<span className="font-medium text-gray-700">Date</span>}
                    initialValue={date}
                  >
                    <DatePicker
                      onChange={(val) => {
                        setDate(dayjs(val));
                        refetch();
                      }}
                      disabled={datePickerDisabled}
                      disabledDate={disablePastDates}
                      className="w-full"
                      placeholder="Select date"
                    />
                  </Form.Item>
                </div>

                <Form.Item
                  label={<span className="font-medium text-gray-700">Interview Time</span>}
                  name={"time"}
                  rules={[
                    { required: true, message: "Interview Time is required" },
                    { validator: validateTimeRange }
                  ]}
                >
                  <TimePicker.RangePicker
                    use12Hours
                    format="h:mm A"
                    minuteStep={15}
                    className="w-full"
                    placeholder={["Start Time", "End Time"]}
                    disabled={datePickerDisabled}
                  />
                </Form.Item>

                <Form.Item
                  label={<span className="font-medium text-gray-700">Interview Method</span>}
                  name={"type"}
                  rules={[{ required: false, message: "Interview Method is required" }]}
                  initialValue={CalendarEventType.InterviewOffline}
                >
                  <Select
                    defaultValue={CalendarEventType.InterviewOffline}
                    className="w-full"
                    options={[
                      {
                        disabled: !userDetails?.userDetailsForAdmin?.email,
                        value: "INTERVIEW_ONLINE",
                        label: "Online Interview"
                      },
                      {
                        value: "INTERVIEW_OFFLINE",
                        label: "Offline Interview"
                      }
                    ]}
                  />
                </Form.Item>

                {interviewTypeValue === "INTERVIEW_OFFLINE" && interviewer && (
                  <div className="space-y-3">
                    <Form.Item name="interview_location">
                      <Radio.Group defaultValue={"interviewerOffice"}>
                        <Radio value="interviewerOffice" className="custom-radio">
                          Interviewer Office
                        </Radio>
                      </Radio.Group>
                    </Form.Item>

                    <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
                      <div className="flex items-start">
                        <div className="mr-2">📍</div>
                        <div>{addressToDisplay}</div>
                      </div>
                    </div>
                  </div>
                )}

                <Form.Item
                  label={<span className="font-medium text-gray-700">Template</span>}
                  name={"template_id"}
                  tooltip="Select the template that will be used to evaluate the candidate"
                  rules={[{ required: true, message: "Feedback template is required" }]}
                >
                  <Select options={templates} className="w-full" placeholder="Select template" />
                </Form.Item>
              </div>

              {!userDetails?.userDetailsForAdmin?.email && (
                <div className="mt-4">
                  <Alert
                    closable
                    type="warning"
                    message={
                      <div>
                        <b>{userDetails?.userDetailsForAdmin?.name}</b> does not have an email id
                        associated with the account. Only offline interview could be scheduled.
                        <br />
                        Click <Link to={`/dashboard/users/${Number(params.user_id)}`}>here</Link> to
                        update the user.
                      </div>
                    }
                  />
                </div>
              )}
            </Form>
          </Col>
          <Col span={10}>
            <Card className="h-full">
              <div className="mb-4">
                <h2 className="text-lg font-medium text-gray-700">Today's Schedule</h2>
                <p className="text-sm text-gray-500">
                  View the interviewer's availability for today
                </p>
              </div>
              <InterviewerTimeline
                busySlots={
                  timeSlots?.getCalendarTimeSlots?.map((slot) => ({
                    start: slot?.start || "",
                    end: slot?.end || "",
                    title: "Busy"
                  })) || []
                }
                isLoading={timeSlotsLoading}
                selectedDate={date}
              />
            </Card>
          </Col>
        </Row>
      </Modal>
      <Outlet />
    </>
  );
};
export default CreateNewInterviewFrUser;
