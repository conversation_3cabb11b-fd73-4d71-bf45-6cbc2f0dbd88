import { UserDeleteOutlined } from "@ant-design/icons";
import { BiUser } from "react-icons/bi";
import { BsClipboard2, BsPersonCheck } from "react-icons/bs";
import { CgFileDocument, CgProfile } from "react-icons/cg";
import { FaRegAddressBook, FaUser } from "react-icons/fa6";
import { GoShareAndroid, GoTools } from "react-icons/go";
import { GrTemplate } from "react-icons/gr";
import { HiOutlineClipboard, HiOutlineDocumentDuplicate } from "react-icons/hi";
import { LiaUserTieSolid } from "react-icons/lia";
import { MdOutlineFeedback, MdOutlineInsertPhoto } from "react-icons/md";
import {
  RiDashboardLine,
  RiExchangeLine,
  RiLayoutGridLine,
  RiSendPlane2Line,
  RiShieldStarLine,
  RiTeamFill
} from "react-icons/ri";
import { TfiLayoutWidthDefault } from "react-icons/tfi";
import NewReleasesIcon from "../pages/new_releases/New_Releases_Icon";

import { LuBar<PERSON>hart3, <PERSON><PERSON>serCog } from "react-icons/lu";

// Define a type for the icon map
interface IconMap {
  [key: string]: React.ElementType;
}

export const iconMap: IconMap = {
  RiLayoutGridLine: RiLayoutGridLine,
  UserDeleteOutlined: UserDeleteOutlined,
  BiUser: BiUser,
  BsClipboard2: BsClipboard2,
  BsPersonCheck: BsPersonCheck,
  FaRegAddressBook: FaRegAddressBook,
  FaUser: FaUser,
  GoShareAndroid: GoShareAndroid,
  GoTools: GoTools,
  GrTemplate: GrTemplate,
  HiOutlineDocumentDuplicate: HiOutlineDocumentDuplicate,
  MdOutlineFeedback: MdOutlineFeedback,
  MdOutlineInsertPhoto: MdOutlineInsertPhoto,
  RiDashboardLine: RiDashboardLine,
  RiSendPlane2Line: RiSendPlane2Line,
  RiTeamFill: RiTeamFill,
  RiShieldStarLine: RiShieldStarLine,
  RiExchangeLine: RiExchangeLine,
  TfiLayoutWidthDefault: TfiLayoutWidthDefault,
  NewReleasesIcon: NewReleasesIcon,
  profile: CgProfile,
  assessment: HiOutlineClipboard,
  document: CgFileDocument,
  interview: LiaUserTieSolid,
  LuBarChart3: LuBarChart3,
  UserCog: LuUserCog
};

export const getIconComponent = (iconName: string): React.ElementType | null => {
  const icon = iconMap[iconName];

  if (!icon) {
    return iconMap["TfiLayoutWidthDefault"];
  }
  return icon;
};
