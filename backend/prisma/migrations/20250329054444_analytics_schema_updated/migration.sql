/*
  Warnings:

  - The values [ONBOARDING_START] on the enum `UserFunnelStage` will be removed. If these variants are still used in the database, this will fail.
  - Added the required column `session_id` to the `user_session_screen_recording` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "UserFunnelStage_new" AS ENUM ('VISITOR', 'LOGIN_PAGE', 'SIGNUP_START', 'SIGNUP_COMPLETE', 'GENERAL_DETAILS', 'WORK_EXPERIENCE', 'DOCUMENTS', 'DOCUMENT_VERIFICATION', 'SKILLS_ASSESSMENT', 'INTERVIEW_SCHEDULED', 'INTERVIEW_COMPLETED', 'TRAINING_STARTED', 'TRAINING_COMPLETED', 'SCREENING_TEST', 'ONBOARDING_COMPLETE', 'ACTIVE_USER', 'TECHNICIAN_HOMEPAGE', 'SERVICE_PROVIDER_HOMEPAGE', 'TRANSFERRED_TO_TMS');
ALTER TABLE "user_activity" ALTER COLUMN "funnel_stage" TYPE "UserFunnelStage_new" USING ("funnel_stage"::text::"UserFunnelStage_new");
ALTER TYPE "UserFunnelStage" RENAME TO "UserFunnelStage_old";
ALTER TYPE "UserFunnelStage_new" RENAME TO "UserFunnelStage";
DROP TYPE "UserFunnelStage_old";
COMMIT;

-- AlterTable
ALTER TABLE "app_installation" ADD COLUMN     "app_version" TEXT,
ADD COLUMN     "installation_source" TEXT,
ADD COLUMN     "last_active_at" TIMESTAMP(3),
ADD COLUMN     "meta" JSONB DEFAULT '{}',
ADD COLUMN     "push_enabled" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "push_token" TEXT,
ADD COLUMN     "session_count" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "total_time_spent" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "team_member_invitations" ALTER COLUMN "expires_at" SET DEFAULT NOW() + interval '7 days';

-- AlterTable
ALTER TABLE "user_session_screen_recording" ADD COLUMN     "session_id" TEXT NOT NULL;

-- CreateIndex
CREATE INDEX "app_installation_last_active_at_idx" ON "app_installation"("last_active_at");

-- CreateIndex
CREATE INDEX "app_installation_app_version_idx" ON "app_installation"("app_version");

-- AddForeignKey
ALTER TABLE "user_session_screen_recording" ADD CONSTRAINT "user_session_screen_recording_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "user_activity"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
