import { ArrowR<PERSON>, FileText, X } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface PolicyReminderProps {
  unreadCount: number;
  onClose: () => void;
}

export function PolicyReminder({ unreadCount, onClose }: PolicyReminderProps) {
  const navigate = useNavigate();

  const onViewPolicies = () => {
    navigate("/policies");
    onClose();
  };
  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-xl max-w-xs w-full py-6 px-4  relative">
        <button
          onClick={onClose}
          className="absolute right-2 top-2 p-1.5 hover:bg-gray-100 rounded-full transition-colors"
        >
          <X className="h-4 w-4 text-gray-400" />
        </button>

        <div className="w-14 h-14 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-3">
          <FileText className="h-7 w-7 text-white" />
        </div>

        <h3 className="text-lg font-semibold text-center text-gray-800 mb-2">
          Policy Review Required
        </h3>

        <p className="text-gray-600 text-center text-sm mb-4">
          You have <span className="font-semibold text-blue-600">{unreadCount}</span>{" "}
          {unreadCount === 1 ? "policy" : "policies"} that require your acknowledgment.
        </p>

        <button
          onClick={onViewPolicies}
          className="w-full flex items-center justify-center gap-2 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
        >
          View Policies
          <ArrowRight className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
